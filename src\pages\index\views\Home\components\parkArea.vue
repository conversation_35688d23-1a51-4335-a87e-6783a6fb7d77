<template>
  <div>
    <div id="parkArea" ref="parkArea" class="contain"></div>
  </div>
</template>

<script>
import { getIndustrialParkAreaApi } from "@/pages/index/data/api/ComponyWatch";
import * as echarts from "echarts";
export default {
  data() {
    return {
      chartData: {},
      yData: [],
      vacantAreaData: [],
      leasedAreaData: [],
      dueData: [],
    };
  },
  mounted() {
    this.initData();
  },
  created() {},
  methods: {
    async initData() {
      //leasedArea  租赁面积
      //vacantArea  空置面积
      //due  预计6个月到期面积
      this.chartData = await getIndustrialParkAreaApi("");
      let vacantAreaWidthNum = 0;
      let leasedAreaWidthNum = 0;
      let dueWidthNum = 0;
      console.table(this.chartData.data)
      this.chartData.data.forEach((e) => {
        this.yData.push(e.parkName);
        if (e.totalArea != 0) {
          vacantAreaWidthNum = (e.vacantArea / e.totalArea) * 100;
        
          this.vacantAreaData.push(vacantAreaWidthNum.toFixed(2));
          console.log(this.vacantAreaData,e.parkName)
          leasedAreaWidthNum = (e.leasedArea / e.totalArea) * 100;
          
          this.leasedAreaData.push(leasedAreaWidthNum.toFixed(2));
          console.log(this.leasedAreaData,e.parkName)
          dueWidthNum = (e.due / e.totalArea) * 100;
       
          this.dueData.push(dueWidthNum.toFixed(2));
          console.log(this.dueData,e.parkName)
        }
      });
      this.drawLine();
    },
    drawLine() {
      let myChart = echarts.init(this.$refs.parkArea);
      let option = {
        title: {
          text: "园区面积",
          textStyle: {
            fontStyle: "normal",
            fontWeight: "bold",
            fontSize: 22,
          },
        },
        dataZoom: [
          {
            type: "slider",
            realtime: true, // 拖动时，是否实时更新系列的视图
            start: 90,
            end: 100,
            startValue: 0,
            endValue: 10,
            width: 8,
            height: "90%",
            top: "5%",
            right: 0,
            brushSelect: false,
            yAxisIndex: [0, 1], // 控制y轴滚动
            fillerColor: "#0093ff", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            backgroundColor: "#cfcfcf", //两边未选中的滑动条区域的颜色
            handleSize: 0, // 两边手柄尺寸
            showDataShadow: false, //是否显示数据阴影 默认auto
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            zoomLock: true,
            moveHandleStyle: {
              opacity: 0,
            },
          },
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            // params 是一个数组，数组中包含每个系列的数据信息
            var res = params[0].name + "<br/>";
            for (var i = 0, l = params.length; i < l; i++) {
              res +=
                "<br/>" +
                params[i].marker +
                params[i].seriesName +
                " : " +
                params[i].value +
                "%<br/>";
            }
            return res;
          },
        },
        legend: {
          top: "5px",
          right: "40px",
        },
        grid: {
          left: "0%",
          right: "10%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          max: 100,
        },
        yAxis: {
          type: "category",
          data: this.yData.reverse(),
        },
        series: [
          {
            name: "空置面积",
            type: "bar",
            label: {
              show: true,
              formatter: "{c} %",
              position: "right",
            },
            // showBackground: true,
            barWidth: "25%",
            itemStyle: {
              // 定义一个颜色的渐变
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "rgba(153,195,255,.5)" }, // 柱图渐变色
                { offset: 0.5, color: "rgba(153,195,255,1)" }, // 中间偏下
                { offset: 1, color: "rgba(181,246,255,1)" }, // 顶部
              ]),
              borderRadius: [0, 20, 20, 0],
            },
            data: this.vacantAreaData.reverse(),
          },
          {
            name: "租赁面积",
            type: "bar",
            label: {
              show: true,
              formatter: "{c} %",
              position: "right",
            },
            // showBackground: true,
            barWidth: "25%",
            itemStyle: {
              // 定义一个颜色的渐变
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "rgba(0,106,255,1)" }, // 柱图渐变色
                { offset: 0.5, color: "rgba(0,106,255,.5)" }, // 中间偏下
                { offset: 1, color: "rgba(0,106,255,1)" }, // 顶部
              ]),
              borderRadius: [0, 20, 20, 0],
            },
            data: this.leasedAreaData.reverse(),
          },
          {
            name: "预计6个月到期",
            type: "bar",
            label: {
              show: true,
              formatter: "{c} %",
              position: "right",
            },
            // showBackground: true,
            barWidth: "25%",
            itemStyle: {
              // 定义一个颜色的渐变
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "rgba(245,158,11,1)" }, // 柱图渐变色
                { offset: 0.5, color: "rgba(255,241,115,.5)" }, // 中间偏下
                { offset: 1, color: "rgba(255,241,115,1)" }, // 顶部
              ]),
              borderRadius: [0, 20, 20, 0],
            },
            data: this.dueData.reverse(),
          },
        ],
      };
      // 绘制图表
      myChart.setOption(option);
      //多图表自适应
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contain {
  width: 100%;
  height: 400px;
}
</style>

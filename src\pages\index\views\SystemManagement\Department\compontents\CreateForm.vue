<!--
* <AUTHOR>
* @time 2020-8-24
* @dec 系统管理 - 部门管理页面 --新增部门
-->
<template>
  <a-modal
    :title="title"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <!-- 检查是否有 id 并且大于0，大于0是修改。其他是新增，新增不显示主键ID -->
        <a-form-item v-show="data && data.id > 0" label=""> </a-form-item>
        <a-form-item label="部门名称">
          <a-input
            v-decorator="[
              'name',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入部门名称'
                  }
                ]
              }
            ]"
            autoComplete="off"
            placeholder="请输入部门名称"
          >
          </a-input>
        </a-form-item>
        <a-form-item label="公司类型">
          <a-radio-group
            @change="switchType"
            v-decorator="[
              'companyType',
              {
                initialValue: '0',
                rules: [{ required: true, message: '请选择公司类型！' }]
              }
            ]"
            :disabled="type === 'edit' || type === 'addChild'"
          >
            <a-radio value="0" style="margin-top:8px">
              内部
            </a-radio>
            <a-radio value="1" style="margin-top:10px">
              外部
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- <a-form-item label="上级部门">
          <a-spin :spinning="departmentListLoading">
            <a-select
              allowClear
              v-decorator="['parentId']"
              placeholder="请选择部门名称"
            >
              <a-select-option v-for="data in departmentList" :key="data.id">{{
                data.name
              }}</a-select-option>
            </a-select>
          </a-spin>
        </a-form-item> -->
        <a-form-item label="部门描述">
          <a-textarea
            placeholder="请添加部门描述"
            v-decorator="['remark', {}]"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick";
import {
  ApiSecuritycreate,
  ApiSecurityupdate
  // ApiSecurityfindDepartmentTree
} from "@/pages/index/data/api/SystemManagement/Department";
// 表单字段
const fields = ["id", "companyType", "name", "remark", "parentId"];

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    },
    type: {
      type: String
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      loading: false,
      title: "新建部门",
      form: this.$form.createForm(this),
      departmentList: [], //部门列表
      departmentListLoading: false //部门列表loading
    };
  },
  created() {
    console.log("custom modal created");
    // 防止表单未注册
    fields.forEach(v => this.form.getFieldDecorator(v));
    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      if (this.type === "edit") {
        this.title = "修改部门";
        this.data && this.form.setFieldsValue(pick(this.data, fields));
      } else if (this.type === "addChild") {
        this.title = "新建子部门";
        let formData = ["id", "companyType", "parentId"];
        this.data && this.form.setFieldsValue(pick(this.data, formData));
      } else {
        this.title = "新建部门";
      }
    });
  },
  watch: {
    // visible(value) {
    //   if (value) {
    //     this.$nextTick(() => {
    //       this.findDepartmentTree(this.form.getFieldValue("companyType"));
    //     });
    //     if (this.data.parentId == "0") {
    //       this.data.parentId = [];
    //     }
    //   }
    // }
  },
  methods: {
    handleOk() {
      this.loading = true;
      this.form.validateFields((errors, values) => {
        if (!errors) {
          if (this.type === "edit") {
            values.id = this.data.id;
            // 修改
            const requestParameters = Object.assign({}, values);
            ApiSecurityupdate(requestParameters)
              .then(res => {
                console.log("loadData", res);
                this.$emit("cancel");
                this.loading = false;
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("修改成功");
              })
              .finally(() => {
                this.loading = false;
              });
          } else if (this.type === "addChild") {
            // 新增子部门
            values.id = this.data.id;
            const requestParameters = Object.assign({}, values);
            requestParameters.parentId = requestParameters.id;
            requestParameters.id = "";
            ApiSecuritycreate(requestParameters)
              .then(res => {
                console.log("loadData", res);
                this.$emit("cancel");
                this.loading = false;
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("新建成功");
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            // 新增
            const requestParameters = Object.assign({}, values);
            ApiSecuritycreate(requestParameters)
              .then(res => {
                console.log("loadData", res);
                this.$emit("cancel");
                this.loading = false;
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("新建成功");
              })
              .finally(() => {
                this.loading = false;
              });
          }
        } else {
          this.loading = false;
        }
      });
    }
    //根据公司类型查询上级部门
    // findDepartmentTree(type) {
    //   const params = {
    //     companyType: type
    //   };
    //   this.departmentListLoading = true;
    //   this.form.setFieldsValue({
    //     parentId: undefined
    //   });
    //   ApiSecurityfindDepartmentTree(params)
    //     .then(res => {
    //       this.departmentList = res.data;
    //       this.data &&
    //         this.form.setFieldsValue({ parentId: this.data.parentId });
    //     })
    //     .finally(() => {
    //       this.departmentListLoading = false;
    //     });
    // },
    //切换公司类型
    //   switchType(e) {
    //     let chooseUType = e.target.value;
    //     let companyType = chooseUType;
    //     console.log("biaodan", fields);
    //     this.findDepartmentTree(companyType);
    //   }
  }
};
</script>

<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 时间选择控件DOM/Form
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <time-option-dom :data="formData"></time-option-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <time-option-form v-bind:data.sync="formData"></time-option-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 时间选择控件 DOM/Form
import {
  TimeOptionDom,
  TimeOptionForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/TimeSelection";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    TimeOptionDom,
    TimeOptionForm
  },
  data() {
    return {
      formData: {
        inputTitle: undefined, //标题
        placeholderText: undefined, //提示文字
        timeType: "HH:mm", //时间选择单选按钮
        notNull: true
      },
      moduleVoList: []
    };
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: this.formData.placeholderText, //提示文字
        notNull: this.formData.notNull ? 1 : 0, //是否必填
        inputType: "timeSelection",
        inputName: this.formData.timeType
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      let dataOption = [];
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        this.switchData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "timeSelection") {
              dataOption.push(item);
            }
          });
        });
        console.log(dataOption);
        this.formData = {
          inputTitle:
            dataOption && dataOption[dataOption.length - 1].inputTitle, //标题
          placeholderText:
            dataOption && dataOption[dataOption.length - 1].placeholder, //提示文字
          timeType: dataOption && dataOption[dataOption.length - 1].inputName, //时间选择单选按钮
          notNull: dataOption && dataOption[dataOption.length - 1].notNull
        };
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

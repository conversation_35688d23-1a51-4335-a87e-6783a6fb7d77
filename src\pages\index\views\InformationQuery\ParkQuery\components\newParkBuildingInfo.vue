<template>
  <div class="form">
    <a-modal
      :title="title"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
    >
    <!-- 园区名称、楼号 -->
      <a-form-model :model="modalForm" ref="ruleForm" :rules="rules">
        <a-form-model-item
          prop="parkName"
          label="园区名称"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            v-model="modalForm.parkName"
            disabled
            placeholder="请输入园区名称"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item
          v-if="title == '编辑楼号'"
          label="修改楼号"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          prop="parkBuildingId"
        >
          <a-select
            v-model="modalForm.parkBuildingId"
            placeholder="全部"
          >
            <a-select-option
              :value="item.parkBuildingId"
              v-for="item in buildingsArr"
              :key="item.parkBuildingId"
              >{{ item.buildingNumber }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          prop="buildingNumber"
          label="楼号"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            v-model="modalForm.buildingNumber"
            placeholder="请输入楼号"
          ></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import { 
  ApiParkBuildingSave,
  ApiParkBuildingModify,
  ApiGetParkBuildings
} from "@/pages/index/data/api/InfomationQuery";
export default {
  props: {
    title:String,
    visible: Boolean,
    newBuildingRecord:Object
  },
  data() {
    return {
      isFlag:true,
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      modalForm: {
        parkId:"",//园区id
        parkName:"",//园区名称
        buildingNumber:"",//楼号（楼层名称）
        parkBuildingId:undefined,
      },
      buildingsArr:[],
      rules: {
        parkName: [
          { required: true, message: "请选择园区名称", trigger: "blur" },
        ],
        parkBuildingId: [
          { required: true, message: "请选择修改楼号", trigger: "blur" },
        ],
        buildingNumber: [
          { required: true, message: "请选择楼号", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    visible: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.modalForm.parkId = this.newBuildingRecord.parkId
          this.modalForm.parkName = this.newBuildingRecord.parkName
          console.log(val);
          if (this.title == '编辑楼号') {
            this.getParkBuildingsList()
          }
        }
      }
    },
  },
  mounted() {
    console.log('newParkInfothis.visible',this.visible);
  },
  methods: {
    // 获取楼号列表
    getParkBuildingsList(){
      ApiGetParkBuildings({ parkName: this.newBuildingRecord.parkName }).then(res => {
        this.buildingsArr = res.data
        console.log('点击编辑楼号按钮调用的方法',this.buildingsArr );
      });
    },
    //弹框点击确认调用的方法
    handleOk: function() {
      if(!this.isFlag) return
      this.isFlag = false
      console.log(this.modalForm,  "kkkkk");
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.title == '新增楼号') {
            let params = this.modalForm
            delete params.parkBuildingId
            console.log(params);
            ApiParkBuildingSave(params).then(res => {
              console.log(res.data);
              if (res.code == 0) {
                this.handleCancel()
                this.$message.info(res.data);
                this.$emit("refresh", true);
              }else{
                this.$message.error(res.data||res.msg);
              }
            }).catch((error) => {
              this.$message.error(error.data.msg);
            })
          } else {
            // 修改楼号
            console.log(this.modalForm);
            ApiParkBuildingModify(this.modalForm).then(res => {
              console.log(res.data);
              //将弹框隐藏
              if (res.code == 0) {
                this.handleCancel()
                this.$message.info(res.data);
                this.$emit("refresh", true);
              }else{
                this.$message.error(res.data||res.msg);
              }
            }).catch((error) => {
              this.$message.error(error.data.msg);
            })
          }
        }
      });
      setTimeout(()=>{
        this.isFlag = true
      },2000)
    },
    //关闭弹框调用的方法
    handleCancel: function() {
      //将弹框隐藏
      this.$emit("close", false);
      // 清空表格
      this.modalForm = {
        parkId:"",//园区id
        parkName:"",//园区名称
        buildingNumber:"",//楼号（楼层名称）
        parkBuildingId:undefined,
      }
      //并重置校验
      this.$refs.ruleForm.resetFields()
    },
  }
};
</script>
<style lang="less" scoped>

</style>

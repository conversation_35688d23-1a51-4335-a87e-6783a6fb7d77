/**
 * 审批流程 工具包
 */
import { deepClone } from "@/common/utils";
import { judgeNodeTypeToChange } from "@/components/DrawFlow/src/utils";
// 将前端总数据转换成后端需要的格式
export function formatSubmitData(data) {
  let params = deepClone(data);
  let formModules =
    params.formDesign &&
    params.formDesign.map(item => {
      return {
        id: item.id,
        // key
        inputId: item.key,
        //组件中文名
        inputName: item.dataname,
        //组建的type
        inputType: item.datatype,
        optionsData: JSON.stringify(item.data.optionsData),
        placeholder: JSON.stringify(item.data.placeholder),
        inputNumber: item.data.inputNumber,
        inputRegex: item.data.inputRegex,
        inputTitle: item.data.inputTitle || null,
        inputValue: item.data.inputValue,
        isRegex: item.data.isRegex ? 1 : 0,
        notNull: item.data.notNull ? 1 : 0,
        orderBy: item.data.orderBy
      };
    });
  // 转换流程
  let processDesign =
    params.processDesign &&
    params.processDesign.map(item => {
      let approvalAuths, authType, formModuleAuths, roleAndUserAuth, ruleInfo;
      // 非规则和结束节点数据
      if (item.type !== "3" && item.type !== "5") {
        // 审批设置
        approvalAuths =
          item.data.approvalAuths &&
          item.data.approvalAuths.map(auths => {
            return {
              // 是否有权限：0 否；1 是
              code: auths.isAdd ? 1 : 0,
              // 操作按钮(字典dict_value)
              operation: auths.dictValue
            };
          });
        // 设置审批人。1-所有人 2-指定角色 3-指定用户
        authType =
          item.data.roleAndUserAuth && item.data.roleAndUserAuth.authType;
        // 表单操作权限 nodeType = 1 选填
        // if (item.type !== "1") {
        formModuleAuths =
          item.data.formModuleAuths &&
          item.data.formModuleAuths.map(auths => {
            return {
              // 组件ID
              inputId: auths.key,
              // 权限code (1表示可编辑，2表示可读，3表示隐藏)
              code: auths.isEdit
                ? 1
                : auths.isReadOnly
                ? 2
                : auths.isHide
                ? 3
                : undefined
            };
          });
        // }
        // 审批人设置
        roleAndUserAuth = {
          // 审批方式。1.依次审批，2.会签，3.或签
          approvalMethod:
            item.data.roleAndUserAuth &&
            item.data.roleAndUserAuth.approvalMethod
        };
        // 设置审批人角色节点 autyType = 2 必填
        if (authType === 2) {
          roleAndUserAuth.roleAuths = item.data.roleAndUserAuth.tags.map(
            auths => {
              return {
                // 角色ID
                roleId: auths.id,
                // 角色name
                roleName: auths.name
              };
            }
          );
        }
        // 设置审批人用户节点 autyType = 3 必填
        if (authType === 3) {
          roleAndUserAuth.userAuths = item.data.roleAndUserAuth.tags.map(
            auths => {
              return {
                // 用户ID
                userId: auths.userId,
                // 用户name
                userName: auths.name
              };
            }
          );
        }
        // 设置审批人组织结构节点 autyType = 5 必填
        if (authType === 5) {
          roleAndUserAuth.groupAuths = item.data.roleAndUserAuth.tags.map(
            auths => {
              return {
                // 组织结构ID
                groupId: auths.id,
                // 组织结构name
                groupName: auths.groupName
              };
            }
          );
        }
        // console.log("adminAuths", item.data.roleAndUserAuth);
      }
      // 规则设置
      if (item.type === "3") {
        ruleInfo = item.data.ruleInfo;
        if (ruleInfo) {
          ruleInfo.conditions = ruleInfo.conditions.map(c => {
            c.formData = JSON.parse(c.formData);
            return {
              // 运算符号
              conditionRule: c.conditionRule,
              // 组件ID
              inputId: c.formData && c.formData.inputId,
              // 组件在表单中定义的输入框类型
              inputType: c.formData && c.formData.inputType,
              inputName: c.formData && c.formData.inputName,
              // 运算关系
              relation: c.relation,
              // 所设置的值
              value: c.value,
              // 规则值
              formKey: c.formKey,
              formKeyName: c.formKeyName
            };
          });
        }
      }
      return {
        // 元素类型。1.开始节点。2.审批节点。3.规则节点。4.抄送节点。5.结束节点
        nodeType: item.type,
        // 节点分组，前端展示使用
        groupId: item.groupId,
        // 节点父分组，前端展示使用
        parentGroupId: item.groupPid,
        // 节点名称
        nodeName: item.title,
        isRow: item.isRow,
        // 节点ID
        nodeId: item.id,
        // 前一个节点ID，多个逗号隔开
        preNodeId: item.pids && item.pids.join(","),
        // 审批设置
        approvalAuths,
        // 设置审批人。1-所有人 2-指定角色 3-指定用户
        authType,
        // 表单操作权限 nodeType = 1 选填
        formModuleAuths,
        // 审批人设置
        roleAndUserAuth,
        // 规则设置
        ruleInfo
      };
    });
  return {
    // id
    templateId: params.id,
    // 表单引擎
    formModules,
    // 模版ID
    gruopId: params.basicSettings.gruopId,
    // 模版名称
    templateName: params.basicSettings.templateName,
    // 图标
    icon: params.basicSettings.icon,
    // 审批流程说明
    remark: params.basicSettings.remark,
    // 审批类型
    approvalType: params.basicSettings.approvalType,
    // 业务类型
    businessType: params.basicSettings.businessType,

    // 流程设置
    processNodes: processDesign
  };
}

/**
 * 将后端总数据转换成前端需要的格式
 */
export function formatResponse(data) {
  let obj = {
    basicSettings: basicSettings(data),
    formDesign: formDesign(data),
    processDesign: processNodes(data)
  };
  return obj;
}
//设置基础配置
function basicSettings({
  gruopId,
  templateName,
  templateId,
  icon,
  remark,
  approvalType,
  businessType
}) {
  let basicSettings = {
    gruopId,
    templateName,
    templateId,
    icon,
    remark,
    approvalType,
    businessType
  };
  return basicSettings;
}
//设置表单设计
export function formDesign({ formModules }) {
  if (!formModules) return;
  let formArr = formModules.map(item => {
    return {
      id: item.id,
      //   // key
      key: item.inputId,
      //   //组件中文名
      dataname: item.inputName,
      //   //组建的type
      datatype: item.inputType,
      data: {
        optionsData:
          (item.optionsData && JSON.parse(item.optionsData)) || undefined,
        placeholder: (item.placeholder && JSON.parse(item.placeholder)) || {},
        inputNumber: item.inputNumber || undefined,
        inputRegex: item.inputRegex || undefined,
        inputTitle: item.inputTitle || undefined,
        inputValue: item.inputValue || undefined,
        isRegex: item.isRegex === 1 ? true : false,
        notNull: item.notNull === 1 ? true : false,
        orderBy: item.orderBy
      }
    };
  });
  return formArr;
}
//设置流程
function processNodes({ processNodes, formModules }) {
  if (!processNodes) return;
  return processNodes.map(processNode => {
    let {
      approvalAuths,
      authType,
      formModuleAuths,
      roleAndUserAuth,
      ruleInfo
    } = processNode;

    if (processNode.nodeType !== "3" && processNode.nodeType !== "5") {
      approvalAuths =
        approvalAuths &&
        approvalAuths.map(auths => {
          return {
            nameCn: auths.nameCn,
            isAdd: auths.code ? true : false,
            dictValue: auths.operation
          };
        });

      // 设置审批人。1-所有人 2-指定角色 3-指定用户
      if (roleAndUserAuth) roleAndUserAuth.authType = authType;
    }
    // if (processNode.nodeType !== "1") {
    formModuleAuths =
      formModuleAuths &&
      formModuleAuths.map(auths => {
        return {
          // 组件ID
          key: auths.inputId,
          // 权限code (1表示可编辑，2表示可读，3表示隐藏)
          isEdit: auths.code === 1,
          isReadOnly: auths.code === 2,
          isHide: auths.code === 3
        };
      });
    // }
    // 设置审批人角色节点 autyType = 2 必填
    if (authType === 2) {
      roleAndUserAuth.tags =
        roleAndUserAuth.roleAuths &&
        roleAndUserAuth.roleAuths.map(auths => {
          return {
            // 角色ID
            id: auths.roleId,
            // 角色name
            name: auths.roleName
          };
        });
    }
    // 设置审批人用户节点 autyType = 3 必填
    if (authType === 3) {
      roleAndUserAuth.tags =
        roleAndUserAuth.userAuths &&
        roleAndUserAuth.userAuths.map(auths => {
          return {
            // 用户ID
            userId: auths.userId,
            // 用户name
            name: auths.userName
          };
        });
    }
    // 设置审批人组织结构节点 autyType = 5 必填
    if (authType === 5) {
      roleAndUserAuth.tags =
        roleAndUserAuth.groupAuths &&
        roleAndUserAuth.groupAuths.map(auths => {
          return {
            // 组织结构ID
            id: auths.groupId,
            // 组织结构name
            groupName: auths.groupName
          };
        });
    }
    // 设置发起人和所有人节点
    if (authType === 1 || authType === 4) {
      roleAndUserAuth.tags = [];
    }
    // 规则设置
    if (processNode.nodeType === "3") {
      if (ruleInfo) {
        ruleInfo.conditions = ruleInfo.conditions.map(c => {
          let e = formModules.find(d => {
            return d.inputId === c.inputId;
          });
          return {
            // 运算符号
            conditionRule: c.conditionRule,
            // 运算关系
            relation: c.relation,
            // 所设置的值
            value: c.value,
            formData: JSON.stringify({
              // 组件ID
              inputId: e.inputId,
              inputTitle: e.inputTitle || e.inputName,
              // 组件在表单中定义的输入框类型
              inputType: e.inputType
            }),
            // 规则值
            formKey: c.formKey,
            formKeyName: c.formKeyName
          };
        });
      }
    }
    let data = {
      approvalAuths,
      roleAndUserAuth,
      formModuleAuths,
      ruleInfo
    };
    let node = {
      // 元素类型。1.开始节点。2.审批节点。3.规则节点。4.抄送节点。5.结束节点
      type: processNode.nodeType,
      // 节点分组，前端展示使用
      groupId: processNode.groupId,
      // 节点父分组，前端展示使用
      groupPid: processNode.parentGroupId,
      // 节点名称
      title: processNode.nodeName,
      isRow: processNode.isRow === "true",
      // 节点ID
      id: processNode.nodeId,
      // 前一个节点ID，多个逗号隔开
      pids: processNode.preNodeId && processNode.preNodeId.split(","),
      data
    };
    judgeNodeTypeToChange(node, node);
    return node;
  });
}

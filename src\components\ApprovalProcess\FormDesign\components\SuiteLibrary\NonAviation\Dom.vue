<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 非航资源套件
-->
<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>非航资源套件</h3>
      <a-form-model-item label="申请主题">
        <a-input
          placeholder="[申请单位]关于用途的非航资源申请单-申请人-申请日期"
        />
      </a-form-model-item>
      <a-form-model-item
        label="租约期间"
        :rules="[
          {
            message: '请选择租约期间！',
            trigger: 'change'
          }
        ]"
      >
        <template>
          <a-range-picker v-model="form.leaseDate" @change="onChangeDate" />
        </template>
      </a-form-model-item>
      <a-form-model-item label="申请资源">
        <a-select
          v-model="value"
          mode="multiple"
          placeholder="请选择"
          option-label-prop="label"
          showArrow="false"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="1" label="离港系统终端设备">
            离港系统终端设备
          </a-select-option>
          <a-select-option value="2" label="有线电视">
            有线电视
          </a-select-option>
          <a-select-option value="3" label="航班查询终端(航显工控机)">
            航班查询终端(航显工控机)
          </a-select-option>
          <a-select-option value="4" label="航班信息显示">
            航班信息显示
          </a-select-option>
          <a-select-option value="5" label="光缆(室内、室外)">
            光缆(室内、室外)
          </a-select-option>
          <a-select-option value="6" label="UTP端口(电话、网络)">
            UTP端口(电话、网络)
          </a-select-option>
          <a-select-option value="7" label="机柜单元">
            机柜单元
          </a-select-option>
          <a-select-option value="8" label="交换机端口">
            交换机端口
          </a-select-option>
          <a-select-option value="9" label="内通电话">
            内通电话
          </a-select-option>
          <a-select-option value="10" label="广播">
            广播
          </a-select-option>
          <a-select-option value="11" label="时钟">
            时钟
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <div class="non-aviation-title-button">
        <div>离港系统终端设备</div>
        <div>
          <a-button type="primary" size="small">
            <a-icon type="plus" />
            添加
          </a-button>
        </div>
      </div>

      <a-table
        :scroll="{ x: 1300 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
    </a-form-model>
  </div>
</template>
<script>
const columns = [
  {
    title: "变更类型",
    dataIndex: "changeType",
    key: "changeType"
  },
  {
    title: "终端类型",
    dataIndex: "terminalType",
    key: "terminalType"
  },
  // {
  //   title: "用途",
  //   dataIndex: "purpose",
  //   key: "purpose"
  // },
  {
    title: "数量",
    dataIndex: "quantity",
    key: "quantity"
  },
  {
    title: "使用位置",
    dataIndex: "usePosition",
    key: "usePosition"
  },
  {
    title: "信息点编号",
    dataIndex: "informationNo",
    key: "informationNo"
  },
  {
    title: "原使用位置",
    dataIndex: "originalUsePosition",
    key: "originalUsePosition"
  },
  {
    title: "原信息点编号",
    dataIndex: "originalInformationNo",
    key: "originalInformationNo"
  },
  {
    title: "备注",
    dataIndex: "remark"
  },
  {
    title: "操作",
    dataIndex: "action",
    scopedSlots: { customRender: "action" }
  }
]

const values = [
  {
    changeType: "新增", //变更类型
    terminalType: "终端电脑(普通PC)", //终端类型
    // purpose: "公司内访问", //用途
    quantity: "30", //数量
    usePosition: "UBH32134", //使用位置
    informationNo: "UBH32134", //信息编号
    originalUsePosition: "UBH32134", //原使用位置
    originalInformationNo: "UBH32134", //原信息编号
    remark: "需关注" //备注
  }
]
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      value: ["1"],
      columns,
      values,
      form: {
        nonnaviation: null
      }
    }
  },
  watch: {
    value(val) {
      console.log(`selected:`, val)
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
.non-aviation-title-button {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
}
</style>

<!-- 备案登记查看 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <div class="detail">
      <LessorInfo :totalParmas="detailParmas"></LessorInfo>
      <div>
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">合同信息</div>
            <div class="titItem" style="padding-left: 50px">
              <p style="margin-right: 16px">流程选择</p>
              <a-radio-group v-model="detailParmas.processFlow" disabled>
                <a-radio :value="0"> 正常申报流程 </a-radio>
                <a-radio :value="1"> 特殊申报流程 </a-radio>
              </a-radio-group>
            </div>
            <div v-if="detailParmas.processFlow == 1">
              <div
                style="
                  width: 100%;
                  color: #f5222d;
                  padding-left: 26px;
                  margin-bottom: 10px;
                  font-size: 16px;
                "
              >
                影响年终内控考核
              </div>
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="特殊通道情况说明(pdf盖章版)"
                    marked="true"
                    mode="preview"
                    :ifNeedPreviewOnline="true"
                    :fileList="fileList14"
                  >
                  </FileAttachmentList>
                </div>
              </div>
            </div>

            <div style="display: flex; justify-content: left">
              <div class="titItem" style="padding-left: 50px">
                <p style="margin-right: 16px">合同类型</p>
                <a-radio-group disabled v-model="detailParmas.contractType">
                  <a-radio :value="0"> 新租 </a-radio>
                  <a-radio :value="1"> 续租 </a-radio>
                  <a-radio :value="3"> 提前终止 </a-radio>
                  <a-radio :value="2"> 变更 </a-radio>
                </a-radio-group>
              </div>
              <div class="titItem" v-if="detailParmas.contractType == 2">
                <p style="margin-right: 16px">是否主体变更</p>
                <a-radio-group
                  disabled
                  v-model="detailParmas.contentChangeType"
                >
                  <a-radio :value="0"> 是 </a-radio>
                  <a-radio :value="1"> 否 </a-radio>
                </a-radio-group>
              </div>
            </div>
            <div v-if="detailParmas.contractType == 0" class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  mode="preview"
                  :ifNeedPreviewOnline="true"
                  title="租赁合同原件（word版）"
                  marked="true"
                  @deleteFile="(file, fileList) => deleteFile(file, fileList)"
                  :fileList="fileList"
                >
                </FileAttachmentList>
              </div>
            </div>
          </div>
          <OldRentalInfo
            :oldleaseTermData="{
              leaseTermEnd: detailParmas.leaseTermEnd,
              leaseTermStart: detailParmas.leaseTermStart,
              leaseTerm: detailParmas.leaseTerm,
            }"
            :relationContractId="detailParmas.relationContractId"
            @oldRental="handleOldRental"
            :processFlow="detailParmas.processFlow"
            :contractType="detailParmas.contractType"
            :isChange="detailParmas.contentChangeType"
            v-if="detailParmas.contractType != 0"
          ></OldRentalInfo>
          <div class="lessorInfo-tit" v-if="detailParmas.contractType == 3">
            <div class="tit">终止信息</div>
            <div
              v-if="detailParmas.contractType == 3"
              style="padding-left: 50px"
            >
              <div class="titItem">
                <p class="mR20">
                  <span style="color: red">*</span> 提前终止日期
                </p>
                <a-date-picker
                  disabled
                  v-model="detailParmas.cessationTime"
                  style="width: 22.5%; height: 40px"
                />
              </div>
              <div class="titItem1">
                <p class="mR20">
                  <span style="color: red">*</span> 提前终止原因
                </p>
                <a-textarea
                  compact="true"
                  disabled
                  placeholder="请输入提前终止原因"
                  style="width: 50%"
                  v-model="detailParmas.cessationReason"
                  :auto-size="{ minRows: 3, maxRows: 6 }"
                />
              </div>
              <div class="titItem1">
                <p class="mR20" style="margin-right: 21px">
                  <span style="color: red">*</span> 特殊约定
                </p>
                <a-textarea
                  compact="true"
                  disabled
                  style="width: 50%"
                  placeholder="如：交付状态等"
                  v-model="detailParmas.cessationTerm"
                  :auto-size="{ minRows: 3, maxRows: 6 }"
                />
              </div>
              <div class="titItem1" style="align-items: center">
                <p class="mR20" style="line-height: 50px">
                  <span style="color: red">*</span> 是否有违约金
                </p>
                <a-radio-group
                  v-model="detailParmas.isDedit"
                  style="width: 240px"
                  disabled
                >
                  <a-radio :value="1"> 是 </a-radio>
                  <a-radio :value="0"> 否 </a-radio>
                </a-radio-group>
                <a-input
                  :title="detailParmas.dedit"
                  v-model="detailParmas.dedit"
                  disabled
                  :placeholder="
                    detailParmas.isDedit == 0 ? '请输入原因' : '请输入金额'
                  "
                  style="width: 34%; height: 40px"
                  maxLength="50"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="lessorInfo" v-if="detailParmas.contractType == 1">
          <div class="lessorInfo-tit">
            <div class="tit">续租合同信息</div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  mode="preview"
                  :ifNeedPreviewOnline="true"
                  title="租赁合同原件（word版）"
                  marked="true"
                  @deleteFile="(file, fileList) => deleteFile(file, fileList)"
                  :fileList="fileList"
                >
                </FileAttachmentList>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <!-- 变更合同信息 -->
        <div class="lessorInfo" v-if="detailParmas.contractType == 2">
          <div class="lessorInfo-tit">
            <div class="tit">变更合同信息</div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  :ifNeedPreviewOnline="true"
                  mode="preview"
                  title="变更补充协议"
                  marked="true"
                  :fileList="fileList1"
                >
                </FileAttachmentList>
              </div>
            </div>
          </div>
        </div>
        <!-- 提前终止合同信息 -->
        <div class="lessorInfo" v-if="detailParmas.contractType == 3">
          <div class="lessorInfo-tit">
            <div class="tit">提前终止合同信息</div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  :ifNeedPreviewOnline="true"
                  mode="preview"
                  title="提前终止合同信息"
                  marked="true"
                  :fileList="fileList13"
                >
                </FileAttachmentList>
              </div>
            </div>
          </div>
        </div>
        <!-- 出租信息 -->
        <div class="lessorInfo" v-if="rentalTShow">
          <div class="lessorInfo-tit">
            <div class="tit" v-if="lessorInfoTit">变更租户信息</div>
            <div class="tit" v-else>租户信息</div>
            <div class="titItem">
              <p class="mR20"><span style="color: red">*</span> 承租方</p>
              <a-input
                :title="detailParmas.tenantry"
                v-model="detailParmas.tenantry"
                disabled
                placeholder="请输入承租方"
                style="width: 24%; height: 40px"
              />
            </div>
            <div class="titItem">
              <p class="mR20"><span style="color: red">*</span> 属地情况</p>
              <a-input
                :title="detailParmas.territorialSituation"
                v-model="detailParmas.territorialSituation"
                disabled
                placeholder="请选择属地情况"
                style="width: 24%; height: 40px"
              />
              <div
                style="width: 25%"
                v-if="detailParmas.territorialSituation == '关联属地'"
              >
                <a-input
                  :title="detailParmas.relationCompany"
                  v-model="detailParmas.relationCompany"
                  placeholder="请输入关联企业的名称"
                  disabled
                  style="width: 70%; height: 40px; margin-left: 16px"
                />
              </div>
              <div
                style="width: 25%"
                v-if="
                  detailParmas.territorialRemark &&
                  detailParmas.territorialRemark.length > 15
                "
              >
                <a-tooltip>
                  <template slot="title">
                    {{ detailParmas.territorialRemark }}
                  </template>
                  <a-input
                    v-if="detailParmas.territorialSituation == '未属地'"
                    v-model="detailParmas.territorialRemark"
                    placeholder="请填写未属地原因及属地计划"
                    disabled
                    style="width: 70%; height: 40px; margin-left: 16px"
                  />
                </a-tooltip>
              </div>
              <div v-else>
                <a-input
                  v-if="detailParmas.territorialSituation == '未属地'"
                  v-model="detailParmas.territorialRemark"
                  placeholder="请填写未属地原因及属地计划"
                  disabled
                  style="width: 70%; height: 40px; margin-left: 16px"
                />
              </div>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  mode="preview"
                  :ifNeedPreviewOnline="true"
                  title="承租方营业执照复印件（盖章版）"
                  marked="true"
                  :fileList="fileList2"
                >
                </FileAttachmentList>
              </div>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  mode="preview"
                  title="承租方法人身份证（盖章版）"
                  :ifNeedPreviewOnline="true"
                  marked="true"
                  :fileList="fileList3"
                >
                </FileAttachmentList>
              </div>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  mode="preview"
                  :ifNeedPreviewOnline="true"
                  title="信用报告"
                  marked="true"
                  :fileList="fileList4"
                >
                </FileAttachmentList>
              </div>
            </div>
          </div>
        </div>
        <!-- 出租信息 -->
        <div class="lessorInfo" v-if="clauseTShow">
          <div class="lessorInfo-tit">
            <div class="tit">出租信息</div>
            <div v-if="adressShow">
              <div
                class="titItem"
                v-for="item in detailParmas.contractBuildingInfo"
                :key="item.contractInfoId"
              >
                <p class="mR20"><span style="color: red">*</span> 出租地址</p>
                <a-input
                  :title="item.parkName"
                  v-model="item.parkName"
                  disabled
                  placeholder="园区"
                  style="
                    width: 20%;
                    height: 40px;
                    margin-right: 16px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  "
                />
                <a-input
                  :title="item.buildingName"
                  v-model="item.buildingName"
                  disabled
                  placeholder="楼号"
                  style="
                    width: 10%;
                    height: 40px;
                    margin-right: 16px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  "
                />
                <a-input
                  v-model="item.floorName"
                  disabled
                  placeholder="楼层"
                  style="width: 10%; height: 40px; margin-right: 16px"
                />
                <a-input
                  v-model="item.roomName"
                  disabled
                  placeholder="房间号"
                  style="width: 10%; height: 40px; margin-right: 16px"
                />
              </div>
              <div class="upload_file_item" v-if="clauseTShow">
                <div class="Item">
                  <FileAttachmentList
                    mode="preview"
                    title="出租物业的产权证明复印件（盖章版，如果部分没有或者全无，需上传情况说明）"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    :fileList="fileList5"
                  >
                  </FileAttachmentList>
                </div>
              </div>
            </div>
            <div style="display: flex">
              <div class="titItem">
                <p class="mR20">租赁面积</p>
                <a-input
                  v-model="detailParmas.leaseArea"
                  disabled
                  style="width: 70%; height: 40px"
                  addon-after="㎡"
                />
              </div>
              <div class="titItem">
                <p class="mR20">累计租赁面积</p>
                <a-input
                  v-model="detailParmas.accumulateLeaseArea"
                  disabled
                  style="width: 70%; height: 40px"
                  addon-after="㎡"
                />
              </div>
            </div>
            <div style="display: flex">
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 租赁用途</p>
                <a-input
                  v-model="detailParmas.leasePurpose"
                  placeholder="请输入租赁用途"
                  disabled
                  style="width: 50%; height: 40px; margin-right: 0"
                />
              </div>
              <div
                class="titItem"
                v-if="
                  detailParmas.leasePurpose == '其他' ||
                  detailParmas.leasePurpose == '商业'
                "
              >
                <!-- <p class="mR20"><span style="color: red">*</span></p> -->
                <a-input
                  v-model="detailParmas.leasePurposeInfo"
                  placeholder="请输入内容"
                  disabled
                  style="width: 60%; height: 40px; margin-right: 0"
                />1212{{ detailParmas.leasePurpose }}
              </div>
            </div>
            <div style="display: flex">
              <div class="titItem" style="width: 33.5%">
                <p class="mR20"><span style="color: red">*</span> 租赁单价</p>
                <a-input
                  type="number"
                  disabled
                  placeholder="请输入租赁单价"
                  v-model="detailParmas.rentalUnitPrice"
                  style="width: 70%; height: 40px"
                  addon-after="元/㎡/天"
                />
              </div>
              <div
                style="
                  width: 66.5%;
                  display: flex;
                  align-items: center;
                  padding-left: 12px;
                  margin-bottom: 0;
                "
              >
                <div class="titItem" style="align-items: center">
                  <p
                    class="mR20"
                    style="margin-left: -15px; margin-right: 20px"
                  >
                    <span style="color: red">*</span>定价依据
                  </p>
                  <div class="basisItem">
                    <FileAttachmentList
                      :ifNeedPreviewOnline="true"
                      mode="preview"
                      :fileList="fileList6"
                    >
                    </FileAttachmentList>
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex">
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 年租金</p>
                <a-input
                  v-model="detailParmas.annualRent"
                  disabled
                  placeholder="请输入年租金"
                  addon-after="元"
                  style="width: 70%; height: 40px; margin-right: 0"
                  maxLength="20"
                />
              </div>
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 租赁保证金</p>
                <a-input
                  v-model="detailParmas.leaseDeposit"
                  disabled
                  placeholder="请输入租赁保证金"
                  addon-after="元"
                  style="width: 70%; height: 40px; margin-right: 0"
                  maxLength="20"
                />
              </div>
              <div class="titItem">
                <p class="mR20">
                  <span style="color: red">*</span> 价格递增机制
                </p>
                <a-input
                  v-model="detailParmas.priceIncrease"
                  placeholder="请选择价格递增机制"
                  disabled
                  style="width: 70%; height: 40px; margin-right: 0px"
                />
              </div>
            </div>
            <div v-if="leaseDepositStatu">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="租赁保证金低于3个月租金情况说明(盖章版)"
                    marked="true"
                    mode="preview"
                    :ifNeedPreviewOnline="true"
                    :fileList="fileList11"
                  >
                  </FileAttachmentList>
                </div>
              </div>
            </div>
            <div
              style="display: flex"
              v-show="priceIncreaseStatu"
              v-for="(item, index) in detailParmas.multTenancyInfoList"
              :key="item.id"
            >
              <div class="titItem">
                <p class="mR20">
                  <span style="color: red">*</span>{{ `租期` + (index + 1) }}
                </p>
                <a-range-picker
                  format="YYYY-MM-DD"
                  disabled
                  :value="[item.startTime, item.endTime]"
                  :placeholder="['请选择', '请选择']"
                  style="width: 70%"
                />
              </div>
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 租赁单价</p>
                <a-input
                  type="number"
                  disabled
                  placeholder="请输入租赁单价"
                  v-model="item.rentalUnitPrice"
                  style="width: 70%; height: 40px"
                  addon-after="元/㎡/天"
                />
              </div>
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 年租金</p>
                <a-input
                  v-model="item.annualRent"
                  disabled
                  placeholder="请输入年租金"
                  addon-after="元"
                  style="width: 70%; height: 40px; margin-right: 0"
                  maxLength="20"
                />
              </div>
            </div>
            <div v-if="priceIncreaseStatu1">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="无价格递增情况说明（盖章版）"
                    marked="true"
                    mode="preview"
                    :ifNeedPreviewOnline="true"
                    :fileList="fileList10"
                  >
                  </FileAttachmentList>
                </div>
              </div>
            </div>
            <div style="display: flex">
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 租期起</p>
                <a-date-picker
                  v-model="detailParmas.leaseTermStart"
                  disabled
                  style="width: 70%"
                />
              </div>
              <div class="titItem">
                <p class="mR20"><span style="color: red">*</span> 租期止</p>
                <a-date-picker
                  v-model="detailParmas.leaseTermEnd"
                  disabled
                  style="width: 70%"
                />
              </div>
              <div class="titItem">
                <p class="mR20">总租期</p>
                <a-input
                  v-model="detailParmas.leaseTerm"
                  disabled
                  addon-after="年"
                  style="width: 70%"
                />
              </div>
            </div>

            <!-- <FileAttachmentList
              title="特殊通道情况说明(pdf盖章版)"
              marked="true"
              :ifNeedPreviewOnline="true"
              @deleteFile="(file, fileList) => deleteFile8(file, fileList)"
              :fileList="fileList8"
            >
            </FileAttachmentList> -->

            <div style="position: relative">
              <div
                style="display: flex"
                v-for="item in detailParmas.contractFreePeriod"
                :key="item.id"
              >
                <div style="display: flex; width: 68%">
                  <div class="titItem">
                    <p class="mR20">免租期起</p>
                    <a-date-picker
                      disabled
                      v-model="item.startTime"
                      style="width: 70%"
                    />
                  </div>
                  <div class="titItem" style="margin-right: 20px">
                    <p class="mR20">免租期止</p>
                    <a-date-picker
                      disabled
                      v-model="item.endTime"
                      style="width: 70%"
                    />
                  </div>
                </div>
              </div>
              <div
                class="titItem"
                style="
                  width: 31%;
                  padding-left: 0;
                  position: absolute;
                  right: 20px;
                  top: 0;
                "
              >
                <p class="mR20">总免租期</p>
                <a-input
                  v-model="detailParmas.rentFreeDays"
                  addon-after="天"
                  disabled
                />
              </div>
              <div class="rentFree">
                <a-icon type="info-circle" />
                <span style="margin-left: 25px"
                  >如分段给予免租期，请分多段录入。</span
                >
              </div>
            </div>
            <div v-if="contractFreePeriodStatu">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="免租期超过租期的10%或超过90天情况说明(盖章版)"
                    marked="true"
                    mode="preview"
                    :ifNeedPreviewOnline="true"
                    :fileList="fileList12"
                  >
                  </FileAttachmentList>
                </div>
              </div>
            </div>
            <div v-if="clauseTShow && detailParmas.contentChangeType == 1">
              <div class="titItem1">
                <p style="width: 108px; margin-right: 18px; margin-left: -7px">
                  <span style="color: red">*</span>
                  变更具体条款及原因
                </p>
                <a-textarea
                  compact="true"
                  style="width: 50%"
                  v-model="detailParmas.alterReason"
                  placeholder="请输入变更具体条款及原因"
                  disabled
                  :auto-size="{ minRows: 4, maxRows: 6 }"
                />
              </div>
            </div>
            <div
              style="
                width: 94%;
                margin: 0 auto;
                padding: 12px;
                margin-bottom: 30px;
                background: #f9f9f9;
              "
            >
              <div class="titItem" style="margin-left: 10px; padding-left: 0">
                <p style="margin-right: 25px">
                  合同中是否对以下必备条款进行约束
                </p>
              </div>
              <div style="margin-left: 15px; font-size: 16px; color: #1d2129">
                <p>
                  1.合同终止情形及免责条款：如因政府市政搬迁、土地收储、无证建筑拆除等情形，公司有权终止合同并予以免责；
                </p>
                <p>
                  2.违约责任条款：对于未按期缴纳租金的情况，应约定滞纳金；对于逾期三个月及以上未按时付清租金的情况，应明确公司有权解除租赁合同，收回物业，且不承担赔偿责任；
                </p>
                <p>
                  3.租约束条款：公司应严格控制转租行为，承租人与次承租人的租赁合同必须报公司备案。严禁层层转租行为。
                </p>
              </div>
              <div class="titItem" style="padding-left: 15px">
                <a-radio-group
                  disabled
                  v-model="detailParmas.essentialSituation"
                >
                  <a-radio :value="'1'"> 是 </a-radio>
                  <a-radio :value="'0'"> 否 </a-radio>
                </a-radio-group>
              </div>
            </div>
            <div>
              <div class="titItem1">
                <p style="width: 90px; margin-right: -7px; margin-left: 50px">
                  特殊条款
                </p>
                <a-textarea
                  compact="true"
                  style="width: 50%"
                  v-model="detailParmas.specialTerms"
                  placeholder="请输入特殊条款"
                  disabled
                  :auto-size="{ minRows: 4, maxRows: 6 }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">决策程序</div>
          <div class="titItem">
            <p style="padding-left: 50px; margin-right: 16px">
              是否法律顾问审核
            </p>
            <a-radio-group v-model="detailParmas.legalAdvisorReview" disabled>
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </div>
          <div class="titItem">
            <p style="padding-left: 50px; margin-right: 16px">
              是否经过董事会决议
            </p>
            <a-radio-group v-model="detailParmas.boardResolution" disabled>
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </div>
          <div class="upload_file_item">
            <div class="Item">
              <FileAttachmentList
                :marked="decMarked"
                mode="preview"
                :ifNeedPreviewOnline="true"
                key="11"
                title="董事会决议（复印件）"
                :fileList="fileList7"
              >
              </FileAttachmentList>
            </div>
          </div>
          <div class="titItem">
            <p style="padding-left: 50px; margin-right: 16px">
              是否经过三重一大
            </p>
            <a-radio-group v-model="detailParmas.isSignificant" disabled>
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </div>
          <div class="upload_file_item">
            <div class="Item">
              <FileAttachmentList
                :marked="decMarked1"
                mode="preview"
                :ifNeedPreviewOnline="true"
                key="22"
                title="三重一大（复印件）"
                :fileList="fileList8"
              >
              </FileAttachmentList>
            </div>
          </div>
        </div>
      </div>
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">备注</div>
          <div class="titItem1" style="padding-left: 50px">
            <a-textarea
              compact="true"
              disabled
              style="width: 58%; line-height: 20px"
              v-model="detailParmas.remark"
              placeholder="请输入备注"
              maxLength="200"
              :auto-size="{ minRows: 3, maxRows: 6 }"
            />
          </div>
        </div>
      </div>
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">审批记录</div>
          <div class="titItem1">
            <a-table
              ref="table"
              size="default"
              :columns="columns"
              :data-source="loadData"
              :scroll="{ x: 1000 }"
              rowKey="id"
            ></a-table>
          </div>
        </div>
      </div>
    </div>
    <div style="text-align: center">
      <a-button class="back" @click="toBack">返回</a-button>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import LessorInfo from "./../common/lessorInfo";
import OldRentalInfo from "./../common/oldRentalInfo";
import { queryContractInfoByIdApi } from "@/pages/index/data/api/RegistrationRecordInquery";
import { ApiGetReviewItem } from "../../../data/api/RegistrationRecordReview/index";
import FileAttachmentList from "@/components/FileAttachmentList";
export default {
  components: {
    LessorInfo,
    OldRentalInfo,
    FileAttachmentList,
  },
  data() {
    return {
      priceIncreaseStatu: false,
      priceIncreaseStatu1: false,
      rentalTShow: false,
      clauseTShow: false,
      adressShow: false,
      lessorInfoTit: false,
      decMarked: true,
      decMarked1: true,
      totalParmas: {},
      fileList: [],
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
      fileList6: [],
      fileList7: [],
      fileList8: [],
      fileList9: [],
      fileList10: [],
      fileList11: [],
      fileList12: [],
      fileList13: [],
      fileList14: [],
      detailParmas: {
        processFlow: "0", //流程类型 0：正常流程 1：特殊通道
        contractType: 0,
        contentChangeType: 0,
        tenantry: "",
        propertyRights: "",
        territorialSituation: "",
        territorialRemark: "",
        relationCompany: "",
        leaseArea: 0,
        leaseDeposit: "", //租赁保证金
        leasePurpose: "",
        leasePurposeInfo: "",
        rentalUnitPrice: "",
        rentFreeDays: "",
        leaseTermStart: "",
        leaseTermEnd: "",
        leaseTerm: "",
        priceIncrease: undefined,
        priceIncreaseMechanism: "",
        accumulateLeaseArea: 0,
        specialTerms: "",
        essentialSituation: "1",
        alterReason: "",

        contractFreePeriod: [], //租赁合同免租期数据
        contractBuildingInfo: [],
        multTenancyInfoList: [],
        relationContractId: "",
        attachments: [],
        boardResolution: 1, //董事会决议 0：否 1：是
        isSignificant: 1, //三重一大 0：否 1：是
        legalAdvisorReview: 1, //法律顾问审核 0：否 1：是
        remark: "",
        attachmentList: [],
      },
      columns: [
        {
          title: "审核人",
          dataIndex: "approver",
          width: 150,
          align: "center",
        },
        {
          title: "审核意见",
          dataIndex: "comments",
          width: 200,
          align: "center",
        },
        {
          title: "审核日期",
          dataIndex: "approvalTime",
          width: 150,
          align: "center",
          render: (text) => {
            return moment(text).format("YYYY-MM-DD HH:mm:ss");
          },
        },
      ],
      loadData: [],
    };
  },
  computed: {
    leaseDepositStatu() {
      // console.log('租赁保证金:',this.detailParmas.leaseDeposit ,'年租金:',this.detailParmas.annualRent , this.detailParmas.leaseDeposit &&this.detailParmas.annualRent && (this.detailParmas.leaseDeposit < this.detailParmas.annualRent/4))
      if (
        this.detailParmas.leaseDeposit &&
        this.detailParmas.annualRent &&
        parseInt(this.detailParmas.leaseDeposit) <
          parseInt(this.detailParmas.annualRent / 4)
      ) {
        return true;
      } else {
        return false;
      }
    },
    contractFreePeriodStatu() {
      let leaseTermDays; //总租期天数
      if (this.detailParmas.leaseTermStart && this.detailParmas.leaseTermEnd) {
        leaseTermDays =
          moment(this.detailParmas.leaseTermEnd).diff(
            moment(this.detailParmas.leaseTermStart),
            "days",
            false
          ) + 1;
      } else {
        leaseTermDays = 0;
      }
      console.log(
        "总租期天数:",
        leaseTermDays,
        "总免租期:",
        this.detailParmas.rentFreeDays
      );
      // 免租期超过租期的10%或超过90天情况说明
      if (
        (leaseTermDays &&
          this.detailParmas.rentFreeDays &&
          this.detailParmas.rentFreeDays / leaseTermDays > 0.1) ||
        this.detailParmas.rentFreeDays > 90
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  created() {
    this.getContractId();
    this.loadDataFn();
  },
  methods: {
    async loadDataFn() {
      await ApiGetReviewItem({
        filingNumber: this.$route.query.filingNumber,
      }).then((res) => {
        console.log(res, "审批记录");
        this.loadData = res.data ? res.data.contractApprovalHistoryAll : [];
      });
    },
    getContractId() {
      if (this.$route.query.id) {
        let parmas = {
          id: this.$route.query.id,
        };
        queryContractInfoByIdApi(parmas).then((res) => {
          console.log(res.data, "queryInfoDetail");
          this.totalParmas = res.data;
          this.detailParmas = this.totalParmas;
          this.detailParmas.contractType = this.totalParmas.contractType;
          if (this.totalParmas.priceIncrease == "1") {
            this.detailParmas.priceIncrease = "是";
            this.priceIncreaseStatu = true;
          } else if (this.totalParmas.priceIncrease == "0") {
            this.detailParmas.priceIncrease = "未满3年无价格递增";
            this.priceIncreaseStatu = false;
          } else if (this.totalParmas.priceIncrease == "2") {
            this.detailParmas.priceIncrease = "租期超3年无价格递增";
            this.priceIncreaseStatu = false;
            this.priceIncreaseStatu1 = true;
          }

          this.detailParmas.contentChangeType =
            this.totalParmas.contentChangeType;
          if (this.detailParmas.contractType == 0) {
            this.rentalTShow = true;
            this.clauseTShow = true;
            this.adressShow = true;
          } else if (this.detailParmas.contractType == 1) {
            this.adressShow = true;
            this.rentalTShow = true;
            this.clauseTShow = true;
          }
          if (
            this.detailParmas.contractType == 2 &&
            this.detailParmas.contentChangeType == 0
          ) {
            this.rentalTShow = true;
            this.clauseTShow = false;
            this.adressShow = false;
            this.lessorInfoTit = true;
          } else if (
            this.detailParmas.contractType == 2 &&
            this.detailParmas.contentChangeType === 1
          ) {
            this.rentalTShow = false;
            this.clauseTShow = true;
            this.cluShowInfo = true;
          }
          if (this.detailParmas.boardResolution == 0) {
            this.decMarked = false;
          } else {
            this.decMarked = true;
          }
          if (this.detailParmas.isSignificant == 0) {
            this.decMarked1 = false;
          } else {
            this.decMarked1 = true;
          }
          this.detailParmas?.attachments?.forEach((e) => {
            if (e.businessType == "contract_info") {
              this.fileList.push(e);
            }
            if (e.businessType == "change_supplementary_agreement") {
              this.fileList1.push(e);
            }
            if (e.businessType == "tenant_business_license") {
              this.fileList2.push(e);
            }
            if (e.businessType == "tenantry_id") {
              this.fileList3.push(e);
            }
            if (e.businessType == "credit_report") {
              this.fileList4.push(e);
            }
            if (e.businessType == "property_right") {
              this.fileList5.push(e);
            }
            if (e.businessType == "pricing_basis") {
              this.fileList6.push(e);
            }
            if (e.businessType == "board_resolution") {
              this.fileList7.push(e);
            }
            if (e.businessType == "triple_and_big") {
              this.fileList8.push(e);
            }
            if (e.businessType == "non_incremental_pricing") {
              this.fileList10.push(e);
            }
            if (e.businessType == "lease_deposit_below_3_months_explanation") {
              this.fileList11.push(e);
            }
            if (e.businessType == "rent_free_period_exceed_explanation") {
              this.fileList12.push(e);
            }
            if (e.businessType == "termination_agreement") {
              this.fileList13.push(e);
            }
            if (e.businessType == "special_channel_description") {
              this.fileList14.push(e);
            }
          });
          if (!this.detailParmas.essentialSituation) {
            this.detailParmas.essentialSituation = "1";
          }
          if (this.detailParmas.contractFreePeriod?.length == 0) {
            this.detailParmas.contractFreePeriod = [
              {
                delId: 0,
                startTime: "",
                endTime: "",
              },
            ];
          }
          if (this.detailParmas.contractBuildingInfo?.length == 0) {
            this.detailParmas.contractBuildingInfo = [
              {
                delId: 0,
                parkName: undefined,
                contractInfoId: undefined,
                buildingName: undefined,
                floorName: undefined,
                roomName: undefined,
              },
            ];
          } else if (!this.detailParmas.contractBuildingInfo[0].parkName) {
            this.detailParmas.contractBuildingInfo = [
              {
                delId: 0,
                parkName: undefined,
                contractInfoId: undefined,
                buildingName: undefined,
                floorName: undefined,
                roomName: undefined,
              },
            ];
          }
        });
      }
    },
    toBack() {
      this.$router.back();
    },
  },
};
</script>

<style lang="less" scoped>
@import "./../common/common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
}
.mR20 {
  width: 115px !important;
}
</style>

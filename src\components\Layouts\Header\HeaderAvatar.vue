<template>
  <a-dropdown>
    <div class="header-avatar" style="cursor: pointer">
      <a-avatar
        class="avatar"
        size="small"
        shape="circle"
        v-if="user && user.avatar"
        :src="user && user.avatar"
      />
      <svg-icon iconClass="user" v-else class="avatar"></svg-icon>
      <span class="name">{{ user && user.name }}</span>
    </div>
    <a-menu :class="['avatar-menu']" slot="overlay">
      <!-- <a-menu-item>
        <a-icon type="user" />
        <span>个人中心</span>
      </a-menu-item>
      <a-menu-item>
        <a-icon type="setting" />
        <span>设置</span>
      </a-menu-item>
      <a-menu-divider />-->
      <a-menu-item @click="logout">
        <a-icon style="margin-right: 8px;" type="poweroff" />
        <span>退出登录</span>
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>

<script>
import { mapGetters } from "vuex";
// import { logout } from "@/services/user";
import { ApiMapiLogout } from "@/data/api/common/auth";
import { removeAuthorization } from "@/common/api";
export default {
  name: "HeaderAvatar",
  computed: {
    ...mapGetters("account", ["user"])
  },
  methods: {
    logout() {
      ApiMapiLogout().then(() => {
        removeAuthorization();
        this.$router.push("/login");
      });
    }
  }
};
</script>

<style lang="less">
.header-avatar {
  display: inline-flex;
  .avatar,
  .name {
    align-self: center;
  }
  .avatar {
    margin-right: 8px;
    font-size: 24px;
    color: @primary-color;
  }
  .name {
    font-weight: 500;
  }
}
.avatar-menu {
  width: 150px;
}
</style>

//实业公司备案登记接口
import api from "@/common/api";
import { BASE_URL } from "Config";

//分页查询备案登记 /mapi/manager/ContractInfo/pageByCondition
export function getListApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/pageByCondition",
    method: "post",
    params
  });
}

//模糊搜索承租方 -- /mapi/manager/ContractInfo/queryTenantry
export function queryTenantryApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/queryTenantry",
    method: "post",
    params
  });
}

//修改备案登记状态 -- /mapi/manager/ContractInfo/updateContractStatus
export function updateContractStatusApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/updateContractStatus",
    method: "post",
    params
  });
}

//查询所有园区  /mapi/buildings/parkBuilding/queryAllPark
export function queryAllParkApi(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/queryAllPark",
    method: "post",
    params
  });
}
//查询当前登陆人园区  /mapi/buildings/parkBuilding/queryAllPark
export function queryAllParkApi1(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/queryOwerPark",
    method: "post",
    params
  });
}

//园区所有楼信息 /mapi/buildings/parkBuilding/queryAllBuilding
export function queryAllBuildingApi(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/queryAllBuilding",
    method: "post",
    params
  });
}

//保存备案登记 /manager/ContractInfo/addContractInfo
export function saveContractInfo(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/addContractInfo",
    method: "post",
    params
  });
}

//更新备案登记 /mapi/manager/ContractInfo/updateContractInfo
export function updateContractInfoApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/updateContractInfo",
    method: "post",
    params
  });
}

//出租方加编号信息lessorInfo--获取实业公司集合 /mapi/leaseReview/industrialCompany/getIndustrialCompanies
export function getIndustrialCompaniesApi(params) {
  return api({
    url: BASE_URL + "/leaseReview/industrialCompany/getIndustrialCompanies",
    method: "post",
    params
  });
}

//出租方加编号信息lessorInfo--获取备案合同编号 /mapi/manager/ContractInfo/getContractNumber
export function getContractNumberApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/getContractNumber",
    method: "post",
    params
  });
}

//出租信息 --查询园区下某一楼栋楼层信息 /mapi/buildings/parkRelation/findFloorsByBuildingInfo
export function findFloorsByBuildingInfoApi(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findFloorsByBuildingInfo",
    method: "post",
    params
  });
}

//出租信息 --查询园区下某一楼栋楼某一层房间信息 /mapi/buildings/parkRelation/findRoomsByBuildingInfo
export function findRoomsByBuildingInfoApi(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findRoomsByBuildingInfo",
    method: "post",
    params
  });
}

//原出租信息oldRentalInfo--根据承租方和出租方查询已有备案合同 /mapi/manager/ContractInfo/queryContractList
export function queryContractListApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/queryContractList",
    method: "post",
    params
  });
}

//原出租信息oldRentalInfo--根据合同ID查询出租信息 /mapi/manager/ContractInfo/queryContractInfoById
export function queryContractInfoByIdApi(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/queryContractInfoById",
    method: "post",
    params
  });
}

// 条款信息clauseInfo--查询承租方在园区累计租赁面积 /buildings/parkRelation/findAccumulatedArea
export function getAreaTotalApi(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findAccumulatedArea",
    method: "post",
    params
  });
}

//条款信息clauseInfo--查询承租方在园区累计租赁面积 /buildings/parkRelation/findRoomsByBuildingInfo
export function getParkAreaApi(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findRoomsByBuildingInfo",
    method: "post",
    params
  });
}

<!--
* <AUTHOR>
* @time 2020-9-21
* @dec 表单引擎 - 机房人员备案套件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="12">
        <a-row :gutter="[50, 50]">
          <a-col>
            <personnel-record-dom :data="formData"></personnel-record-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="12">
        <personnel-record-form
          v-bind:data.sync="formData"
        ></personnel-record-form
      ></a-col>
    </a-row>
  </a-card>
</template>
<script>
// 日期选择控件 DOM/Form
import {
  PersonnelRecordDom,
  PersonnelRecordForm
} from "@/components/ApprovalProcess/FormDesign/components/SuiteLibrary/PersonnelRecord";

export default {
  components: {
    PersonnelRecordDom,
    PersonnelRecordForm
  },
  data() {
    return {
      formData: {}
    };
  }
};
</script>
<style scoped lang="less"></style>

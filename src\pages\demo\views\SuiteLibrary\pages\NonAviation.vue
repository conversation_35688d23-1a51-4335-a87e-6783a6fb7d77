<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 表单引擎 - 非航资源套件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="12">
        <a-row :gutter="[50, 50]">
          <a-col>
            <non-aviation-dom :data="formData"></non-aviation-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="12">
        <non-aviation-form v-bind:data.sync="formData"></non-aviation-form
      ></a-col>
    </a-row>
  </a-card>
</template>
<script>
// 非航资源套件 DOM/Form
import {
  NonAviationDom,
  NonAviationForm
} from "@/components/ApprovalProcess/FormDesign/components/SuiteLibrary/NonAviation";
export default {
  components: {
    NonAviationDom,
    NonAviationForm
  },
  data() {
    return {
      formData: {}
    };
  }
};
</script>
<style scoped lang="less"></style>

<template>
  <div>
    <div id="EnterpriseRating" ref="EnterpriseRating" class="contain"></div>
  </div>
</template>

<script>
// import { getAreaOccupancyApi } from "@/pages/index/data/api/ComponyWatch";
import * as echarts from "echarts";
export default {
  data() {
    return {
      chartData: {},
      yData: [],
      seriesData: [],
    };
  },
  mounted() {
    this.initData();
  },
  created() {},
  methods: {
    async initData() {
      // this.chartData = await getAreaOccupancyApi("");
      // this.chartData.data.forEach((e) => {
      //   this.seriesData.push(Number(e.occupancyRate).toFixed(2));
      //   this.yData.push(e.parkName);
      // });
      this.drawLine();
    },
    drawLine() {
      let myChart = echarts.init(this.$refs.EnterpriseRating);
      let option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          
          show: false,
        },
        radar: {
          shape: "circle",
          indicator: [
            { text: "技术创新", max: 100, color: "#000" },
            { text: "研发实力", max: 100, color: "#000" },
            { text: "行业潜力", max: 100, color: "#000" },
            { text: "科创资质", max: 100, color: "#000" },
            { text: "知识产权", max: 100, color: "#000" },
            { text: "市场分析", max: 100, color: "#000" },
            { text: "财务信息", max: 100, color: "#000" },
            { text: "企业成长性", max: 100, color: "#000" },
          ],
        },
        series: [
          {
            type: "radar",
            tooltip: {
              trigger: "item",
            },
            areaStyle: {},
            data: [
              {
                value: [78, 86, 95, 56, 33, 98, 45, 78],
                name: "企业评级",
              },
            ],
          },
        ],
      };
      // 绘制图表
      myChart.setOption(option);
      //多图表自适应
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contain {
  width: 100%;
  height: 400px;
}
</style>

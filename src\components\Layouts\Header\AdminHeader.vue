<template>
  <a-layout-header :class="[headerTheme, 'admin-header']">
    <div :class="['admin-header-wide', layout]">
      <router-link
        v-if="isMobile || layout === 'head'"
        to="/"
        :class="['logo', isMobile ? null : 'pc', headerTheme]"
      >
        <img width="32" src="~@/assets/image/common/big_logo.png" />
        <h1 v-if="!isMobile">{{ systemName }}</h1>
      </router-link>
      <a-divider v-if="isMobile" type="vertical" />
      <a-icon
        v-if="layout === 'side'"
        class="trigger"
        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        @click="toggleCollapse"
      />
      <div v-if="layout == 'head' && !isMobile" class="admin-header-menu">
        <i-menu
          class="head-menu"
          style="height: 64px; line-height: 64px;box-shadow: none"
          :theme="headerTheme"
          mode="horizontal"
          :options="menuData"
          @select="onSelect"
        />
      </div>
      <div :class="['admin-header-right', headerTheme]">
        <header-notice class="header-item" style="display:none" />
        <div class="notification" @click="jumpToNotificationList">
          <div class="header-notification">
            <a-icon
              type="bell"
              style="font-size:24px; color:#1777FF;"
              theme="filled"
            />
            <div
              v-if="notificationInfo.readNumber > 0"
              class="red-filled-point"
            ></div>
          </div>
        </div>
        <header-avatar class="header-item" />
        <!-- <a-dropdown class="lang header-item">
          <div><a-icon type="global" /> {{ langAlias }}</div>
          <a-menu
            @click="val => setLang(val.key)"
            :selected-keys="[lang]"
            slot="overlay"
          >
            <a-menu-item v-for="lang in langList" :key="lang.key">{{
              lang.key.toLowerCase() + " " + lang.name
            }}</a-menu-item>
          </a-menu>
        </a-dropdown> -->
      </div>
    </div>
  </a-layout-header>
</template>

<script>
import HeaderNotice from "./HeaderNotice";
import HeaderAvatar from "./HeaderAvatar";
import IMenu from "../components/Menu/Menu";
import { mapState, mapMutations } from "vuex";
import { ApiNoticeReadCount } from "APIs/PortalManagement/NotificationManagement/index.js";
export default {
  name: "AdminHeader",
  components: { IMenu, HeaderAvatar, HeaderNotice },
  props: ["collapsed", "menuData"],
  data() {
    return {
      langList: [
        { key: "CN", name: "简体中文", alias: "简体" },
        { key: "HK", name: "繁體中文", alias: "繁體" },
        { key: "US", name: "English", alias: "English" },
      ],
      notificationInfo: {
        readNumber: 0,
      },
    };
  },
  computed: {
    ...mapState("setting", [
      "theme",
      "isMobile",
      "layout",
      "systemName",
      "lang",
      "notification",
    ]),
    headerTheme() {
      if (
        this.layout == "side" &&
        this.theme.mode == "dark" &&
        !this.isMobile
      ) {
        return "light";
      }
      return this.theme.mode;
    },
    langAlias() {
      let lang = this.langList.find((item) => item.key == this.lang);
      return lang.alias;
    },
  },
  mounted() {
    ApiNoticeReadCount().then((res) => {
      console.log(res);
      if (res.code == 0) {
        // const result = JSON.stringify(res.data);
        // localStorage.setItem("notification_info", result);

        // const notificationInfo = this.$store.getters["notification/data"];
        this.$set(this, "notificationInfo", res.data);
      }
    });
    this.$bus.$on("notify", (info) => {
      this.$set(this, "notificationInfo", info);
    });
  },
  beforeDestroy() {
    this.$bus.$off("notify");
  },
  methods: {
    jumpToNotificationList: function() {
      //跳转至通知列表
      this.$router.push({
        path: "/portal-management/notification-management",
      });
    },
    toggleCollapse() {
      this.$emit("toggleCollapse");
    },
    onSelect(obj) {
      this.$emit("menuSelect", obj);
    },
    ...mapMutations("setting", ["setLang"]),
  },
};
</script>

<style lang="less" scoped>
@import "index";
.notification {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.header-notification {
  margin-right: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .red-filled-point {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #ff0000;
    width: 6px;
    height: 6px;
    border-radius: 6px;
  }
}
</style>

<!--
  * <AUTHOR>
  * @dec 绘制流程图
-->
<template>
  <div id="drawProcessDesign">
    <draw-flow
      ref="flow"
      :FlowConfig="FlowConfig"
      v-on="$listeners"
    ></draw-flow>
  </div>
</template>
<script>
import DrawFlow from "@/components/DrawFlow";
import { FlowNode } from "./getNode";
import { mapGetters } from "vuex";
export default {
  name: "DrawProcessDesign",
  components: { DrawFlow },
  data() {
    return {
      FlowConfig: [
        {
          id: "root",
          groupId: null,
          type: "1",
          title: "发起人",
          content: "所有人",
          isRow: true,
          isRoot: true,
          data: { roleAndUserAuth: { authType: 1, tags: [] } }
        }
      ]
    };
  },
  created() {},
  watch: {
    processDesign() {
      // this.setData(data);
      this.init();
    }
  },
  destroyed() {},
  computed: {
    // 监听vuex数据变化
    ...mapGetters("approvalProcess", ["processDesign"])
  },
  methods: {
    /**
     * 1、创建一个row length 1
     * 2、创建一个col节点
     * 3、创建一组col length 1
     * return [{pid id type}]
     */
    getNodeArr() {
      this.FlowConfig = this.$refs.flow.getNodeArr();
      return this.FlowConfig;
    },
    init() {
      if (this.processDesign.length) {
        this.FlowConfig = this.transform(this.processDesign);
      }
    },
    transform(arr) {
      let resArr = [];
      arr.forEach(i => {
        let node = new FlowNode(i);
        node.id && resArr.push(node);
      });

      return resArr;
    },
    nodeChange(data) {
      this.$refs.flow.nodeChange(data);
    }
  }
};
</script>
<style lang="less">
#drawProcessDesign {
  width: 100%;
  position: relative;
  height: calc(100vh - 65px);
}
</style>

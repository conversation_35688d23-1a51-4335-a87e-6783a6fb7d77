<template>
  <div class="keyClueForm">
    <a-form-model
      ref="formData"
      :model="formData"
      layout="horizontal"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :rules="rules"
    >
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="申请人">
            <a-input
              disabled="true"
              placeholder="请输入"
              v-model="formData.initiateName"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="招商分部">
            <a-input
              disabled="true"
              v-model="formData.businessDivision"
              placeholder="请选择招商分部"
            >
            </a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="申请日期">
            <a-input
              disabled="true"
              placeholder="请输入申请日期"
              v-model="formData.applicationDate"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="编号">
            <a-input
              disabled="true"
              placeholder="请输入编号"
              v-model="formData.applicationNumber"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="企业名称"
            prop="enterpriseName"
            :label-col="{ span: 4 }"
          >
            <a-input
              placeholder="请输入企业名称"
              v-model="formData.enterpriseName"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="企业性质" prop="enterpriseNature">
            <a-select
              v-model="formData.enterpriseNature"
              placeholder="请选择企业性质"
              allowClear
            >
              <a-select-option
                :value="item.value"
                :key="item.value"
                v-for="item in enterpriseNatureArr"
              >
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="法人代表" prop="legalRepresentative">
            <a-input
              placeholder="请输入法人代表"
              v-model="formData.legalRepresentative"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="法人代表手机号" prop="legalPhone">
            <a-input
              placeholder="请输入法人代表手机号"
              v-model="formData.legalPhone"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="办公经营地址" prop="officeAddress">
            <a-input
              placeholder="请输入办公经营地址"
              v-model="formData.officeAddress"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="经营范围"
            prop="businessScope"
            :labelCol="{ span: 4 }"
          >
            <a-input
              type="textarea"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder=" 请输入经营范围"
              v-model="formData.businessScope"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="经营期限(起)" prop="startDate">
            <a-date-picker
              v-model="formData.startDate"
              placeholder="请选择经营期限(起)"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="经营期限(止)" prop="endDate">
            <a-date-picker
              v-model="formData.endDate"
              placeholder="请选择经营期限(止)"
              style="width: 100%"
              :disabledDate="disabledDate"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="注册资金(万)" prop="registeredCapital">
            <a-input-number
              placeholder="请输入注册资金(万)"
              v-model="formData.registeredCapital"
              style="width: 100%"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="币种(注册资金)" prop="currency">
            <a-select
              v-model="formData.currency"
              allowClear
              placeholder="请选择币种(注册资金)"
            >
              <a-select-option value="1">人民币</a-select-option>
              <a-select-option value="2">美元</a-select-option>
              <a-select-option value="3">欧元</a-select-option>
              <a-select-option value="4"> 日元</a-select-option>
              <a-select-option value="5">港币</a-select-option>
              <a-select-option value="6">英镑 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="行业类别" prop="industryCategory">
            <a-select
              v-model="formData.industryCategory"
              placeholder="请选择行业类别"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in industryCategoryArr"
                :key="item.value"
                >{{ item.desc }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="统一社会信用代码" prop="creditCode">
            <a-input
              placeholder="请输入统一社会信用代码"
              v-model="formData.creditCode"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="行业类别备注">
            <a-input
              placeholder="请输入行业类别备注"
              v-model="formData.industryCategoryRemark"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="重点企业" prop="isKeyEnterprise">
            <a-select
              v-model="formData.isKeyEnterprise"
              placeholder="请选择重点企业"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="总部经济" prop="isHeadquartersEconomy">
            <a-select
              v-model="formData.isHeadquartersEconomy"
              placeholder="请选择总部经济"
              allowClear
            >
              <a-select-option value="1"> 跨国公司</a-select-option>
              <a-select-option value="2"> 民营 </a-select-option>
              <a-select-option value="3"> 研发中心 </a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-model-item label="招商企业" prop="investmentCompany">
            <a-select
              v-model="formData.investmentCompany"
              placeholder="请选择招商企业"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col> -->
        <a-col :span="12">
          <a-form-model-item label="500强企业" prop="isTop500">
            <a-select
              v-model="formData.isTop500"
              placeholder="请选择500强企业"
              allowClear
            >
              <a-select-option value="1"> 世界五百强 </a-select-option>
              <a-select-option value="2"> 中国五百强 </a-select-option>
              <a-select-option value="3"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="4大产业集群" prop="isFourClusters">
            <a-select
              v-model="formData.isFourClusters"
              placeholder="请选择是否4大产业集群"
              allowClear
            >
              <a-select-option value="1"> 人工智能 </a-select-option>
              <a-select-option value="2"> 生命健康 </a-select-option>
              <a-select-option value="3"> 科技金融 </a-select-option>
              <a-select-option value="4"> 艺术传媒 </a-select-option>
              <a-select-option value="5"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="行业标杆" prop="isIndustryBenchmark">
            <a-select
              v-model="formData.isIndustryBenchmark"
              placeholder="请选择是否行业标杆"
              allowClear
            >
              <a-select-option value="1"> 央企投资 </a-select-option>
              <a-select-option value="2"> 上市公司 </a-select-option>
              <a-select-option value="3"> 优质外资</a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="科创引领" prop="isTechLeader">
            <a-select
              v-model="formData.isTechLeader"
              placeholder="请选择科创引领"
              allowClear
            >
              <a-select-option value="1"> 高新技术企业 </a-select-option>
              <a-select-option value="2"> 专精特新 </a-select-option>
              <a-select-option value="3"> 科技小巨人 </a-select-option>
              <a-select-option value="4"> 专精特新小巨人 </a-select-option>
              <a-select-option value="5"> 独角兽 </a-select-option>
              <a-select-option value="6"> 瞪羚企业 </a-select-option>
              <a-select-option value="7"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="工商注册地址"
            prop="businessRegistrationAddress"
          >
            <a-input
              placeholder="请输入工商注册地址"
              v-model="formData.businessRegistrationAddress"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="招商类型" prop="investmentTypeCategory">
            <a-select
              v-model="formData.investmentTypeCategory"
              placeholder="请选择招商类型"
              allowClear
            >
              <a-select-option value="1"> 新设 </a-select-option>
              <a-select-option value="2"> 迁入 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="工商登记日期"
            prop="businessRegistrationDate"
          >
            <a-date-picker
              v-model="formData.businessRegistrationDate"
              placeholder="请选择工商登记日期"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="原区属下放">
            <a-select
              v-model="formData.isOriginalDistrict"
              placeholder="请选择原区属下放"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否工商迁移" prop="isBusinessTransfer">
            <a-select
              v-model="formData.isBusinessTransfer"
              placeholder="请选择是否工商迁移"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="迁入企业原注册地">
            <a-input
              placeholder="请输入迁入企业原注册地"
              v-model="formData.transferOriginalAddress"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="工商变更日期">
            <a-date-picker
              v-model="formData.businessChangeDate"
              placeholder="请选择工商变更日期"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="引进日期(工商)">
            <a-date-picker
              v-model="formData.introductionDate"
              placeholder="请选择引进日期(工商)"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row> -->
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="合作引进方">
            <a-input
              v-model="formData.cooperationIntroducer"
              placeholder="请填写合作引进方"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="经办类型" prop="handlingType">
            <a-select
              v-model="formData.handlingType"
              placeholder="请选择经办类型"
              allowClear
            >
              <a-select-option value="1"> 企业自办</a-select-option>
              <a-select-option value="2"> 分部代办</a-select-option>
              <a-select-option value="3"> 经发代办 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="经办人" prop="handlingPerson">
            <a-input
              v-model="formData.handlingPerson"
              placeholder="请填写经办人"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否占用资源" prop="isResourceOccupation">
            <a-select
              v-model="formData.isResourceOccupation"
              placeholder="请选择是否占用资源"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="虚拟地址申请流程">
            <a-input
              v-model="formData.virtualAddressApplicationProcess"
              placeholder="请填写虚拟地址申请流程"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="所属区域" prop="belongingArea">
            <a-select
              v-model="formData.belongingArea"
              placeholder="请选择所属区域"
              allowClear
              showSearch
            >
              <!-- @change="changeBelongingArea()" -->
              <a-select-option
                :value="item.value"
                v-for="item in belongingAreaArr"
                :key="item.value"
              >
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label=" 交叉功能区">
            <a-select
              v-model="formData.crossFunctionalArea"
              placeholder="请选择交叉功能区"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in crossFunctionalAreaTypeArr"
                :key="item.value"
              >
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="入住楼宇">
            <a-input
              v-model="formData.buildingEntry"
              placeholder="请填写入住楼宇"
              allowClear
            ></a-input>
            <!-- <a-select
              showSearch
              v-model="formData.buildingEntry"
              placeholder="请选择入住楼宇"
              allowClear
            >
              <a-select-option
                :value="item"
                v-for="item in buildingEntryArr"
                :key="item.id"
                >{{ item }}</a-select-option
              >
            </a-select> -->
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label=" 申报时间">
            <a-input
              disabled="true"
              v-model="formData.declarationDate"
              placeholder="自动带出"
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="税务外迁">
            <a-select
              v-model="formData.taxRelocation"
              placeholder="请选择税务外迁"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="税务注销">
            <a-select
              v-model="formData.taxCancellation"
              placeholder="请选择税务注销"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="企业星级">
            <a-select
              v-model="formData.starRating"
              placeholder="请选择企业星级"
              allowClear
            >
              <a-select-option value="1"> ★</a-select-option>
              <a-select-option value="2"> ★ ★</a-select-option>
              <a-select-option value="3"> ★ ★ ★</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="营业执照"
            :label-col="{ span: 4 }"
            prop="attachmentList"
          >
            <FileAttachmentList
              v-if="formData.attachmentList.length > 0"
              title=""
              :ifNeedPreviewOnline="true"
              marked="true"
              @deleteFile="(file, fileList) => deleteConFile(file, fileList)"
              :fileList="formData.attachmentList"
            >
            </FileAttachmentList>
            <my-upload
              v-else
              :accept="accept"
              businessId="business_license"
              @handleFileCallback="handleConFileCallback"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item label="备注" :label-col="{ span: 4 }">
            <a-input
              type="textarea"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入备注"
              v-model="formData.remarks"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <div class="box1">
      <div class="box1Title">
        <span>归属单位</span>
        <div v-if="status != 3">
          <a-button
            type="primary"
            style="margin-right: 20px"
            @click="addTabel(1)"
            >新增</a-button
          >
          <!-- <a-button
            type="danger"
            :disabled="selectedRowKeys.length == 0"
            @click="deleteFn()"
            >删除{{
              selectedRowKeys.length ? `(${selectedRowKeys.length})` : ""
            }}</a-button
          > -->
        </div>
      </div>
      <a-table
        :columns="columns"
        :data-source="householdAffiliationDTOList"
        bordered
        :pagination="false"
        :rowKey="(record, index) => index"
      >
        <template slot="index" slot-scope="text, record, index">
          {{ (pages.pageNo - 1) * pages.pageSize + index + 1 }}
        </template>
        <template slot="department" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled" style="display: block; width: 100%">
              <a-select
                v-model="record.department"
                placeholder="请选择归属分部"
                style="width: 100%"
                showSearch
              >
                <a-select-option
                  :value="item.abbr"
                  v-for="item in businessDivisionArr"
                  :key="item.abbr"
                >
                  {{ item.abbr }}
                </a-select-option>
              </a-select>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="ratio" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.ratio"
                placeholder="请填写比例"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="type" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-select
                v-model="record.type"
                placeholder="请选择类型"
                width="100%"
              >
                <a-select-option value="单一">单一</a-select-option>
                <a-select-option value="分享"> 分享 </a-select-option>
              </a-select>
            </span>
            <span v-else>{{ text }} </span>
          </div>
        </template>
        <template slot="remark" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.remark"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else> {{ text }}</span>
          </div>
        </template>
        <template
          slot="operate"
          slot-scope="text, record, index"
          v-if="status != 3"
        >
          <a
            href="javascript:;"
            style="margin: 0 10px"
            @click="editFn(index, 1)"
            >{{ record.disabled ? "编辑" : "保存" }}</a
          >
          <a
            href="javascript:;"
            v-if="householdAffiliationDTOList.length > 1"
            @click="deleteFn(index, 1)"
            >删除</a
          >
        </template>
      </a-table>
      <a-pagination
        style="display: flex; justify-content: flex-end; margin: 20px 0"
        show-size-changer
        :total="householdAffiliationDTOList.length"
        @change="onShowSizeChange"
      />
    </div>
    <div class="box1">
      <div class="box1Title">
        <span>企业联系人</span>
        <div>
          <a-button
            type="primary"
            style="margin-right: 20px"
            @click="addTabel(2)"
            >新增</a-button
          >
          <!-- <a-button
            type="danger"
            v-if="status != 3"
            :disabled="selectedRowKeys1.length == 0"
            >删除
            {{
              selectedRowKeys1.length ? `(${selectedRowKeys1.length})` : ""
            }}</a-button
          > -->
        </div>
      </div>
      <a-table
        :columns="columns1"
        :pagination="false"
        :data-source="householdContactDTOList"
        bordered
        :rowKey="(record, index) => index"
      >
        <!-- :row-selection="
          status != 3
            ? {
                selectedRowKeys: selectedRowKeys1,
                onChange: onChangeFn1,
              }
            : undefined
        " -->
        <template slot="index" slot-scope="text, record, index">
          {{ (pages1.pageNo - 1) * pages1.pageSize + index + 1 }}
        </template>
        <template slot="contactName" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.contactName"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="position" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.position"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="contactPhone" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.contactPhone"
                placeholder="请填写"
                v-decorator="[
                  'otherPhone',
                  {
                    rules: [
                      { required: true, message: '请输入手机号!' },
                      { validator: checkMobile },
                    ],
                  },
                ]"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="contactEmail" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.contactEmail"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="otherPhone" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="!record.disabled">
              <a-input
                v-model="record.otherPhone"
                placeholder="请填写"
                v-decorator="[
                  'otherPhone',
                  {
                    rules: [
                      { required: false, message: '请输入手机号!' },
                      { validator: checkMobile },
                    ],
                  },
                ]"
                width="100%"
              ></a-input>
            </span>
            <span v-else> {{ text }}</span>
          </div>
        </template>
        <template slot="operate" slot-scope="text, record, index">
          <a
            href="javascript:;"
            style="margin: 0 10px"
            @click="editFn(index, 2)"
            >{{ record.disabled ? "编辑" : "保存" }}</a
          >
          <a
            href="javascript:;"
            v-if="householdContactDTOList.length > 1 && status != 3"
            @click="deleteFn(index, 2)"
            >删除</a
          >
        </template>
      </a-table>
      <a-pagination
        style="display: flex; justify-content: flex-end; margin: 20px 0"
        show-size-changer
        :total="householdContactDTOList.length"
        @change="onShowSizeChange1"
      />
    </div>
    <!-- //审批详情 -->
    <!-- //审批详情 -->
    <template v-if="status == 3 || status == 4">
      <template v-for="item in householdApprovalHistory">
        <div class="summarizeBox" :key="item.id">
          <div class="comTitle">
            {{
              item.reviewNodes == "初审节点"
                ? "初审小结"
                : item.reviewNodes == "复审节点"
                ? "复审小结"
                : item.reviewNodes == "终审节点"
                ? "终审小结"
                : item.reviewNodes == "预审节点"
                ? "预审小结"
                : ""
            }}
          </div>
          <div style="margin-bottom: 20px">
            <span style="margin-right: 50px; padding-left: 20px"
              >{{ item.approver }}：{{ item.remark }}
            </span>
            <span style="margin-right: 50px"
              >审批意见：{{ item.comments }}
            </span>
            <span>审批时间：{{ item.approvalTime }} </span>
          </div>
        </div>
      </template>
    </template>
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      "
    >
      <a-button @click="toBack">返回</a-button>
      <a-button type="primary" style="margin: 0 10px" @click="onSave()"
        >保存</a-button
      >
      <a-button type="primary" @click="onSubmit()" v-if="status != 3"
        >提交申请</a-button
      >
    </div>
  </div>
</template>
<script>
import {
  getHouseholdBaseInfo,
  getCompanyAbbrList,
  addHousehold,
  getHouseholdDetail,
  updateHousehold,
  // findAllParkName,
  // queryAllBuilding,
} from "@/pages/index/data/api/keyAndHouse/index";
import { checkMobile } from "@/common/validate";

import FileAttachmentList from "@/components/fileView";
import myUpload from "@/components/Importer";
import moment from "moment";
import { setTimeout } from "core-js";
export default {
  components: {
    FileAttachmentList,
    myUpload,
  },
  data() {
    return {
      accept: ".pdf,.png,.jpeg,.jpg",
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
      pageType: "", //1编辑
      id: "", //户管信息主id
      examineApproveData: {
        handle: undefined, //审批处理
        remark: "", //审批意见
      },
      approveRules: {
        handle: [
          { required: true, message: "请选择审批处理", trigger: "change" },
        ],
      },
      // rowSelection: {
      //   selectedRowKeys: this.selectedRowKeys,
      //   onChange: this.onChangeFn,
      // },
      // rowSelection1: {
      //   selectedRowKeys: this.selectedRowKeys1,
      //   onChange: this.onChangeFn1,
      // },

      householdAffiliationDTOList: [], //户管归属单位
      householdContactDTOList: [], //户管企业联系人
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "id",
          width: 100,
          scopedSlots: { customRender: "index" },
        },
        {
          title: "归属分部",
          dataIndex: "department",
          key: "department",
          width: 150,
          scopedSlots: { customRender: "department" },
        },
        {
          title: "比例（%）",
          dataIndex: "ratio",
          key: "ratio",
          width: 150,
          scopedSlots: { customRender: "ratio" },
        },
        {
          title: "类型",
          dataIndex: "type",
          key: "type",
          width: 150,
          scopedSlots: { customRender: "type" },
        },
        {
          title: "备注",
          dataIndex: "remark",
          key: "remark",
          width: 150,
          scopedSlots: { customRender: "remark" },
        },
        {
          title: "操作",
          dataIndex: "operate",
          key: "operate",
          width: 120,
          scopedSlots: { customRender: "operate" },
        },
      ],
      columns1: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          width: 80,
          scopedSlots: { customRender: "index" },
        },
        {
          title: "联系人",
          dataIndex: "contactName",
          key: "contactName",
          width: 100,
          scopedSlots: { customRender: "contactName" },
        },
        {
          title: "职务",
          dataIndex: "position",
          key: "position",
          width: 100,
          scopedSlots: { customRender: "position" },
        },
        {
          title: "手机号码",
          dataIndex: "contactPhone",
          key: "contactPhone",
          width: 100,
          scopedSlots: { customRender: "contactPhone" },
        },
        {
          title: "邮箱地址",
          dataIndex: "contactEmail",
          key: "contactEmail",
          width: 100,
          scopedSlots: { customRender: "contactEmail" },
        },
        {
          title: "其他联系电话",
          dataIndex: "otherPhone",
          key: "otherPhone",
          width: 150,
          scopedSlots: { customRender: "otherPhone" },
        },
        {
          title: "操作",
          dataIndex: "operate",
          key: "operate",
          width: 100,
          scopedSlots: { customRender: "operate" },
        },
      ],
      selectedRowKeys: [],
      selectedRows: [],
      selected: [],
      selectedRowKeys1: [],
      selectedRows1: [], //
      selected1: [],
      pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      pages1: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      formData: {
        initiateName: "", //申请人
        businessDivision: undefined, //招商分部
        applicationDate: "", //申请日期
        applicationNumber: "", //申请编号
        enterpriseName: "", //企业名称
        enterpriseNature: undefined, //企业性质
        legalRepresentative: "", //法定代表人
        legalPhone: "", //法定代表人电话
        officeAddress: "", //办公经营地址
        businessScope: "", //经营范围
        startDate: "", //经营期限起
        endDate: "", //经营期限止
        registeredCapital: "", //注册资本
        currency: undefined, //币种
        industryCategory: undefined, //行业类别
        creditCode: "", //统一社会信用代码
        industryCategoryRemark: "", //行业类别备注
        isKeyEnterprise: undefined, //重点企业
        isHeadquartersEconomy: undefined, //总部经济
        isTop500: undefined, //500强企业
        isFourClusters: undefined, //4大产业集群
        isIndustryBenchmark: undefined, //行业标杆
        isTechLeader: undefined, //科创引领
        businessRegistrationAddress: "", //工商注册地址
        investmentTypeCategory: undefined, //招商类型
        businessRegistrationDate: "", //工商登记日期
        isOriginalDistrict: undefined, //原区属下放
        isBusinessTransfer: undefined, //是否工商迁移
        transferOriginalAddress: "", //迁入企业原注册地
        businessChangeDate: "", //工商变更日期
        introductionDate: "", //引进日期(工商)
        cooperationIntroducer: "", //合作引进方
        handlingType: undefined, //经办类型
        handlingPerson: "", //经办人
        isResourceOccupation: undefined, //是否占用资源
        virtualAddressApplicationProcess: "", //虚拟地址申请流程
        belongingArea: undefined, //所属区域
        crossFunctionalArea: undefined, //交叉功能区
        buildingEntry: "", //入住楼宇
        declarationDate: "", //申报时间
        taxRelocation: undefined, //税务外迁
        taxCancellation: undefined, //税务注销
        starRating: undefined, //企业星级
        attachmentList: [], //营业执照
        remarks: "", //备注
      },
      rules: {
        enterpriseName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "不能少于1字符，且不超过30个字符",
            trigger: "blur",
          },
        ],
        enterpriseNature: [
          { required: true, message: "请选择企业性质", trigger: "change" },
        ],
        legalRepresentative: [
          { required: true, message: "请输入法定代表人", trigger: "blur" },
        ],
        legalPhone: [
          { required: true, message: "请输入法定代表人电话", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        officeAddress: [
          { required: true, message: "请输入办公经营地址", trigger: "blur" },
        ],
        businessScope: [
          { required: true, message: "请输入经营范围", trigger: "blur" },
        ],
        startDate: [
          { required: true, message: "请选择经营期限起", trigger: "change" },
        ],
        endDate: [
          { required: true, message: "请选择经营期限止", trigger: "change" },
        ],
        registeredCapital: [
          { required: true, message: "请输入注册资金", trigger: "blur" },
        ],
        currency: [
          { required: true, message: "请选择币种", trigger: "change" },
        ],
        industryCategory: [
          { required: true, message: "请选择行业类别", trigger: "change" },
        ],
        creditCode: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: "blur",
          },
          {
            min: 1,
            max: 30,
            message: "不能少于1字符，且不超过30个字符",
            trigger: "blur",
          },
        ],
        isKeyEnterprise: [
          { required: true, message: "请选择重点企业", trigger: "change" },
        ],
        isHeadquartersEconomy: [
          { required: true, message: "请选择总部经济", trigger: "change" },
        ],
        // investmentCompany: [
        //   { required: true, message: "请选择招商企业", trigger: "change" },
        // ],
        isTop500: [
          { required: true, message: "请选择500强企业", trigger: "change" },
        ],
        isFourClusters: [
          { required: true, message: "请选择4大产业集群", trigger: "change" },
        ],
        isIndustryBenchmark: [
          { required: true, message: "请选择行业标杆", trigger: "change" },
        ],
        isTechLeader: [
          { required: true, message: "请选择科创引领", trigger: "change" },
        ],
        businessRegistrationAddress: [
          { required: true, message: "请输入工商注册地址", trigger: "blur" },
        ],
        investmentTypeCategory: [
          { required: true, message: "请选择招商类型", trigger: "change" },
        ],
        businessRegistrationDate: [
          { required: true, message: "请选择工商登记日期", trigger: "change" },
        ],
        // isOriginalDistrict: [
        //   { required: true, message: "请选择原区属下放", trigger: "change" },
        // ],
        isBusinessTransfer: [
          { required: true, message: "请选择是否工商迁移", trigger: "change" },
        ],
        handlingType: [
          { required: true, message: "请选择经办类型", trigger: "change" },
        ],
        handlingPerson: [
          { required: true, message: "请输入经办人", trigger: "blur" },
        ],
        isResourceOccupation: [
          { required: true, message: "请选择是否占用资源", trigger: "change" },
        ],

        belongingArea: [
          { required: true, message: "请选择所属区域", trigger: "change" },
        ],

        // buildingEntry: [
        //   { required: true, message: "请输入入住楼宇", trigger: "blur" },
        // ],

        attachmentList: [
          { required: true, message: "请上传营业执照", trigger: "change" },
        ],
      },
      businessDivisionArr: [],
      openType: "", //0保存、1提交
      businessLicenseInfoFile: false,
      enterpriseNatureArr: [], //企业性质:
      industryCategoryArr: [], //行业类别
      belongingAreaArr: [], //区域
      // buildingEntryArr: [], //楼宇
      householdApprovalHistory: [], //审批详情
      status: "",
      crossFunctionalAreaTypeArr: [],
    };
  },
  created() {
    this.getCompanyAbbrData();
    this.getEnterpriseNature();
    this.getIndustryCategory();
    this.getFindAllParkName();
    this.getcrossFunctionalAreaType();
    if (this.$route.query.id && this.$route.query.statu) {
      this.id = this.$route.query.id; //获取路由参数
      this.pageType = this.$route.query.statu; //获取路由参数 1
      this.status = this.$route.query.status; //1或3
      this.getHouseholdDetail();
    } else {
      const curTime = moment().format("YYYY-MM-DD");
      this.$set(this.formData, "declarationDate", curTime);
      this.getHouseholdBaseInfo();
    }
  },

  methods: {
    checkMobile,
    disabledDate(current) {
      // 设置最大日期为2023年1月1日
      const maxDate = new Date("2099-12-31");
      return current && current > maxDate; // 如果当前日期大于最大日期，则禁用
    },
    //获取基本信息
    async getHouseholdBaseInfo() {
      await getHouseholdBaseInfo().then((res) => {
        console.log(res);
        if (res.code == 0) {
          this.householdAffiliationDTOList.push({
            department: res.data.businessDivision,
            ratio: "100%",
            remark: "",
            type: "单一",
            disabled: true,
          });
          this.formData.initiateName = res.data.initiateName;
          this.formData.businessDivision = res.data.businessDivision;
          this.formData.applicationDate = res.data.applicationDate;
          this.formData.applicationNumber = res.data.applicationNumber;
        }
      });
    },
    //获取营商分布枚举
    async getCompanyAbbrData() {
      await getCompanyAbbrList().then((res) => {
        if (res.code == 0) {
          this.businessDivisionArr = res.data;
        } else {
          this.businessDivisionArr = [];
        }
      });
    },
    //企业性质
    async getEnterpriseNature() {
      this.enterpriseNatureArr = await this.$getDictByType({
        dictCode: "enterprise_nature",
      });
    },
    //行业类别
    async getIndustryCategory() {
      this.industryCategoryArr = await this.$getDictByType({
        dictCode: "industry_category",
      });
    },
    //获取所属区域
    async getFindAllParkName() {
      this.belongingAreaArr = await this.$getDictByType({
        dictCode: "belonging_area_type",
      });
      // await findAllParkName().then((res) => {
      //   if (res.code == 0) {
      //     this.belongingAreaArr = res.data;
      //   }
      // });
    },
    //获取交叉功能区
    async getcrossFunctionalAreaType() {
      this.crossFunctionalAreaTypeArr = await this.$getDictByType({
        dictCode: "cross_functional_area_type",
      });
    },

    //改变所属区域，获取不同楼宇list
    // changeBelongingArea() {
    //   this.buildingEntryArr = [];
    //   if (this.formData.belongingArea) {
    //     queryAllBuilding({ parkName: this.formData.belongingArea }).then(
    //       res => {
    //         if (res.code == 0) {
    //           this.buildingEntryArr = res.data.buildingNumbers;
    //         }
    //       }
    //     );
    //   }
    // },
    //获取户管信息详情
    async getHouseholdDetail() {
      await getHouseholdDetail({ id: this.id }).then((res) => {
        if (res.code == 0) {
          console.log(res.data, "xiangqing");
          this.formData = res.data;
          this.formData.attachmentList = this.formData.attachmentList
            ? this.formData.attachmentList
            : [];
          this.householdAffiliationDTOList =
            res.data.householdAffiliationDTOList;
          this.householdContactDTOList = res.data.householdContactDTOList;
          this.householdApprovalHistory = res.data.householdApprovalHistory;
          this.householdAffiliationDTOList.forEach((item) => {
            item.disabled = true;
          });
          this.householdContactDTOList.forEach((item) => {
            item.disabled = true;
          });
          // this.changeBelongingArea();
        }
      });
    },
    //上传营业执照
    handleConFileCallback: function (file) {
      this.$refs["formData"].clearValidate(["attachmentList"]);
      this.businessLicenseInfoFile = true;
      let tmpList = this.formData.attachmentList;
      tmpList.push(file);
      this.$set(this.formData, "attachmentList", tmpList);
    },

    //删除营业执照
    deleteConFile: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.businessLicenseInfoFile = false;
      }
      // this.formData.taxFileList = fileList;
      this.$set(this.formData, "attachmentList", fileList);
    },
    //信息保存、提交
    saveInfo() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          console.log(
            this.householdAffiliationDTOList,
            "this.householdAffiliationDTOList"
          );
          if (
            !this.householdAffiliationDTOList.every((item) => item.disabled)
          ) {
            this.$message.error("户管归属单位区域存在为保存的信息");
            return;
          }
          if (this.householdContactDTOList.length == 0) {
            this.$message.error("企业联系人区域至少填写一条信息");
            return;
          }
          if (!this.householdContactDTOList.every((item) => item.disabled)) {
            this.$message.error("企业联系人区域存在为保存的信息");
            return;
          }

          let params = JSON.parse(JSON.stringify(this.formData));
          params.openType = this.openType; //新增和提交
          params.householdAffiliationDTOList = this.householdAffiliationDTOList;
          params.householdContactDTOList = this.householdContactDTOList;
          addHousehold(params).then((res) => {
            if (res.code == 0) {
              this.$message.success(
                `${this.openType == 0 ? "保存" : "提交"}成功`
              );
              setTimeout(() => {
                this.$router.push(`/household-infor-managment`);
              }, 1000);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //更新信息
    updataInfo() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (
            !this.householdAffiliationDTOList.every((item) => item.disabled)
          ) {
            this.$message.error("户管归属单位区域存在未保存的信息");
            return;
          }
          if (!this.householdContactDTOList.every((item) => item.disabled)) {
            this.$message.error("企业联系人区域存在未保存的信息");
            return;
          }
          let params = JSON.parse(JSON.stringify(this.formData));

          params.openType = this.openType;
          params.householdAffiliationDTOList = this.householdAffiliationDTOList;
          params.householdContactDTOList = this.householdContactDTOList;
          updateHousehold(params).then((res) => {
            if (res.code == 0) {
              this.$message.success(
                `${this.openType == 0 ? "保存" : "提交"}成功`
              );
              setTimeout(() => {
                this.$router.push(`/household-infor-managment`);
              }, 1000);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //提交申请
    onSubmit() {
      this.openType = "1";
      if (this.pageType == 1) {
        this.updataInfo();
      } else {
        this.saveInfo();
      }
    },
    //保存
    onSave() {
      this.openType = "0";
      if (this.pageType == 1) {
        this.updataInfo();
      } else {
        this.saveInfo();
      }
    },
    //返回
    toBack() {
      let that = this;
      this.$confirm({
        title: "当前内容尚未保存，请确认是否返回？",
        content: "",
        onOk() {
          that.$router.push(`/household-infor-managment`);
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    },
    //新增户管归属单位、户管联系人
    addTabel(type) {
      if (type == 1) {
        this.householdAffiliationDTOList.push({
          department: undefined,
          ratio: "",
          remark: "",
          type: "单一",
          disabled: false,
        });
      } else if (type == 2) {
        this.householdContactDTOList.push({
          contactName: "",
          position: "",
          contactPhone: "",
          contactEmail: "",
          otherPhone: "",
          disabled: false,
        });
      }
    },
    //编辑
    editFn(index, type) {
      console.log(this.householdAffiliationDTOList, "33333");
      if (type == 1) {
        if (!this.householdAffiliationDTOList[index].disabled) {
          if (
            !this.householdAffiliationDTOList[index].department ||
            !this.householdAffiliationDTOList[index].ratio ||
            !this.householdAffiliationDTOList[index].type
          ) {
            this.$message.error("归属分部、比例、类型都不能为空");
            return;
          }
        }

        this.$set(
          this.householdAffiliationDTOList[index],
          "disabled",
          !this.householdAffiliationDTOList[index].disabled
        );
        this.$forceUpdate();
      } else {
        const regex = /^1[3-9]\d{9}$/;
        if (!this.householdContactDTOList[index].disabled) {
          console.log(1);
          if (
            !this.householdContactDTOList[index].contactName ||
            !this.householdContactDTOList[index].position ||
            !this.householdContactDTOList[index].contactPhone ||
            !this.householdContactDTOList[index].contactEmail ||
            !this.householdContactDTOList[index].otherPhone
          ) {
            this.$message.error(
              "联系人、职务、手机号码、邮箱地址、其他人联系手机号都不能为空"
            );
            return;
          } else {
            if (
              !regex.test(this.householdContactDTOList[index].contactPhone) ||
              !regex.test(this.householdContactDTOList[index].otherPhone)
            ) {
              this.$message.error("手机号码或者其他人联系手机号格式不正确");
              return;
            }
          }
        }
        console.log("222");
        this.$set(
          this.householdContactDTOList[index],
          "disabled",
          !this.householdContactDTOList[index].disabled
        );
        this.$forceUpdate();
      }
    },
    deleteFn(index, type) {
      if (type == 1) {
        this.householdAffiliationDTOList.splice(index, 1);
      } else {
        this.householdContactDTOList.splice(index, 1);
      }
    },
    //翻页
    onShowSizeChange(current, pageSize) {
      console.log(current, pageSize);
      this.pages.pageNo = current;
      this.pages.pageSize = pageSize;
      this.pages.total = this.dataSource.length;
    },
    onShowSizeChange1(current, pageSize) {
      console.log(current, pageSize);
      this.pages1.pageNo = current;
      this.pages1.pageSize = pageSize;
      this.pages1.total = this.dataSource1.length;
    },
  },
};
</script>
<style lang="less" scoped>
.keyClueForm {
  width: 100%;
  padding: 20px;
  background: #fff;
  .comTitle {
    position: relative;
    width: 100%;
    margin: 10px 0;
    padding: 0 10px;
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    &::before {
      content: "";
      width: 4px;
      height: 20px;
      background: #1777ff;
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translateY(-10px);
    }
  }
  .approveBox {
  }
  .box1 {
    width: 100%;
    background: #fff;
    padding: 10px;
    .box1Title {
      height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      font-size: 14px;
      font-weight: bold;
    }
  }
  .boxWidth {
    /deep/.ant-form-item-control-wrapper {
      width: 100%;
    }
  }
  .label-box {
    width: 80%; // 建议80%，太长就会超出内容
    display: inline-block;
    height: auto !important;
    white-space: break-spaces;
    line-height: 18px;
    text-align: right;
    vertical-align: bottom; // 这是为了让整体的字往下移动一点
  }
  .ant-form-item {
    margin-bottom: 16px;
  }
  .ant-form-item-label {
    text-align: left;
  }
  .ant-form-item-control {
    margin-left: 0px;
  }
}
</style>

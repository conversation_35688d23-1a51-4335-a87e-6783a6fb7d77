<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 图片控件 
-->
<template>
  <a-form-model
    :rules="rules"
    :model="form"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
  >
    <a-form-model-item label="标题">
      <a-input v-model="form.inputTitle" placeholder="请输入" maxLength="20" />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        maxLength="20"
        v-model="form.placeholder.placeholderText"
        placeholder="最多20字"
      />
    </a-form-model-item>
    <a-form-model-item label="每个文件大小" props="fileSize">
      <a-input
        v-model="form.optionsData.fileSize"
        suffix="MB"
        placeholder="请输入文件大小"
        oninput="value=value.replace(/[^\d]/g,'')"
      >
      </a-input>
      <!-- <a-input v-model="form.optionsData.fileSize">
        <a-select
          slot="addonAfter"
          default-value="MB"
          style="width: 80px"
          @change="selectType"
        >
          <a-select-option value="KB">
            KB
          </a-select-option>
          <a-select-option value="MB">
            MB
          </a-select-option>
          <a-select-option value="GB">
            GB
          </a-select-option>
        </a-select>
      </a-input> -->
    </a-form-model-item>
    <a-form-model-item label="文件个数" help="支持图片格式：bmp,jpg,png">
      <a-input-number v-model="form.optionsData.fileNum" min="1" max="10" />
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { checkPositiveNum } from "@/common/validate";

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        inputTitle: null, //标题
        placeholder: {},
        optionsData: {
          fileSize: null, //文件大小
          // fileCompany: "MB", //文件大小的单位
          fileNum: null //文件数量
        },
        notNull: false, //是否必填
        rules: {
          fileSize: [
            {
              validator: checkPositiveNum,
              trigger: "change"
            }
          ]
        }
      }
    };
  },
  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        console.log("表单", form);
        this.$emit("update:data", form);
      },
      deep: true
    }
  },
  methods: {
    checkPositiveNum

    // selectType(value) {
    //   this.$set(this.form.optionsData, "fileCompany", value);
    // }
  }
};
</script>

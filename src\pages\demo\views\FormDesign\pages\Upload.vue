<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 上传控件dom/form
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <upload-dom :data="formData"></upload-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <upload-form v-bind:data.sync="formData"></upload-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 上传控件 DOM/Form
import {
  UploadDom,
  UploadForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/Upload";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    UploadDom,
    UploadForm
  },
  data() {
    return {
      formData: {
        title: undefined, //标题
        fileNum: "", //文件个数
        isNeed: true, //是不是必填
        optionsData: {
          checkAll: false, //当前这个选中
          indeterminate: false, //是不是全选
          checkedList: [], //选中的文件类型
          fileSize: "MB", //文件大小
          plainOptions: [
            "PDF",
            "Word",
            "Excel",
            "TXT",
            "图片",
            "压缩文件",
            "音频",
            "视频"
          ]
        }
      },
      moduleVoList: []
    };
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.title, //标题
        placeholder: this.formData.placeholderText, //提示文字
        notNull: this.formData.notNull ? 1 : 0, //是否必填
        inputType: "upload", //选中的文件类型
        inputName: this.formData.fileNum, //文件个数
        optionsData: JSON.stringify({
          checkAll: this.formData.optionsData.checkAll, //当前这个选中
          indeterminate: this.formData.optionsData.indeterminate ? 1 : 0, //是不是全选
          checkedList: this.formData.optionsData.checkedList, //选中的文件类型
          fileSize: this.formData.optionsData.fileSize, //文件大小
          plainOptions: this.formData.optionsData.plainOptions
        }) //是不是全选
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      let dataOption = [];
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "upload") {
              dataOption.push(item);
            }
          });
        });
        let dataOptionItem = dataOption && dataOption[dataOption.length - 1];
        let optionsData = JSON.parse(
          dataOption && dataOption[dataOption.length - 1].optionsData
        );

        this.formData = {
          title: dataOptionItem.inputTitle, //标题
          fileNum: dataOptionItem.inputName, //文件个数
          notNull: dataOptionItem.notNull, //是不是必填
          optionsData: {
            checkAll: optionsData.checkAll, //当前这个选中
            indeterminate: optionsData.indeterminate, //是不是全选
            checkedList: optionsData.checkedList, //选中的文件类型
            fileSize: optionsData.fileSize, //文件大小
            plainOptions: optionsData.plainOptions //总的复选框个数
          }
        };
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

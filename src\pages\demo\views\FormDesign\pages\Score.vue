<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 评分控件DOM/Form
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <score-dom :data="formData"></score-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <score-form v-bind:data.sync="formData"></score-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 评分控件 DOM/Form
import {
  ScoreDom,
  ScoreForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/Score";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    ScoreDom,
    ScoreForm
  },
  data() {
    return {
      formData: {
        inputTitle: "", //标题
        starNumber: "", //几颗星
        notNull: true, //是否必填
        optionsData: {
          score: "", //满分
          showScore: true, //显示分数
          needOption: true //允许半选
        }
      },
      moduleVoList: []
    };
  },
  mounted() {
    this.moduleVoList = [];
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: "", //提示文字
        notNull: this.formData.notNull ? 1 : 0,
        inputType: "score",
        inputName: this.formData.starNumber,
        optionsData: JSON.stringify({
          score: this.formData.optionsData.score, //满分
          showScore: this.formData.optionsData.showScore ? 1 : 0, //显示分数
          needOption: this.formData.optionsData.needOption ? 1 : 0 //允许半选
        }) //是否必须
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      let dataOption = [];
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        this.switchData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "score") {
              dataOption.push(item);
            }
          });
        });
        console.log(dataOption);
        this.formData = {
          inputTitle:
            dataOption && dataOption[dataOption.length - 1].inputTitle, //标题
          starNumber: dataOption && dataOption[dataOption.length - 1].inputName, //几颗星
          placeholderText: JSON.parse(
            dataOption && dataOption[dataOption.length - 1].optionsData
          ).score, //提示文字
          timeType: dataOption && dataOption[dataOption.length - 1].inputName, //时间选择单选按钮
          notNull: dataOption && dataOption[dataOption.length - 1].notNull,
          optionsData: {
            score: JSON.parse(
              dataOption && dataOption[dataOption.length - 1].optionsData
            ).score, //满分
            showScore: JSON.parse(
              dataOption && dataOption[dataOption.length - 1].optionsData
            ).showScore, //满分
            needOption: JSON.parse(
              dataOption && dataOption[dataOption.length - 1].optionsData
            ).needOption //满分
          }
        };
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

/*
 * @Description:
 * @Author: cyw
 * @Date: 2023-09-25 14:38:53
 * @LastEditTime: 2023-10-26 09:24:26
 * @LastEditors: cyw
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 *
 *获取企业信息的数据
 */
export function ApiGetCompanyMessage() {
  console.log(
    "BUILD_ENV",
    process.env.VUE_APP_MOCK,
    BASE_URL,
    process.env.VUE_APP_MOCK_HOST_DEV
  );
  return api({
    url: BASE_URL + "/home/<USER>/companyStatistics",
    // url: process.env.VUE_APP_MOCK_HOST_DEV
    //   ? "/Mock/enterpriseMonitoring/companyMessage"
    //   : BASE_URL + "/enterpriseMonitoring/companyMessage",
    method: "get",
  });
}

/**
 *
 *获取税收信息的数据
 */
export function ApiGetTaxMessage() {
  return api({
    // url: "/Mock" + "/enterpriseMonitoring/taxMessage",
    url: BASE_URL + "/home/<USER>/taxStatistics",
    method: "get",
  });
}

/**
 *
 *获取各种标签企业的总税收和企业数量
 */
export function ApiGetLabelCompanyMessage() {
  return api({
    // url: "/Mock" + "/enterpriseMonitoring/labelCompany",
    url: BASE_URL + "/home/<USER>/labelCompany",
    method: "get",
  });
}

/**
 *
 *获取4种类型下表格的数据
 */
export function ApiGetTableData() {
  return api({
    // url: "/Mock" + "/enterpriseMonitoring/companyTable",
    url: BASE_URL + "/home/<USER>/companyTable",
    method: "get",
  });
}

/**
 *
 *获取违规记录
 */
export function ApiGetViolationHistory(params) {
  return api({
    url: process.env.VUE_APP_MOCK_HOST_DEV
      ? "/Mock/enterpriseMonitoring/violationHistory"
      : BASE_URL + "/enterpriseMonitoring/violationHistory",
    method: "post",
    params,
  });
}

/**
 *
 *获取辖区每月税收和目标税收
 */
export function ApiGetTax() {
  return api({
    url: BASE_URL + "/home/<USER>/taxationTrend",
    method: "get",
  });
}

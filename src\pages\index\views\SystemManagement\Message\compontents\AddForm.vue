<!--
* <AUTHOR>
* @time 2020-12-22
* @dec 系统管理-消息模板管理-新增/修改
-->
<template>
  <a-modal
    :title="modelTitle"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel')
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="标题">
          <a-input
            autoComplete="off"
            v-decorator="[
              'title',
              {
                rules: [{ required: true, message: '请输入标题!' }]
              }
            ]"
            placeholder="请输入标题"
          />
        </a-form-item>
        <a-form-item label="流程id">
          <a-input
            v-decorator="[
              'templateId',
              {
                rules: [{ required: true, message: '请输入流程id!' }]
              }
            ]"
            placeholder="请输入流程id"
          />
        </a-form-item>
        <a-form-item label="节点id">
          <a-input
            v-decorator="[
              'nodeId',
              {
                rules: [{ required: true, message: '请输入节点id!' }]
              }
            ]"
            placeholder="请输入节点id"
          />
        </a-form-item>
        <a-form-item label="类型">
          <a-select
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
            v-decorator="[
              'type',
              {
                rules: [
                  {
                    required: true,
                    message: '请选择类型'
                  }
                ]
              }
            ]"
            allowclear
            placeholder="请选择类型"
          >
            <a-select-option value="0">
              0
            </a-select-option>
            <a-select-option value="1">
              1
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="文案">
          <a-input
            v-decorator="[
              'content',
              {
                rules: [{ required: true, message: '请输入文案!' }]
              }
            ]"
            placeholder="请输入备注"
          ></a-input>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick"
import { ApiSystemSaveTemplate } from "@/pages/index/data/api/SystemManagement/Message"
// 表单字段
const fields = ["title", "templateId", "nodeId", "type", "content"]

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    }
    return {
      loading: false,
      form: this.$form.createForm(this),
      modelTitle: "" //弹框标题
    }
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          this.form.setFieldsValue({
            title: "",
            templateId: "",
            nodeId: "",
            type: undefined,
            content: ""
          })
          this.modelTitle = this.data.id ? "修改" : "添加"
        }
      },
      deep: true
    }
  },
  mounted() {},
  created() {
    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v))
    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields))
    })
  },
  methods: {
    //确定提交
    handleOk(e) {
      this.loading = true
      e.preventDefault()
      this.form.validateFields((errors, values) => {
        if (!errors) {
          let params = {
            nodeId: values.nodeId,
            type: values.type,
            title: values.title,
            templateId: values.templateId,
            content: values.content
          }
          if (this.data.id) {
            params.id = this.data.id
            ApiSystemSaveTemplate(params)
              .then((res) => {
                if (res.code == 0) {
                  this.$emit("cancel")
                  // 重置表单数据
                  this.form.resetFields()
                  // 刷新表格
                  this.$emit("ok")
                  this.$message.info("修改成功！")
                }
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            ApiSystemSaveTemplate(params)
              .then((res) => {
                if (res.code == 0) {
                  this.$emit("cancel")
                  // 重置表单数据
                  this.form.resetFields()
                  // 刷新表格
                  this.$emit("ok")
                  this.$message.info("添加成功！")
                }
              })
              .finally(() => {
                this.loading = false
              })
          }
        } else {
          this.loading = false
        }
      })
    }
  }
}
</script>

<template>
  <div>
    <div id="ParkOccupancyRate" ref="ParkOccupancyRate" class="contain"></div>
  </div>
</template>

<script>
import { getAreaOccupancyApi } from "@/pages/index/data/api/ComponyWatch";
import * as echarts from "echarts";
export default {
  data() {
    return {
      chartData: {},
      yData: [],
      seriesData: [],
    };
  },
  mounted() {
    this.initData();
  },
  created() {},
  methods: {
    async initData() {
      this.chartData = await getAreaOccupancyApi("");
      this.chartData.data.forEach((e) => {
        this.seriesData.push(Number(e.occupancyRate).toFixed(2));
        this.yData.push(e.parkName);
      });
      this.drawLine();
    },
    drawLine() {
      let myChart = echarts.init(this.$refs.ParkOccupancyRate);
      let option = {
        title: {
          text: "园区入驻率",
          textStyle: {
            fontStyle: "normal",
            fontWeight: "bold",
            fontSize: 22,
          },
        },
        dataZoom: [
          {
            type: "slider",
            realtime: true, // 拖动时，是否实时更新系列的视图
            start: 80,
            end: 100,
            startValue: 0,
            endValue: 5,
            width: 8,
            height: "90%",
            top: "5%",
            right: 0,
            brushSelect: false,
            yAxisIndex: [0, 1], // 控制y轴滚动
            fillerColor: "#0093ff", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            backgroundColor: "#cfcfcf", //两边未选中的滑动条区域的颜色
            handleSize: 0, // 两边手柄尺寸
            showDataShadow: false, //是否显示数据阴影 默认auto
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            zoomLock: true,
            moveHandleStyle: {
              opacity: 0,
            },
          },
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            // params 是一个数组，数组中包含每个系列的数据信息
            var res = params[0].name + "<br/>";
            for (var i = 0, l = params.length; i < l; i++) {
              res +=
                "<br/>" +
                params[i].marker +
                params[i].seriesName +
                " : " +
                params[i].value +
                "%<br/>";
            }
            return res;
          },
        },
        legend: {
          show:false,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          max: 100,
        },
        yAxis: {
          type: "category",
          data: this.yData,
        },
        series: [
          {
            name: "入住率",
            type: "bar",
            label: {
              show: true,
              formatter: "{c} %",
              position: "insideRight",
            },
            showBackground: true,
            backgroundStyle: {
              color: "rgba(108,155,255,.3)",
            },
            barWidth: "40%",
            itemStyle: {
              // 定义一个颜色的渐变
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "rgba(108,155,255,.5)" }, // 柱图渐变色
                { offset: 0.5, color: "#6C9BFF" }, // 中间偏下
                { offset: 1, color: "#0052FF" }, // 顶部
              ]),
              borderRadius: [0, 20, 20, 0],
            },
            data: this.seriesData,
          },
        ],
      };
      // 绘制图表
      myChart.setOption(option);
      //多图表自适应
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contain {
  width: 100%;
  height: 400px;
}
</style>

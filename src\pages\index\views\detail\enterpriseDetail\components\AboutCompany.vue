// 信息查询--园区查询
<template>
  <div>
    <park-table ref="table" :columns="curColumns" :data="curData"></park-table>
  </div>
</template>

<script>
import parkTable from "../../../InformationQuery/ParkQuery/components/parkTable.vue";
export default {
  components: {
    parkTable,
  },
  data() {
    const curColumns = [
      {
        title: "序号",
        width: 100,
        dataIndex: "name",
        key: "name",
      },
      {
        title: "企业名称",
        dataIndex: "age",
        key: "age",
      },
      {
        title: "关联人",
        dataIndex: "address",
        key: "address",
      },
      {
        title: "职位",
        dataIndex: "address",
        key: "address",
      },
      {
        title: "企业地址",
        dataIndex: "address",
        key: "2",
      },
    ];
    const curData = [];
    for (let i = 0; i < 100; i++) {
      curData.push({
        key: i,
        action: i,
        name: `${i + 1}`,
        age: "星联科技园",
        compony: "百度科技",
        address: `宜山路${i}`,
      });
    }
    return {
      curData,
      curColumns,
    };
  },
  methods: {},
};
</script>

<style lang="less" scoped></style>

<!-- 企业查询 -->
<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <a-card class="enterprise" v-else>
    <div class="enterpriseFrom">
      <a-form>
        <a-row>
          <a-col :span="24">
            <div style="display: flex;">
              <a-button
                type="primary"
                style="margin-right: 10px;"
                @click="handleSubmit(1)"
              >
                从租赁表导入企业
              </a-button>
              <a-button
                type="primary"
                style="margin-right: 10px;"
                @click="handleSubmit(2)"
              >
                从税收表导入企业
              </a-button>
              <a-button
                type="primary"
                style="margin-right: 10px;"
                @click="handleSubmit(3)"
              >
                从启信宝同步企业基础信息
              </a-button>
              <a-button
                type="primary"
                style="margin-right: 10px;"
                @click="handleSubmit(4)"
              >
                更新企业类型信息
              </a-button>
              <a-button
                type="primary"
                style="margin-right: 10px;"
                @click="handleSubmit(5)"
              >
                导入税收监控企业
              </a-button>
              <a-button
                type="primary"
                style="margin-right: 10px;"
                @click="handleSubmit(6)"
              >
                导入税收预警数据
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-form>
      <div class="import">
        <div>
          <a-upload
            name="file"
            :multiple="false"
            :action="
              `${VUE_APP_MOCK_HOST}/manager/companyTaxation/importFromExcel`
            "
            :headers="headers"
            @change="handleChange"
          >
            <a-button> <a-icon type="upload" /> 上传税收数据</a-button>
          </a-upload>
        </div>
        <div>
          <custom-importer
            accept=".xls,.xlsx"
            :disabled="false"
          ></custom-importer>
        </div>
      </div>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
  </a-card>
</template>

<script>
import CustomImporter from "@/components/Importer";
import {
  ApiLeaseToEnterPrise,
  ApiTaxiToEnterPrise,
  ApiQixinBaoBaseInfo,
  ApiUpdateEnterPrise,
  ApiImportTaxInfomation,
  ApiTaxWarningInfo,
} from "@/pages/index/data/api/SystemManagement/ImportData";
import { BASE_URL } from "Config";

export default {
  components: {
    CustomImporter,
  },
  data() {
    return {
      VUE_APP_MOCK_HOST: BASE_URL,
      headers: {
        authorization: "authorization-text",
      },
    };
  },
  mounted() {},
  methods: {
    handleSubmit(type) {
      switch (type) {
        case 1:
          ApiLeaseToEnterPrise();
          break;
        case 2:
          ApiTaxiToEnterPrise();
          break;
        case 3:
          ApiQixinBaoBaseInfo();
          break;
        case 4:
          ApiUpdateEnterPrise();
          break;
        case 5:
          ApiImportTaxInfomation();
          break;
        case 6:
          ApiTaxWarningInfo();
          break;
      }
    },
    handleChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === "done") {
        console.log(info.file, "info.file");
        let code = info.file.response.code;
        if (code == 0) {
          this.$message.success(`${info.file.response.msg}`);
        } else {
          this.$message.error(`${info.file.response.msg}`);
        }
      } else if (info.file.status === "error") {
        this.$message.error(`${info.file.name} file upload failed.`);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.import {
  margin-top: 20px;
}
</style>

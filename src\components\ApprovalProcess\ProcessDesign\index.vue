<!--
 * @dec 流程设计
-->
<template>
  <div class="process-design-page">
    <draw-process-design
      ref="flow"
      @clickNode="clickNode"
    ></draw-process-design>
    <!-- 抽屉 -->
    <a-drawer
      class="drawer-header-theme"
      placement="right"
      :width="drawer.width"
      :closable="false"
      :visible="drawer.visible"
      :after-visible-change="afterVisibleChange"
      @close="onClose"
    >
      <template slot="title">
        <div class="drawer-header-theme-edit">
          <span
            class="cursor-pointer"
            @click="
              editTitle.isEdit = true;
              editTitle.value = drawer.title;
              $nextTick(() => {
                $refs.editTitleInput.focus();
              });
            "
            v-if="!editTitle.isEdit"
            >{{ drawer.title }}<a-icon type="edit" class="drawer-header-anticon"
          /></span>
          <div v-if="editTitle.isEdit">
            <a-input
              v-model="editTitle.value"
              ref="editTitleInput"
              @blur="
                editTitle.isEdit = false;
                drawer.title = editTitle.value;
              "
              placeholder="请输入"
            />
          </div>
        </div>
      </template>
      <template v-if="drawer.visible">
        <component
          ref="drawerComponent"
          :is="drawer.componentName"
          :data="drawer.data"
          :openWay="drawer.openWay"
        ></component>
      </template>
    </a-drawer>
  </div>
</template>
<script>
// 发起人/审批人/抄送人 设置
import FlowDrawer from "./components/FlowDrawer";
// 条件 设置
import SetRuleBranch from "./components/SetRuleBranch";
// 绘制流程图
import DrawProcessDesign from "./components/DrawProcessDesign";
export default {
  data() {
    return {
      // 抽屉
      drawer: {
        // 标题
        title: "",
        // 是否显示
        visible: false,
        // 组件名称
        componentName: "",
        // 宽度
        width: 520,
        // 组件数据
        data: {},
        // 类型
        openWay: ""
      },
      // 修改标题事件
      editTitle: {
        isEdit: false,
        value: ""
      }
    };
  },
  components: { FlowDrawer, SetRuleBranch, DrawProcessDesign },
  methods: {
    //点击发布调用
    submit() {
      let arr = this.$refs.flow.getNodeArr();
      return arr;
    },
    /**
     * 点击node节点
     * STARTEVENT("1","开始节点"), USERTASK("2","审批节点"), BUSINESSRULETASK("3","规则节点"), CCTASK("4","抄送节点"), ENDEVENT("5","结束节点");
     */
    clickNode(item) {
      this.drawer.data = item;
      this.drawer.title = item.title;
      this.$emit("changeFormDesin");
      switch (item.type) {
        case "1":
          this.handleSponsorSettings(item);
          break;
        case "2":
          this.handleApprovedSettings(item);
          break;
        case "3":
          this.handleConditionSettings(item);
          break;
        case "4":
          this.handleCCPersonSettings(item);
          break;
        case "6":
          this.handleTransferTo(item);
          break;
        default:
          break;
      }
    },
    /**
     * 抽屉改变状态时触发
     */
    // afterVisibleChange(val) {
    //   console.log("drawer.visible", val);
    // },
    //发起人设置
    handleSponsorSettings() {
      this.drawer.width = 520;
      this.drawer.componentName = "FlowDrawer";
      this.drawer.openWay = "launch";
      this.drawer.visible = true;
    },
    //审批人设置
    handleApprovedSettings() {
      this.drawer.width = 520;
      this.drawer.componentName = "FlowDrawer";
      this.drawer.openWay = "approval";
      this.drawer.visible = true;
    },
    //抄送人设置
    handleCCPersonSettings() {
      this.drawer.width = 520;
      this.drawer.componentName = "FlowDrawer";
      this.drawer.openWay = "CC";
      this.drawer.visible = true;
    },
    //流装至设置
    handleTransferTo() {
      this.drawer.width = 520;
      this.drawer.componentName = "FlowDrawer";
      this.drawer.openWay = "transferTo";
      this.drawer.visible = true;
    },
    //条件设置
    handleConditionSettings() {
      this.drawer.width = 820;
      this.drawer.componentName = "SetRuleBranch";
      this.drawer.visible = true;
    },
    onClose() {
      this.drawer.visible = false;
      let data = {
        ...this.$refs.drawerComponent.getData(),
        title: this.drawer.title
      };
      this.$refs.flow.nodeChange(data);
    }
  }
};
</script>
<style lang="less" scoped>
// .process-design-page {
//   padding: 50px 0 0 50px;
// }
// 设置抽屉栏header样式
.drawer-header-theme {
  /deep/ .ant-drawer-header {
    padding: 8px 24px;
    .ant-drawer-title {
      height: 34px;
      display: flex;
      align-items: center;
      .drawer-header-theme-edit {
        .drawer-header-anticon {
          margin-left: 5px;
        }
      }
    }
  }
}
</style>

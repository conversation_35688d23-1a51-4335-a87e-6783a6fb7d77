.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
}
.lessorInfo {
  width: 100%;
  background-color: #fff;
  margin-bottom: 38px;
}
.lessorInfo .lessorInfo-tit {
  width: 96%;
  margin: 0 auto;
}
.lessorInfo .lessorInfo-tit .tit {
  padding-left: 12px;
  margin-bottom: 32px;
  height: 20px;
  font-size: 20px;
  font-weight: 500;
  color: #1d2129;
  line-height: 20px;
  border-left: 4px solid #1777ff;
}
.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}
.name {
  width: 200px;
  min-height: 66px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f9ff;
  border: 1px solid #deeafb;
  font-size: 16px;
  color: #262626;
}
.employeesInfoOut {
  display: flex;
  width: 20%;
  margin-bottom: 8px;
  line-height: normal !important;
}
.employeesInfoOut .item-out {
  display: flex;
  width: 200px;
  height: 30px;
  padding-top: 8px;
}
.employeesInfoOut .item-out .item {
  padding: 2px 6px;
  margin-right: 4px;
  color: #4c96ff;
  background-color: #e8f3ff;
  border: 1px solid #e8f3ff;
  border-radius: 4px;
  box-sizing: border-box;
  color: #262626;
}
.partnersInfoOut {
  display: flex;
  width: 50%;
  margin-bottom: 8px;
  line-height: normal !important;
}
.partnersInfoOut .item {
  width: 100%;
  padding: 2px 6px;
  margin-right: 4px;
  box-sizing: border-box;
  color: #262626;
}
.partnersInfoOut .item1 {
  padding-top: 2px;
  background-color: #fff7e8;
  color: #ffaf60;
  border: 1px solid #fff7e8;
  border-radius: 4px;
}
.partnersInfoOut .item::before {
  content: "•";
  color: #4c96ff;
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 18px;
  transform: scale(3);
  text-align: center;
}
.componyType {
  width: 160px;
  display: flex;
  align-items: center;
  text-indent: 1em;
  border: 1px solid #deeafb;
}
.aptitudeStyle {
  height: 34px;
  line-height: 34px;
  padding: 0px 12px;
  background: #e8ffea;
  border-radius: 4px;
  font-size: 16px;
  color: #00b42a;
  margin: 15px 20px;
}
.title {
  width: 70px;
  height: 38px;
  line-height: 38px;
  border-radius: 45%;
  background-color: #e8e4e4;
  color: #cbc5c5;
  font-size: 16px;
  text-align: center;
  margin-right: 20px;
}
.title-font {
  font-size: 21px;
  font-weight: bold;
}
.echart-item {
  width: 49%;
  margin: 2px auto;
  height: 320px;
}
.customer {
  width: 98%;
  height: 60px;
  line-height: 60px;
  margin: 0 auto;
  background: #f5f8fa;
  border-radius: 8px;
}
.customer .customer-box {
  width: 70%;
  margin: 0 auto;
  display: flex;
  justify-content: space-around;
}
.customer .customer-box .customer-item {
  display: flex;
  align-items: center;
}
.customer .customer-box .customer-item span {
  font-size: 20px;
  margin-left: 5px;
}
.ant-advanced-search-form /deep/ .ant-checkbox-group .ant-checkbox-group-item {
  font-size: 17px !important;
  margin-left: 68px;
  margin-bottom: 30px;
}
.ant-advanced-search-form /deep/ .ant-col-8 .ant-col-sm-8 {
  width: 31% !important;
}
.ant-advanced-search-form /deep/ .ant-col-8 .ant-col-sm-16 {
  width: 69% !important;
}
.ant-advanced-search-form /deep/ .ant-col-16 .ant-col-sm-8 {
  width: 15% !important;
}
.ant-advanced-search-form /deep/ .ant-col-16 .ant-col-sm-16 {
  width: 85% !important;
}
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label {
  font-size: 16px;
}
.ant-advanced-search-form /deep/ .ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
}

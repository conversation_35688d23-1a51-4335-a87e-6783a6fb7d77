<!-- 报表管理--园区分析报表 -->
<template>
  <div class="park">
    <div class="parkFrom">
      <a-form
        class="all"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-row :gutter="100" align="center">
          <a-col :span="8">
            <a-form-item label="统计周期" class="label">
              <a-space direction="vertical" :size="12">
                <a-range-picker
                  style="width: 100%"
                  v-model="queryParams.period"
                  @change="date()"
                />
              </a-space>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="园区名称" class="label">
              <a-select
                v-model="queryParams.parkName"
                style="width: 100%"
                placeholder="请选择"
                allowClear
                :filterMultiple="false"
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
              >
                <a-select-option
                  :value="item"
                  v-for="item in parkArr"
                  :key="item"
                  >{{ item }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <div class="btnGroup">
              <a-button type="primary" class="query" @click="search">
                查询
              </a-button>
              <a-button type="primary" class="query" @click="reset"
                >重置</a-button
              >
              <a-button type="primary" class="export" @click="deduced()"
                >导出</a-button
              >
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="tablePart">
      <a-card style="width: 100%">
        <s-table
          ref="table"
          size="default"
          rowKey="id"
          :columns="columns"
          :scroll="{ x: 1200, y: 800 }"
          :data="loadData"
        >
          <span slot="ordinal" slot-scope="text, record, index">
            {{ (pageNo - 1) * pageSize + index + 1 }}
          </span>
        </s-table>
      </a-card>
    </div>
  </div>
</template>

<script>
// import ParkPublicFrom from "../compontents/ParkPublicFrom.vue";
// import PublicTable from "../compontents/PublicTable.vue";
import {
  ApiGetParkReport,
  ApiExportReportPark,
} from "@/pages/index/data/api/ReportManagement/ParkAnalysisReport";
// import moment from "moment"
import { ApiGetParksName } from "@/pages/index/data/api/InfomationQuery/index";
import STable from "@/components/Table";
import { parseNumFloat } from "@/common/utils/utils.js";
import * as utils from "@/common/utils/index.js";
export default {
  components: {
    // ParkPublicFrom,
    // PublicTable
    STable,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 },
        },
      },
      parkArr: [],
      queryParams: {
        period: [],
        parkName: undefined,
      },
      defaultValue: "排序",
      sortArr: [
        {
          value: "1",
          name: "按园区面积排序",
        },
        {
          value: "2",
          name: "按楼宇数排序",
        },
      ],
      tableData: [],
      columns: [
        {
          title: "序号",
          width: 120,
          scopedSlots: { customRender: "ordinal" },
          fixed: "left",
          align: "center",
        },
        {
          title: "园区名称",
          width: 200,
          dataIndex: "parkName",
          fixed: "left",
          align: "center",
        },
        {
          title: "园区面积（平米）",
          dataIndex: "parkArea",
          width: 150,
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "楼宇数（栋）",
          dataIndex: "buildingNum",
          width: 150,
          align: "center",
        },
        {
          title: "税收金额（万元）",
          dataIndex: "taxation",
          width: 150,
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "每平米产出（元）",
          dataIndex: "output",
          width: 200,
          align: "center",
        },
        {
          title: "已入驻企业数（家）",
          dataIndex: "settledNum",
          width: 160,
          align: "center",
        },
        {
          title: "入驻率（%）",
          dataIndex: "settledRate",
          width: 150,
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "空置面积（平米）",
          dataIndex: "vacancyArea",
          width: 150,
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "空置率（%）",
          dataIndex: "vacancyRate",
          width: 150,
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "属地率（%）",
          dataIndex: "apanageRate",
          width: 150,
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "新增企业数（家）",
          dataIndex: "newaddNum",
          width: 150,
          align: "center",
        },
        {
          title: "退租企业数（家）",
          dataIndex: "surrenderNum",
          width: 150,
          align: "center",
        },
        {
          title: "简介",
          dataIndex: "parkDescrip",
          width: 200,
          align: "center",
        },
      ],
      data: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        period: [],
        createDateStart: "",
        createDateEnd: "",
      };
      this.leaseEndTime = [];
      this.$refs.table.refresh(true);
    },
    search() {
      console.log(this.queryParam);
      if (this.queryParams.period.length > 0) {
        this.queryParams.createDateStart = this.queryParams.period[0].format(
          "YYYY-MM-DD hh:mm:ss"
        );
        this.queryParams.createDateEnd = this.queryParams.period[1].format(
          "YYYY-MM-DD hh:mm:ss"
        );
      }
      this.$refs.table.refresh(true);
    },
    date(value) {
      console.log(value, "value");

      if (this.queryParams.period.length > 0) {
        this.queryParams.createDateStart = this.queryParams.period[0].format(
          "YYYY-MM-DD hh:mm:ss"
        );
        this.queryParams.createDateEnd = this.queryParams.period[1].format(
          "YYYY-MM-DD hh:mm:ss"
        );
      } else {
        this.queryParams.createDateStart = "";
        this.queryParams.createDateEnd = "";
      }
    },
    getOption(e) {
      this.defaultValue = e;
    },
    //导出按钮
    deduced() {
      ApiExportReportPark(this.queryParams);
    },
    async init() {
      ApiGetParksName().then(res => {
        this.parkArr= res.data;
      });
      // ApiGetParksName({ dictCode: "industryCategory" }).then((res) => {
      //   this.parkArr = res.data;
      // });
      // ApiGetParksName({ dictCode: "company_label" }).then(res => {
      //   this.natureArr = res.data;
      // });
    },
    async loadData({ pageNo, pageSize }) {
      this.pageNo = pageNo;
      this.pageSize = pageSize;
      const requestParameters = Object.assign(this.queryParams, {
        currentPage: pageNo,
        pageSize,
      });
      return ApiGetParkReport(requestParameters).then((res) => {
        let dataObj = res.data;
        res.data.records.forEach((element) => {
          element["id"] = utils.getRandomNum("t", 8);
        });
        dataObj.data = res.data.records;
        dataObj.totalCount = res.data.total;
        dataObj.pageSize = res.data.size;
        dataObj.pageNo = res.data.current;
        return dataObj;
      });
      // return new Promise((resolve) => {
      //   setTimeout(() => {
      //     resolve({
      //       pageNo: 1,
      //       pageSize: 10,
      //       total: 0,
      //       data: [
      //         {
      //           ordinal: 1,
      //           name: "星联科技园",
      //           size: 20000,
      //           buildings: 8,
      //           tax: 2000,
      //           squareMeter: 100,
      //           highTech: 30,
      //           specializedNew: 10,
      //           foreignCapital: 20,
      //           listedCompanies: 15,
      //           settledEnterprises: 75,
      //           occupancyRate: 70,
      //           empty: 1000,
      //           vacancyRate: 50,
      //           territorialRate: 80,
      //           resident: 50,
      //           add: 10,
      //           moveOut: 5,
      //           leaseWithdrawal: 2,
      //           propertyFee: 5,
      //           rent: 35,
      //           introduction: "非常的好"
      //         }
      //       ]
      //     })
      //   }, 1000)
      // })
    },
  },
};
</script>

<style lang="less" scoped>
.park {
  display: flex;
  flex-wrap: wrap;
  .parkFrom {
    width: 100%;
    height: 4rem;
    border-width: 0px;
    position: absolute;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    border: none;
    border-radius: 2px;
    .all {
      // display: flex;
      // flex-wrap: nowrap;
      margin-top: 0.8rem;
      // margin-left: 0.5rem;
      .label {
        // display: flex;
        // flex-wrap: nowrap;
        // margin-right: 30px;
      }
      .btnGroup {
        // margin-left: auto;
        margin-right: 20px;
        float: right;
        margin-top: 2px;
        .query {
          margin-right: 10px;
        }
      }
    }
  }
  .tablePart {
    margin-top: 80px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
</style>

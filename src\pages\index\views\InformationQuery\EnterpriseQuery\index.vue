<!-- 企业查询 -->
<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <div class="enterprise" v-else>
    <div class="enterpriseFrom">
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
        @submit="handleSubmit"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="园区名称">
              <a-select
                v-model="queryParam.parkName"
                placeholder="请选择园区名称"
                allowClear
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
              >
                <a-select-option
                  :value="item"
                  v-for="item in enumerateObj.parkList"
                  :key="item"
                  >{{ item }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="企业名称">
              <a-input
                maxLength="50"
                allowClear
                v-model="queryParam.companyName"
                placeholder="请输入企业名称"
              >
              </a-input>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="企业分布">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                allowClear
                v-model="queryParam.industryCategory"
                placeholder="请选择企业分布"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in parkArr"
                  :key="item.value"
                  >{{ item.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="企业标签">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                v-model="queryParam.companyLabel"
                placeholder="请选择企业标签"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in natureArr"
                  :key="item.value"
                  >{{ item.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="是否属地企业">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                v-model="queryParam.districtDecentralize"
                placeholder="请选择是否属地企业"
                allowClear
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in isBelongArr"
                  :key="item.value"
                  >{{ item.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="40" align="center">
          <div
            style="display: flex;justify-content: flex-end;margin-right: 30px;"
          >
            <a-button type="primary" @click="search">查询</a-button>
            <a-button type="default" @click="reset" style="margin-left:15px"
              >重置</a-button
            >
            <a-button
              type="primary"
              class="export"
              style="margin-left:15px"
              @click="deduced()"
              >导出</a-button
            >
          </div>
        </a-row>
      </a-form>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
    <a-card style="width:100%;margin-top:20px">
      <s-table
        ref="table"
        size="default"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        rowKey="id"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <a href="javascript:;" @click="toDetail(text, record)">查看</a>
        </span>
        <template slot="companyName" slot-scope="text">
          <div>{{ text }}</div>
        </template>
        <template slot="buildingAddress" slot-scope="text">
          <div>
            <a-tooltip>
              <template slot="title">
                {{ text }}
              </template>
              <div class="ellipse">{{ text }}</div>
            </a-tooltip>
          </div>
        </template>
      </s-table>
    </a-card>
  </div>
</template>

<script>
import { backGroundPageByCondition } from "@/pages/demo/data/api/api/company";
import {
  ApiGetParksName,
  ApiGetComponeyInfo,
  ApiExportEnterSearch,
} from "@/pages/index/data/api/InfomationQuery/index";
import STable from "@/components/Table";
export default {
  components: {
    STable,
  },
  data() {
    return {
      isBelongArr: [
        {
          desc: "属地企业",
          value: 0,
        },
        {
          desc: "非属地企业",
          value: 1,
        },
      ],
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 80,
          fixed: "left",
          align: "center",
        },
        {
          title: "企业名称",
          dataIndex: "companyName",
          width: 250,
          align: "center",
          scopedSlots: { customRender: "companyName" },
        },
        {
          title: "企业分布",
          dataIndex: "industryCategory",
          width: 240,
          align: "center",
        },
        {
          title: "企业标签",
          dataIndex: "companyLabel",
          width: 200,
          align: "center",
        },
        {
          title: "是否属地企业",
          dataIndex: "districtDecentralize",
          width: 120,
          align: "center",
          customRender: (text, row) => {
            return row.districtDecentralize ? "非属地企业" : "属地企业";
          },
        },
        {
          title: "关联企业(家)",
          dataIndex: "entRelationName",
          align: "center",
          width: 200,
        },

        {
          title: "入驻楼宇",
          dataIndex: "buildingAddress",
          align: "center",
          width: 200,
          scopedSlots: { customRender: "buildingAddress" },
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          scopedSlots: { customRender: "action" },
          width: 120,
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      labelCol: { span: 4 },
      parkArr: [
        {
          name: "华泾镇",
          value: "1",
        },
        {
          name: "星联科技园",
          value: "2",
        },
      ],
      defaultValue: "排序",
      sortArr: [
        {
          value: "1",
          name: "按园区面积排序",
        },
        {
          value: "2",
          name: "按楼宇数排序",
        },
      ],
      enumerateObj: {
        parkList: [],
      }, // 查询条件对象
      // 查询条件参数
      queryParam: {
        parkName: undefined,
        companyName: undefined,
        industryCategory: undefined,
        companyLabel: undefined,
        districtDecentralize: undefined,
      },
      natureArr: [
        { value: "1", desc: "高新技术" },
        { value: "2", desc: "专精特新" },
        { value: "3", desc: "外资总部" },
        { value: "4", desc: "上市公司" },
        { value: "5", desc: "外迁预警" },
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: (values) => {
        console.log(values, "values");
        this.pageNo = values.pageNo;
        this.pageSize = values.pageSize;
        const requestParameters = Object.assign(
          { pageNum: values.pageNo, pageSize: values.pageSize },
          this.queryParam
        );
        return backGroundPageByCondition(requestParameters).then((res) => {
          let dataObj = res.data;
          dataObj.data = res.data.records || [];
          return dataObj;
        });
      },
    };
  },
  created() {
    let name = this.$route.query.name;
    if (name) {
      this.queryParam.companyLabel = name;
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    deduced() {
      ApiExportEnterSearch(this.queryParam);
    },
    reset() {
      this.queryParam = {};
      this.leaseEndTime = [];
      this.$refs.table.refresh(true);
    },
    async init() {
      ApiGetParksName().then((res) => {
        // this.enumerateObj.parkList = res.data.parkBuildingBos;
        this.enumerateObj.parkList = res.data;
      });
      ApiGetComponeyInfo({ dictCode: "industryCategory" }).then((res) => {
        this.parkArr = res.data;
      });
      ApiGetComponeyInfo({ dictCode: "company_label" }).then((res) => {
        this.natureArr = res.data;
      });
    },
    search() {
      this.$refs.table.refresh(true);
    },
    toDetail(text, record) {
      this.$router.push(`/information-query/enterprise-detail?id=${record.id}`);
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise {
  display: flex;
  flex-wrap: wrap;
  .enterpriseFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }
  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
.ellipse {
  width: 100%;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 这里是超出几行省略 */
}
</style>

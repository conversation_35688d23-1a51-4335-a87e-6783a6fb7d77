module.exports = {
  presets: [
    "@vue/cli-plugin-babel/preset",
    [
      "@babel/preset-env",
      {
        targets: {
          browsers: ["last 2 versions", "ie > 9"]
        }
      }
    ]
  ],
  plugins: [
    ["@babel/plugin-transform-runtime"],
    ["@babel/plugin-proposal-decorators", { legacy: true }],
    ["@babel/plugin-proposal-class-properties", { loose: true }],
    [
      "@babel/plugin-proposal-object-rest-spread",
      { loose: true, useBuiltIns: true }
    ],
    ["@babel/plugin-syntax-dynamic-import"]
  ]
};

<!--
* <AUTHOR>
* @time 2020-9-21
* @dec 机房人员备案套件
-->
<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>机房人员备案套件</h3>
      <a-form-model-item label="申请主题">
        <a-input
          placeholder="[申请单位]关于用途的非航资源申请单-申请人-申请日期"
        />
      </a-form-model-item>
      <a-form-model-item label="航站楼">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="1">
            T1 机房
          </a-select-option>
          <a-select-option value="2">
            T2 东交机房
          </a-select-option>
          <a-select-option value="3">
            T2 楼内机房
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <div class="personnel-record-title-button">
        <div>备案人员信息</div>
        <div>
          <a-button type="primary" size="small">
            <a-icon type="plus" />
            添加
          </a-button>
        </div>
      </div>
      <!-- x:750 -->
      <a-table
        :scroll="{ x: 1000 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * 10 + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
    </a-form-model>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "serial" }
    // width: "60px"
  },
  {
    title: "姓名",
    dataIndex: "name",
    key: "name"
    // width: "85px"
  },
  {
    title: "联系方式",
    dataIndex: "phoneNo",
    key: "phoneNo"
    // width: "125px"
  },
  {
    title: "身份证号/通行证号",
    dataIndex: "cardId",
    key: "cardId"
  },
  {
    title: "单位",
    dataIndex: "companyName",
    key: "companyName"
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "120px",
    scopedSlots: { customRender: "action" }
  }
]

const values = [
  {
    name: "王二", //姓名
    phoneNo: "14388896547", //联系方式
    cardId: "320902198408085030", //身份证号/通行证号
    companyName: "东方航空" //所属单位
  }
]
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columns,
      values,
      form: {
        machineroom: null
      }
    }
  },
  watch: {
    value(val) {
      console.log(`selected:`, val)
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
.personnel-record-title-button {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 10px 0;
}
</style>

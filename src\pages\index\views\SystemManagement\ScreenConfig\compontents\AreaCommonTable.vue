<template>
  <div>
    <div class="header">
      <!-- 插槽相当于占位将传入的东西放在这里显示 -->
      <slot></slot>
      <div class="edit">
        <a-checkbox v-model="checked"></a-checkbox>
        <a-icon type="edit" v-if="changeData" @click="editData()"></a-icon>
        <a-button
          type="primary"
          class="export"
          @click="save(dataSource.sign)"
          v-else
          >保存</a-button
        >
      </div>
    </div>
    <table class="table">
      <thead>
        <td v-for="item in dataSource.tableHeader" :key="item.id">
          {{ item.name }}
        </td>
      </thead>
      <tbody>
        <tr v-for="row in dataSource.tableMain" :key="row.id">
          <td>
            <!-- 下面的class类使用了动态属性，当后面的值为true时这个类就存在，为false时这个类就不存在 -->
            <input
              v-model="row.name"
              :disabled="disabled1"
              :class="{ special: firstcolum }"
            />
          </td>
          <td>
            <!-- 下面的class类使用了动态属性，当后面的值为true时这个类就存在，为false时这个类就不存在 -->
            <input
              v-model="row.number"
              :disabled="disabled"
              :class="{ inputStyle: logo }"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    dataSource: String
  },
  data() {
    return {
      checked: false,
      // firstcolum控制表格的第一列，第一列有的可以修改有的不能修改，为true时启用这个类属性
      // 将输入框的边框和背景颜色去掉
      firstcolum: true,
      // logo和上面的一致，只是logo控制的是时刻都需要修改的
      logo: true,
      // changeData控制编辑图形和保存按钮的，为true时显示编辑，为false显示保存
      changeData: true,
      // disabled控制所有能够修改的数据，为true代表输入框不可以编辑
      disabled: true,
      // disabled1和上面一致，只是控制的是表格第一列的数据，因为有的第一列可以修改有的不能修改
      disabled1: true,
      // 定义数组去存储用户修改后的数据
      newDataSource: []
    };
  },
  methods: {
    // 点击编辑按钮之后输入框出现并且可以编辑，同时编辑按钮变成保存按钮
    editData() {
      this.logo = false;
      this.disabled = false;
      this.changeData = false;
    },
    save(sign) {
      this.newDataSource = [];
      // 利用循环将用户修改后的数据存储到新的数组里
      for (let i = 0; i < this.dataSource.tableMain.length; i++) {
        this.newDataSource.push(this.dataSource.tableMain[i].number);
      }
      // 点击保存后数据又不能编辑了，边框和背景色也要去掉
      this.logo = true;
      this.disabled = true;
      this.changeData = true;
      // sign是从父组件传过来的标识具体是哪一个表格的
      console.log(sign);
      console.log(this.newDataSource);
    }
  }
};
</script>
<style lang="less" scoped>
.title {
  font-weight: 600;
  margin-left: 42px;
}
.header {
  display: flex;
  flex-wrap: nowrap;
  .edit {
    margin-left: auto;
    margin-right: 47px;
  }
}
.table {
  //表格居中配置
  width: 871px;
  height: 275px;
  margin: 10px auto;
  border: 1px solid rgb(218, 217, 217);
  //表格行交替色实现
  thead {
    height: 50px;
    color: white;
    background-color: rgba(19, 194, 194);
  }

  tbody {
    tr:nth-child(2n) {
      background-color: #fafafa;
    }

    tr:nth-child(2n + 1) {
      background-color: #ffffff;
    }
  }
  .inputStyle {
    border: none;
    background-color: rgba(255, 255, 255, 0);
  }
  .special {
    border: none;
    background-color: rgba(255, 255, 255, 0);
  }
}
</style>

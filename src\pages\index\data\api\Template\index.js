import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 租赁合同经发审核查询 初审表下载
 * @param {*} params
 * @returns
 */
export function ApiLeaseReviewAuditQuery(params) {
  return api({
    url: BASE_URL + "/leaseReviewAudit/economicCompanyAuditQuery",
    method: "post",
    params,
  });
}

/**
 * 审核流转表下载
 * @param {*} params
 * @returns
 */
export function ApiLeaseReviewAudit(params) {
  return api({
    url: BASE_URL + "/leaseReviewAudit/ExamineCirculationDownload",
    method: "post",
    params,
  });
}

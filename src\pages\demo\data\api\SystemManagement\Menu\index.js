/**
 * <AUTHOR>
 * @time 2020-8-14
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /security/menu/findMenu
 * @dec ApiSecurityFindMenu
 */
/**
 * @dec 表单提交接口
 */
import api from "@/common/api";
import { BASE_URL } from "Config";
/**
 * 表单提交
 */
export function ApiSecurityFindMenu(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/findMenu",
    method: "post",
    middle,
    params,
  });
}

/**
 * 新增菜单
 */
export function ApiSecurityCreate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/create",
    method: "post",
    middle,
    params,
  });
}

/**
 * 修改菜单
 */
export function ApiSecurityUpdate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/update",
    method: "post",
    middle,
    params,
  });
}

/**
 * 根据ID查找菜单数据
 */
export function ApiSecurityFindById(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/findById",
    method: "post",
    middle,
    params,
  });
}

/**
 * 获取资源列表 - 带分页
 */
export function ApiSecurityPageByCondition(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/pageByCondition",
    method: "post",
    middle,
    params,
  });
}

/**
 * 删除菜单
 */
export function ApiSecurityDeleteById(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/deleteById",
    method: "post",
    middle,
    params,
  });
}

/**
 * 移除资源
 */
export function ApiSecurityDeleteBindByResourceIdAndMenuId(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/deleteBindByResourceIdAndMenuId",
    method: "post",
    middle,
    params,
  });
}

/**
 * 添加资源
 */
export function ApiSecuritySaveMenuWithResource(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/saveMenuWithResource",
    method: "post",
    middle,
    params,
  });
}

/**
 * 根据ID查找资源
 */
export function ApiSecurityFindResourceByMenuId(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/menu/findResourceByMenuId",
    method: "post",
    middle,
    params,
  });
}

<template>
  <div>
    <a-tabs default-active-key="1" @change="callback">
      <a-tab-pane key="1" tab="基本信息">
        <basic-Information style="background:#fff"></basic-Information>
      </a-tab-pane>
      <a-tab-pane key="2" tab="楼宇信息" force-render>
        <building-Information></building-Information>
      </a-tab-pane>
      <a-tab-pane key="3" tab="企业信息">
        <company-Information></company-Information>
      </a-tab-pane>
      <a-tab-pane key="4" tab="税收信息">
        <pay-Information></pay-Information>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import BasicInformation from "./components/BasicInformation.vue";
import BuildingInformation from "./components/BuildingInformation.vue";
import CompanyInformation from "./components/CompanyInformation";
import PayInformation from "./components/PayInformation.vue";
export default {
  components: {
		BasicInformation,
		BuildingInformation,
		CompanyInformation,
		PayInformation,
	},
  methods: {
    callback(key) {
      console.log(key);
    },
  },
};
</script>

<style lang="less" scoped>
/depp/ .ant-tabs-nav-scroll {
  background: #fff;
}
</style>

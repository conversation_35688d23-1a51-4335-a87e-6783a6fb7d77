<!-- 编辑备案登记 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <div class="detail">
      <LessorInfo
        :totalParmas="decisionParmas"
        @lessorInfo="handleLessor"
      ></LessorInfo>
      <a-form-model
        class="ant-advanced-search-form"
        ref="ruleForm"
        :model="decisionParmas"
        :rules="rules"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <div>
          <div class="lessorInfo">
            <div class="lessorInfo-tit">
              <div class="tit">合同信息</div>
              <div class="titItem" style="padding-left: 50px">
                <p style="margin-right: 16px">流程选择</p>
                <a-radio-group v-model="decisionParmas.processFlow" disabled>
                  <a-radio :value="0"> 正常申报流程 </a-radio>
                  <a-radio :value="1"> 特殊申报流程 </a-radio>
                </a-radio-group>
              </div>
              <div v-if="decisionParmas.processFlow == 1">
                <div
                  style="
                    width: 100%;
                    color: #f5222d;
                    padding-left: 26px;
                    margin-bottom: 10px;
                    font-size: 16px;
                  "
                >
                  影响年终内控考核
                </div>
                <div class="upload_file_item">
                  <div class="Item">
                    <FileAttachmentList
                      title="特殊通道情况说明(pdf盖章版)"
                      marked="true"
                      :ifNeedPreviewOnline="true"
                      @deleteFile="
                        (file, fileList) => deleteFile8(file, fileList)
                      "
                      :fileList="fileList8"
                    >
                    </FileAttachmentList>
                  </div>
                  <div v-if="!showInfo">
                    <my-upload
                      businessId="special_channel_description"
                      @handleFileCallback="handleFileCallback8"
                    />
                  </div>
                </div>
              </div>
              <div
                style="display: flex; justify-content: left; padding-left: 50px"
              >
                <div class="titItem">
                  <p style="margin-right: 16px">合同类型</p>
                  <a-radio-group disabled v-model="decisionParmas.contractType">
                    <a-radio :value="0"> 新租 </a-radio>
                    <a-radio :value="1"> 续租 </a-radio>
                    <a-radio :value="3"> 提前终止 </a-radio>
                    <a-radio :value="2"> 变更 </a-radio>
                  </a-radio-group>
                </div>
                <div class="titItem" v-if="decisionParmas.contractType == 2">
                  <p style="margin-right: 16px">是否主体变更</p>
                  <a-radio-group
                    disabled
                    v-model="decisionParmas.contentChangeType"
                  >
                    <a-radio :value="0"> 是 </a-radio>
                    <a-radio :value="1"> 否 </a-radio>
                  </a-radio-group>
                </div>
              </div>
              <div
                class="upload_file_item"
                v-if="decisionParmas.contractType == 0"
              >
                <div class="Item">
                  <FileAttachmentList
                    title="租赁合同原件（word版）"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteConFile(file, fileList)
                    "
                    :fileList="contractFileList"
                  >
                  </FileAttachmentList>
                </div>
                <div>
                  <my-upload
                    :accept="accept"
                    businessId="contract_info"
                    @handleFileCallback="handleConFileCallback"
                  />
                </div>
              </div>
            </div>
            <!-- @oldRental="handleOldRental" -->
            <OldRentalInfo
              ref="childRef"
              :oldleaseTermData="{
                leaseTermEnd: decisionParmas.leaseTermEnd,
                leaseTermStart: decisionParmas.leaseTermStart,
                leaseTerm: decisionParmas.leaseTerm,
              }"
              :processFlow="decisionParmas.processFlow"
              :isChange="decisionParmas.contentChangeType"
              :contractType="decisionParmas.contractType"
              :relationContractId="decisionParmas.relationContractId"
              @oldRentalInfoRules="getoldRentalInfoRules"
              v-if="decisionParmas.contractType != 0"
            ></OldRentalInfo>
            <div class="lessorInfo-tit" v-if="decisionParmas.contractType == 3">
              <div class="tit">终止信息</div>
              <div style="padding-left: 50px">
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-model-item
                      prop="cessationTime"
                      label="提前终止日期"
                    >
                      <a-date-picker
                        valueFormat="YYYY-MM-DD"
                        v-model="decisionParmas.cessationTime"
                        style="width: 100%; height: 40px"
                      />
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="12">
                    <a-form-model-item
                      prop="cessationReason"
                      label="提前终止原因"
                      style="margin-left: -82px"
                    >
                      <a-textarea
                        compact="true"
                        style="width: 100%"
                        placeholder="请输入提前终止原因"
                        v-model="decisionParmas.cessationReason"
                        :auto-size="{ minRows: 2, maxRows: 5 }"
                      />
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="12">
                    <a-form-model-item
                      prop="cessationTerm"
                      label="特殊约定"
                      style="margin-left: -82px"
                    >
                      <a-textarea
                        compact="true"
                        style="width: 100%"
                        placeholder="如：交付状态等"
                        v-model="decisionParmas.cessationTerm"
                        :auto-size="{ minRows: 3, maxRows: 6 }"
                      />
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-model-item prop="isDedit" label="是否有违约金">
                      <a-radio-group
                        v-model="decisionParmas.isDedit"
                        @change="isDeditChange"
                        style="width: 100%"
                      >
                        <a-radio :value="1"> 是 </a-radio>
                        <a-radio :value="0"> 否 </a-radio>
                      </a-radio-group>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item prop="dedit" style="margin-left: -82px">
                      <a-input
                        v-model="decisionParmas.dedit"
                        :placeholder="
                          decisionParmas.isDedit == 0
                            ? '请输入原因'
                            : '请输入金额'
                        "
                        style="width: 92%; height: 40px"
                        maxLength="50"
                      />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
          <div class="lessorInfo" v-if="decisionParmas.contractType == 1">
            <div class="lessorInfo-tit">
              <div class="tit">续租合同信息</div>
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="租赁合同原件（word版）"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteConFile(file, fileList)
                    "
                    :fileList="contractFileList"
                  >
                  </FileAttachmentList>
                </div>
                <div>
                  <my-upload
                    businessId="contract_info"
                    @handleFileCallback="handleConFileCallback"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="lessorInfo" v-if="decisionParmas.contractType == 3">
            <div class="lessorInfo-tit">
              <div class="tit">续租合同信息</div>
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="提前终止合同原件（word版）"
                    :ifNeedPreviewOnline="true"
                    marked="true"
                    @deleteFile="
                      (file, fileList) => deleteConFile1(file, fileList)
                    "
                    :fileList="terminationFileList"
                  >
                  </FileAttachmentList>
                </div>
                <div>
                  <my-upload
                    :accept="accept"
                    businessId="termination_agreement"
                    @handleFileCallback="handleConFileCallback1"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 出租信息 -->
        <!-- 变更合同信息 -->
        <div class="lessorInfo" v-if="decisionParmas.contractType == 2">
          <div class="lessorInfo-tit">
            <div class="tit">变更合同信息</div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="变更补充协议"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="
                    (file, fileList) => deleteagreementFile(file, fileList)
                  "
                  :fileList="agreementFileList"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="change_supplementary_agreement"
                  @handleFileCallback="handleagreementFileCallback"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 租户信息 -->
        <div class="lessorInfo" v-if="rentalTShow">
          <div class="lessorInfo-tit">
            <div class="tit" v-if="lessorInfoTit">变更租户信息</div>
            <div class="tit" v-else>租户信息</div>

            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="tenantry" label="承租方">
                  <a-input
                    v-if="decisionParmas.contractType == 1"
                    disabled
                    :title="decisionParmas.tenantry"
                    v-model="decisionParmas.tenantry"
                  />
                  <a-auto-complete
                    v-else
                    placeholder="请输入承租方"
                    :title="decisionParmas.tenantry"
                    v-model="decisionParmas.tenantry"
                    :data-source="dataSource"
                    @search="onSearch"
                    @change="tenantryChange"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="territorialSituation" label="属地情况">
                  <a-select
                    v-model="decisionParmas.territorialSituation"
                    placeholder="请选择属地情况"
                    @change="onChangeMain"
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in territorialArr"
                      :key="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col
                :span="8"
                v-if="decisionParmas.territorialSituation == '关联属地'"
              >
                <a-form-model-item prop="relationCompany">
                  <a-auto-complete
                    v-model="decisionParmas.relationCompany"
                    :disabled="showInfo"
                    :data-source="relationSource"
                    placeholder="请输入关联企业的名称"
                    style="width: 100%; height: 40px"
                    @search="onSearchRelation"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :span="8"
                v-if="decisionParmas.territorialSituation == '未属地'"
              >
                <a-form-model-item prop="territorialRemark">
                  <a-input
                    v-model="decisionParmas.territorialRemark"
                    :disabled="showInfo"
                    placeholder="请填写未属地原因及属地计划"
                    style="width: 100%; height: 40px"
                    maxLength="50"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>

            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="承租方营业执照复印件（盖章版）"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  :fileList="licenseFileList"
                  @deleteFile="
                    (file, fileList) => deleteLicenseFile(file, fileList)
                  "
                ></FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="tenant_business_license"
                  @handleFileCallback="handleLicenseFileCallback"
                />
              </div>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="承租方法人身份证（盖章版）"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  :fileList="fileList1"
                  @deleteFile="(file, fileList) => deleteFile1(file, fileList)"
                ></FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="tenantry_id"
                  @handleFileCallback="handleFileCallback1"
                />
              </div>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="信用报告"
                  :ifNeedPreviewOnline="true"
                  marked="true"
                  @deleteFile="(file, fileList) => deleteFile2(file, fileList)"
                  :fileList="fileList2"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="credit_report"
                  @handleFileCallback="handleFileCallback2"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 出租信息 -->
        <div class="lessorInfo" v-if="clauseTShow">
          <div class="lessorInfo-tit">
            <div class="tit">出租信息</div>
            <div v-if="adressShow">
              <div
                class="titItem"
                style="padding-left: 13px"
                v-for="(item, index) in decisionParmas.contractBuildingInfo"
                :key="item.contractInfoId"
              >
                <p class="mR20" style="margin-right: 16px">
                  <span style="color: red">*</span> 出租地址
                </p>
                <a-select
                  v-model="item.parkName"
                  :disabled="decisionParmas.contractType == 1 ? true : false"
                  style="width: 14%; height: 40px; margin-right: 16px"
                  @focus="getParkName"
                  @change="ParkNameChange(item)"
                  placeholder="园区"
                >
                  <a-select-option
                    :value="item2.parkName"
                    v-for="item2 in parkNameArr"
                    :key="item2.item"
                  >
                    <a-tooltip placement="top">
                      <template slot="title">
                        <span> {{ item2.parkName }}</span>
                      </template>
                      {{ item2.parkName }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="item.buildingName"
                  :disabled="decisionParmas.contractType == 1 ? true : false"
                  style="width: 10%; height: 40px; margin-right: 16px"
                  @focus="buildingNameFocus(item)"
                  @change="buildingNameChange(item)"
                  placeholder="楼号"
                >
                  <a-select-option
                    :value="item2"
                    v-for="item2 in buildingNameArr"
                    :key="item2.item"
                  >
                    <a-tooltip placement="top">
                      <template slot="title">
                        <span> {{ item2 }}</span>
                      </template>
                      {{ item2 }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="item.floorName"
                  :disabled="decisionParmas.contractType == 1 ? true : false"
                  style="width: 10%; height: 40px; margin-right: 16px"
                  @change="floorNameChange(item)"
                  @focus="floorNameFocus(item)"
                  placeholder="楼层"
                >
                  <a-select-option
                    :value="item2"
                    v-for="item2 in floorNameArr"
                    :key="item2.item"
                  >
                    <a-tooltip placement="top">
                      <template slot="title">
                        <span> {{ item2 }}</span>
                      </template>
                      {{ item2 }}
                    </a-tooltip></a-select-option
                  >
                </a-select>
                <a-select
                  v-model="item.roomName"
                  style="width: 10%; height: 40px; margin-right: 16px"
                  @focus="roomNameChange(item)"
                  @change="onBlur(item)"
                  placeholder="房间号"
                  :disabled="decisionParmas.contractType == 1 ? true : false"
                >
                  <a-select-option
                    :value="item1.roomNumber"
                    v-for="item1 in roomNameArr"
                    :key="item1.item"
                    >{{ item1.roomNumber }}</a-select-option
                  >
                </a-select>
                <a-button
                  v-if="item.delId == 0 && decisionParmas.contractType != 1"
                  @click="addItem"
                  style="font-size: 30px; color: #1777ff; border: 0"
                  icon="plus-circle"
                />
                <a-button
                  v-if="item.delId > 0 && decisionParmas.contractType != 1"
                  @click="delItem(index)"
                  style="font-size: 30px; color: #fe6a6a; border: 0"
                  icon="minus-circle"
                />
              </div>
              <div class="upload_file_item" v-if="clauseTShow">
                <div class="Item">
                  <FileAttachmentList
                    title="出租物业的产权证明复印件（盖章版，如果部分没有或者全无，需上传情况说明）"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteFile3(file, fileList)
                    "
                    :fileList="fileList3"
                  >
                  </FileAttachmentList>
                </div>
                <div v-if="!showInfo">
                  <my-upload
                    businessId="property_right"
                    @handleFileCallback="handleFileCallback3"
                  />
                </div>
              </div>
            </div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="leaseArea" label="租赁面积">
                  <a-input
                    v-model="decisionParmas.leaseArea"
                    disabled
                    style="width: 100%; height: 40px"
                    addon-after="㎡"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item
                  prop="accumulateLeaseArea"
                  label="累计租赁面积"
                >
                  <a-input
                    v-model="decisionParmas.accumulateLeaseArea"
                    style="width: 100%; height: 40px"
                    addon-after="㎡"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :span="8"
                v-if="
                  decisionParmas.leasePurpose == '办公' ||
                  !decisionParmas.leasePurpose
                "
              >
                <a-form-model-item prop="leasePurpose" label="租赁用途">
                  <!-- <a-input
                    placeholder="请输入租赁用途"
                    :disabled="cluShowInfo"
                    v-model="decisionParmas.leasePurpose"
                    style="width: 100%; height: 40px"
                    maxLength="20"
                  /> -->
                  <a-select
                    v-model="decisionParmas.leasePurpose"
                    :disabled="cluShowInfo"
                    placeholder="请选择租赁用途"
                    @change="onChangeLeasePurpose"
                    style="width: 100%; height: 40px"
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in leasePurposeArr"
                      :key="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row
              :gutter="24"
              v-if="
                decisionParmas.leasePurpose == '商业' ||
                decisionParmas.leasePurpose == '其他'
              "
            >
              <a-col :span="8">
                <a-form-model-item prop="leasePurpose" label="租赁用途">
                  <a-select
                    v-model="decisionParmas.leasePurpose"
                    :disabled="cluShowInfo"
                    placeholder="请选择租赁用途"
                    style="width: 100%; height: 40px"
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in leasePurposeArr"
                      :key="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leasePurposeInfo" label="">
                  <a-input
                    placeholder="请输入内容"
                    :disabled="cluShowInfo"
                    v-model="decisionParmas.leasePurposeInfo"
                    style="width: 100%; height: 40px"
                    allowClear
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="rentalUnitPrice" label="租赁单价">
                  <a-input
                    type="number"
                    step="0.01"
                    @input="handleInput1(decisionParmas.rentalUnitPrice)"
                    placeholder="请输入租赁单价"
                    v-model="decisionParmas.rentalUnitPrice"
                    :disabled="showInfo"
                    style="width: 100%; height: 40px; line-height: 40px"
                    addon-after="元/㎡/天"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="16">
                <div
                  style="
                    width: 100%;
                    display: flex;
                    align-items: center;
                    padding-left: 12px;
                    margin-bottom: 0;
                  "
                >
                  <div class="titItem" style="align-items: center">
                    <p class="mR20">
                      <span style="color: red">*</span> 定价依据
                    </p>
                    <div class="basisItem">
                      <FileAttachmentList
                        @deleteFile="
                          (file, fileList) => deleteFile4(file, fileList)
                        "
                        :fileList="fileList4"
                        :ifNeedPreviewOnline="true"
                      >
                      </FileAttachmentList>
                    </div>
                    <div v-if="!showInfo">
                      <my-upload
                        businessId="pricing_basis"
                        @handleFileCallback="handleFileCallback4"
                      />
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="annualRent" label="年租金">
                  <a-input
                    type="number"
                    step="0.01"
                    v-model="decisionParmas.annualRent"
                    @input="handleInput2(decisionParmas.annualRent)"
                    placeholder="请输入年租金"
                    style="width: 100%; height: 40px; margin-right: 0"
                    addon-after="元"
                    maxLength="20"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseDeposit" label="租赁保证金">
                  <a-input
                    v-model="decisionParmas.leaseDeposit"
                    type="number"
                    addon-after="元"
                    @input="handleInput5(decisionParmas.leaseDeposit)"
                    placeholder="请输入租赁保证金"
                    style="width: 100%; height: 40px; margin-right: 0"
                    maxLength="20"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="priceIncrease" label="价格递增机制">
                  <a-select
                    v-model="decisionParmas.priceIncrease"
                    placeholder="请选择价格递增机制"
                    style="width: 100%; height: 40px"
                    @change="priceIncreaseChange"
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in priceIncreaseArr"
                      :key="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <div v-if="leaseDepositStatu">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="租赁保证金低于3个月租金情况说明(盖章版)"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteFile6(file, fileList)
                    "
                    :fileList="fileList6"
                  >
                  </FileAttachmentList>
                </div>
                <div v-if="!showInfo">
                  <my-upload
                    businessId="lease_deposit_below_3_months_explanation"
                    @handleFileCallback="handleFileCallback6"
                  />
                </div>
              </div>
            </div>
            <div v-if="priceIncreaseStatu1">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="无价格递增情况说明（盖章版）"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteFile5(file, fileList)
                    "
                    :fileList="fileList5"
                  >
                  </FileAttachmentList>
                </div>
                <div v-if="!showInfo">
                  <my-upload
                    businessId="non_incremental_pricing"
                    @handleFileCallback="handleFileCallback5"
                  />
                </div>
              </div>
            </div>
            <a-row
              :gutter="24"
              v-for="(item, index) in decisionParmas.multTenancyInfoList"
              :key="item.id"
              style="position: relative"
              v-show="priceIncreaseStatu"
            >
              <a-col :span="8">
                <a-form-model-item :label="`租期` + (index + 1)" required>
                  <a-range-picker
                    format="YYYY-MM-DD"
                    :value="[item.startTime, item.endTime]"
                    :disabled="showInfo"
                    :placeholder="['请选择', '请选择']"
                    style="width: 100%"
                    @change="
                      (date, dateString) =>
                        onChangezuqi(date, dateString, index)
                    "
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item label="租赁单价" required>
                  <a-input
                    type="number"
                    step="0.01"
                    @input="handleInput3(index, item.rentalUnitPrice)"
                    v-model="item.rentalUnitPrice"
                    :disabled="showInfo"
                    placeholder="请输入租赁单价"
                    style="width: 100%; height: 40px; line-height: 40px"
                    addon-after="元/㎡/天"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item label="年租金" required>
                  <a-input
                    type="number"
                    step="0.01"
                    v-model="item.annualRent"
                    @input="handleInput4(index, item.annualRent)"
                    placeholder="请输入年租金"
                    style="width: 100%; height: 40px; margin-right: 0"
                    addon-after="元"
                    maxLength="20"
                  />
                </a-form-model-item>
              </a-col>
              <a-button
                v-if="item.delId == 0"
                @click="addItem2"
                style="
                  font-size: 30px;
                  color: #1777ff;
                  border: 0;
                  position: absolute;
                  top: -4px;
                  right: -15px;
                "
                icon="plus-circle"
              />
              <a-button
                v-if="item.delId > 0"
                @click="delItem2(index)"
                style="
                  font-size: 30px;
                  color: #fe6a6a;
                  border: 0;
                  position: absolute;
                  top: -4px;
                  right: -15px;
                "
                icon="minus-circle"
              />
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="leaseTermStart" label="租期起">
                  <!-- :disabled="cluShowInfo" -->
                  <a-date-picker
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    v-model="decisionParmas.leaseTermStart"
                    :disabled-date="disabledStartDate"
                    style="width: 100%"
                    @change="leaseTimeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseTermEnd" label="租期止">
                  <a-date-picker
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    v-model="decisionParmas.leaseTermEnd"
                    :disabled="!decisionParmas.leaseTermStart"
                    :disabled-date="disabledEndDate"
                    style="width: 100%"
                    @change="leaseTimeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseTerm" label="总租期">
                  <a-input
                    v-model="decisionParmas.leaseTerm"
                    disabled
                    addon-after="年"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>

            <div style="position: relative">
              <div
                style="display: flex"
                v-for="(item, index) in decisionParmas.contractFreePeriod"
                :key="item.id"
              >
                <div style="display: flex; width: 68%">
                  <div class="titItem" style="padding-left: 14px">
                    <p class="mR20">免租期起</p>
                    <a-date-picker
                      format="YYYY-MM-DD"
                      :disabled="showInfo"
                      v-model="item.startTime"
                      style="width: 68.5%"
                      @change="rentFreeChange"
                    />
                  </div>
                  <div class="titItem" style="padding-left: 12px">
                    <p class="mR20">免租期止</p>
                    <a-date-picker
                      format="YYYY-MM-DD"
                      :disabled="showInfo"
                      v-model="item.endTime"
                      style="width: 68.5%"
                      @change="rentFreeChange"
                    />
                  </div>
                </div>
                <a-button
                  v-if="item.delId == 0"
                  @click="addItem1"
                  style="
                    font-size: 30px;
                    color: #1777ff;
                    border: 0;
                    margin-top: -3px;
                    margin-left: -30px;
                  "
                  icon="plus-circle"
                />
                <a-button
                  v-if="item.delId > 0"
                  @click="delItem1(index)"
                  style="
                    font-size: 30px;
                    color: #fe6a6a;
                    border: 0;
                    margin-top: -3px;
                    margin-left: -30px;
                  "
                  icon="minus-circle"
                />
              </div>
              <div
                class="titItem"
                style="
                  width: 30.5%;
                  padding-left: 0;
                  position: absolute;
                  right: 12px;
                  top: 0;
                "
              >
                <p class="mR20">总免租期</p>
                <a-input
                  v-model="decisionParmas.rentFreeDays"
                  addon-after="天"
                  disabled
                />
              </div>
              <div class="rentFree">
                <a-icon type="info-circle" />
                <span style="margin-left: 25px"
                  >如分段给予免租期，请分多段录入。</span
                >
              </div>
            </div>
            <div v-if="contractFreePeriodStatu">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="免租期超过租期的10%或超过90天情况说明(盖章版)"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteFile7(file, fileList)
                    "
                    :fileList="fileList7"
                  >
                  </FileAttachmentList>
                </div>
                <div v-if="!showInfo">
                  <my-upload
                    businessId="rent_free_period_exceed_explanation"
                    @handleFileCallback="handleFileCallback7"
                  />
                </div>
              </div>
            </div>
            <div v-if="clauseTShow && decisionParmas.contentChangeType == 1">
              <div class="titItem1">
                <p style="width: 108px; margin-right: 20px">
                  <span style="color: red">*</span>
                  变更具体条款及原因
                </p>
                <a-textarea
                  compact="true"
                  style="width: 50%; line-height: 20px"
                  v-model="decisionParmas.alterReason"
                  placeholder="请输入变更具体条款及原因"
                  :disabled="showInfo"
                  :auto-size="{ minRows: 3, maxRows: 6 }"
                />
              </div>
            </div>
            <div
              style="
                width: 94%;
                margin: 0 auto;
                padding: 12px;
                margin-bottom: 30px;
                background: #f9f9f9;
              "
            >
              <div class="titItem" style="margin-left: 10px; padding-left: 0">
                <p style="margin-right: 25px">
                  合同中是否对以下必备条款进行约束
                </p>
              </div>
              <div style="margin-left: 15px; font-size: 16px; color: #1d2129">
                <p>
                  1.合同终止情形及免责条款：如因政府市政搬迁、土地收储、无证建筑拆除等情形，公司有权终止合同并予以免责；
                </p>
                <p>
                  2.违约责任条款：对于未按期缴纳租金的情况，应约定滞纳金；对于逾期三个月及以上未按时付清租金的情况，应明确公司有权解除租赁合同，收回物业，且不承担赔偿责任：
                </p>
                <p>
                  3.租约束条款：公司应严格控制转租行为，承租人与次承租人的租赁合同必须报公司备案。严禁层层转租行为。
                </p>
              </div>
              <div class="titItem" style="padding-left: 15px">
                <a-radio-group
                  :disabled="showInfo"
                  v-model="decisionParmas.essentialSituation"
                >
                  <a-radio :value="'1'"> 是 </a-radio>
                  <a-radio :value="'0'"> 否 </a-radio>
                </a-radio-group>
              </div>
            </div>
            <div>
              <div class="titItem1">
                <p style="width: 90px; margin-right: -8px; margin-left: 50px">
                  特殊条款
                </p>
                <a-textarea
                  style="width: 50%"
                  v-model="decisionParmas.specialTerms"
                  placeholder="请输入特殊条款"
                  :disabled="showInfo"
                  :auto-size="{ minRows: 3, maxRows: 6 }"
                  maxLength="40"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">决策程序</div>
            <div class="titItem" style="padding-left: 50px">
              <p style="margin-right: 16px">是否法律顾问审核</p>
              <a-radio-group
                v-model="decisionParmas.legalAdvisorReview"
                @change="legalAdvisorReviewChange"
              >
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="0"> 否 </a-radio>
              </a-radio-group>
            </div>
            <div class="titItem" style="padding-left: 50px">
              <p style="margin-right: 16px">是否经过董事会决议</p>
              <a-radio-group
                v-model="decisionParmas.boardResolution"
                @change="boardResolutionChange"
              >
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="0"> 否 </a-radio>
              </a-radio-group>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  key="11"
                  title="董事会决议（复印件）"
                  :marked="decMarked"
                  :ifNeedPreviewOnline="true"
                  :fileList="boardFileList"
                  @deleteFile="(file, fileList) => deleteFile(file, fileList)"
                >
                </FileAttachmentList>
              </div>
              <div>
                <my-upload
                  businessId="board_resolution"
                  @handleFileCallback="handleFileCallback"
                />
              </div>
            </div>
            <div class="titItem" style="padding-left: 50px">
              <p style="margin-right: 16px">是否经过三重一大</p>
              <a-radio-group
                v-model="decisionParmas.isSignificant"
                @change="isSignificantChange"
              >
                <a-radio :value="1"> 是 </a-radio>
                <a-radio :value="0"> 否 </a-radio>
              </a-radio-group>
            </div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  key="22"
                  title="三重一大（复印件）"
                  :marked="decMarked1"
                  :ifNeedPreviewOnline="true"
                  :fileList="tbFileList"
                  @deleteFile="(file, fileList) => deleteTbFile(file, fileList)"
                >
                </FileAttachmentList>
              </div>
              <div>
                <my-upload
                  businessId="triple_and_big"
                  @handleFileCallback="handleTBFileCallback"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit"><span style="color:#f5222d">*</span>备注</div>
            <div class="titItem1" style="padding-left: 50px">
              <a-textarea
                compact="true"
                style="width: 58%"
                v-model="decisionParmas.remark"
                placeholder="请输入备注"
                maxLength="50"
                :auto-size="{ minRows: 3, maxRows: 6 }"
              />
            </div>
          </div>
        </div>
      </a-form-model>
    </div>
    <div style="text-align: center">
      <a-button class="back" @click="toBack">返回</a-button>
      <a-button type="primary" @click="editSubmit(0)" class="back"
        >保存</a-button
      >
      <a-button type="primary" @click="editSubmit(1)" class="back"
        >提交申请</a-button
      >
    </div>
  </div>
</template>

<script>
import FileAttachmentList from "@/components/FileAttachmentList";
import myUpload from "@/components/Importer";
import LessorInfo from "./../common/lessorInfo";
import OldRentalInfo from "./../common/oldRentalInfo";
import moment from "moment";
import {
  queryContractInfoByIdApi,
  updateContractInfoApi,
  getAreaTotalApi,
  findFloorsByBuildingInfoApi,
  queryAllParkApi1,
  queryAllBuildingApi,
  findRoomsByBuildingInfoApi,
  queryTenantryApi,
} from "@/pages/index/data/api/RegistrationRecordInquery";
export default {
  components: {
    OldRentalInfo,
    myUpload,
    LessorInfo,
    FileAttachmentList,
  },
  data() {
    return {
      accept: ".doc,.docx",
      formItemLayout: {
        labelCol: {
          xs: { span: 16 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 17 },
        },
      },
      rules: {
        tenantry: [
          { required: true, message: "请输入承租方", trigger: "blur" },
        ],
        territorialSituation: [
          { required: true, message: "请选择属地情况", trigger: "change" },
        ],
        relationCompany: [
          {
            required: true,
            message: "必填项请输入",
            trigger: "change",
          },
        ],
        territorialRemark: [
          {
            required: true,
            message: "必填项请输入",
            trigger: "change",
          },
        ],
        leasePurpose: [
          { required: true, message: "请选择租赁用途", trigger: "blur" },
        ],
        leasePurposeInfo: [
          { required: true, message: "请输入内容", trigger: "blur" },
        ],
        rentalUnitPrice: [
          { required: true, message: "请输入租赁单价", trigger: "blur" },
        ],
        annualRent: [
          { required: true, message: "请输入年租金", trigger: "blur" },
        ],
        leaseDeposit: [
          { required: true, message: "请输入租赁保证金", trigger: "blur" },
        ],
        priceIncrease: [
          { required: true, message: "请选择价格递增机制", trigger: "change" },
        ],
        leaseTermStart: [
          { required: true, message: "请选择租期开始时间", trigger: "change" },
        ],
        leaseTermEnd: [
          { required: true, message: "请选择租期结束时间", trigger: "change" },
        ],
        cessationTime: [
          { required: true, message: "请选择提前终止日期", trigger: "change" },
        ],
        cessationReason: [
          { required: true, message: "请输入提前终止原因", trigger: "blur" },
        ],
        cessationTerm: [
          { required: true, message: "请输入特殊约定", trigger: "blur" },
        ],
        dedit: [{ required: true, message: "请输入", trigger: "blur" }],
        isDedit: [{ required: true, message: "", trigger: "blur" }],
      },
      rentalTShow: false,
      clauseTShow: false,
      oldRentalInfoRules: true,
      adressShow: false,
      lessorInfoTit: false,
      isContractInfo: true,
      isTermination: true,
      isBoardResolution: true,
      priceIncreaseStatu: false,
      priceIncreaseStatu1: false,
      //文件必传
      contractInfoFile: false,
      boardResolutionFile: false,
      tripleAndBigFile: false,
      tenantBusinessLicenseFile: false,
      tenantryIdFile: false,
      nonIncrementalPricing: false,
      leaseDepositBelowFile: false,
      contractFreePeriodFile: false,
      specialChannelPeriodFile: false,
      creditReportFile: false,
      propertyRightFile: false,
      pricingBasisFile: false,
      changeSupplementaryAgreementFile: false,
      isTripleAndBig: true,
      isUploadFlie: false,
      decMarked: true,
      decMarked1: true,
      isFlag: true,
      visible: false,
      parkNameArr: [],
      buildingNameArr: [],
      floorNameArr: [],
      roomNameArr: [],
      lessorParmas: {
        lessor: undefined,
        filingNumber: "",
      },
      clauseParmas: {},
      oldRentalParmas: {},
      parentRentalParmas: {},
      contractFileList: [],
      terminationFileList: [],
      terminationFile: false,
      agreementFileList: [],
      boardFileList: [],
      licenseFileList: [],
      rentalFlie: [],
      dataSource: [],
      relationSource: [],
      territorialArr: [
        { label: "镇属", value: "镇属" },
        { label: "区属", value: "区属" },
        { label: "关联属地", value: "关联属地" },
        { label: "未属地", value: "未属地" },
      ],
      leasePurposeArr: [
        { label: "办公", value: "办公" },
        { label: "商业", value: "商业" },
        { label: "其他", value: "其他" },
      ],
      priceIncreaseArr: [
        { label: "是", value: "1" },
        { label: "未满3年无价格递增", value: "0" },
        { label: "租期超3年无价格递增", value: "2" },
      ],
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
      fileList6: [],
      fileList7: [],
      fileList8: [],
      tbFileList: [],
      decisionFile: [],
      decisionParmas: {
        contractType: 0,
        contentChangeType: null,
        relationContractId: "",
        attachments: [],
        boardResolution: 1, //董事会决议 0：否 1：是
        isSignificant: 1, //三重一大 0：否 1：是
        legalAdvisorReview: 1, //法律顾问审核 0：否 1：是
        attachmentList: [],
        openType: 1,
        filingNumber: "",
        id: "",
        tenantry: "",
        territorialSituation: undefined,
        territorialRemark: "",
        relationCompany: "",
        leaseArea: 0,
        annualRent: "", //年租金
        leaseDeposit: "", //租赁保证金
        leasePurpose: undefined,
        leasePurposeInfo: "",
        rentalUnitPrice: "",
        rentFreeDays: "",
        leaseTermStart: "",
        leaseTermEnd: "",
        leaseTerm: "",
        priceIncrease: undefined,
        accumulateLeaseArea: 0,
        specialTerms: "",
        essentialSituation: "1",
        alterReason: "",
        cessationTime: "",
        cessationReason: "",
        cessationTerm: "",
        remark: "",
        isDedit: 1,
        dedit: "",
        contractFreePeriod: [
          {
            delId: 0,
            startTime: "",
            endTime: "",
          },
        ], //租赁合同免租期数据
        multTenancyInfoList: [], //分段租金递增
        contractBuildingInfo: [
          {
            delId: 0,
            parkName: undefined,
            contractInfoId: undefined,
            buildingName: undefined,
            floorName: undefined,
            roomName: undefined,
          },
        ],
      },
      statu: "",
      showInfo: false,
      oldleaseTermEnd: "",
    };
  },
  computed: {
    leaseDepositStatu() {
      // console.log('租赁保证金:',this.decisionParmas.leaseDeposit ,'年租金:',this.decisionParmas.annualRent , this.decisionParmas.leaseDeposit &&this.decisionParmas.annualRent && (this.decisionParmas.leaseDeposit < this.decisionParmas.annualRent/4))
      if (
        this.decisionParmas.leaseDeposit &&
        this.decisionParmas.annualRent &&
        parseInt(this.decisionParmas.leaseDeposit) <
          parseInt(this.decisionParmas.annualRent / 4)
      ) {
        return true;
      } else {
        return false;
      }
    },
    contractFreePeriodStatu() {
      let leaseTermDays; //总租期天数
      if (
        this.decisionParmas.leaseTermStart &&
        this.decisionParmas.leaseTermEnd
      ) {
        leaseTermDays =
          moment(this.decisionParmas.leaseTermEnd).diff(
            moment(this.decisionParmas.leaseTermStart),
            "days",
            false
          ) + 1;
      } else {
        leaseTermDays = 0;
      }
      console.log(
        "总租期天数:",
        leaseTermDays,
        "总免租期:",
        this.decisionParmas.rentFreeDays
      );
      // 免租期超过租期的10%或超过90天情况说明
      if (
        (leaseTermDays &&
          this.decisionParmas.rentFreeDays &&
          this.decisionParmas.rentFreeDays / leaseTermDays > 0.1) ||
        this.decisionParmas.rentFreeDays > 90
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  created() {
    this.getContractId();
  },
  methods: {
    onChangeLeasePurpose() {
      this.$set(this.decisionParmas, "leasePurposeInfo", "");
    },
    handleOldRental: function (parmas) {
      console.log(parmas, "子组件传参");
      if (this.decisionParmas.contentChangeType != 0) {
        this.$set(this.decisionParmas, "tenantry", parmas.tenantry);
      }
      this.oldRentalParmas = parmas;
      this.oldleaseTermEnd = this.oldRentalParmas.leaseTermEnd;
      this.$set(this.decisionParmas, "relationContractId", parmas.id);
      this.$set(this.decisionParmas, "leaseArea", parmas.leaseArea);
      this.$set(
        this.decisionParmas,
        "leasePurpose",
        parmas.leasePurpose ? parmas.leasePurpose : undefined
      );
      this.$set(
        this.decisionParmas,
        "leasePurposeInfo",
        parmas.leasePurposeInfo ? parmas.leasePurposeInfo : ""
      );
      // this.$set(this.decisionParmas, "leaseDeposit", parmas.leaseDeposit);
      // this.$set(this.decisionParmas, "annualRent", parmas.annualRent);
      if (this.decisionParmas.contentChangeType == 1) {
        this.$set(this.decisionParmas, "leaseTermStart", parmas.leaseTermStart);
        this.$set(this.decisionParmas, "leaseTermEnd", parmas.leaseTermEnd);
        this.$set(this.decisionParmas, "leaseTerm", parmas.leaseTerm);
      }
      this.$set(this.decisionParmas, "rentalUnitPrice", parmas.rentalUnitPrice);
      this.$set(
        this.decisionParmas,
        "accumulateLeaseArea",
        parmas.accumulateLeaseArea
      );
      this.$set(
        this.decisionParmas,
        "contractBuildingInfo",
        parmas.contractBuildingInfo
      );
    },
    handleLessor(v) {
      this.lessorParmas = v;
    },
    handleInput1(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.rentalUnitPrice = val;
    },
    handleInput2(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.annualRent = val;
    },
    handleInput3(i, value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.multTenancyInfoList[i].rentalUnitPrice = val;
    },
    handleInput4(i, value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.multTenancyInfoList[i].annualRent = val;
    },
    handleInput5(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.leaseDeposit = val;
    },

    getContractId() {
      //根据id查询所有数据
      if (this.$route.query.id) {
        let parmas = {
          id: this.$route.query.id,
        };
        queryContractInfoByIdApi(parmas).then((res) => {
          console.log(res.data, "根据id查询所有数据");
          this.decisionParmas = res.data;

          this.lessorParmas.lessor = res.data.lessor;
          this.lessorParmas.filingNumber = res.data.filingNumber;
          this.decisionParmas?.attachments?.forEach((e) => {
            if (e.businessType == "board_resolution") {
              this.boardFileList.push(e);
              this.boardResolutionFile = true;
            }
            if (e.businessType == "triple_and_big") {
              this.tbFileList.push(e);
              this.tripleAndBigFile = true;
            }
            if (e.businessType == "contract_info") {
              this.contractFileList.push(e);
              this.contractInfoFile = true;
            }
            if (e.businessType == "termination_agreement") {
              this.terminationFileList.push(e);
              this.terminationFile = true;
            }
            if (e.businessType == "tenant_business_license") {
              this.licenseFileList.push(e);
              this.tenantBusinessLicenseFile = true;
            }
            if (e.businessType == "tenantry_id") {
              this.fileList1.push(e);
              this.tenantryIdFile = true;
            }
            if (e.businessType == "credit_report") {
              this.fileList2.push(e);
              this.creditReportFile = true;
            }
            if (e.businessType == "property_right") {
              this.fileList3.push(e);
              this.propertyRightFile = true;
            }
            if (e.businessType == "pricing_basis") {
              this.fileList4.push(e);
              this.pricingBasisFile = true;
            }
            if (e.businessType == "non_incremental_pricing") {
              this.fileList5.push(e);
              this.nonIncrementalPricing = true;
            }
            if (e.businessType == "lease_deposit_below_3_months_explanation") {
              this.fileList6.push(e);
              this.leaseDepositBelowFile = true;
            }
            if (e.businessType == "rent_free_period_exceed_explanation") {
              this.fileList7.push(e);
              this.contractFreePeriodFile = true;
            }
            if (e.businessType == "change_supplementary_agreement") {
              this.agreementFileList.push(e);
              this.changeSupplementaryAgreementFile = true;
            }
            if (e.businessType == "special_channel_description") {
              this.fileList8.push(e);
              this.specialChannelPeriodFile = true;
            }
          });
          if (this.decisionParmas.boardResolution == 0) {
            this.decMarked = false;
            this.boardResolutionFile = true;
          }
          if (this.decisionParmas.isSignificant == 0) {
            this.decMarked1 = false;
            this.tripleAndBigFile = true;
          }
          if (!this.decisionParmas.essentialSituation) {
            this.decisionParmas.essentialSituation = "1";
          }
          if (this.decisionParmas.contractType == 0) {
            this.rentalTShow = true;
            this.clauseTShow = true;
            this.adressShow = true;
            this.lessorInfoTit = false;
          } else if (this.decisionParmas.contractType == 1) {
            this.adressShow = true;
            this.rentalTShow = true;
            this.clauseTShow = true;
            this.lessorInfoTit = false;
          }
          if (this.decisionParmas.contractType == 3) {
            this.lessorInfoTit = false;
          }
          if (
            this.decisionParmas.contractType == 2 &&
            this.decisionParmas.contentChangeType == 0
          ) {
            this.rentalTShow = true;
            this.clauseTShow = false;
            this.adressShow = false;
            this.lessorInfoTit = true;
          } else if (
            this.decisionParmas.contractType == 2 &&
            this.decisionParmas.contentChangeType === 1
          ) {
            this.rentalTShow = false;
            this.clauseTShow = true;
            this.adressShow = false;
            this.cluShowInfo = true;
          }
          if (this.decisionParmas.contractFreePeriod?.length == 0) {
            this.decisionParmas.contractFreePeriod = [
              {
                delId: 0,
                startTime: "",
                endTime: "",
              },
            ];
          }
          if (this.decisionParmas.contractBuildingInfo?.length == 0) {
            this.decisionParmas.contractBuildingInfo = [
              {
                delId: 0,
                parkName: undefined,
                contractInfoId: undefined,
                buildingName: undefined,
                floorName: undefined,
                roomName: undefined,
              },
            ];
          } else if (!this.decisionParmas.contractBuildingInfo[0].parkName) {
            this.decisionParmas.contractBuildingInfo = [
              {
                delId: 0,
                parkName: undefined,
                contractInfoId: undefined,
                buildingName: undefined,
                floorName: undefined,
                roomName: undefined,
              },
            ];
          } else {
            this.decisionParmas.contractBuildingInfo.forEach((e) => {
              if (!e.parkName) {
                e.parkName = undefined;
              }
              if (!e.buildingName) {
                e.buildingName = undefined;
              }
              if (!e.floorName) {
                e.floorName = undefined;
              }
              if (!e.roomName) {
                e.roomName = undefined;
              }
            });
          }
          if (!this.decisionParmas.leasePurpose) {
            // this.decisionParmas.leasePurpose = undefined;
            this.$set(this.decisionParmas, "leasePurpose", undefined);
            this.$set(this.decisionParmas, "leasePurposeInfo", "");
          }
          if (!this.decisionParmas.territorialSituation) {
            this.decisionParmas.territorialSituation = undefined;
          }
          if (!this.decisionParmas.priceIncrease) {
            this.decisionParmas.priceIncrease = undefined;
          } else if (this.decisionParmas.priceIncrease == "1") {
            this.priceIncreaseStatu = true;
          } else if (this.decisionParmas.priceIncrease == "2") {
            this.priceIncreaseStatu1 = true;
          }
        });
      }
    },
    isDeditChange() {
      this.decisionParmas.dedit = "";
    },
    getoldRentalInfoRules(v) {
      this.oldRentalInfoRules = v;
    },
    editSubmit(v) {
      let filelist = this.decisionFile.concat(
        this.tbFileList,
        this.fileList1,
        this.fileList2,
        this.fileList3,
        this.fileList4,
        this.fileList5,
        this.fileList6,
        this.fileList7,
        this.fileList8,
        this.contractFileList,
        this.licenseFileList,
        this.boardFileList,
        this.agreementFileList,
        this.terminationFileList
      );
      this.decisionParmas.attachmentList = [
        ...new Set(this.decisionParmas.attachmentList),
      ];
      const parmas = Object.assign(this.decisionParmas, this.lessorParmas);
      parmas.openType = v;
      if (parmas.attachments || parmas.attachments?.length > 0) {
        parmas.attachmentList = filelist.filter((v) =>
          parmas.attachments.every((val) => val.fileId != v.fileId)
        );
      } else {
        parmas.attachmentList = filelist;
      }
      parmas.isFreePeriod = this.contractFreePeriodStatu ? 0 : 1;
      parmas.isLeaseDeposit = this.leaseDepositStatu ? 0 : 1;
      if (parmas.legalAdvisorReview == 0 && parmas.openType == 1) {
        this.warning(
          "您提交的信息未经过法律顾问审核，请您通过法律顾问审核后提交"
        );
      } else if (parmas.essentialSituation == 0 && parmas.openType == 1) {
        this.warning(
          "您提交的信息未进行必备条款约束，请您先进行必备条款约束后提交"
        );
      } else {
        if (parmas.lessor) {
          if (parmas.openType == 1) {
            this.isUploadLength(parmas);
            if (this.isUploadFlie) {
              if (parmas.contractType != 0) {
                this.$refs.childRef.onSubmit();
              }
              console.log(parmas, "提交参数1");
              this.$refs.ruleForm.validate((valid) => {
                if (valid && this.oldRentalInfoRules) {
                 if(this.decisionParmas.remark == undefined || this.decisionParmas.remark == ""){
                  this.$message.warning("请填写备注信息！");
                  return;
                }
                  if (this.isRoomName() && parmas.contractType == 0) {
                    this.warning("请选择出租地址");
                  } else if (
                    parmas.contentChangeType == 1 &&
                    !parmas.alterReason
                  ) {
                    this.warning("请输入变更具体条款及原因！");
                  } else if (this.isMultTenancy()) {
                    this.warning("请填写分段租期！");
                  } else {
                    updateContractInfoApi(parmas)
                      .then(() => {
                        this.$message.success("提交申请成功");
                        this.$router.push(`/registration-record-inquiry/query`);
                      })
                      .catch((error) => {
                        this.$message.warning(error.data.data);
                      });
                  }
                } else {
                  return this.$message.warning("有必填项未填写，请检查！");
                }
              });
            } else {
              this.warning("有附件未上传，请检查！");
            }
          } else {
            console.log(parmas, "提交参数2");
            updateContractInfoApi(parmas).then((res) => {
              console.log(res.data);
              this.$message.success("保存成功");
              this.$router.push(`/registration-record-inquiry/query`);
            });
          }
        } else {
          if (parmas.openType == 0) {
            this.$message.warning("请选择出租方后，再进行保存");
          } else {
            this.$message.warning("请选择出租方后，再进行提交");
          }
        }
      }
    },
    isRoomName() {
      for (var key in this.decisionParmas.contractBuildingInfo) {
        if (
          this.decisionParmas.contractBuildingInfo[key].roomName == "" ||
          this.decisionParmas.contractBuildingInfo[key].roomName == undefined
        ) {
          return true;
        }
      }
      return false;
    },
    isMultTenancy() {
      if (
        this.decisionParmas.priceIncrease == "1" &&
        (this.decisionParmas.contractType != 3 ||
          this.decisionParmas.contentChangeType != 1)
      ) {
        for (var key in this.decisionParmas.multTenancyInfoList) {
          if (
            this.decisionParmas.multTenancyInfoList[key].rentalUnitPrice ==
              "" ||
            this.decisionParmas.multTenancyInfoList[key].annualRent == "" ||
            this.decisionParmas.multTenancyInfoList[key].startTime == "" ||
            this.decisionParmas.multTenancyInfoList[key].endTime == ""
          ) {
            return true;
          }
        }
        return false;
      } else {
        return false;
      }
    },
    isUploadLength(parmas) {
      if (parmas.contractType == 0 || parmas.contractType == 1) {
        if (
          this.tenantBusinessLicenseFile &&
          this.tenantryIdFile &&
          this.creditReportFile &&
          this.propertyRightFile &&
          this.pricingBasisFile &&
          this.contractInfoFile &&
          this.boardResolutionFile &&
          this.tripleAndBigFile &&
          (!this.priceIncreaseStatu1 ||
            (this.priceIncreaseStatu1 && this.nonIncrementalPricing)) &&
          (!this.leaseDepositStatu ||
            (this.leaseDepositStatu && this.leaseDepositBelowFile)) &&
          (!this.contractFreePeriodStatu ||
            (this.contractFreePeriodStatu && this.contractFreePeriodFile))
        ) {
          this.isUploadFlie = true;
        } else {
          this.isUploadFlie = false;
        }
      }
      if (parmas.contractType == 3) {
        if (
          this.boardResolutionFile &&
          this.tripleAndBigFile &&
          this.terminationFile
        ) {
          this.isUploadFlie = true;
        } else {
          this.isUploadFlie = false;
        }
      }
      if (parmas.contractType == 2 && parmas.contentChangeType == 0) {
        if (
          this.changeSupplementaryAgreementFile &&
          this.tenantBusinessLicenseFile &&
          this.tenantryIdFile &&
          this.creditReportFile &&
          this.boardResolutionFile &&
          this.tripleAndBigFile
        ) {
          this.isUploadFlie = true;
        } else {
          this.isUploadFlie = false;
        }
      }
      if (parmas.contractType == 2 && parmas.contentChangeType == 1) {
        if (
          this.changeSupplementaryAgreementFile &&
          this.pricingBasisFile &&
          this.boardResolutionFile &&
          this.tripleAndBigFile &&
          (!this.priceIncreaseStatu1 ||
            (this.priceIncreaseStatu1 && this.nonIncrementalPricing)) &&
          (!this.leaseDepositStatu ||
            (this.leaseDepositStatu && this.leaseDepositBelowFile)) &&
          (!this.contractFreePeriodStatu ||
            (this.contractFreePeriodStatu && this.contractFreePeriodFile))
        ) {
          this.isUploadFlie = true;
        } else {
          this.isUploadFlie = false;
        }
      }
      if (parmas.processFlow == 1 && !this.specialChannelPeriodFile) {
        this.isUploadFlie = false;
      }
    },
    warning(v) {
      this.$warning({
        title: "提示",
        content: v,
      });
    },
    toBack() {
      this.$router.push(`/registration-record-inquiry/query`);
    },
    legalAdvisorReviewChange(v) {
      this.decisionParmas.legalAdvisorReview = v.target.value;
    },
    boardResolutionChange(v) {
      if (v.target.value == 0) {
        this.decMarked = false;
        this.boardResolutionFile = true;
      } else {
        this.decMarked = true;
        if (this.boardFileList.length > 0) {
          this.boardResolutionFile = true;
        } else {
          this.boardResolutionFile = false;
        }
      }
    },
    isSignificantChange(v) {
      if (v.target.value == 0) {
        this.decMarked1 = false;
        this.tripleAndBigFile = true;
      } else {
        this.decMarked1 = true;
        if (this.tbFileList.length > 0) {
          this.tripleAndBigFile = true;
        } else {
          this.tripleAndBigFile = false;
        }
      }
    },
    onSearch(searchText) {
      let parmas = {
        contractType: 0,
        tenantry: searchText,
      };
      queryTenantryApi(parmas).then((res) => {
        if (res.data.length > 50) {
          this.dataSource = res.data.splice(0, 50);
        } else {
          this.dataSource = res.data;
          if (!this.dataSource.length) {
            this.decisionParmas.tenantry = "";
          }
        }
      });
    },
    tenantryChange() {
      if (this.decisionParmas.leaseArea > 0) {
        this.areaCount();
      }
    },
    onSearchRelation(searchText) {
      let parmas = {
        contractType: 0,
        tenantry: searchText,
      };
      queryTenantryApi(parmas).then((res) => {
        if (res.data.length > 50) {
          this.relationSource = res.data.splice(0, 50);
        } else {
          this.relationSource = res.data;
        }
      });
    },
    getParkName() {
      queryAllParkApi1("").then((res) => {
        this.parkNameArr = res.data.parkList;
      });
    },
    buildingNameFocus(v) {
      if (v.parkName) {
        let parmas = {
          parkName: v.parkName,
        };
        queryAllBuildingApi(parmas).then((res) => {
          this.buildingNameArr = res.data.buildingNumbers;
        });
      } else {
        this.buildingNameArr = [];
      }
    },
    floorNameFocus(v) {
      let parmas = {
        buildingNumber: v.buildingName, //楼栋号
        floorNumber: v.floorName, // 楼层
        parkName: v.parkName, // 园区名称
        tenantry: this.decisionParmas.tenantry, // 承租方
      };
      findFloorsByBuildingInfoApi(parmas).then((res) => {
        this.floorNameArr = res.data;
      });
    },
    ParkNameChange(item) {
      this.$set(item, "buildingName", undefined);
      this.$set(item, "floorName", undefined);
      this.$set(item, "roomName", undefined);
      this.$set(item, "roomArea", 0);
      this.areaCount();
    },
    buildingNameChange(item) {
      this.$set(item, "floorName", undefined);
      this.$set(item, "roomName", undefined);
      this.$set(item, "roomArea", 0);
      this.areaCount();
    },
    floorNameChange(item) {
      this.$set(item, "roomName", undefined);
      this.$set(item, "roomArea", 0);
      this.areaCount();
    },
    //主体变更上传文件
    onChangeMain(e) {
      if (e != "未属地") {
        this.decisionParmas.territorialRemark = "";
      }
      if (e != "关联属地") {
        this.decisionParmas.relationCompany = "";
      }
    },
    addItem() {
      if (this.decisionParmas.contractBuildingInfo[0].roomName) {
        let len = this.decisionParmas.contractBuildingInfo.length - 1;
        this.decisionParmas.contractBuildingInfo.push({
          delId:
            Number(this.decisionParmas.contractBuildingInfo[len].delId) + 1,
          parkName: undefined,
          contractInfoId: undefined,
          buildingName: undefined,
          floorName: undefined,
          roomName: undefined,
        });
      }
    },
    delItem(v) {
      this.decisionParmas.contractBuildingInfo.splice(v, 1);
      this.areaCount();
    },
    roomNameChange(v) {
      let parmas = {
        buildingNumber: v.buildingName, //楼栋号
        floorNumber: v.floorName, // 楼层
        parkName: v.parkName, // 园区名称
        tenantry: this.decisionParmas.tenantry, // 承租方
      };
      findRoomsByBuildingInfoApi(parmas).then((res) => {
        this.roomNameArr = res.data;
      });
    },
    areaCount() {
      let list = this.decisionParmas.contractBuildingInfo;
      if (list.length == 1) {
        this.decisionParmas.leaseArea = Number(list[0].roomArea).toFixed(2);
      } else {
        let num = 0;
        list.forEach((e) => {
          if (e.roomArea) {
            num += Number(e.roomArea);
          }
        });
        this.decisionParmas.leaseArea = num.toFixed(2);
      }
      let parmas1 = {
        tenantry: this.decisionParmas.tenantry, // 承租方
      };
      getAreaTotalApi(parmas1).then((res) => {
        if (res.data) {
          this.decisionParmas.accumulateLeaseArea = Number(
            Number(this.decisionParmas.leaseArea) + Number(res.data)
          ).toFixed(2);
        } else {
          this.decisionParmas.accumulateLeaseArea = Number(
            this.decisionParmas.leaseArea
          ).toFixed(2);
        }
      });
    },
    onBlur(v) {
      this.roomNameArr.forEach((e) => {
        if (v.roomName == e.roomNumber) {
          this.$set(v, "roomArea", e.roomArea);
        }
      });
      this.areaCount();
    },
    onChange(v) {
      console.log(v);
    },
    priceIncreaseChange(v) {
      if (v == 1) {
        this.priceIncreaseStatu = true;
        this.priceIncreaseStatu1 = false;
        this.decisionParmas.multTenancyInfoList = [
          {
            delId: 0,
            startTime: "",
            endTime: "",
            rentalUnitPrice: "",
            annualRent: "",
          },
        ];
      } else if (v == 2) {
        this.priceIncreaseStatu = false;
        this.priceIncreaseStatu1 = true;
      } else {
        this.priceIncreaseStatu = false;
        this.priceIncreaseStatu1 = false;
        this.decisionParmas.multTenancyInfoList = [];
      }
    },

    disabledStartDate(current) {
      if (this.decisionParmas.processFlow == 0) {
        return current && current < moment().subtract(1, "days");
      }
    },
    disabledEndDate(current) {
      return (
        current &&
        current < moment(this.decisionParmas.leaseTermStart).subtract(1, "days")
      );
    },
    leaseTimeChange() {
      if (
        this.decisionParmas.leaseTermStart &&
        this.decisionParmas.leaseTermEnd
      ) {
        let num = moment(this.decisionParmas.leaseTermEnd).diff(
          moment(this.decisionParmas.leaseTermStart),
          "days",
          false
        );
        this.decisionParmas.leaseTerm = (Number(num) / 365).toFixed(2);
      } else {
        this.decisionParmas.leaseTerm = "";
      }
    },
    rentFreeChange() {
      let num = 0;
      this.decisionParmas.contractFreePeriod.forEach((e) => {
        if (e.startTime && e.endTime) {
          num += moment(e.endTime).diff(moment(e.startTime), "days", false) + 1;
        }
      });
      this.decisionParmas.rentFreeDays = num;
    },
    addItem1() {
      let len = this.decisionParmas.contractFreePeriod.length - 1;
      this.decisionParmas.contractFreePeriod.push({
        delId: this.decisionParmas.contractFreePeriod[len].delId + 1,
        startTime: "",
        endTime: "",
      });
    },
    delItem1(v) {
      this.decisionParmas.contractFreePeriod.splice(v, 1);
      this.rentFreeChange();
    },
    onChangezuqi(date, dateString, i) {
      console.log(date, dateString, i, "dsfsdf");
      this.decisionParmas.multTenancyInfoList[i].startTime = dateString[0];
      this.decisionParmas.multTenancyInfoList[i].endTime = dateString[1];
    },
    addItem2() {
      let len = this.decisionParmas.multTenancyInfoList.length - 1;
      this.decisionParmas.multTenancyInfoList.push({
        delId: this.decisionParmas.multTenancyInfoList[len].delId + 1,
        startTime: "",
        endTime: "",
        rentalUnitPrice: "",
        annualRent: "",
      });
    },
    delItem2(v) {
      this.decisionParmas.multTenancyInfoList.splice(v, 1);
    },
    //租赁合同原件（盖章版）
    handleConFileCallback: function (file) {
      this.isContractInfo = true;
      this.contractInfoFile = true;
      let tmpList = this.contractFileList;
      tmpList.push(file);
      this.$set(this, "contractFileList", tmpList);
    },
    deleteConFile: function (file, fileList) {
      this.isContractInfo = false;
      if (fileList.length == 0) {
        this.contractInfoFile = false;
      }
      this.contractFileList = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    //提前终止合同原件（盖章版）
    handleConFileCallback1: function (file) {
      this.isTermination = true;
      this.terminationFile = true;
      let tmpList = this.terminationFileList;
      tmpList.push(file);
      this.$set(this, "terminationFileList", tmpList);
    },
    deleteConFile1: function (file, fileList) {
      this.isTermination = false;
      if (fileList.length == 0) {
        this.terminationFile = false;
      }
      this.terminationFileList = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    //董事会决议（复印件）
    handleFileCallback: function (file) {
      this.isBoardResolution = true;
      this.boardResolutionFile = true;
      let tmpList = this.boardFileList;
      tmpList.push(file);
      this.$set(this, "boardFileList", tmpList);
    },
    deleteFile: function (file, fileList) {
      this.isBoardResolution = false;
      if (this.decisionParmas.boardResolution == 1) {
        if (fileList.length == 0) {
          this.boardResolutionFile = false;
        }
      }
      this.boardFileList = fileList;
      if (this.decisionParmas.attachments) {
        this.decisionParmas.attachments =
          this.decisionParmas.attachments.filter(
            (item) => item.fileId !== file.fileId
          );
      }
    },
    //三重一大（复印件）
    handleTBFileCallback: function (file) {
      this.isTripleAndBig = true;
      this.tripleAndBigFile = true;
      let tmpList = this.tbFileList;
      tmpList.push(file);
      this.$set(this, "tbFileList", tmpList);
    },
    deleteTbFile: function (file, fileList) {
      this.isTripleAndBig = false;
      if (this.decisionParmas.isSignificant == 1) {
        if (fileList.length == 0) {
          this.tripleAndBigFile = false;
        }
      }
      this.tbFileList = fileList;
      console.log(this.tbFileList, "删除三重一大（复印件）");
      if (this.decisionParmas.attachments) {
        this.decisionParmas.attachments =
          this.decisionParmas.attachments.filter(
            (item) => item.fileId !== file.fileId
          );
      }
    },
    handleLicenseFileCallback: function (file) {
      this.tenantBusinessLicenseFile = true;
      let tmpList = this.licenseFileList;
      tmpList.push(file);
      this.$set(this, "licenseFileList", tmpList);
    },
    deleteLicenseFile: function (file, fileList) {
      if (fileList.length == 0) {
        this.tenantBusinessLicenseFile = false;
      }
      this.licenseFileList = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback1: function (file) {
      this.tenantryIdFile = true;
      let tmpList = this.fileList1;
      tmpList.push(file);
      this.$set(this, "fileList1", tmpList);
    },
    deleteFile1: function (file, fileList) {
      if (fileList.length == 0) {
        this.tenantryIdFile = false;
      }
      this.fileList1 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback2: function (file) {
      this.creditReportFile = true;
      let tmpList = this.fileList2;
      tmpList.push(file);
      this.$set(this, "fileList2", tmpList);
    },
    deleteFile2: function (file, fileList) {
      if (fileList.length == 0) {
        this.creditReportFile = false;
      }
      this.fileList2 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback3: function (file) {
      this.propertyRightFile = true;
      let tmpList = this.fileList3;
      tmpList.push(file);
      this.$set(this, "fileList3", tmpList);
    },
    deleteFile3: function (file, fileList) {
      if (fileList.length == 0) {
        this.propertyRightFile = false;
      }
      this.fileList3 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback4: function (file) {
      this.pricingBasisFile = true;
      let tmpList = this.fileList4;
      tmpList.push(file);
      this.$set(this, "fileList4", tmpList);
    },
    deleteFile4: function (file, fileList) {
      if (fileList.length == 0) {
        this.pricingBasisFile = false;
      }
      console.log(file, fileList);
      this.fileList4 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback5: function (file) {
      this.nonIncrementalPricing = true;
      let tmpList = this.fileList5;
      tmpList.push(file);
      this.$set(this, "fileList5", tmpList);
    },
    deleteFile5: function (file, fileList) {
      if (fileList.length == 0) {
        this.nonIncrementalPricing = false;
      }
      this.fileList5 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback6: function (file) {
      this.leaseDepositBelowFile = true;
      let tmpList = this.fileList6;
      tmpList.push(file);
      this.$set(this, "fileList6", tmpList);
    },
    deleteFile6: function (file, fileList) {
      if (fileList.length == 0) {
        this.leaseDepositBelowFile = false;
      }
      this.fileList6 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback7: function (file) {
      this.contractFreePeriodFile = true;
      let tmpList = this.fileList7;
      tmpList.push(file);
      this.$set(this, "fileList7", tmpList);
    },
    deleteFile7: function (file, fileList) {
      if (fileList.length == 0) {
        this.contractFreePeriodFile = false;
      }
      this.fileList7 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    //特殊申报流程
    handleFileCallback8: function (file) {
      this.specialChannelPeriodFile = true;
      let tmpList = this.fileList8;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile8: function (file, fileList) {
      if (fileList.length == 0) {
        this.specialChannelPeriodFile = false;
      }
      this.fileList8 = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleagreementFileCallback: function (file) {
      this.changeSupplementaryAgreementFile = true;
      let tmpList = this.agreementFileList;
      tmpList.push(file);
      this.$set(this, "agreementFileList", tmpList);
    },
    deleteagreementFile: function (file, fileList) {
      if (fileList.length == 0) {
        this.changeSupplementaryAgreementFile = false;
      }
      console.log(file, fileList);
      this.agreementFileList = fileList;
      this.decisionParmas.attachments = this.decisionParmas.attachments.filter(
        (item) => item.fileId !== file.fileId
      );
    },
  },
};
</script>

<style lang="less" scoped>
@import "./../common/common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}

// .ant-advanced-search-form {
//   width: 100%;
//   padding: 24px;
//   background: #fbfbfb;
//   border: 1px solid #d9d9d9;
//   border-radius: 6px;
// }
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  // width: 166px !important;
}
// .ant-advanced-search-form .ant-form-item-control-wrapper {
//   width: 100% !important;
//   height: 40px !important;
//   line-height: 40px !important;
//   flex: 1;
// }
</style>

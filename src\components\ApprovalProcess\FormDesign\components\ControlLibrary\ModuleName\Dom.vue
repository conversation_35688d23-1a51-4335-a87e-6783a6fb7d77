<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 模块名称控件
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '基础信息'"
      style="margin-bottom:unset"
    >
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        modulename: null
      }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

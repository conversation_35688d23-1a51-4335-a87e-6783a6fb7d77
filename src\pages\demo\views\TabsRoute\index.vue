<template>
  <div class="tabs-router">
    <div class="tabs-head">
      <div>
        <a-tabs
          :tabBarStyle="{ margin: 0 }"
          @change="navigate"
          :activeKey="activeKey"
        >
          <a-tab-pane tab="列表a" key="1"></a-tab-pane>
          <a-tab-pane tab="列表b" key="2"></a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div class="tabs-content">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  name: "TabsRoute",
  computed: {
    activeKey() {
      switch (this.$route.path) {
        case "/form/tabs-route/list-a":
          return "1";
        case "/form/tabs-route/list-b":
          return "2";
        default:
          return "1";
      }
    }
  },
  methods: {
    navigate(key) {
      switch (key) {
        case "1":
          this.$router.push("/form/tabs-route/list-a");
          break;
        case "2":
          this.$router.push("/form/tabs-route/list-b");
          break;
        default:
          this.$router.push("/form/tabs-route/list-a");
      }
    }
  }
};
</script>

<style lang="less" scoped>
.search-head {
  background-color: @base-bg-color;
  margin: -25px -24px -24px;
  padding: 0 24px;
}
.search-content {
  margin-top: 48px;
}
</style>

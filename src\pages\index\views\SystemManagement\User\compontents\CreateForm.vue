<!--
* <AUTHOR>
* @time 2020-8-21
* @dec 系统管理 - 用户管理 - 用户新增/修改/查看
-->
<template>
  <a-modal
    :title="modelTitle"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="公司类型">
          <a-radio-group
            @change="switchType"
            v-decorator="[
              'orgType',
              {
                initialValue: 'inside',
                rules: [{ required: true, message: '请选择用户类型!' }]
              }
            ]"
          >
            <a-radio value="inside">
              内部
            </a-radio>
            <a-radio value="outside">
              外部
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="部门">
          <a-spin :spinning="departmentListLoading">
            <a-tree-select
              allow-clear
              v-decorator="[
                'deptId',
                {
                  rules: [{ required: true, message: '请选择部门!' }]
                }
              ]"
              placeholder="请选择部门"
              :tree-data="departmentList"
              :replaceFields="{ title: 'name', value: 'id', key: 'id' }"
            >
              <span v-for="d in departmentList" :key="String(d.id)">
                {{ d.name }}
              </span>
            </a-tree-select>
          </a-spin>
        </a-form-item>
        <a-form-item label="用户名">
          <a-input
            :disabled="openWay == 'edit'"
            v-decorator="[
              'username',
              { rules: [{ required: true, message: '请输入用户名!' }] }
            ]"
            autoComplete="off"
            placeholder="请输入用户名"
          />
        </a-form-item>
        <a-form-item label="姓名">
          <a-input
            v-decorator="[
              'name',
              {
                rules: [
                  { required: true, message: '请输入姓名!' },
                  { validator: checkName }
                ]
              }
            ]"
            autoComplete="off"
            placeholder="请输入姓名"
          />
        </a-form-item>
        <a-form-item label="性别">
          <a-radio-group
            v-decorator="[
              'sex',
              {
                initialValue: 'man',
                rules: [{ required: true, message: '请选择性别!' }]
              }
            ]"
          >
            <a-radio value="man">
              男
            </a-radio>
            <a-radio value="woman">
              女
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="手机号">
          <a-input
            v-decorator="[
              'phoneNo',
              {
                rules: [
                  { required: false, message: '请输入手机号!' },
                  { validator: checkMobile }
                ]
              }
            ]"
            autoComplete="off"
            placeholder="请输入手机号"
          />
        </a-form-item>
        <a-form-item label="邮箱">
          <a-input
            v-decorator="[
              'email',
              {
                rules: [
                  { required: false, message: '请输入邮箱!' },
                  { validator: checkEmail }
                ]
              }
            ]"
            autoComplete="off"
            placeholder="请输入邮箱"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick";
import {
  ApiSecurityCreate,
  ApiSecurityUpdate,
  ApiSecurityFindDepartmentTree
} from "@/pages//index/data/api/SystemManagement/User";
// 校验
import { checkName, checkMobile, checkEmail } from "@/common/validate";
import { translateDataToTree } from "@/common/utils";

// 表单字段
const fields = [
  "deptId",
  "username",
  "name",
  "sex",
  "phoneNo",
  "email",
  "orgType"
];

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    },
    openWay: {
      type: String,
      default: null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      loading: false,
      form: this.$form.createForm(this),
      modelTitle: null, //动态标题
      departmentList: [], //部门列表
      departmentListLoading: false //部门列表loading
    };
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          if (this.openWay == "edit") {
            this.modelTitle = "修改用户";
            let comType = this.data.orgType == "inside" ? "0" : "1";
            this.findDepartmentTree(comType);
          } else {
            this.modelTitle = "新增用户";
            this.findDepartmentTree("0");
          }
        }
      },
      deep: true
    }
  },
  created() {
    // 防止表单未注册
    fields.forEach(v => this.form.getFieldDecorator(v));

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields));
    });
  },
  methods: {
    checkName,
    checkMobile,
    checkEmail,
    //确定提交
    handleOk() {
      this.loading = true;
      this.form.validateFields((errors, values) => {
        if (!errors) {
          // console.log("values", values);
          if (this.data.id) {
            values.id = this.data.id;
            const requestParameters = Object.assign({}, values);
            ApiSecurityUpdate(requestParameters)
              .then(() => {
                // console.log("loadData", res);
                this.$emit("cancel");
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("修改成功");
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            // 新增
            const requestParameters = Object.assign({}, values);
            ApiSecurityCreate(requestParameters)
              .then(() => {
                // console.log("loadData", res);
                this.$emit("cancel");
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("新增成功");
              })
              .finally(() => {
                this.loading = false;
              });
          }
        } else {
          this.loading = false;
        }
      });
    },
    //根据用户类型查询部门列表
    findDepartmentTree(type) {
      const params = {
        companyType: type
      };
      this.departmentListLoading = true;
      ApiSecurityFindDepartmentTree(params)
        .then(res => {
          this.departmentList = translateDataToTree(res.data);
          this.departmentListLoading = false;
        })
        .finally(() => {
          this.departmentListLoading = false;
        });
    },
    //切换用户类型
    switchType(e) {
      let chooseUType = e.target.value;
      let companyType = chooseUType == "inside" ? "0" : "1";
      this.form.setFieldsValue({
        deptId: undefined
      });
      this.findDepartmentTree(companyType);
    }
  }
};
</script>

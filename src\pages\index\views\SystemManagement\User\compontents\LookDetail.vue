<!--
* <AUTHOR>
* @time 2020-8-27
* @dec 系统管理 - 用户管理 - 用户查看
-->
<template>
  <a-modal
    title="查看用户"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
    footer=""
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="公司类型">
          <span>{{ this.dataForm.orgType == "inside" ? "内部" : "外部" }}</span>
        </a-form-item>
        <a-form-item label="部门">
          <span>{{ this.dataForm.deptName }}</span>
        </a-form-item>
        <a-form-item label="用户名">
          <span>{{ this.dataForm.username }}</span>
        </a-form-item>
        <a-form-item label="姓名">
          <span>{{ this.dataForm.name }}</span>
        </a-form-item>
        <a-form-item label="性别">
          <span>{{ this.dataForm.sex == "man" ? "男" : "女" }}</span>
        </a-form-item>
        <a-form-item label="手机号">
          <span>{{ this.dataForm.phoneNo }}</span>
        </a-form-item>
        <a-form-item label="邮箱">
          <span>{{ this.dataForm.email }}</span>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      loading: false,
      form: this.$form.createForm(this),
      dataForm: {}
    };
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          this.dataForm = this.data;
        }
      },
      deep: true
    }
  },
  created() {},
  methods: {}
};
</script>

<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 流程设计-抽屉-添加角色
-->
<template>
  <a-modal
    title="添加角色"
    :width="690"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="modelCancel"
  >
    <a-spin :spinning="loading">
      <div class="flow-drawer-people">
        <a-row :gutter="30">
          <a-col :span="12">
            <h3>选择</h3>
            <a-card class="card-style">
              <a-input-search
                style="margin-bottom:8px"
                placeholder="搜索"
                @change="onChange"
              />
              <a-tree
                v-model="checkedKeys"
                checkable
                :auto-expand-parent="autoExpandParent"
                :tree-data="searchList.length ? searchList : roleTree"
                @select="onSelect"
                :replaceFields="{
                  title: 'name',
                  key: 'id'
                }"
              />
            </a-card>
          </a-col>
          <a-col :span="12">
            <h3>已选</h3>
            <a-card class="card-style">
              <div
                v-for="(tag, index) in tagsList"
                :key="index"
                class="tag-item"
              >
                <span>{{ tag.name }}</span>
                <a-icon
                  type="close-circle"
                  @click="handleClose(tag)"
                  size="small"
                />
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    dataList: {
      type: Array,
      default: () => []
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      autoExpandParent: true,
      checkedKeys: [],
      tagsList: [],
      // 搜索数据
      searchList: []
    };
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          this.roleTree = JSON.parse(JSON.stringify(this.dataList));
          this.tagsList = this.tags;
          this.checkedKeys = this.tagsList.map(item => item.id);
        }
      },
      deep: true
    },
    checkedKeys(val) {
      let allList = [];
      allList = this.roleTree;
      const subList = [];
      val.forEach(n => {
        allList.forEach(function(v) {
          if (n == v.id) {
            subList.push(v);
          }
        });
      });
      this.tagsList = subList;
    }
  },
  mounted() {},
  methods: {
    //关闭时清空初始值
    clearData() {
      this.tagsList = [];
      this.roleTree = [];
    },
    //确定提交
    handleOk() {
      this.$emit("cancel", this.tagsList);
    },
    //弹框取消
    modelCancel() {
      this.$emit("cancel", this.tags);
      this.clearData();
    },
    //搜索事件
    onChange(value) {
      this.searchList = this.dataList.filter(item => {
        return item.name.includes(value.target.value);
      });
    },
    //tree节点点击
    onSelect(selectedKeys, info) {
      let infoItem = info.node.dataRef;
      var ret2 = this.checkedKeys.findIndex(v => {
        return v == infoItem.id;
      });
      if (ret2 == -1) {
        this.checkedKeys.push(infoItem.id);
        return;
      } else {
        this.checkedKeys.splice(
          this.checkedKeys.findIndex(item1 => item1 === infoItem.id),
          1
        );
      }
    },
    //删除已选
    handleClose(removedTag) {
      // console.log("删除", removedTag);
      const tags = this.checkedKeys.filter(tag => tag !== removedTag.id);
      this.checkedKeys = tags;
    }
  }
};
</script>
<style lang="less" scoped>
@import "index.less";
</style>

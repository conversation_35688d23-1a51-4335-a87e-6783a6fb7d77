//实业公司备案登记接口
import api from "@/common/api";
import { BASE_URL } from "Config";

//分页查询企业建档信息 /mapi/manager/company/filing/pageByCondition
export function getListApi(params) {
    return api({
        url: BASE_URL + "/manager/company/filing/pageByCondition",
        method: "post",
        params,
    });
}

//新增企业建档信息 /mapi/manager/company/filing/addCompanyFilingInfo
export function addContractInfoApi(params) {
    return api({
        url: BASE_URL + "/manager/company/filing/addCompanyFilingInfo",
        method: "post",
        params,
    });
}

//编辑企业建档信息 /mapi/manager/company/filing/updateCompanyFilingInfo
export function updateContractInfoApi(params) {
    return api({
        url: BASE_URL + "/manager/company/filing/updateCompanyFilingInfo",
        method: "post",
        params,
    });
}

//删除企业建档信息 /mapi/manager/company/filing/deleteById
export function deleteContractInfoApi(params) {
    return api({
        url: BASE_URL + "/manager/company/filing/deleteById",
        method: "post",
        params,
    });
}

//根据Id查询企业建档信息  /mapi/manager/company/filing/selectCompanyFilingById
export function queryContractInfoByIdApi(params) {
    return api({
        url: BASE_URL + "/manager/company/filing/selectCompanyFilingById",
        method: "post",
        params,
    });
}

//企业全名查询企业建档信息 -- /mapi/qiXinBao/getCompanyDetailAll
export function getCompanyDetailApi(params) {
    return api({
        url: BASE_URL + "/qiXinBao/getCompanyDetailAll",
        method: "post",
        params,
    });
}

//审批企业建档信息 -- /mapi/manager/company/filing/approvalCompanyFiling
export function approvalContractApi(params) {
    return api({
        url: BASE_URL + "/manager/company/filing/approvalCompanyFiling",
        method: "post",
        params,
    });
}
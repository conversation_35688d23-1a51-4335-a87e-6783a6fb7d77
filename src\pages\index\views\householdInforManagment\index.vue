<template>
  <router-view v-if="$route.meta.level == 2"> </router-view>
  <div class="enterprise" v-else>
    <div class="enterpriseFrom">
      <div class="carrierFrom">
        <a-form
          :form="form"
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
          @submit="handleSubmit"
        >
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="企业名称">
                <a-input
                  allowClear
                  v-model="queryParam.enterpriseName"
                  placeholder="请输入企业名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="统一社会信用代码">
                <a-input
                  allowClear
                  v-model="queryParam.creditCode"
                  placeholder="请输入统一社会信用代码"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="招商分部">
                <a-select
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  allowClear
                  v-model="queryParam.businessDivision"
                  placeholder="请选择招商分部"
                >
                  <a-select-option
                    :value="item.abbr"
                    v-for="item in attractInvestmentArr"
                    :key="item.abbr"
                    >{{ item.abbr }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="招商类型">
                <a-select
                  allowClear
                  v-model="queryParam.investmentTypeCategory"
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  placeholder="请选择招商类型"
                >
                  <a-select-option value="1">新设</a-select-option>
                  <a-select-option value="2">迁入</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="审批状态">
                <!-- <a-range-picker v-model="queryParam.approvalStatus" /> -->
                <a-select
                  allowClear
                  v-model="queryParam.status"
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  placeholder="请选择审批状态"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in approvalStatusArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="申请日期">
                <a-range-picker
                  v-model="queryParam.applicationDeta"
                  :format="'YYYY-MM-DD'"
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  placeholder="请选择申请日期"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <div style="display: flex; justify-content: flex-end">
            <a-button
              type="primary"
              v-if="roles.includes('户管信息发起人')"
              @click="add()"
              style="margin-right: 20px"
              >新增</a-button
            >
            <a-button type="primary" @click="search" style="margin-right: 20px"
              >查询</a-button
            >
            <a-button type="default" @click="reset">重置</a-button>
          </div>
        </a-form>
      </div>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
    <a-card style="width: 100%; margin-top: 20px">
      <div class="list-tit">
        <a-tabs
          v-model="activeKey"
          size="small"
          @click="callbackfn"
          style="width: 100%"
        >
          <a-tab-pane key="0" tab="待办事项"></a-tab-pane>
          <a-tab-pane key="1" tab="流转中事项"></a-tab-pane>
          <a-tab-pane key="2" tab="已完结事项"></a-tab-pane>
        </a-tabs>
        <a-button
          class="btnSty"
          type="primary"
          :disabled="selectedRowKeys.length == 0"
          v-if="activeKey == 2"
          @click="exportFn()"
          >批量导出{{
            selectedRowKeys.length ? `(${selectedRowKeys.length})` : ""
          }}</a-button
        >
      </div>
      <div>
        <a-table
          ref="table"
          size="default"
          :pagination="false"
          :columns="columns"
          :data-source="loadData"
          :scroll="{ x: 1000 }"
          :row-selection="
            activeKey == 2
              ? {
                  selectedRowKeys: selectedRowKeys,
                  onChange: onChangeFn,
                }
              : undefined
          "
          rowKey="id"
        >
          <span slot="index" slot-scope="text, record, index">
            {{ (pages.pageNo - 1) * pages.pageSize + index + 1 }}
          </span>
          <template slot="companyName" slot-scope="text, record">
            <a-tooltip>
              <template slot="title">
                {{ text }}
              </template>
              <div
                style="
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  color: #1677ff;
                  cursor: pointer;
                "
                @click="toDetail(record, 2)"
              >
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <template slot="industryCategory" slot-scope="text">
            <a-tooltip>
              <template slot="title">
                {{
                  text
                    ? industryCategoryArr.filter((res) => {
                        return res.value == text;
                      }).length > 0
                      ? industryCategoryArr.filter((res) => {
                          return res.value == text;
                        })[0].desc
                      : ""
                    : ""
                }}
              </template>
              <div
                style="
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                {{
                  text
                    ? industryCategoryArr.filter((res) => {
                        return res.value == text;
                      }).length > 0
                      ? industryCategoryArr.filter((res) => {
                          return res.value == text;
                        })[0].desc
                      : ""
                    : ""
                }}
              </div>
            </a-tooltip>
          </template>
          <span slot="action" slot-scope="text, record">
            <a
              v-if="record.status == 0 || record.status == 4"
              href="javascript:;"
              @click="toDetail(record, 1)"
              >编辑</a
            >
            <a
              href="javascript:;"
              style="margin: 0 10px"
              v-if="record.status != 0 && record.status != 4"
              @click="toDetail(record, 2)"
              >查看</a
            >
            <a
              href="javascript:;"
              v-if="record.status == 3 && roles.includes('户管信息发起人')"
              style="margin: 0 10px"
              @click="toDetail(record, 4)"
              >编辑</a
            >
            <a
              href="javascript:;"
              v-if="
                (record.status == 1 && roles.includes('户管信息初审人')) ||
                (record.status == 2 && roles.includes('户管信息复审人')) ||
                (record.status == 5 && roles.includes('户管信息终审人')) ||
                (record.status == 6 && roles.includes('户管信息预审人'))
              "
              @click="toDetail(record, 3)"
              >处理</a
            >
          </span>
        </a-table>
        <a-pagination
          style="display: flex; justify-content: flex-end"
          show-size-changer
          :total="pages.total"
          @change="onShowSizeChange"
        />
      </div>
    </a-card>
  </div>
</template>

<script>
import moment from "moment";
import {
  getPageByCondition,
  getCompanyAbbrList,
  exportHousehold,
} from "@/pages/index/data/api/keyAndHouse/index";

export default {
  data() {
    return {
      //行业类别
      industryCategoryArr: [],
      approvalStatusArr: [
        {
          value: "0",
          label: "待提交",
        },
        {
          value: "6",
          label: "待预审",
        },
        {
          value: "1",
          label: "待初审",
        },
        {
          value: "2",
          label: "待复审",
        },
        {
          value: "5",
          label: "待终审",
        },
        {
          value: "3",
          label: "已完成",
        },
        {
          value: "4",
          label: "已驳回",
        },
      ], //审批状态枚举
      attractInvestmentArr: [], //招商分部
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "index" },
          width: 80,
          fixed: "left",
          align: "center",
        },
        {
          title: "企业名称",
          dataIndex: "enterpriseName",
          scopedSlots: { customRender: "companyName" },
          width: 180,
          align: "center",
        },
        {
          title: "注册资本（万）",
          dataIndex: "registeredCapital",
          scopedSlots: { customRender: "registeredCapital" },
          width: 180,
          align: "center",
        },
        {
          title: "币种",
          dataIndex: "currency",
          width: 120,
          align: "center",
          customRender: (text) => {
            return text == 1
              ? "人民币"
              : text == 2
              ? "美元"
              : text == 3
              ? "欧元"
              : text == 4
              ? "日元"
              : text == 5
              ? "港币"
              : text == 6
              ? "英镑"
              : "";
          },
        },
        {
          title: "行业类别",
          dataIndex: "industryCategory",
          width: 120,
          align: "center",
          scopedSlots: { customRender: "industryCategory" },
        },

        {
          title: "招商类型",
          dataIndex: "investmentTypeCategory",
          width: 180,
          align: "center",
          customRender: (text) => {
            return text == 1 ? "新设" : text == 2 ? "迁入" : "";
          },
        },
        {
          title: "招商分部",
          dataIndex: "businessDivision",
          width: 180,
          align: "center",
        },
        {
          title: "申请时间",
          dataIndex: "applicationDate",
          width: 180,
          align: "center",
        },

        {
          title: "审批状态",
          dataIndex: "status",
          width: 100,
          align: "center",
          scopedSlots: { customRender: "status" },
          customRender: (text) => {
            return text == 0
              ? "待提交"
              : text == 1
              ? "待初审"
              : text == 2
              ? "待复审"
              : text == 3
              ? "已完成"
              : text == 5
              ? "待终审"
              : text == 6
              ? "待预审"
              : "已驳回";
          },
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          scopedSlots: { customRender: "action" },
          width: 150,
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 },
        },
      },
      queryParam: {
        enterpriseName: undefined, //企业名称
        creditCode: undefined, //统一社会信用代码
        investmentTypeCategory: undefined, //招商类型
        businessDivision: undefined, //招商分部
        status: undefined, //审批状态
        applicationDeta: [], //申请日期
      },
      loadData: [],
      pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      activeKey: "0",
      selectedRowKeys: [],
      selectedRows: [],
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
    };
  },
  mounted() {
    this.getIndustryCategory();
    this.loadDataFn();
    this.getCompanyAbbr();
    console.log(this.roles);
  },
  watch: {
    $route(to, from) {
      // 路由变化时执行的代码
      console.log("Route changed:", to.path, from.path);
      if (to.path == "/household-infor-managment") {
        this.loadDataFn();
      }
    },
  },
  methods: {
    moment,
    //获取营商分布
    async getCompanyAbbr() {
      await getCompanyAbbrList({}).then((res) => {
        console.log(res, "formData");
        if (res.code == 0) {
          this.attractInvestmentArr = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    //行业类别
    async getIndustryCategory() {
      this.industryCategoryArr = await this.$getDictByType({
        dictCode: "industry_category",
      });
    },
    //新增
    add() {
      this.$router.push(`/household-infor-managment/addHosehold`);
    },
    //编辑、查看、
    // n 1：编辑 2：查看 3：处理 4 已完成查看
    toDetail(record, n) {
      if (n == 2 || n == 3) {
        this.$router.push(
          "/household-infor-managment/viewHosehold?statu=" +
            n +
            "&id=" +
            record.id +
            "&status=" +
            record.status
        );
      } else if (n == 4) {
        //成功后的编辑
        this.$router.push(
          "/household-infor-managment/editHosehold?statu=" +
            1 +
            "&id=" +
            record.id +
            "&status=" +
            record.status
        );
      } else {
        this.$router.push(
          //未提交的编辑
          "/household-infor-managment/editHosehold?statu=" +
            n +
            "&id=" +
            record.id +
            "&status=" +
            record.status
        );
      }
    },
    callbackfn() {
      console.log(this.activeKey, "3333");
      this.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      };
      this.selectedRowKeys = [];
      this.selectedRows = [];
      this.loadData = [];
      this.loadDataFn();
    },
    loadDataFn() {
      this.loading = true;
      let params = JSON.parse(JSON.stringify(this.queryParam));
      if (this.queryParam.applicationDeta.length == 2) {
        params.applicationStartDate =
          this.queryParam.applicationDeta[0].format("YYYY-MM-DD");
        params.applicationEndDate =
          this.queryParam.applicationDeta[1].format("YYYY-MM-DD");
        params.applicationDeta = "";
      }
      const requestParameters = Object.assign(
        {
          currentPage: this.pages.pageNo,
          pageSize: this.pages.pageSize,
          queryType: Number(this.activeKey),
        },
        params
      );
      getPageByCondition(requestParameters).then((res) => {
        if (res.code == 0) {
          this.loading = false;
          console.log(res, "列表数据");
          this.loadData = res.data.records;
          this.pages.total = res.data.total;
        } else {
          this.loading = false;
          this.loadData = [];
          this.pages.total = 0;
        }
      });
    },
    //翻页
    onShowSizeChange(current, pageSize) {
      console.log(current, pageSize, "iii");
      this.pages.pageNo = current;
      this.pages.pageSize = pageSize;
      this.loadData = [];
      this.loadDataFn();
    },
    // 复选框选中行
    //复选框选中行
    onChangeFn(selectedRowKeys, selectedRows) {
      console.log(Object.prototype.toString.call(selectedRowKeys));

      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;

      console.log(this.selectedRowKeys, selectedRows, "selectedRowKeys");
    },

    reset() {
      this.queryParam = {
        projectName: undefined, //项目名称
        taxScale: undefined, //税收规模
        investmentType: undefined, //招商类型
        attractInvestment: undefined, //招商分部
        approvalStatus: undefined, //审批状态
        applicationDeta: [],
      };

      this.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      };
      this.loadDataFn();
    },
    search() {
      this.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      };
      this.loadData = [];
      this.selectedRowKeys = [];
      this.selectedRows = [];
      this.loadDataFn();
    },

    toEdit(record, n) {
      this.$router.push(
        `/pre-declaration-check/query/add?statu=` + n + `&id=` + record.id
      );
    },
    // 批量导出
    exportFn() {
      exportHousehold({ householdIds: this.selectedRowKeys });
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise {
  display: flex;
  flex-wrap: wrap;

  .enterpriseFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }

  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;

    .sort {
      margin-left: auto;

      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }

  .table {
    width: 100%;
    margin-top: 10px;
  }

  .list-tit {
    position: relative;
    display: flex;
    justify-content: space-between;
    .btnSty {
      position: absolute;
      right: 0;
      top: 0;
    }

    p {
      font-size: 20px;
    }
  }
}

.ellipse {
  width: 100%;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 这里是超出几行省略 */
}
</style>

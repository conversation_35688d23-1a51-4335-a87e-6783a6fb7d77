<!--
* <AUTHOR>
* @time 2020-9-10
* @dec 门户管理-首页管理-轮播图片
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="标题">
              <a-input
                v-model="queryParam.bannerName"
                placeholder="请输入标题"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="() => (this.queryParam = {})"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div>
      <div class="table-operator">
        <a-button type="primary" icon="plus" @click="handleAdd">添加</a-button>
      </div>
      <s-table
        ref="table"
        size="default"
        rowKey="id"
        :columns="columns"
        :data="loadData"
        :showPagination="true"
      >
        <span slot="status" slot-scope="text, record">
          <a-switch
            :checked="record.status"
            @change="handleChangeStatus(record.status, record)"
          />
        </span>
        <span slot="bannerImage" slot-scope="text">
          <img alt="暂无图片或未正确显示" height="40px" :src="text" />
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="请认真核对，选中数据是否删除?"
              @confirm="() => handleDel(record)"
            >
              <a href="javascript:;">删除</a>
            </a-popconfirm>
          </template>
        </span>
      </s-table>
      <add-form
        ref="addModal"
        :visible="visible"
        :data="mdl"
        @cancel="handleCancel"
        @ok="$refs.table.refresh()"
      />
    </div>
  </a-card>
</template>
<script>
import STable from "@/components/Table";
// import Ellipsis from "@/components/Ellipsis";
// API接口
import {
  ApiBusinessPageByConditionn,
  ApiBusinessUpdateDisabled,
  ApiBusinessDeleteById
} from "@/pages/index/data/api/PortalManagement/HomePage/Rotation";
import AddForm from "./components/AddForm";
const columns = [
  {
    title: "排序",
    dataIndex: "sort"
  },
  {
    title: "标题名称",
    dataIndex: "bannerName"
  },
  {
    title: "图片",
    dataIndex: "bannerImage",
    scopedSlots: { customRender: "bannerImage" }
  },
  {
    title: "是否启用",
    dataIndex: "status",
    scopedSlots: { customRender: "status" }
  },
  {
    title: "操作",
    dataIndex: "action",
    scopedSlots: { customRender: "action" }
  }
];
export default {
  components: {
    STable,
    AddForm
  },
  data() {
    this.columns = columns;
    return {
      visible: false,
      mdl: null,
      // 查询参数
      queryParam: {
        ascs: "",
        bannerName: "",
        currentPage: 1,
        descs: ""
        // pageSize: 10
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        this.queryParam.currentPage = parameter.pageNo;
        this.queryParam.current = this.queryParam.currentPage;
        const requestParameters = Object.assign({}, parameter, this.queryParam);
        return ApiBusinessPageByConditionn(requestParameters).then(res => {
          return res.data;
        });
      }
    };
  },
  methods: {
    //添加
    handleAdd() {
      this.visible = true;
      this.mdl = {};
    },
    //修改
    handleEdit(record) {
      this.visible = true;
      this.mdl = { ...record };
      console.log("this.mdl", this.mdl);
    },
    //删除
    handleDel(record) {
      this.loading = true;
      const params = {
        id: record.id
      };
      ApiBusinessDeleteById(params)
        .then(() => {
          this.$message.info("删除成功");
          this.$refs.table.refresh();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //弹框关闭
    handleCancel() {
      this.visible = false;
    },
    // 是否启用表格调接口
    handleChangeStatus(enable, record) {
      record.status = !enable;
      const params = {
        bannerId: record.id,
        disabled: record.status
      };
      ApiBusinessUpdateDisabled(params)
        .then(() => {
          if (record.status) {
            this.$message.info("启用成功");
          } else {
            this.$message.info("禁用成功");
          }
        })
        .catch(() => {
          record.status = !record.status;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>
<style lang="less" scoped></style>

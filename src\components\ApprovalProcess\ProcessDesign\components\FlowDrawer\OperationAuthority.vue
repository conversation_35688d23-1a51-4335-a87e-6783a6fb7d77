<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 流程设计-抽屉-表单操作权限
-->
<template>
  <div class="operation-authority">
    <a-table
      :columns="columns"
      :data-source="formData"
      rowKey="key"
    >
      <span slot="data.inputTitle" slot-scope="text, record">
        {{ text || record.dataname }}
      </span>
      <span slot="editTitle" class="display-inline-flex">
        <a-radio
          :checked="checksAllTitle.isEdit"
          @click="handleChangeEdit('isEdit')"
          >可编辑
        </a-radio>
      </span>
      <span :checked="text" slot="readOnlyTitle" class="display-inline-flex">
        <a-radio
          :checked="checksAllTitle.isReadOnly"
          @click="handleChangeEdit('isReadOnly')"
          >只读
        </a-radio>
      </span>
      <span :checked="text" slot="hideTitle" class="display-inline-flex">
        <a-radio
          :checked="checksAllTitle.isHide"
          @click="handleChangeEdit('isHide')"
          >隐藏
        </a-radio>
      </span>
      <span slot="isEdit" slot-scope="text, record">
        <a-radio
          :name="'edit_' + record.key"
          :checked="text"
          @change="handleChildChange('isEdit', record)"
        >
        </a-radio>
      </span>
      <span slot="isReadOnly" slot-scope="text, record">
        <a-radio
          :name="'edit_' + record.key"
          :checked="text"
          @change="handleChildChange('isReadOnly', record)"
        >
        </a-radio>
      </span>
      <span slot="isHide" slot-scope="text, record">
        <a-radio
          :name="'edit_' + record.key"
          :checked="text"
          @change="handleChildChange('isHide', record)"
        >
        </a-radio>
      </span>
    </a-table>
  </div>
</template>
<script>
const columns = [
  {
    title: "表单字段",
    dataIndex: "data.inputTitle",
    scopedSlots: { customRender: "data.inputTitle" }
  },
  {
    dataIndex: "isEdit",
    scopedSlots: { customRender: "isEdit" },
    slots: { title: "editTitle" }
  },
  {
    dataIndex: "isReadOnly",
    scopedSlots: { customRender: "isReadOnly" },
    slots: { title: "readOnlyTitle" }
  },
  {
    dataIndex: "isHide",
    scopedSlots: { customRender: "isHide" },
    slots: { title: "hideTitle" }
  }
];
export default {
  props: {
    data: {}
  },
  data() {
    return {
      loading: false,
      checksAllTitle: {
        isEdit: false,
        isReadOnly: false,
        isHide: false
      },
      formData: [],
      columns
    };
  },
  methods: {
    /**
     * 获取该组件数据
     */
    getData() {
      return this.formData;
    },
    /**
     * 设置数据
     */
    setData() {
      let formModuleAuths = this.data.data.formModuleAuths;
      if (formModuleAuths && formModuleAuths.length) {
        this.formData = this.$store.getters["approvalProcess/formDesign"].map(
          item => {
            let a = formModuleAuths.find(b => b.key === item.key);
            if (a) {
              return { ...item, ...a };
            } else {
              return {
                ...item,
                isEdit: true,
                isReadOnly: false,
                isHide: false
              };
            }
          }
        );
      } else {
        this.formData = this.$store.getters["approvalProcess/formDesign"]
          // .filter(item => {
          //   return item.data.isRegex;
          // })
          .map(item => {
            return {
              ...item,
              isEdit: true,
              isReadOnly: false,
              isHide: false
            };
          });
      }
    },
    /**
     * 标题Radio事件
     */
    handleChangeEdit(type) {
      this.checksAllTitle.isEdit = false;
      this.checksAllTitle.isReadOnly = false;
      this.checksAllTitle.isHide = false;
      this.checksAllTitle[type] = true;
      this.formData.map(item => {
        this.$set(item, "isEdit", false);
        this.$set(item, "isReadOnly", false);
        this.$set(item, "isHide", false);
        this.$set(item, type, true);
        return item;
      });
    },
    /**
     * 节点Radio事件
     */
    handleChildChange(type, record) {
      this.checksAllTitle.isEdit = false;
      this.checksAllTitle.isReadOnly = false;
      this.checksAllTitle.isHide = false;
      this.$set(record, "isEdit", false);
      this.$set(record, "isReadOnly", false);
      this.$set(record, "isHide", false);
      this.$set(record, type, true);
    }
  },
  mounted() {
    this.setData();
  }
};
</script>
<style lang="less" scoped>
.operation-authority {
  .operation-authority-radio {
    display: block;
    width: 14px;
    height: 14px;
  }
  .disp-inline {
    display: inline;
  }
}
</style>

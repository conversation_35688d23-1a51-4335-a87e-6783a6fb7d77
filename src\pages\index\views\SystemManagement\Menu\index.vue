<!--
* <AUTHOR>
* @time 2020-8-19
* @dec 菜单管理 - 用户管理页面
-->
<template>
  <a-card :bordered="false">
    <s-table
      ref="table"
      :showPagination="false"
      size="default"
      :columns="columns"
      :data="loadData"
    >
      <span slot="description" slot-scope="text">
        <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
      </span>

      <span slot="action" slot-scope="text, record">
        <template v-if="record.type !== 'resource'">
          <a @click="handleAddMenu(record)" v-auth="'mapi:security:menu:create'"
            >添加菜单</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:menu:saveMenuWithResource')"
          />
          <a
            @click="handleAddResources(record)"
            v-auth="'mapi:security:menu:saveMenuWithResource'"
            >配置资源</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:menu:update')"
          />
          <a
            @click="handleAddMenu(record, 'edit')"
            v-auth="'mapi:security:menu:update'"
            >修改</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:menu:deleteById')"
          />
          <a-spin
            v-if="record.parentId !== ''"
            style="display:inline-block"
            :spinning="!!record.handleDelLoading"
            v-auth="'mapi:security:menu:deleteById'"
          >
            <a-icon slot="indicator" type="loading" spin />
            <a-popconfirm
              placement="top"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDel(record)"
              title="确认删除?"
            >
              <a>删除</a>
            </a-popconfirm>
          </a-spin>
        </template>
        <template v-if="record.type === 'resource'">
          <a-spin
            :spinning="!!record.handleRemoveLoading"
            v-auth="'mapi:security:menu:deleteBindByResourceIdAndMenuId'"
          >
            <a-icon slot="indicator" type="loading" spin />
            <a-popconfirm
              placement="top"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleRemove(record)"
              title="确认移除?"
            >
              <a>移除</a>
            </a-popconfirm>
          </a-spin>
        </template>
      </span>
    </s-table>
    <!-- 添加菜单 -->
    <modal-add-menu
      ref="modalAddMenu"
      :visible="modal.modalAddMenu.visible"
      :data="modal.modalAddMenu.data"
      :type="modal.modalAddMenu.type"
      @cancel="
        () => {
          modal.modalAddMenu.visible = false;
          modal.modalAddMenu.data = {};
          $refs.modalAddMenu.form.resetFields();
        }
      "
      @ok="$refs.table.refresh()"
    />
    <!-- 添加资源 -->
    <modal-add-resources
      ref="modalAddResources"
      :visible="modal.modalAddResources.visible"
      :data="modal.modalAddResources.data"
      @cancel="
        () => {
          modal.modalAddResources.visible = false;
          modal.modalAddResources.data = {};
        }
      "
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table";
import Ellipsis from "@/components/Ellipsis";
// API接口
import {
  ApiSecurityFindMenu,
  ApiSecurityDeleteById,
  ApiSecurityDeleteBindByResourceIdAndMenuId
} from "@/pages//index/data/api/SystemManagement/Menu";
import { translateDataToTree } from "@/common/utils";

// 添加菜单
import ModalAddMenu from "./compontents/ModalAddMenu";
// 添加资源
import ModalAddResources from "./compontents/ModalAddResources";

const columns = [
  {
    title: "菜单",
    dataIndex: "name",
    scopedSlots: { customRender: "name" }
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "260px",
    scopedSlots: { customRender: "action" }
  }
];

export default {
  name: "SystemManagementMenu",
  components: {
    STable,
    Ellipsis,
    ModalAddMenu,
    ModalAddResources
  },
  data() {
    this.columns = columns;
    return {
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 加载数据方法 必须为 Promise 对象
      loadData: () => {
        const requestParameters = Object.assign({}, this.queryParam);
        return ApiSecurityFindMenu(requestParameters).then(res => {
          res.data = res.data.map(item => {
            item.key = item.id;
            return item;
          });
          res.data = translateDataToTree(res.data);
          return res;
        });
      },
      modal: {
        // 添加菜单
        modalAddMenu: {
          visible: false,
          type: undefined,
          data: {}
        },
        // 添加资源
        modalAddResources: {
          visible: false,
          data: {}
        }
      }
    };
  },
  created() {},
  computed: {},
  methods: {
    /**
     * 添加菜单
     */
    handleAddMenu(record, type) {
      this.modal.modalAddMenu.visible = true;
      this.modal.modalAddMenu.data = { ...record };
      this.modal.modalAddMenu.type = type;
    },
    /**
     * 添加资源
     */
    handleAddResources(record) {
      this.modal.modalAddResources.visible = true;
      this.modal.modalAddResources.data = { ...record };
    },
    /**
     * 删除
     */
    handleDel(record) {
      let { id } = record;
      record.handleDelLoading = true;
      ApiSecurityDeleteById({ id })
        .then(() => {
          this.$refs.table.refresh();
          this.$message.info("删除成功");
        })
        .catch()
        .finally(() => {
          record.handleDelLoading = false;
        });
    },
    /**
     * 移除
     */
    handleRemove(record) {
      let { id, parentId } = record;
      record.handleRemoveLoading = true;
      ApiSecurityDeleteBindByResourceIdAndMenuId({
        menuId: parentId,
        resourceIds: [id]
      })
        .then(() => {
          this.$refs.table.refresh();
          this.$message.info("移除成功");
        })
        .catch()
        .finally(() => {
          record.handleRemoveLoading = false;
        });
    }
  }
};
</script>

<template>
  <div>
    <div :class="module_name + '-search-statistics'">
      <div>
        <a-form :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row gutter="24">
            <a-col :span="12">
              <a-form-item>
                <a-range-picker allowClear @change="handleChange" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div :class="module_name + '-state'">
        <rectangle-block
          :type="1"
          :class="module_name + '-all'"
          title="全部"
          :quantity="totalNum"
          unit="条"
        ></rectangle-block>
        <rectangle-block
          :type="2"
          :class="module_name + '-processed'"
          title="已处理"
          :quantity="threeNum"
          unit="条"
        ></rectangle-block>
        <rectangle-block
          :type="3"
          :class="module_name + '-distributed'"
          title="已派发"
          :quantity="oneNum"
          unit="条"
        ></rectangle-block>
        <rectangle-block
          :type="4"
          :class="module_name + '-signed'"
          title="已签收"
          :quantity="twoNum"
          unit="条"
        ></rectangle-block>
        <rectangle-block
          :type="5"
          :class="module_name + '-to-be-processed'"
          title="待处理"
          :quantity="zeroNum"
          unit="条"
        ></rectangle-block>
      </div>
    </div>

    <div style="margin-top: 40px; background-color: #fff;">
      <div class="table-title">预警统计列表</div>
      <common-table
        ref="prewarningStatisticsTable"
        :columns="columns"
        :loadData="loadData"
        :showPagination="true"
      ></common-table>
      <!-- <s-table
        style="margin:20px;margin-top:unset;"
        ref="prewarningStatisticsTable"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        :rowKey="(record) => record.id"
      >
      </s-table> -->
    </div>
  </div>
</template>

<script>
import { ApiQueryWarningStatistics } from "APIs/PrewarningManagement";
import RectangleBlock from "@/components/RectangleBlock";
// import STable from "@/components/Table";
import CommonTable from "@/components/CommonTable/index.js";
import moment from "moment";
import * as utils from "@/common/utils/index.js";
export default {
  components: {
    RectangleBlock,
    CommonTable,
    // STable,
  },
  data() {
    return {
      labelCol: {
        span: 8,
      },
      wrapperCol: {
        span: 16,
      },
      module_name: "prewarning-statistics",
      queryParams: {
        startTime: null,
        endTime: null,
      },
      totalNum: 0,
      zeroNum: 0,
      oneNum: 0,
      twoNum: 0,
      threeNum: 0,
      columns: [
        {
          title: "序号",
          dataIndex: "serialNo",
          align: "center",
          width: 80,
        },
        {
          title: "园区名称",
          dataIndex: "parkName",
          align: "center",
        },
        {
          title: "总预警数（条）",
          dataIndex: "totalNum",
          align: "center",
        },
        {
          title: "待处理预警数（条）",
          dataIndex: "oneNum",
          align: "center",
        },
        {
          title: "处理中预警数（条）",
          dataIndex: "twoNum",
          align: "center",
        },
        {
          title: "已完成预警数（条）",
          dataIndex: "threeNum",
          align: "center",
        },
      ],
    };
  },
  methods: {
    loadData: function(queryParams) {
      const params = Object.assign(
        { currentPage: queryParams.pageNum },
        queryParams,
        this.queryParams
      );
      return ApiQueryWarningStatistics(params).then((res) => {
        this.totalNum = res.data?.totalNum || 0;
        this.zeroNum = res.data?.zeroNum || 0;
        this.oneNum = res.data?.oneNum || 0;
        this.twoNum = res.data?.twoNum || 0;
        this.threeNum = res.data?.threeNum || 0;
        let data = res.data?.warningStatisticsList;
        const records = data?.records || [];
        records.forEach((element) => {
          element["id"] = utils.getRandomNum("p", 8);
        });
        data["records"] = records;
        return data;
      });
    },
    // loadData: function(queryParams) {
    //   const params = Object.assign(
    //     { currentPage: queryParams.pageNum },
    //     queryParams,
    //     this.queryParams
    //   );
    //   return ApiQueryWarningStatistics(params).then((res) => {
    //     this.totalNum = res.data?.totalNum || 0;
    //     this.zeroNum = res.data?.zeroNum || 0;
    //     this.oneNum = res.data?.oneNum || 0;
    //     this.twoNum = res.data?.twoNum || 0;
    //     this.threeNum = res.data?.threeNum || 0;
    //     let data = res.data?.warningStatisticsList;
    //     const records = data?.records || [];
    //     records.forEach((element) => {
    //       element["id"] = utils.getRandomNum("p", 8);
    //     });
    //     data["records"] = records;
    //     return data;
    //   });
    // },
    handleChange: function(e) {
      let tmpStartTime = null;
      let tmpEndTime = null;
      if (e.length > 0) {
        tmpStartTime = moment(e[0]).format("YYYY-MM-DD HH:mm:ss");
        tmpEndTime = moment(e[1]).format("YYYY-MM-DD HH:mm:ss");
      } else {
        tmpStartTime = null;
        tmpEndTime = null;
      }
      this.$set(this.queryParams, "startTime", tmpStartTime);
      this.$set(this.queryParams, "endTime", tmpEndTime);
      this.$refs.prewarningStatisticsTable.search(this.queryParams);
    },
  },
};
</script>

<style lang="less" scoped>
.prewarning-statistics-search-statistics {
  background-color: #fff;
  padding: 32px;
  border-radius: 8px;
}
.prewarning-statistics-state {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 132px;
  border-radius: 16px;
  background-image: linear-gradient(95deg, #ffffff 0%, #ffffff 100%);
}
.prewarning-statistics-processed,
.prewarning-statistics-distributed,
.prewarning-statistics-signed,
.prewarning-statistics-to-be-processed {
  margin-left: 8px;
}
.table-title {
  font-size: 20px;
  font-weight: 600;
  color: #595959;
  padding: 20px;
  padding-right: unset;
}
</style>

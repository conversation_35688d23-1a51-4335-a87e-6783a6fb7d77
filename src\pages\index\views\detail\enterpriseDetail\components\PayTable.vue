// 信息查询--园区查询
<template>
  <a-card style="width: 100%; margin-top: 20px" :bordered="false">
    <s-table
      ref="table"
      size="default"
      :columns="columns"
      :data="loadData"
      :scroll="{ x: 1000 }"
      rowKey="key"
    >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * pageSize + index + 1 }}
      </span>
    </s-table>
  </a-card>
</template>

<script>
import { pageOneCompany } from "@/pages/demo/data/api/api/company";
import STable from "@/components/Table";
export default {
  components: {
    STable,
  },
  data() {
    return {
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 120,
          fixed: "left",
        },
        // {
        //   title: "缴纳时间",
        //   dataIndex: "createTime"
        // },
        {
          title: "缴纳期间",
          dataIndex: "paymentTime",
          align: "center",
        },
        // {
        //   title: "缴纳名称",
        //   dataIndex: "companyName"
        // },
        {
          title: "纳税金额(万元)",
          dataIndex: "taxation",
          align: "center",
          customRender: (text, row) => {
            return row.taxation.toFixed(2);
          },
        },
      ],
      id: "",
    };
  },
  created() {
    this.id = this.$route.query.id;
  },
  methods: {
    // 加载数据方法 必须为 Promise 对象
    loadData: function (values) {
      if (!this.id) {
        return;
      }
      this.pageNo = values.pageNo;
      this.pageSize = values.pageSize;
      const requestParameters = Object.assign({
        id: this.id,
        currentPage: values.pageNo,
        pageSize: values.pageSize,
      });
      console.log("---", this.queryParam);
      return pageOneCompany(requestParameters).then((res) => {
        let dataObj = res.data;
        dataObj.data = res.data.records;
        return dataObj;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.sort {
  text-align: right;
}
/deep/.ant-select {
  width: 10rem;
}
</style>

<template>
  <div>
    <a-modal
      :width="990"
      title="详情"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      okText="提交并关闭"
      cancelText="关闭"
      @cancel="handleCancel"
    >
      <div class="model-out">
        <div class="model-tit-out">
          <div class="model-tit">{{ ModalText.tenantry }}</div>
          <div class="con-icon con-icon-y">预警</div>
        </div>
        <div class="color-b">租赁到期预警</div>
      </div>
      <div style="width: 96%; margin: 0 auto">
        <a-table
          style="width: 100%"
          :columns="columns"
          :data-source="leaseData"
        >
        </a-table>
        <a-radio-group
          v-model="operateType"
          @change="onChange"
          style="margin-top: 20px; margin-left: 15px"
        >
          <a-radio :style="radioStyle" :value="1"> 关闭不再提醒 </a-radio>
          <a-radio :style="radioStyle" :value="2"> 近一个月内不再提醒 </a-radio>
          <a-radio :style="radioStyle" :value="3"> 临最后30天再提醒 </a-radio>
          <a-radio :style="radioStyle" :value="4">
            给出处理方案并关闭预警提醒
          </a-radio>
        </a-radio-group>
        <div style="margin-top: 30px; margin-left: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">
            <span v-if="operateType == 4" style="color: red">*</span>  处理意见 
          </div>
          <a-textarea
            v-model="remark"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { dealLeasesWaringApi } from "@/pages/index/data/api/home";
import moment from "moment";
const columns = [
  {
    title: "企业名称",
    dataIndex: "tenantry",
    align: "center",
    key: "tenantry",
  },
  {
    title: "租赁面积(㎡)",
    dataIndex: "leaseArea",
    align: "center",
    key: "leaseArea",
  },
  {
    title: "租赁开始日期",
    dataIndex: "leaseStartTime",
    align: "center",
    key: "leaseStartTime",
  },
  {
    title: "租赁结束日期",
    dataIndex: "leaseEndTime",
    align: "center",
    key: "leaseEndTime",
  },
  {
    title: "租赁地址",
    dataIndex: "leaseAddress",
    align: "center",
    key: "leaseAddress",
  },
];
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    textList: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      operateType: 1,
      radioStyle: {
        height: "30px",
        lineHeight: "30px",
      },
      ModalText: "Content of the modal",
      confirmLoading: false,
      remark: "",
      remindDate: "",
      leaseData: [],
      columns,
    };
  },
  created() {
    this.$watch("textList", () => {
      this.leaseData = this.textList;
      this.ModalText = this.textList[0];
      console.log(this.ModalText);
    });
  },
  mounted() {},
  methods: {
    moment,
    handleOk() {
      if (this.operateType == 4 && !this.remark) {
        this.$message.error("请输入处理意见后再进行提交！");
      } else {
        let parmas = {
          operateType: this.operateType,
          remark: this.remark,
          remindDate: this.remindDate,
          warningId: this.ModalText.warningId,
        };
        dealLeasesWaringApi(parmas).then(() => {
          this.$message.success("提交成功");
          this.$parent.getEarlyWarning();
          this.visible = false;
          this.operateType = 1;
        });
      }
      this.remark = "";
    },
    handleCancel() {
      this.visible = false;
      this.$emit("myself", this.visible);
      this.operateType = 1;
      this.remark = "";
    },
    onChange(v) {
      if (v.target.value == 2) {
        this.remindDate = moment(new Date())
          .add(1, "months")
          .format("YYYY-MM-DD");
      }
      if (v.target.value == 3) {
        this.remindDate = moment(this.ModalText.leaseEndTime)
          .subtract(30, "days")
          .format("YYYY-MM-DD");
      }
      console.log(this.remindDate);
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/carousel.css";

.model-out {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.model-tit-out {
  display: flex;
  margin-top: 15px;

  .model-tit {
    font-size: 22px;
    font-weight: 600;
    margin-right: 15px;
  }

  .con-icon {
    width: 50px;
    height: 23px;
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    border-radius: 4px;
    margin-top: 5px;
  }

  .con-icon-y {
    color: #fff;
    background-color: #ffa32e;
    border: 1px solid #ffa32e;
  }

  .con-icon-b {
    color: #fff;
    background-color: #0090ff;
    border: 1px solid #0090ff;
  }
}

.color-b {
  margin-top: 8px;
  font-weight: bold;
  color: #1677ff;
}

.con-fontw {
  margin-top: 15px;
  text-indent: 2em;
}
</style>

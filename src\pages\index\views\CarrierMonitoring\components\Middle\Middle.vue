<!--
 * @Description: 
 * @Author: cyw
 * @Date: 2023-09-19 17:47:30
 * @LastEditTime: 2023-10-12 13:01:55
 * @LastEditors: cyw
-->
<template>
  <div class="middleContent">
    <div class="tab">
      <a-tabs
        type="card"
        @change="callback"
        tabBarStyle="backgroundColor: white;"
      >
        <a-tab-pane :key="1" tab="镇属">
          <CommonTable typeLogo="1"></CommonTable>
        </a-tab-pane>
        <a-tab-pane :key="2" tab="区属">
          <CommonTable typeLogo="2"></CommonTable>
        </a-tab-pane>
        <a-tab-pane :key="3" tab="2头在外(本区)">
          <CommonTable typeLogo="3"></CommonTable>
        </a-tab-pane>
        <a-tab-pane :key="4" tab="非属地">
          <CommonTable typeLogo="4"></CommonTable>
        </a-tab-pane>
        <a-tab-pane :key="5" tab="2头在外(外区)">
          <CommonTable typeLogo="5"></CommonTable>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>
<script>
import CommonTable from "../Middle/CommonTable.vue";
export default {
  components: {
    CommonTable,
  },
};
</script>
<style scoped lang="less">
.middleContent {
  background-color: white;
  margin-top: 10px;
  border-radius: 10px;
  /deep/.ant-tabs-nav .ant-tabs-tab {
    background-color: #ffffff;
    border-radius: 30px;
    font-size: 14px;
    color: #000;
    padding: 0 15px;
    margin-right: 10px;
    border: none;
    height: 35px;
  }
  /deep/.ant-tabs-nav .ant-tabs-tab-active {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
  }
}
</style>

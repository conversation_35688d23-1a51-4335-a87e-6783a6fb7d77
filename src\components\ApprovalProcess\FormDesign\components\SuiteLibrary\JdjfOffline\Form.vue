<!--
 * <AUTHOR>
 * @time 2021-04-22
 * @dec 机电机房线下套件
-->
<template>
  <div>
    <a-table
      :columns="columns"
      :data-source="form.optionsData.regex"
    >
      <span slot="isRegex" slot-scope="text, record">
        <template>
          <a-switch v-model="record.isRegex" />
        </template>
      </span>
    </a-table>
  </div>
</template>
<script>
const columns = [
  {
    title: "字段",
    dataIndex: "name",
    key: "name",
    // width: 200,
    scopedSlots: { customRender: "name" }
  },
  {
    title: "是否验证",
    dataIndex: "isRegex",
    key: "isRegex",
    width: 100,
    scopedSlots: { customRender: "isRegex" }
  }
];

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      columns,
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        inputTitle: "机房资源套件",

        optionsData: {
          businessType: undefined,
          // 参与校验逻辑
          regex: [
            {
              name: "航站楼",
              isRegex: true
            },
            {
              name: "机房",
              isRegex: true
            },
            {
              name: "机房作业类别",
              isRegex: true
            }
          ]
        }
      }
    };
  },
  watch: {
    data: {
      handler: function(data) {
        if (data.optionsData && data.optionsData.regex) this.form = data;
      }
    },
    form: {
      handler: function() {
        setTimeout(() => {
          this.$emit("update:data", this.form);
        }, 50);
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="less" scoped></style>

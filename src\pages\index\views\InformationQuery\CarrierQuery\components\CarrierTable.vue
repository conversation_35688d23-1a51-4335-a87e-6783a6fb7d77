<template>
	<div>
		<div>
			<a-table
				ref="table"
				:columns="columns"
				:data-source="data"
				:scroll="{ x: 1500, y: 300 }"
			>
			</a-table>
      
      {{ $store.state.query }}
		</div>
	</div>
</template>
<script>
export default {
	data() {
		const columns = [
			{
				title: '序号',
				width: 100,
				dataIndex: 'name',
				key: 'name',
				fixed: 'left',
			},
			{
				title: '园区',
				width: 160,
				dataIndex: 'age',
				key: 'age',
				fixed: 'left',
				customCell: (text, record) => {
					return {
						style: {
							color: 'blue', 
						},
						on: {
							click: () => {
								//点击事件，也可以加其他事件
								console.log(22222222, record, text);
								this.$router.push(
									`/information-query/park-detail`
								);
							},
						},
					};
				},
			},
			{
				title: '楼号',
				dataIndex: 'address',
				key: '1',
				width: 150,
			},
			{
				title: '楼层',
				dataIndex: 'address',
				key: '2',
				width: 150,
			},
			{
				title: '房间号',
				dataIndex: 'address',
				key: '3',
				width: 150,
			},
			{
				title: '房间面积(平米)',
				dataIndex: 'address',
				key: '4',
				width: 150,
			},
			{
				title: '状态',
				dataIndex: 'address',
				key: '5',
				width: 150,
			},
			{
				title: '承租方',
				dataIndex: 'compony',
				key: '6',
				width: 150,
        customCell: (text, record) => {
					return {
						style: {
							color: 'blue',
						},
						on: {
							click: () => {
                //点击事件，也可以加其他事件
								console.log(22222222, record, text);
								this.$router.push(
									`/information-query/enterprise-detail`
								);
							},
						},
					};
				},
			},
			{
				title: '租赁单价(元/天/平米)',
				dataIndex: 'address',
				key: '7',
				width: 170,
			},
			{
				title: '租赁到期时间',
				dataIndex: 'address',
				key: '8',
			},
		];
		const data = [];
		for (let i = 0; i < 100; i++) {
			data.push({
				key: i,
				name: `${i + 1}`,
				age: '星联科技园',
				compony: '百度科技',
				address: `宜山路${i}`,
			});
		}
		return {
			data,
			columns,
		};
	},
	methods: {
		onRefresh() {
			console.log(1212);
			this.$emit('clickRefresh');
			this.$refs.table.refresh();
		},
	},
};
</script>
<style lang="less" scoped></style>

import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 从租赁表导入企业
 */
export function ApiLeaseToEnterPrise(params) {
  return api({
    url: BASE_URL + "/manager/companyDataInit/addCompanyFromLeaseInfo",
    method: "post",
    params,
  });
}
/**
 * 从税收表导入企业
 */
export function ApiTaxiToEnterPrise(params) {
  return api({
    url: BASE_URL + "/manager/companyDataInit/addCompanyFromTaxInfo",
    method: "post",
    params,
  });
}
/**
 * 启信宝同步基础信息
 */
export function ApiQixinBaoBaseInfo(params) {
  return api({
    url: BASE_URL + "/manager/companyDataInit/syncBasicInfoFromQXB",
    method: "post",
    params,
  });
}
/**
 * 更新企业类型信息
 */
export function ApiUpdateEnterPrise(params) {
  return api({
    url: BASE_URL + "/manager/companyDataInit/updateCompanyType",
    method: "post",
    params,
  });
}
/**
 * 导入税收监控企业
 */
export function ApiImportTaxInfomation(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxationWarning/syncWatchingCompanies",
    method: "post",
    params,
  });
}
/**
 * 导入税收预警数据
 */

export function ApiTaxWarningInfo(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxationWarning/syncWarningData",
    method: "post",
    params,
  });
}

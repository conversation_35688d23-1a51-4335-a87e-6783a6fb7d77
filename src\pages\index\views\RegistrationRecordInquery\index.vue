<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <div class="enterprise" v-else>
    <div class="enterpriseFrom">
      <div class="carrierFrom">
        <a-form
          :form="form"
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
          @submit="handleSubmit"
        >
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="园区名称">
                <a-input
                  allowClear
                  v-model="queryParam.parkName"
                  placeholder="请输入园区名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="承租方">
                <a-input
                  allowClear
                  v-model="queryParam.tenantry"
                  placeholder="请输入承租方名称"
                ></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="申请时间">
                <a-range-picker v-model="queryParam.applyTime" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="审核状态">
                <a-select
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  allowClear
                  v-model="queryParam.status"
                  placeholder="全部"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in auditMethodArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8"></a-col>
            <a-col :span="8" align="right">
              <a-form-item :wrapper-col="{ span: 24 }">
                <a-button
                  type="primary"
                  @click="search"
                  style="margin-right: 20px"
                  >查询</a-button
                >
                <a-button type="default" @click="reset">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
    <a-card style="width: 100%; margin-top: 20px">
      <div class="list-tit">
        <p>备案登记查询列表</p>
        <a-button
          type="primary"
          @click="toAdd(3)"
          v-if="roles.includes('实业公司')"
          style="font-size: 16px; height: 40px; border-radius: 6px"
          >新增备案登记</a-button
        >
      </div>
      <s-table
        ref="table"
        size="default"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        rowKey="id"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </span>
        <template slot="parkName" slot-scope="text">
          <a-tooltip>
            <template slot="title">
              {{ text }}
            </template>
            <div
              style="
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <template slot="lessor" slot-scope="text">
          <a-tooltip>
            <template slot="title">
              {{ text }}
            </template>
            <div
              style="
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <template slot="tenantry" slot-scope="text">
          <a-tooltip>
            <template slot="title">
              {{ text }}
            </template>
            <div
              style="
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <span slot="action" slot-scope="text, record">
          <a href="javascript:;" @click="toDetail(text, record, 1)">查看</a>
          <a
            v-if="record.status == 7"
            href="javascript:;"
            style="margin-left: 5px"
            @click="toUpload(record)"
          >
            上传合同
          </a>
          <a
            href="javascript:;"
            v-if="record.status == 0 || record.status == 9"
            style="margin-left: 5px"
            @click="toEdit(2, record)"
            >编辑</a
          >
          <a
            href="javascript:;"
            v-if="canDownloadPreliminary(record)"
            style="margin-left: 5px"
            @click="downloadPreliminary(record, false)"
            >下载初审单</a
          >
          <!-- <a
            :href="
              `${domain_url}/common/attachment/download?id=${record.preliminaryAttachment.id}`
            "
            v-if="
              record.preliminaryAttachment &&
                (record.status == 4 ||
                  record.status == 5 ||
                  record.status == 6 ||
                  record.status == 7 ||
                  record.status == 8 ||
                  record.status == 9)
            "
            style="margin-left: 5px"
            >下载初审单</a
          > -->
          <a
            href="javascript:;"
            v-if="canDownloadFlowTable(record)"
            style="margin-left: 5px"
            @click="downloadFlow(record)"
            >下载流转表</a
          >
          <!-- <a
            style="margin-left: 5px"
            v-if="record.status == 4"
            @click="toUpload(record)"
            >上传流转表</a
          > -->
        </span>
      </s-table>
    </a-card>
    <preliminary-form
      v-if="preliminaryVisible"
      :modalVisible="preliminaryVisible"
      :businessInfo="preliminaryInfo"
      :isDownload="isDownloadVisible"
      @closeModal="handlePreliminaryModal"
    ></preliminary-form>
    <flow-form
      v-if="flowVisible"
      :modalVisible="flowVisible"
      :businessInfo="flowInfo"
      @closeModal="handleFlowModal"
    ></flow-form>
    <!-- <UploadModal
      title="上传流转表"
      :visible="uploadVisible"
      @ok="uploadModalOk"
      @cancel="unloadCancel"
      businessId="workflow_form"
    >
    </UploadModal> -->
    <UploadModal
      title="上传合同"
      :visible="uploadVisible"
      accept=".pdf"
      @ok="uploadModalOk"
      @cancel="unloadCancel"
      businessId="review_completed_contract"
    >
    </UploadModal>
  </div>
</template>

<script>
import moment from "moment";
import {
  getListApi,
  updateContractStatusApi,
} from "@/pages/index/data/api/RegistrationRecordInquery";
import { ApiExportEnterSearch } from "@/pages/index/data/api/InfomationQuery/index";
import STable from "@/components/Table";
import PreliminaryForm from "@/pages/common/views/Template/PreliminaryForm.vue";
import FlowForm from "@/pages/common/views/Template/FlowTable.vue";
import UploadModal from "../RegistrationRecordReview/components/UploadModal.vue";
import { BASE_URL } from "Config";
export default {
  components: {
    STable,
    PreliminaryForm,
    FlowForm,
    UploadModal,
  },
  data() {
    return {
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles,
      domain_url: "",
      title: "上传流转表",
      uploadVisible: false,
      bussinessId: "workflow_form", //workflow_form  上传流转表
      isFlag: true,
      current: {},
      preliminaryVisible: false,
      isDownloadVisible: false,
      preliminaryInfo: {
        filingNumber: "",
      },
      flowVisible: false,
      flowInfo: {
        filingNumber: "",
      },
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: "60px",
          fixed: "left",
          align: "center",
        },
        {
          title: "园区",
          dataIndex: "parkName",
          scopedSlots: { customRender: "parkName" },
          width: 180,
          align: "center",
        },
        {
          title: "出租方",
          dataIndex: "lessor",
          width: 180,
          align: "center",
          scopedSlots: { customRender: "lessor" },
        },
        {
          title: "租赁面积(㎡)",
          dataIndex: "leaseArea",
          width: 120,
          align: "center",
        },
        {
          title: "承租方",
          dataIndex: "tenantry",
          width: 180,
          align: "center",
          scopedSlots: { customRender: "tenantry" },
        },
        {
          title: "合同类型",
          dataIndex: "contractTypeDesc",
          width: 120,
          align: "center",
        },
        {
          title: "审核方式",
          dataIndex: "auditType",
          align: "center",
          width: 110,
          customRender: (text) => {
            return text == 0
              ? "备案"
              : text == 1
              ? "审批"
              : text == 2
              ? "会审"
              : "";
          },
        },
        {
          title: "申请时间",
          dataIndex: "createTime",
          width: 140,
          align: "center",
        },
        {
          title: "审核状态",
          dataIndex: "statusDesc",
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          scopedSlots: { customRender: "action" },
          width: 160,
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      labelCol: { span: 4 },
      auditMethodArr: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "起草",
          value: 0,
        },
        {
          label: "发起",
          value: 1,
        },
        {
          label: "初审",
          value: 2,
        },
        {
          label: "复核",
          value: 3,
        },
        // {
        //   label: "上传流转表",
        //   value: 4,
        // },
        {
          label: "镇领导审核",
          value: 5,
        },
        // {
        //   label: "经发公司通知",
        //   value: 6,
        // },
        {
          label: "上传合同",
          value: 7,
        },
        {
          label: "完成",
          value: 8,
        },
        {
          label: "经发驳回",
          value: 9,
        },
        // {
        //   label: "经发公司上传初审表",
        //   value: 11,
        // },
        {
          label: "镇领导复核",
          value: 12,
        },
        {
          label: "上传会议纪要",
          value: 13,
        },
        {
          label: "上传会议材料",
          value: 14,
        },
      ],
      defaultValue: "排序",
      enumerateObj: {
        parkList: [],
      },
      queryParam: {
        parkName: undefined, //园区名称
        applyTime: [], //申请时间
        createTimeStart: undefined, //申请时间开始
        createTimeEnd: undefined, //申请时间结束
        queryEntity: 0, //查询实体：0 实业公司, 1 经发公司
        tenantry: undefined, //租赁企业
        status: undefined, //审核方式
      },
      natureArr: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: (values) => {
        this.pageNo = values.pageNo;
        this.pageSize = values.pageSize;
        if (this.queryParam.applyTime?.length > 0) {
          this.queryParam.createTimeStart = moment(
            this.queryParam.applyTime[0]
          ).format("YYYY-MM-DD 00:00:00");
          this.queryParam.createTimeEnd = moment(
            this.queryParam.applyTime[1]
          ).format("YYYY-MM-DD 23:59:59");
        } else {
          this.queryParam.applyTime = [];
          this.queryParam.createTimeStart = undefined;
          this.queryParam.createTimeEnd = undefined;
        }

        const requestParameters = Object.assign(
          {
            currentPage: values.pageNo,
            pageSize: values.pageSize,
            queryEntity: 0,
          },
          this.queryParam
        );
        return getListApi(requestParameters).then((res) => {
          let dataObj = res.data;
          dataObj.data = res.data.records || [];
          return dataObj;
        });
      },
    };
  },
  mounted() {
    this.$set(this, "domain_url", BASE_URL);
  },
  methods: {
    moment,
    toUpload(record) {
      this.current = record;
      this.uploadVisible = true;
    },
    uploadModalOk(fileList) {
      this.modelValue = false;
      this.handleFileCallback(fileList, this.current);
    },
    unloadCancel() {
      this.uploadVisible = false;
    },
    deduced() {
      ApiExportEnterSearch(this.queryParam);
    },
    reset() {
      (this.queryParam = {
        parkName: undefined, //园区名称
        applyTime: [], //申请时间
        createTimeStart: undefined, //申请时间开始
        createTimeEnd: undefined, //申请时间结束
        queryEntity: 0, //查询实体：0 实业公司, 1 经发公司
        tenantry: undefined, //租赁企业
        status: undefined, //审核方式
      }),
        this.$refs.table.refresh(true);
    },
    search() {
      console.log(this.queryParam);
      this.$refs.table.refresh(true);
    },
    toDetail(text, record, n) {
      console.log(text, record);
      this.$router.push(
        `/registration-record-inquiry/query/detail?statu=` +
          n +
          `&lessor=` +
          record.lessor +
          `&id=` +
          record.id +
          `&tenantry=` +
          record.tenantry +
          "&filingNumber=" +
          record.filingNumber
      );
    },
    toAdd(n) {
      this.$router.push(`/registration-record-inquiry/query/add?statu=` + n);
    },
    toEdit(n, record) {
      this.$router.push(
        `/registration-record-inquiry/query/edit?statu=` +
          n +
          `&lessor=` +
          record.lessor +
          `&contractType=` +
          record.contractType +
          `&id=` +
          record.id +
          `&tenantry=` +
          record.tenantry
      );
    },
    handlePreliminaryModal: function () {
      this.$set(this, "preliminaryInfo", {});
      this.$set(this, "preliminaryVisible", false);
    },
    canDownloadPreliminary: function (record) {
      let canDownload = false;
      //短流程
      if (record.auditType == 0) {
        if (record.status == 7 || record.status == 8) {
          canDownload = true;
        }
        //长流程
      } else if (record.auditType == 1) {
        if (
          record.status == 5 ||
          record.status == 12 ||
          record.status == 7 ||
          record.status == 8
        ) {
          canDownload = true;
        }
        //超长流程
      } else if (record.auditType == 2) {
        if (
          record.status == 5 ||
          record.status == 12 ||
          record.status == 13 ||
          record.status == 7 ||
          record.status == 8
        ) {
          canDownload = true;
        }
      }
      return canDownload;
    },
    downloadPreliminary: function (record, v) {
      this.$set(this, "preliminaryVisible", true);
      this.$set(this, "isDownloadVisible", v);
      this.$set(this, "preliminaryInfo", { filingNumber: record.filingNumber });
    },
    canDownloadFlowTable: function (record) {
      let canDownload = false;
      //长流程/超长流程
      if (record.auditType == 1 || record.auditType == 2) {
        if (record.status == 7 || record.status == 8) {
          canDownload = true;
        }
      }
      return canDownload;
    },
    downloadFlow: function (record) {
      console.log(record, "~~~~");
      this.$set(this, "flowVisible", true);
      this.$set(this, "flowInfo", { filingNumber: record.filingNumber });
    },
    handleFlowModal: function () {
      this.$set(this, "flowInfo", {});
      this.$set(this, "flowVisible", false);
    },
    handleFileCallback(file, record) {
      console.log("上传", file);
      if (this.isFlag) {
        this.isFlag = false;
        if (file.length > 0) {
          let params = {
            id: record.id,
            status: 8,
            attachmentList: file,
          };
          updateContractStatusApi(params).then((res) => {
            if (res.code === 0) {
              this.$message.success("上传成功！");
              this.$refs.table.refresh(true);
              this.unloadCancel();
            } else {
              this.$message.error(res.data.msg || "上传失败");
            }
          });
        } else {
          this.$message.warning("请先上传合同");
        }
        setTimeout(() => {
          this.isFlag = true;
        }, 2000);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise {
  display: flex;
  flex-wrap: wrap;

  .enterpriseFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }

  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;

    .sort {
      margin-left: auto;

      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }

  .table {
    width: 100%;
    margin-top: 10px;
  }

  .list-tit {
    display: flex;
    justify-content: space-between;

    p {
      font-size: 20px;
    }
  }
}

.ellipse {
  width: 100%;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 这里是超出几行省略 */
}
</style>

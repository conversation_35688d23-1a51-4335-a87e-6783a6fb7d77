<template>
  <div class="container" v-if="status">
    <div class="loading" v-if="isLoading">
      <a-spin size="large" />
    </div>
    <div class="container" v-else>
      <div class="statistics">
        统计信息
      </div>
      <a-descriptions title="接口测通统计">
        <a-descriptions-item label="总接口数据">{{
          total
        }}</a-descriptions-item>
        <a-descriptions-item label="接口通过数">{{
          suceedInfo.count
        }}</a-descriptions-item>
        <a-descriptions-item label="接口通过率">{{
          suceedInfo.rate
        }}</a-descriptions-item>
        <a-descriptions-item label="接口失败数">{{
          failureInfo.count
        }}</a-descriptions-item>
        <a-descriptions-item label="接口失败率">{{
          failureInfo.rate
        }}</a-descriptions-item>
      </a-descriptions>
      <div class="error-list-title">
        错误信息列表
      </div>
      <template v-for="(item, index) in failureArr">
        <div :key="index">
          <a-descriptions title="错误接口">
            <a-descriptions-item :label="接口名称">{{
              item.label
            }}</a-descriptions-item>
            <a-descriptions-item label="错误码">{{
              item.code
            }}</a-descriptions-item>
            <a-descriptions-item label="错误信息">{{
              item.msg
            }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </template>
    </div>
  </div>
  <div v-else>
    <div class="form-item">
      <a-button type="primary" @click="queryCompanyIndustry"
        >辖区企业性质分布信息</a-button
      >
    </div>
    <div class="form-item">
      <a-button type="primary" @click="queryEnterpriseDistribution"
        >辖区企业行业分布信息</a-button
      >
    </div>
    <div class="form-item">
      <a-button type="primary" @click="queryJurisdictionMsg"
        >辖区的大屏显示的信息</a-button
      >
    </div>
    <div class="form-item">
      <a-button type="primary" @click="queryScreenParkMsg"
        >获取大屏展示所有园区数据</a-button
      >
    </div>
    <div class="form-item">
      <a-button type="primary" @click="queryTownTaxData">辖区税收趋势</a-button>
    </div>
    <a-result></a-result>
  </div>
</template>

<script>
import {
  ApiQueryCompanyIndustry,
  ApiQueryEnterpriseDistribution,
  ApiQueryJurisdictionMsg,
  ApiQueryScreenParkMsg,
  ApiQueryTownTaxData,
  ApiGetSettledByTerritoryAndMonth,
  ApiGetTownOverView,
  ApiParkInfo,
  ApiParkTaxation,
} from "APIs/PortalApi";
export default {
  data() {
    return {
      status: true,
      isLoading: false,
      total: 0,
      suceedInfo: {
        count: 0,
        rate: "100%",
      },
      failureInfo: {
        count: 0,
        rate: "0%",
      },
      failureArr: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function() {
      this.isLoading = true;
      this.queryCompanyIndustry();
    },
    calculateData: function(response, name, callback) {
      const total = this.total;
      const count = this.suceedInfo.count;
      const failureCount = this.failureInfo.count;
      let failureArr = this.failureArr;
      let tmpTotal = total + 1; //统计接口总数
      this.$set(this, "total", tmpTotal);
      if (response) {
        if (response.code == 0) {
          this.$set(this.suceedInfo, "count", count + 1); //统计成功接口数
        } else {
          this.$set(this.failureInfo, "count", failureCount + 1); //统计失败接口数
          const label = name + "接口";
          const code = response.code;
          const msg = response.msg;
          let result = {
            label,
            code,
            msg,
          };
          failureArr.push(result);

          this.$set(this.suceedInfo, "rate", this.suceedInfo.count / tmpTotal); //统计成功率
          this.$set(
            this.failureInfo,
            "rate",
            this.failureInfo.count / tmpTotal
          ); //统计失败率
          this.$set(this, "failureArr", failureArr); //统计失败数组数据
        }
      }

      callback();
    },
    //辖区企业性质分布信息
    queryCompanyIndustry: function() {
      ApiQueryCompanyIndustry().then(
        (res) => {
          this.calculateData(
            res,
            "辖区企业性质分布信息",
            function() {
              this.queryEnterpriseDistribution();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "辖区企业性质分布信息",
            function() {
              this.queryEnterpriseDistribution();
            }.bind(this)
          );
        }
      );
    },
    //辖区企业行业分布信息
    queryEnterpriseDistribution: function() {
      ApiQueryEnterpriseDistribution().then(
        (res) => {
          this.calculateData(
            res,
            "辖区企业行业分布信息",
            function() {
              this.queryJurisdictionMsg();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "辖区企业行业分布信息",
            function() {
              this.queryJurisdictionMsg();
            }.bind(this)
          );
        }
      );
    },
    //辖区的大屏显示的信息
    queryJurisdictionMsg: function() {
      ApiQueryJurisdictionMsg().then(
        (res) => {
          this.calculateData(
            res,
            "辖区的大屏显示的信息",
            function() {
              this.queryScreenParkMsg();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "辖区的大屏显示的信息",
            function() {
              this.queryScreenParkMsg();
            }.bind(this)
          );
        }
      );
    },
    //获取大屏展示所有园区数据
    queryScreenParkMsg: function() {
      ApiQueryScreenParkMsg().then(
        (res) => {
          this.calculateData(
            res,
            "获取大屏展示所有园区数据",
            function() {
              this.queryTownTaxData();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "获取大屏展示所有园区数据",
            function() {
              this.queryTownTaxData();
            }.bind(this)
          );
        }
      );
    },
    //辖区税收趋势
    queryTownTaxData: function() {
      ApiQueryTownTaxData().then(
        (res) => {
          this.calculateData(
            res,
            "辖区税收趋势",
            function() {
              this.getSettledByTerritoryAndMonth();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "辖区税收趋势",
            function() {
              this.getSettledByTerritoryAndMonth();
            }.bind(this)
          );
        }
      );
    },
    //根据年月份获取辖区企业入驻总数
    getSettledByTerritoryAndMonth: function() {
      ApiGetSettledByTerritoryAndMonth().then(
        (res) => {
          this.calculateData(
            res,
            "辖区根据年月份获取辖区企业入驻总数",
            function() {
              this.getTownOverView();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "辖区根据年月份获取辖区企业入驻总数",
            function() {
              this.getTownOverView();
            }.bind(this)
          );
        }
      );
    },
    //辖区总体概览
    getTownOverView: function() {
      ApiGetTownOverView().then(
        (res) => {
          this.calculateData(
            res,
            "辖区总体概览",
            function() {
              // this.queryTownTaxData();
              this.queryParkInfo();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "辖区总体概览",
            function() {
              // this.queryTownTaxData();
              this.queryParkInfo();
            }.bind(this)
          );
        }
      );
    },
    //园区-中间展示信息
    queryParkInfo: function() {
      ApiParkInfo({ screenParkName: "天华信息科技园" }).then(
        (res) => {
          this.calculateData(
            res,
            "园区-中间展示信息",
            function() {
              // this.queryTownTaxData();
              this.queryParkTaxation();
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "园区-中间展示信息",
            function() {
              // this.queryTownTaxData();
              this.queryParkTaxation();
            }.bind(this)
          );
        }
      );
    },
    //园区-三年税收信息
    queryParkTaxation: function() {
      ApiParkTaxation({ screenParkName: "天华信息科技园" }).then(
        (res) => {
          this.calculateData(
            res,
            "园区-三年税收信息",
            function() {
              // this.queryTownTaxData();
              this.$set(this, "isLoading", false);
            }.bind(this)
          );
        },
        (result) => {
          this.calculateData(
            result.data,
            "园区-三年税收信息",
            function() {
              // this.queryTownTaxData();
              this.$set(this, "isLoading", false);
            }.bind(this)
          );
        }
      );
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  margin-top: 40px;
}
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.form-item {
  margin-bottom: 40px;
  font-size: 18px;
}
.failure-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 18px;
  font-weight: 400;
  .normal {
    margin-left: 20px;
  }
}
.error-list-title,
.statistics {
  margin-top: 40px;
  margin-bottom: 40px;
  font-size: 24px;
  font-weight: 600;
}
</style>

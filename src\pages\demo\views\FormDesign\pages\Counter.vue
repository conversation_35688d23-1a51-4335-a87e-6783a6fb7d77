<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 计数器
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <counter-dom :data="formData"></counter-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <counter-form v-bind:data.sync="formData"></counter-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 计数器控件 DOM/Form
import {
  CounterDom,
  CounterForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/Counter";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    CounterDom,
    CounterForm
  },
  data() {
    return {
      formData: {
        inputTitle: "", //标题
        isNeed: true, //是否必填
        placeholder: {
          placeholderText: ""
        },
        isRegex: true, //是否验证
        optionsData: {
          max: "", //最大值
          min: "", //最小值
          step: "", //步长
          precision: "" //精度
        }
      },
      moduleVoList: []
    };
  },
  mounted() {
    this.moduleVoList = [];
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: JSON.stringify(this.formData.placeholder), //提示文字
        optionsData: JSON.stringify(this.formData.optionsData), //文件信息
        notNull: this.formData.isNeed ? 1 : 0, //是否必填
        isRegex: this.formData.isRegex ? 1 : 0, //是否验证
        inputType: "counter",
        inputName: "1"
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data).then(() => {
        this.$message.info("保存成功");
      });
    },
    handCheck() {
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        let allData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "counter") {
              let itemData = {
                placeholder: JSON.parse(item.placeholder),
                inputTitle: item.inputTitle,
                isNeed: item.notNull,
                isRegex: item.isRegex,
                optionsData: JSON.parse(item.optionsData)
              };
              allData.push(itemData);
            }
          });
        });
        this.formData = allData[allData.length - 1];
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

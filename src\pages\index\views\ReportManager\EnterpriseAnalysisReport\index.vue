<!-- 报表管理--企业分析报表 -->
<template>
  <div class="park">
    <div class="enterpriseFrom">
      <a-form
        class="all"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-row :gutter="25" align="center">
          <a-col :span="8">
            <a-form-item label="统计周期" class="label">
              <a-space direction="vertical" :size="12">
                <a-range-picker
                  allowClear
                  picker="month"
                  format="YYYY-MM"
                  :placeholder="['开始月份', '结束月份']"
                  :value="value"
                  style="width: 100%"
                  @change="handleMonthChange"
                />
              </a-space>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所属分部" class="label">
              <a-select
                allowClear
                v-model="queryParam.parkName"
                style="width: 100%"
                placeholder="全部"
                :getPopupContainer="
                  triggerNode => {
                    return triggerNode.parentNode || document.body;
                  }
                "
              >
                <a-select-option
                  :value="item.name"
                  v-for="item in parkArr"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="企业标签" class="label">
              <a-select
                v-model="queryParam.companyLabel"
                placeholder="全部"
                @change="getCharacter($event)"
                maxTagCount="1"
                maxTagTextLength="6"
                style="width: 100%"
                allowClear
                :getPopupContainer="
                  triggerNode => {
                    return triggerNode.parentNode || document.body;
                  }
                "
              >
                <a-select-option
                  :value="character.value"
                  v-for="character in characterArr"
                  :key="character.value"
                  >{{ character.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- todo 等待联调 -->
        <a-row :gutter="25" align="center">
          <a-col :span="8">
            <a-form-item label="税收区间" class="label">
              <a-select
                v-model="queryParam.taxAmountType"
                @change="getKinds($event)"
                style="width: 100%"
                placeholder="全部"
                allowClear
                :getPopupContainer="
                  triggerNode => {
                    return triggerNode.parentNode || document.body;
                  }
                "
              >
                <a-select-option
                  :value="kinds.value"
                  v-for="kinds in enterprisesKinds"
                  :key="kinds.value"
                  >{{ kinds.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="是否重点企业" class="label">
              <a-select
                v-model="queryParam.keypointCompany"
                @change="getImportant($event)"
                style="width: 100%"
                placeholder="全部"
                allowClear
                :getPopupContainer="
                  triggerNode => {
                    return triggerNode.parentNode || document.body;
                  }
                "
              >
                <a-select-option
                  :value="important.value"
                  v-for="important in importantEnterprises"
                  :key="important.value"
                  >{{ important.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="25" type="flex" align="center">
          <a-col :span="8" :offset="15" flex="0.9">
            <div class="btnGroup">
              <a-button type="primary" class="query" @click="query()">
                查询
              </a-button>
              <a-button type="primary" class="query reset" @click="reset()"
                >重置</a-button
              >
              <a-button type="primary" class="export" @click="deduced()"
                >导出</a-button
              >
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="tablePart">
      <a-card style="width: 100%">
        <s-table
          ref="reportTable"
          size="default"
          rowKey="id"
          :columns="columns"
          :scroll="{ x: 1200, y: 800 }"
          :data="loadData"
        >
          <span slot="ordinal" slot-scope="text, record, index">
            {{ (pageNo - 1) * pageSize + index + 1 }}
          </span>
        </s-table>
      </a-card>
    </div>
  </div>
</template>

<script>
// import EnterprisePublicFrom from "../compontents/EnterprisePublicFrom.vue";
// import PublicTable from "../compontents/PublicTable.vue";
// import { store } from "@/data/store";
import STable from "@/components/Table";
import { parseNumFloat } from "@/common/utils/utils.js";
import { ApiGetDictByParentCode } from "@/pages/index/data/api/Common/index";
import {
  ApiGetEnterListReport,
  ApiExportEnterReport
} from "@/pages/index/data/api/ReportManagement/EnterpriseAnalysisReport";
import { ApiGetIndustryCompany } from "@/pages/index/data/api/InfomationQuery";
import * as utils from "@/common/utils/index.js";
export default {
  components: {
    // EnterprisePublicFrom,
    // PublicTable
    STable
  },
  data() {
    return {
      value: null,
      mode: ["month", "month"],

      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      parkArr: [
        {
          name: "星联科技园",
          value: "星联科技园"
        },
        {
          name: "桂林科技园",
          value: "桂林科技园"
        },
        {
          name: "虹三产业园",
          value: "虹三产业园"
        },
        {
          name: "华泾广场",
          value: "华泾广场"
        }
      ],
      territorialEnterprises: [
        {
          name: "是",
          value: 1
        },
        {
          name: "否",
          value: 0
        }
      ],
      enterprisesKinds: [
        {
          name: "小于200万",
          value: 1
        },
        {
          name: "200万-500万",
          value: 2
        },
        {
          name: "500万以上",
          value: 3
        }
      ],
      importantEnterprises: [
        {
          name: "是",
          value: 1
        },
        {
          name: "否",
          value: 0
        }
      ],
      characterArr: [],
      queryParam: {
        period: undefined,
        parkName: undefined,
        companyNature: undefined,
        dependencyCompany: undefined,
        companyDistribute: undefined,
        keypointCompany: undefined,
        startBirthdayTime: undefined,
        endBirthdayTime: undefined
      },
      defaultValue: "排序",
      sortArr: [
        {
          value: "1",
          name: "按注册资本排序"
        },
        {
          value: "2",
          name: "按税收金额排序"
        }
      ],
      columns: [
        {
          title: "序号",
          width: 120,
          scopedSlots: { customRender: "ordinal" },
          fixed: "left",
          align: "center"
        },
        {
          title: "企业名称",
          width: 250,
          dataIndex: "companyName",
          align: "center"
        },
        {
          title: "所属分部（园区一级）",
          dataIndex: "parkName",
          width: 200,
          align: "center"
        },
        {
          title: "企业标签",
          dataIndex: "companyLabel",
          align: "center",
          width: 200
        },
        {
          title: "注册资本",
          dataIndex: "registerCapital",
          align: "center",
          width: 240
        },
        {
          title: "是否重点企业",
          dataIndex: "keypointCompany",
          align: "center",
          width: 150,
          customRender: function(text) {
            if (text === 0) {
              return "否";
            } else if (text === 1) {
              return "是";
            } else {
              return "否";
            }
          }
        },
        {
          title: "总税收（万元）",
          dataIndex: "taxAmount",
          align: "center",
          width: 150,
          customRender(text) {
            return parseNumFloat(text);
          }
        },
        {
          title: "同比增减幅（%）",
          dataIndex: "taxAmountDeltaRate",
          align: "center",
          width: 150,
          customRender(text) {
            return parseNumFloat(text);
          }
        },
        {
          title: "同比增减量（万元）",
          dataIndex: "taxAmountDelta",
          align: "center",
          width: 160,
          customRender(text) {
            return parseNumFloat(text);
          }
        },
        {
          title: "区税收（万元）",
          dataIndex: "districtTaxAmount",
          align: "center",
          width: 150,
          customRender(text) {
            return parseNumFloat(text);
          }
        },
        {
          title: "注册情况",
          dataIndex: "registrationStatus",
          width: 150,
          align: "center"
        }
      ],
      refreshTable: false
    };
  },
  created() {
    this.init();
  },
  methods: {
    handleMonthChange(dates, dateStrings) {
      console.log(dates, dateStrings, "mmmmm");
      // if (dates.length === 2) {
        const [start, end] = dateStrings;
        // 更新dateValue
        this.value = [start, end];
        this.queryParam.startYearMonth = this.value[0];
        this.queryParam.endYearMonth = this.value[1];
        // const startYear = start.split("-")[0];
        // const endYear = end.split("-")[0];

        // if (startYear !== endYear) {
        //   this.value = []; // 重置日期
        //   alert("请选择同一年份下的月份");
        // } else {
        //   // 更新dateValue
        //   this.value = [start, end];
        //   this.queryParam.startYearMonth = this.value[0];
        //   this.queryParam.endYearMonth = this.value[1];
        // }
      // }
    },
    async init() {
      let parkArr = await ApiGetIndustryCompany();
      this.parkArr = parkArr.data.map(item => {
        return {
          name: item,
          value: item
        };
      });
      ApiGetDictByParentCode({ dictCode: "company_label" }).then(res => {
        this.characterArr = res.data;
      });
      console.log(this.parkArr, "this.parkArray");
    },
    //导出按钮
    deduced() {
      // let formData = new FormData()
      // Object.keys(this.queryParam).forEach((key) => {
      //   if (this.queryParam[key]) {
      //     formData.append(key, this.queryParam[key])
      //   }
      // })
      ApiExportEnterReport(this.queryParam)
        .then(result => {
          console.log("res", result.data);
          // let link = document.createElement("a")
          // link.href = window.URL.createObjectURL(new Blob([result]))
          // link.target = "_blank"
          // link.download = "企业分析报表.xls"
          // document.body.appendChild(link)
          // link.click()
          // document.body.removeChild(link)
          this.$message.success("导出成功！");
        })
        .catch(err => {
          console.log(err, "err");
        });
    },
    getOption(e) {
      this.defaultValue = e;
    },
    loadData: function(values) {
      this.pageNo = values.pageNo;
      this.pageSize = values.pageSize;
      const requestParameters = Object.assign(
        { currentPage: values.pageNo, pageSize: values.pageSize },
        this.queryParam
      );
      return ApiGetEnterListReport(requestParameters).then(res => {
        let dataObj = res.data;
        res.data.records.forEach(element => {
          element["id"] = utils.getRandomNum("t", 8);
        });
        dataObj.data = res.data.records;
        return dataObj;
      });
    },
    //查询按钮
    query() {
      console.log(this.queryParam, "2222");
      const endYear = this.queryParam.endYearMonth?.split("-")[0];
      const startYear = this.queryParam.startYearMonth?.split("-")[0];
      if (startYear !== endYear) {
        this.$message.info("选择的日期不能跨年，请重新选择！");
      } else {
        this.queryParam = {
          ...this.queryParam
        };
        this.refreshTable = true;
        this.$refs.reportTable.loadData({}, this.queryParam);
        console.log(this.queryParam, "2222");
      }
    },
    reset: function() {
      const tmpqueryParam = {
        period: undefined,
        parkName: undefined,
        companyNature: undefined,
        dependencyCompany: undefined,
        companyDistribute: undefined,
        keypointCompany: undefined,
        startYearMonth: undefined,
        endYearMonth: undefined
      };
      this.value = [];
      this.queryParam = tmpqueryParam;
      this.refreshTable = false;
      this.$refs.reportTable.loadData({}, this.queryParam);
    }
    //导出按钮
    // deduced() {},
    // getOption(e) {
    //   this.defaultValue = e;
    // },
  }
};
</script>

<style lang="less" scoped>
.park {
  display: flex;
  flex-wrap: wrap;
  .enterpriseFrom {
    width: 100%;
    height: 12rem;
    border-width: 0px;
    position: absolute;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    border: none;
    border-radius: 2px;
    padding-right: 25px;

    .all {
      // display: flex;
      // flex-wrap: wrap;
      margin-top: 0.8rem;
      // margin-left: 0.5rem;
      .label {
        // display: flex;
        // flex-wrap: nowrap;
        // margin-right: 30px;
      }
      .btnGroup {
        // margin-left: auto;
        margin-top: 8px;
        float: right;
        margin-right: -25px;
        .query {
          margin-right: 10px;
        }
      }
    }
  }
  .tablePart {
    margin-top: 200px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
// /deep/.ant-select-selection--single {
//   width: 12rem;
// }
// /deep/.ant-select-selection--multiple {
//   width: 11rem;
// }
// /deep/.ant-form-item-control {
//   width: 11rem;
// }
</style>

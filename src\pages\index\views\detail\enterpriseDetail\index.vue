<template>
  <div>
    <a-tabs default-active-key="1" @change="callback">
      <a-tab-pane key="1" tab="基本信息">
        <basic-Information style="background:#fff"></basic-Information>
      </a-tab-pane>
      <a-tab-pane key="2" tab="入驻园区" force-render>
        <do-park></do-park>
      </a-tab-pane>
      <!-- <a-tab-pane key="4" tab="关联企业" force-render>
        <about-company></about-company>
      </a-tab-pane> -->
      <a-tab-pane key="3" tab="税收信息">
        <div style="margin-bottom:10px;">基础信息</div>
        <pay-Information></pay-Information>
        <div style="margin-bottom:10px;margin-top:10px;">纳税记录</div>
        <pay-table></pay-table>
      </a-tab-pane>
      <a-tab-pane key="4" tab="关联企业">
        <relation-componey style="background:#fff"></relation-componey>
      </a-tab-pane>
      <a-tab-pane key="5" tab="企业预警">
        <warning-component style="background:#fff"></warning-component>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import BasicInformation from "./components/BasicInformation.vue"
import DoPark from "./components/DoPark.vue"
import PayInformation from "./components/PayInformation.vue"
import PayTable from "./components/PayTable.vue"
import RelationComponey from "./components/RelationComponey.vue"
import WarningComponent from "./components/WarningComponent"
export default {
  components: {
    BasicInformation,
    PayInformation,
    PayTable,
    DoPark,
    RelationComponey,
    WarningComponent
  },
  methods: {
    callback(key) {
      console.log(key)
    }
  }
}
</script>

<style lang="less" scoped>
/depp/ .ant-tabs-nav-scroll {
  background: #fff;
}
</style>

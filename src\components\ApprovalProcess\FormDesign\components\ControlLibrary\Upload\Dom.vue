<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 上传控件Dom
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :help="
        data.optionsData.fileNum || data.optionsData.fileSize
          ? uploadTip
          : false
      "
      :label="data.inputTitle || '上传'"
      style="margin-bottom:unset"
      prop="fileList"
      :rules="[
        {
          required: data.notNull,
          message: '请上传文件！',
          trigger: 'change'
        }
      ]"
    >
      <a-button type="primary">
        点击上传
      </a-button>
      <div
        v-if="
          data.optionsData.fileNum ||
            data.optionsData.fileSize ||
            data.optionsData.checkedList
        "
      ></div>
      <div>
        {{
          (data.placeholder && data.placeholder.placeholderText) ||
            "请输入提示文字"
        }}
      </div>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      uploadTip: [] //文件上传提示
    };
  },
  watch: {
    "data.optionsData": {
      handler: function(flag) {
        if (flag) {
          this.uploadTip = [];
          if (this.data.optionsData.fileNum) {
            let tip = "最多可上传" + this.data.optionsData.fileNum + "个";
            this.uploadTip.push(tip);
          }
          if (this.data.optionsData.fileSize) {
            let tip =
              "每个文件大小不超过" + this.data.optionsData.fileSize + "MB";
            this.uploadTip.push(tip);
          }
          this.uploadTip = this.uploadTip.join(",");
          console.log("上传控件属性", this.data.optionsData, this.uploadTip);
        }
      },
      deep: true
    }
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

<!--
* <AUTHOR>
* @time 2020-9-10
* @dec 门户管理 - 首页管理 - 快捷入口管理 - 添加/修改
-->
<template>
  <a-modal
    :title="modelTitle"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel')
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="审批模板名称">
          <a-spin :spinning="templateNameloading">
            <a-select
              :getPopupContainer="
                (triggerNode) => {
                  return triggerNode.parentNode || document.body
                }
              "
              ref="select"
              placeholder="请选择审批模板"
              allowClear
              @change="handleChange"
              v-decorator="[
                'templateId',
                {
                  rules: [{ required: true, message: '审批模板不能为空!' }]
                }
              ]"
            >
              <a-select-option
                v-for="item in templateGroup"
                :key="item.id"
                :value="item.id"
              >
                {{ item.templateName }}
              </a-select-option>
            </a-select>
          </a-spin>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick"
import {
  ApiBusinessFindByTemplateId,
  ApiBusinessUpdate
} from "@/pages/index/data/api/PortalManagement/HomePage/QuickEntry"

// 表单字段
const fields = ["templateId", "id", "templateIcon", "sort", "templateName"]

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    }
    return {
      templateNameloading: false,
      loading: false,
      form: this.$form.createForm(this),
      icon: "",
      modelTitle: "" //弹框标题
    }
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          this.FindFlowProcessRecord()
          this.modelTitle = this.data.id ? "修改" : "添加"
        }
      }
    },
    deep: true
  },
  created() {
    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v))

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields))
    })
    console.log(this.data)
  },

  methods: {
    handleChange(value) {
      console.log("999999999", value)
      this.templateGroup.forEach((item) => {
        if (item.id === value) {
          console.log("icon", item.icon)
          this.icon = item.icon
        }
      })
      console.log("1234563", this.icon)
    },
    formatText(text) {
      if (this.templateGroup) {
        return (
          this.templateGroup.filter((i) => i.id === text)[0] &&
          this.templateGroup.filter((i) => i.id === text)[0].templateName
        )
      }
    },
    //调用查询分组接口
    FindFlowProcessRecord() {
      this.loading = true
      let params = {
        templateId: this.data.templateId
      }
      ApiBusinessFindByTemplateId(params)
        .then((res) => {
          this.templateGroup = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    //确定提交
    handleOk() {
      this.loading = true
      this.form.validateFields((errors, values) => {
        if (!errors) {
          let params = {
            templateId: values.templateId,
            id: this.data.id,
            templateIcon: this.icon,
            sort: this.data.sort,
            templateName: this.formatText(values.templateId)
          }
          if (this.data.id) {
            params.id = this.data.id
            ApiBusinessUpdate(params)
              .then((res) => {
                if (res.code == 0) {
                  this.$emit("cancel")
                  // 重置表单数据
                  this.form.resetFields()
                  // 刷新表格
                  this.$emit("ok")
                  this.$message.info("修改成功!")
                }
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            ApiBusinessUpdate(params)
              .then((res) => {
                if (res.code == 0) {
                  this.$emit("cancel")
                  // 重置表单数据
                  this.form.resetFields()
                  // 刷新表格
                  this.$emit("ok")
                  this.$message.info("添加成功!")
                }
              })
              .finally(() => {
                this.loading = false
              })
          }
        } else {
          this.loading = false
        }
      })
    }
  }
}
</script>

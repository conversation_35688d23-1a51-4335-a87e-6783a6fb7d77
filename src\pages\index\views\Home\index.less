.out {
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  .tit {
    width:100% ;
    margin: 0 auto;
    height: 60px;
    line-height: 45px;
    font-size: 16px;
    font-weight: 800;
    text-align: left;
    padding:0 15px;
  }
  .left {
    width:73%;
    .left-t {
      display: flex;
      justify-content: space-between;
      height: 120px;
      .left-t-con {
        width: 24%;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 120px;
        border: 1px solid #fff;
        border-radius: 8px;
        background-color: #fff;
        box-sizing: border-box;
        p {
          width: 100%;
          font-size: 16px;
        }
        .con-font {
          font-size: 26px;
        }
      }
      .con-img {
        width: 46px;
        height: 48px;
      }
    }
    .left-b {
      margin-top: 12px;
      // height: 657px;
      padding: 7.5px;
      border: 1px solid #fff;
      border-radius: 8px;
      box-sizing: border-box;
      background-color: #fff;
      .b-con-out {
        height: 550px;
        display: flex !important;
        flex-wrap: wrap;
        overflow-x: hidden;
        overflow-y: auto;
      }
      .left-b-con {
        width: 22%;
        height: 200px;
        text-align: center;
        border: 1px solid #fff;
        border-radius: 8px;
        background-color: #f4faff;
        box-sizing: border-box;
        position: relative;
        margin: 8px;
        margin-left: 10px;
        .con-icon {
          width: 50px;
          height: 23px;
          line-height: 20px;
          font-size: 12px;
          text-align: center;
          position: absolute;
          left: 5px;
          top: 5px;
          border-radius: 4px;
        }
        .con-btn {
          font-weight: 600;
          position: absolute;
          right: 8px;
          top: 3px;
          cursor: pointer;
        }
        .con-border-y {
          width: 190px;
          height: 200px;
          box-sizing: border-box;
          border: 1px solid #ffa32e;
          border-radius: 8px;
        }
        .con-icon-y {
          color: #fff;
          background-color: #ffa32e;
          border: 1px solid #ffa32e;
        }
        .con-fontw {
          font-weight: 600;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          cursor: pointer;
        }
      }
    }
  }
  .right {
    width: 27%;
    .right-t {
      height: 450px;
      width: 96%;
      margin: 0 auto;
      border: 1px solid #fff;
      border-radius: 8px;
      background-color: #fff;
      margin-bottom: 24px;
      .tit-out {
        position: relative;
        .year {
          position: absolute;
          top: 35px;
          right: 10px;
          font-size:12px;
          color: #1677ff;
          // font-weight: bolder;
        }
      }
      .t-con-out {
        height: 375px;
        overflow-x: hidden;
        overflow-y: auto;
      }
      .r-t-con {
        width: 90%;
        margin: 0 auto;
        display: flex;
        margin-bottom: 15px;
      }
    }

    .right-b {
      width: 96%;
      margin: 0 auto;
      // height: 315px;
      border: 1px solid #fff;
      border-radius: 8px;
      background-color: #fff;

      .r-b-r {
        width: 95%;
        margin: 0 auto;

        .b-r-con {
          cursor: pointer;
          width: 96%;
          margin: 0 auto;
          .con-main {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
        }
      }
    }
  }
  .color-b {
    color: #29a2ff;
  }
  .color-9 {
    margin-top: 10px;
    color: #a7a2a2;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 5px; /* 滚动条宽度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #ffffff; /* 轨道背景颜色 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #e6e6e6; /* 滑块背景颜色 */
}

/* 滚动条滑块悬停状态 */
::-webkit-scrollbar-thumb:hover {
  background-color: #e6e6e6; /* 悬停状态下滑块背景颜色 */
}

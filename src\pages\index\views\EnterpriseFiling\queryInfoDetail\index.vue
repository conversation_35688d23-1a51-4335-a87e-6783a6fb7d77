<!-- 企业建档表 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <div class="lessorInfo">
      <div
        class="lessorInfo-tit"
        style="
          height: 100px;
          display: flex;
          align-items: center;
          border-bottom: 1px solid #ece5e5;
        "
      >
        <div class="title">申请人</div>
        <div class="title-font">{{ decisionParmas.initiateName }}</div>
      </div>
    </div>
    <div class="detail">
      <!-- 企业基础信息 -->
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">企业基础信息</div>
          <div
            style="width: 1360px;margin-left:20px;border: 1px solid #deeafb;"
          >
            <div style="display: flex;">
              <InputItem
                widthVal="480"
                name="企业名称"
                :value="decisionParmas.companyName"
              ></InputItem>
              <InputItem
                widthVal="480"
                name="企业性质"
                :value="decisionParmas.econKind"
              ></InputItem>
            </div>
            <div style="display: flex;">
              <InputItem
                widthVal="480"
                name="注册资金"
                :value="decisionParmas.registCapi"
              ></InputItem>
              <InputItem
                widthVal="480"
                name="创立时间"
                :value="decisionParmas.startDate"
              ></InputItem>
            </div>
            <InputItem
              widthVal="1160"
              name="工商登记机关"
              :value="decisionParmas.belongOrg"
            ></InputItem>
            <InputItem
              widthVal="1160"
              name="注册地址"
              :value="decisionParmas.address"
            ></InputItem>
            <InputItem
              widthVal="1160"
              name="办公地址"
              :value="decisionParmas.officeAddress"
            ></InputItem>
          </div>
        </div>
      </div>
      <!-- 企业资质评估信息 -->
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">企业资质评估信息</div>
          <div
            style="width: 1360px;margin-left:20px;border: 1px solid #deeafb;"
          >
            <div style="display: flex;">
              <div class="name">
                参保人数
              </div>
              <div
                style="padding-left: 15px;width:1160px;display: flex; flex-wrap: wrap;border: 1px solid #deeafb;"
              >
                <div
                  class="employeesInfoOut"
                  v-for="(item, index) in decisionParmas.insuranceNum"
                  :key="index"
                >
                  <div
                    class="item-out"
                    v-if="decisionParmas.insuranceNum[index][0]"
                    style="width: 160px;"
                  >
                    <div class="item">
                      {{ decisionParmas.insuranceNum[index][0] }}
                    </div>
                    <div style="padding-top: 2px; color: #262626;">
                      {{ decisionParmas.insuranceNum[index][1] }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex;">
              <div class="name">
                主要人员及职位
              </div>
              <div
                style="padding-left: 15px;width:1160px;display: flex; flex-wrap: wrap;border: 1px solid #deeafb;"
              >
                <div
                  class="employeesInfoOut"
                  v-for="(item, index) in decisionParmas.employeesInfo"
                  :key="index"
                >
                  <div
                    class="item-out"
                    v-if="decisionParmas.employeesInfo[index][0]"
                  >
                    <div class="item">
                      {{ decisionParmas.employeesInfo[index][0] }}
                    </div>
                    <div style="padding-top: 2px;">
                      {{ decisionParmas.employeesInfo[index][1] }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex;">
              <div class="name">
                主要股东及持股比例
              </div>
              <div
                style="padding-left: 15px;width:1160px;display: flex; flex-wrap: wrap;border: 1px solid #deeafb;"
              >
                <div
                  class="partnersInfoOut"
                  v-for="(item, index) in decisionParmas.partnersInfo"
                  :key="index"
                >
                  <div
                    v-if="decisionParmas.partnersInfo[index][0]"
                    style="display: flex;height: 30px;padding-top: 8px;"
                  >
                    <div class="item">
                      {{ decisionParmas.partnersInfo[index][0] }}
                    </div>
                    <div class="item1">
                      {{
                        (decisionParmas.partnersInfo[index][1] * 100).toFixed(
                          2
                        )
                      }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex;">
              <div class="name">
                主要关联公司
              </div>
              <div style="width: 1160px;">
                <div style="display: flex;min-height: 40px;">
                  <div class="componyType">
                    分公司及子公司
                  </div>
                  <div
                    style="width: 100%; display: flex; flex-wrap: wrap;border: 1px solid #deeafb;padding-left: 15px;"
                  >
                    <div
                      class="partnersInfoOut"
                      v-for="(item, index) in decisionParmas.branchesInfo"
                      :key="index"
                    >
                      <div
                        v-if="decisionParmas.branchesInfo[index][0]"
                        style="display: flex;height: 30px;padding-top: 8px;"
                      >
                        <div class="item">
                          {{ decisionParmas.branchesInfo[index][0] }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div style="display: flex;min-height: 40px;">
                  <div class="componyType">
                    对外投资公司
                  </div>
                  <div
                    style="width: 100%; display: flex; flex-wrap: wrap;border: 1px solid #deeafb;padding-left: 15px;"
                  >
                    <div
                      class="partnersInfoOut"
                      v-for="(item, index) in decisionParmas.investsInfo"
                      :key="index"
                    >
                      <div
                        v-if="decisionParmas.investsInfo[index][0]"
                        style="display: flex;height: 30px;padding-top: 8px;"
                      >
                        <div class="item">
                          {{ decisionParmas.investsInfo[index][0] }}
                        </div>
                        <div class="item1">
                          {{ decisionParmas.investsInfo[index][1] }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 专利情况 -->
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">专利情况</div>
          <div
            style="width: 1360px;margin-left:20px;border: 1px solid #deeafb;"
          >
            <div style="display: flex;">
              <InputItem
                widthVal="250"
                name="发明专利申请"
                :value="decisionParmas.patentNum"
                unit="个"
              ></InputItem>
              <InputItem
                widthVal="250"
                name="商标信息"
                :value="decisionParmas.trademarkNum"
                unit="个"
              ></InputItem>
              <InputItem
                widthVal="260"
                name="著作权（不含软著）"
                :value="decisionParmas.copyrightNum"
                unit="个"
              ></InputItem>
            </div>
            <div style="display: flex;">
              <InputItem
                widthVal="250"
                name="资质认证"
                :value="decisionParmas.certNum"
                unit="个"
              ></InputItem>
              <InputItem
                widthVal="710"
                name="软件著作权"
                :value="decisionParmas.softwareNum"
                unit="个"
              ></InputItem>
            </div>
          </div>
        </div>
      </div>
      <!-- 资质情况 -->
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">资质情况</div>
          <div
            style="width: 1360px;margin-left:20px;border: 1px solid #deeafb;"
          >
            <div style="display: flex;">
              <div
                class="aptitudeStyle"
                v-for="item in aptitudeInfos"
                :key="item.value"
              >
                <p>{{ item.label }}</p>
              </div>
            </div>

            <div style="display: flex;">
              <div class="name">
                主营业务
              </div>
              <div
                style="padding-left: 15px;width:1160px;display: flex; flex-wrap: wrap;border: 1px solid #deeafb;"
              >
                <div style="width: 100%; display: flex; flex-wrap: wrap;">
                  <div
                    class="partnersInfoOut"
                    v-for="(item, index) in decisionParmas.entPortraitInfo"
                    :key="index"
                  >
                    <div
                      v-if="decisionParmas.entPortraitInfo[index][0]"
                      style="display: flex;"
                    >
                      <div class="item">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <InputItem
              widthVal="1160"
              name="品牌产品"
              :value="decisionParmas.projectInfo"
            ></InputItem>
            <div style="display: flex;">
              <div class="name">
                获奖及成就
              </div>
              <div
                style="padding-left: 15px;width:1160px;display: flex; flex-wrap: wrap;border: 1px solid #deeafb;"
              >
                <div style="width: 100%; display: flex; flex-wrap: wrap;">
                  <div
                    class="partnersInfoOut"
                    v-for="(item, index) in decisionParmas.awardsAchievements"
                    :key="index"
                  >
                    <div
                      v-if="decisionParmas.awardsAchievements[index][0]"
                      style="display: flex;"
                    >
                      <div class="item">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex;">
              <div class="name">
                融资情况
              </div>
              <div
                style="padding-left: 15px;width:1160px;display: flex; flex-wrap: wrap;border: 1px solid #deeafb;"
              >
                <div style="width: 100%; display: flex; flex-wrap: wrap;">
                  <div
                    class="partnersInfoOut"
                    v-for="(item, index) in decisionParmas.financeInfo"
                    :key="index"
                  >
                    <div
                      v-if="decisionParmas.financeInfo[index][0]"
                      style="display: flex;"
                    >
                      <div class="item">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <InputItem
              widthVal="1160"
              name="上市情况"
              :value="decisionParmas.entProfileInfo"
            ></InputItem>
          </div>
        </div>
      </div>
    </div>
    <div class="detail">
      <!-- 企业评级 -->
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">企业评级</div>
          <div style="display: flex">
            <div class="echart-item">
              <EnterpriseRating></EnterpriseRating>
            </div>
            <div class="echart-item">
              <EnterpriseLevel></EnterpriseLevel>
            </div>
          </div>
        </div>
      </div>
      <!-- 客户评级 -->
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">客户评级</div>
          <div class="customer">
            <div class="customer-box">
              <div class="customer-item">
                <img
                  :src="
                    decisionParmas.customerRating == 1
                      ? require(`./../../../../../assets/image/common/customer-start1.png`)
                      : require(`./../../../../../assets/image/common/customer-start2.png`)
                  "
                  alt=""
                />
                <span>重要客户</span>
              </div>
              <div class="customer-item">
                <img
                  :src="
                    decisionParmas.customerRating == 2
                      ? require(`./../../../../../assets/image/common/customer-start1.png`)
                      : require(`./../../../../../assets/image/common/customer-start2.png`)
                  "
                  alt=""
                /><span>非常重要</span>
              </div>
              <div class="customer-item">
                <img
                  :src="
                    decisionParmas.customerRating == 3
                      ? require(`./../../../../../assets/image/common/customer-start1.png`)
                      : require(`./../../../../../assets/image/common/customer-start2.png`)
                  "
                  alt=""
                /><span>普通客户</span>
              </div>
              <div class="customer-item">
                <img
                  :src="
                    decisionParmas.customerRating == 4
                      ? require(`./../../../../../assets/image/common/customer-start1.png`)
                      : require(`./../../../../../assets/image/common/customer-start2.png`)
                  "
                  alt=""
                /><span>伙伴客户</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="text-align: center">
      <a-button class="back" @click="toBack">返回</a-button>
    </div>
  </div>
</template>

<script>
import { EnterpriseRating, EnterpriseLevel } from "../../EnterpriseFilingCheck/components";
import { queryContractInfoByIdApi } from "@/pages/index/data/api/EnterpriseFiling";
import InputItem from "./../component/inputItem.vue";
export default {
  components: {
    EnterpriseRating,
    EnterpriseLevel,
    InputItem,
  },
  data() {
    return {
      plainOptions: [
        { label: "国家科技型中小企业", value: "1" },
        { label: "国家高新技术企业", value: "2" },
        { label: "市专精特新中小企业", value: "3" },
        { label: "国家专精特新小巨人企业", value: "4" },
        { label: "区企业技术中心", value: "5" },
        { label: "市企业技术中心", value: "6" },
      ],
      aptitudeInfos: [],
      decisionParmas: {
        companyName: "", //企业名称
        econKind: "", //企业性质
        registCapi: "", //注册资金
        startDate: "", //创立时间
        belongOrg: "", //工商登记机关
        address: "", //注册地址
        officeAddress: "", //办公地址
        insuranceNum: "", //参保人数
        employeesInfo: "", //主要人员及职位
        partnersInfo: "", //主要股东及持股比例
        branchesInfo: "", //分公司及子公司
        investsInfo: "", //对外投资公司
        patentNum: 0, //发明专利申请
        trademarkNum: 0, //有效注册商标
        copyrightNum: 0, //著作权（不含软著）
        certNum: 0, //资质认证
        softwareNum: 0, //软件著作权
        aptitudeInfos: [], //资质情况
        aptitudeOther: ["1"], //资质情况其他
        aptitudeOtherDescription: "", //其他资质描述
        entPortraitInfo: "", //主营业务
        projectInfo: "", //品牌产品
        awardsAchievements: "", //获奖及成就
        financeInfo: "", //融资情况
        entProfileInfo: "", //上市情况
        customerRating: "", //客户评级
      },
    };
  },

  methods: {
    getContractId() {
      //根据id查询所有数据
      if (this.$route.query.id) {
        let parmas = {
          id: this.$route.query.id,
        };
        queryContractInfoByIdApi(parmas).then((res) => {
          this.decisionParmas = res.data;
          // let arr = [];
          // arr.push(res.data.aptitudeOther.toString());
          // this.decisionParmas.aptitudeOther = arr;

          let insuranceNumArr = this.decisionParmas.insuranceNum?.split(";");
          this.decisionParmas.insuranceNum = [];
          insuranceNumArr?.forEach((e) => {
            this.decisionParmas.insuranceNum.push(e.split(":"));
          });

          let employeesInfoArr = this.decisionParmas.employeesInfo?.split(";");
          this.decisionParmas.employeesInfo = [];
          employeesInfoArr?.forEach((e) => {
            this.decisionParmas.employeesInfo.push(e.split(":"));
          });

          let partnersInfoArr = this.decisionParmas.partnersInfo?.split(";");
          this.decisionParmas.partnersInfo = [];
          partnersInfoArr?.forEach((e) => {
            this.decisionParmas.partnersInfo.push(e.split(":"));
          });

          let investsInfoArr = this.decisionParmas.investsInfo?.split(";");
          this.decisionParmas.investsInfo = [];
          investsInfoArr?.forEach((e) => {
            this.decisionParmas.investsInfo.push(e.split(":"));
          });

          let branchesInfoArr = this.decisionParmas.branchesInfo?.split(";");
          this.decisionParmas.branchesInfo = [];
          branchesInfoArr?.forEach((e) => {
            this.decisionParmas.branchesInfo.push(e.split(":"));
          });
          //investsInfo  对外投资公司   branchesInfo 分公司及子公司

          //融资情况
          let financeInfoArr = this.decisionParmas.financeInfo?.split(";");
          this.decisionParmas.financeInfo = financeInfoArr;
          //主营业务
          let entPortraitInfoArr = this.decisionParmas.entPortraitInfo?.split(
            ";"
          );
          this.decisionParmas.entPortraitInfo = entPortraitInfoArr;
          //获奖及成就
          let awardsAchievementsArr = this.decisionParmas.awardsAchievements?.split(
            ";"
          );
          this.decisionParmas.awardsAchievements = awardsAchievementsArr;
          //资质
          this.aptitudeInfos = this.plainOptions.filter((item1) =>
            this.decisionParmas.aptitudeInfos.some(
              (item2) => item2 === item1.value
            )
          );
        });
      }
    },
    toBack() {
      this.$router.push(`/enterprise-filing/query`);
    },
  },
  mounted() {
    this.getContractId();
  },
};
</script>

<style lang="less" scoped>
@import "./common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
  margin-bottom: 30px;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}

.ant-advanced-search-form /deep/ .ant-checkbox-group {
  .ant-checkbox-group-item {
    font-size: 17px !important;
    margin-left: 68px;
    margin-bottom: 30px;
  }
}
.ant-advanced-search-form /deep/ .ant-col-8 {
  .ant-col-sm-8 {
    width: 31% !important;
  }
  .ant-col-sm-16 {
    width: 69% !important;
  }
}
.ant-advanced-search-form /deep/ .ant-col-16 {
  .ant-col-sm-8 {
    width: 15% !important;
  }
  .ant-col-sm-16 {
    width: 85% !important;
  }
}
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  // width: 166px !important;
}
</style>

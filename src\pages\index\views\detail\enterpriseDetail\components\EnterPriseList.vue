<!-- 企业查询 -->
<template>
  <div class="enterprise">
    <div class="enterpriseFrom">
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
        @submit="handleSubmit"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="企业名称">
              <a-input
                maxLength="50"
                v-model="queryParam.companyName"
                placeholder="请输入企业名称"
                allowClear
              >
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8" align="right">
            <a-button type="primary" @click="search">查询</a-button>
            <a-button type="default" @click="reset" style="margin-left: 15px"
              >重置</a-button
            >
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
    <a-card style="width: 100%; margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :row-selection="{
          onChange: onChange,
          onSelectAll: (selected, selectedRows, changeRows) => {
            console.log(selected, selectedRows, changeRows);
          },
        }"
        rowKey="key"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ index + 1 }}
        </span>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { ApiGetNotRelationComponeyInfo } from "@/pages/index/data/api/InfomationQuery";
export default {
  props: {
    selected: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    selected: {
      handler(newValue) {
        console.log(newValue);
        this.selectedRows = newValue.map((item) => item.id);
      },
    },
  },
  data() {
    return {
      tableData: [],
      selectedRowKeys: [],
      selectedRows: [],
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 60,
          fixed: "left",
          align: "center",
        },
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center",
          scopedSlots: { customRender: "companyName" },
        },
        {
          title: "注册资金",
          dataIndex: "registeredCapital",
          align: "center",
        },
        {
          title: "是否属地企业",
          dataIndex: "districtDecentralize",
          align: "center",
          customRender: (text, row) => {
            return row.districtDecentralize ? "非属地企业" : "属地企业";
          },
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      labelCol: { span: 4 },
      // 查询条件参数
      queryParam: {
        companyId: this.$route.query.id,
        companyName: "",
      },
    };
  },
  mounted() {},
  methods: {
    onChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        "selectedRows: ",
        selectedRows
      );
    },
    reset() {
      this.queryParam = {};
    },
    search() {
      if (this.queryParam.companyName) {
        const requestParameters = Object.assign(
          { companyId: this.$route.query.id },
          {
            currentPage: 1,
            pageSize: 10,
          },
          this.queryParam
        );
        ApiGetNotRelationComponeyInfo(requestParameters).then((res) => {
          this.tableData = res.data.records;
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise {
  display: flex;
  flex-wrap: wrap;
  .enterpriseFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }
  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
.ellipse {
  width: 100%;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 这里是超出几行省略 */
}
</style>

/**
 * <AUTHOR>
 * @time 2020-8-24
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

//分页查询
export function ApiSecuritypageByCondition(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/role/pageByCondition",
    method: "post",
    middle,
    params,
  });
}
//新增用户
export function ApiSecuritycreate(params) {
  return api({
    url: BASE_URL + "/security/role/create",
    method: "post",
    params,
  });
}
// 权限配置数据
export function ApiSecurityFindPermissionTree(params) {
  return api({
    url: BASE_URL + "/security/role/findPermissionTree",
    method: "post",
    params,
  });
}
// 权限配置保存
export function ApiSecuritysaveRoleWithPermission(params) {
  return api({
    url: BASE_URL + "/security/role/saveRoleWithPermission",
    method: "post",
    params,
  });
}
// 修改角色
export function ApiSecurityupdate(params) {
  return api({
    url: BASE_URL + "/security/role/update",
    method: "post",
    params,
  });
}
// 删除数据
export function ApiSecuritydelete(params) {
  return api({
    url: BASE_URL + "/security/role/delete",
    method: "post",
    params,
  });
}
// 是否启用接口
export function ApiupsecuritydateDisabled(params) {
  return api({
    url: BASE_URL + "/security/role/updateDisabled",
    method: "post",
    params,
  });
}

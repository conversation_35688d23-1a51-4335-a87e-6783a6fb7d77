<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 开关控件dom/form
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <switch-dom :data="formData"></switch-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <switch-form v-bind:data.sync="formData"></switch-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 开关控件 DOM/Form
import {
  SwitchDom,
  SwitchForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/Switch";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable,
  ApiFormFindAllFormComponent
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    SwitchDom,
    SwitchForm
  },
  data() {
    return {
      formData: {
        notNull: true,
        inputTitle: undefined, //标题
        placeholder: {
          placeholderText: undefined, //提示文字
          openPlaceHolder: undefined, //开启提示
          closePlaceHolder: undefined //关闭提示
        }
      },
      moduleVoList: []
    };
  },
  mounted() {
    this.moduleVoList = [];
    this.handCheckList();
  },
  methods: {
    // 查询控件
    handCheckList() {
      ApiFormFindAllFormComponent({
        id: "",
        name: "",
        type: ""
      }).then();
    },
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: JSON.stringify(this.formData.placeholder), //提示文字
        notNull: this.formData.notNull ? 1 : 0, //是否必填
        inputType: "switch",
        inputName: "1"
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      let dataOption = [];
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "switch") {
              dataOption.push(item);
            }
          });
        });
        let dataOptionItem = dataOption && dataOption[dataOption.length - 1];
        let optionsData = JSON.parse(
          dataOption && dataOption[dataOption.length - 1].placeholder
        );
        this.formData = {
          notNull: dataOptionItem.notNull,
          inputTitle: dataOptionItem.inputTitle, //标题
          placeholder: {
            placeholderText: optionsData.placeholderText, //提示文字
            openPlaceHolder: optionsData.openPlaceHolder, //开启提示
            closePlaceHolder: optionsData.closePlaceHolder //关闭提示
          }
        };
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

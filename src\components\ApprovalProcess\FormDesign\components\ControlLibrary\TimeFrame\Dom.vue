<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 时间范围Dom
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '时间范围'"
      class="title-start"
      :rules="[
        {
          required: data.notNull,
          message: '请选择时间！',
          trigger: 'onChange'
        }
      ]"
    >
      <a-date-picker :placeholder="data.optionsData.startTime || '开始时间'" />
      ~ <a-date-picker :placeholder="data.optionsData.endTime || '结束时间'" />
    </a-form-model-item>
    <a-row class="title-end" v-if="data.optionsData.autoComputerTime">
      <a-col :span="8">
        <span class="text">{{ data.optionsData.timeLength || "时长" }}</span>
      </a-col>
    </a-row>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          optionsData: ""
        };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
.title-start {
  margin-bottom: unset;
}
.title-line {
  text-align: center;
}
.title-end {
  height: 40px;
  line-height: 40px;
  .text {
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>

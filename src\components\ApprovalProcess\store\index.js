/**
 * <AUTHOR>
 * @time 2020-9-10
 * @dec 审批流程数据仓库
 */
import { deepClone } from "@/common/utils";
import {
  ApiSecurityFindUserByOrgType,
  ApiSecurityFindByAppType,
  ApiSecurityFindGroup
} from "@/data/api/components/ApprovalProcess";
const approvalProcess = {
  namespaced: true,
  state: {
    //基础设置
    basicSettings: {
      gruopId: "",
      templateName: "",
      templateGroup: "",
      icon: "",
      remark: ""
    },
    //表单设计
    formDesign: [],
    //流程设计
    processDesign: [],

    //获取人员列表
    userData: undefined,
    //获取角色列表
    roleData: undefined,
    //获取组织结构列表
    organData: undefined
  },
  getters: {
    //获取总数据
    getAllData(state) {
      return {
        basicSettings: deepClone(state.basicSettings),
        formDesign: deepClone(state.formDesign),
        processDesign: deepClone(state.processDesign)
      };
    },
    //获取基础设置
    basicSettings(state) {
      return deepClone(state.basicSettings);
    },
    //获取基础设置
    formDesign(state) {
      console.log("getformDesign", state.formDesign);
      return deepClone(state.formDesign);
    },
    //获取基础设置
    processDesign(state) {
      return deepClone(state.processDesign);
    },

    //获取用户列表数据
    userData(state) {
      return deepClone(state.userData);
    },
    //获取角色列表数据
    roleData(state) {
      return deepClone(state.roleData);
    },
    //获取角色列表数据
    organData(state) {
      return deepClone(state.organData);
    }
  },
  mutations: {
    //设置基础设置数据
    setBasicSettings(state, data) {
      console.log("setBasicSettings", data);
      state.basicSettings = deepClone(data);
    },
    //设置表单设计数据
    setFormDesign(state, data) {
      console.log("setFormDesign", data);
      state.formDesign = deepClone(data || []);
    },
    //设置流程设计数据
    setProcessDesign(state, data) {
      console.log("setProcessDesign", data);
      state.processDesign = deepClone(data || []);
    },

    // 设置人员列表数据
    setUserData(state, data) {
      state.userData = deepClone(data);
    },
    // 设置人员列表数据
    setRoleData(state, data) {
      state.roleData = deepClone(data);
    },
    // 设置人员列表数据
    setOrganData(state, data) {
      state.organData = deepClone(data);
    }
  },
  actions: {
    // 获取人员列表数据
    getUserData({ commit, state }) {
      return new Promise((resolve, reject) => {
        if (state.userData) {
          resolve([...state.userData]);
          return;
        }
        ApiSecurityFindUserByOrgType({ orgType: "inside" })
          .then(res => {
            commit("setUserData", res.data);
            resolve(res.data);
          })
          .catch(e => {
            reject(e);
          });
      });
    },
    // 获取角色列表数据
    getRoleData({ commit, state }) {
      return new Promise((resolve, reject) => {
        if (state.roleData) {
          resolve([...state.roleData]);
          return;
        }
        ApiSecurityFindByAppType({ appType: "" })
          .then(res => {
            commit("setRoleData", res.data);
            resolve(res);
          })
          .catch(e => {
            reject(e);
          });
      });
    },
    // 获取组织结构列表数据
    getOrganData({ commit, state }) {
      return new Promise((resolve, reject) => {
        if (state.organData) {
          resolve([...state.organData]);
          return;
        }
        ApiSecurityFindGroup({ groupName: "" })
          .then(res => {
            commit("setOrganData", res.data);
            resolve(res);
          })
          .catch(e => {
            reject(e);
          });
      });
    }
  }
};

export default approvalProcess;

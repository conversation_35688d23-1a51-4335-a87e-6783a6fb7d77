/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 获取列表接口
 * @dec 接口： /security/resource/pageByCondition
 * @dec ApiSystemFindByTree
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

export function ApiSystemFindByTree(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/sysdict/findByTree",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 新增接口
 * @dec 接口： /system/sysdict/create
 * @dec 命名：ApiSystemCreate
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */

export function ApiSystemCreate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/sysdict/create",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 修改接口
 * @dec 接口： /system/sysdict/update
 * @dec 命名：ApiSystemUpdate
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSystemUpdate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/sysdict/update",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 删除接口
 * @dec 接口： /system/sysdict/delete
 * @dec 命名：ApiSystemDelete
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSystemDelete(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/sysdict/delete",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 是否启用接口
 * @dec 接口： /security/resource/updateDisabled
 * @dec 命名：ApiSecurityUpdateDisabled
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSecurityUpdateDisabled(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/updateDisabled",
    method: "post",
    middle,
    params,
  });
}

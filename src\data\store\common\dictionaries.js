/**
 * <AUTHOR>
 * @time 2020-08-26
 * @dec 字典配置,所有字典数据会在页面初始化加载时一次加载进来
 */
import { ApiCommonDict } from "@/data/api/common";
export default {
  namespaced: true,
  state: {
    // 字典数据
    data: null
  },
  getters: {
    // 获取全部字典
    data(state) {
      if (!state.data) {
        try {
          const data = localStorage.getItem(process.env.VUE_APP_DICT_KEY);
          state.data = JSON.parse(data);
        } catch (e) {
          console.error(e);
        }
      }
      return state.data || {};
    },
    /**
     * @dec 获取字典某类型的数据
     * @param {String} type 字典类型
     * */
    getType: (state, getters) => type => {
      return getters.data[type];
    },
    /**
     * @dec 根据字典类型和code获取child
     * @param {String} type 字典类型
     * @param {String} code 字典编码
     * @return {Array} childs 子集
     * */
    getChildByTypeCode: (state, getters) => (type, code) => {
      let _type = getters.data[type];
      if (_type && _type.length) {
        let _dict = _type.find(item => item.dictValue === code);
        return _dict && _dict.childs;
      }
    },
    /**
     * @dec 获取字典多类型的数据集合
     * @param {Array[String]} types 字典类型集合
     * */
    getTypes: (state, getters) => types => {
      let _data = [];
      types.forEach(item => {
        let _i = getters.data[item];
        if (_i) {
          _data = _data.concat(_i);
        }
      });
      return _data;
    },
    // 根据类型及code获取name
    getNameFromTypeCode: (state, getters) => ({ type, code }) => {
      let _type = getters.data[type];
      if (_type && _type.length) {
        let _dict = _type.find(item => item.dictValue === code);
        return _dict && _dict.nameCn;
      }
      return code;
    },
    // 根据类型及name获取code
    getCodeFromTypeName: (state, getters) => ({ type, name }) => {
      let _type = getters.data[type];
      if (_type && _type.length) {
        let _dict = _type.find(item => item.nameCn === name);
        return _dict && _dict.dictValue;
      }
      return name;
    }
  },
  mutations: {
    // 设置字典数据
    setDictionaries(state, data) {
      localStorage.setItem(process.env.VUE_APP_DICT_KEY, JSON.stringify(data));
      state.data = data;
    }
  },
  actions: {
    // 设置字典数据
    getDictionaries({ commit }) {
      return new Promise((resolve, reject) => {
        ApiCommonDict({})
          .then(res => {
            commit("setDictionaries", res.data);
            resolve(res);
          })
          .catch(e => {
            reject(e);
          });
      });
    }
  }
};

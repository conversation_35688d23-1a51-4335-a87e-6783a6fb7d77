/**
 * <AUTHOR>
 * @time 2020-10-19
 * @function 获取列表接口
 * @dec 接口： /security/resource/pageByCondition
 * @dec ApiSystemFindByTree
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

//查询列表选项字典信息
export function ApiSystemFindOptionByTree(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/optdict/findOptionByTree",
    method: "post",
    middle,
    params,
  });
}

//新建列表选项字典信息
export function ApiSystemCreate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/optdict/create",
    method: "post",
    middle,
    params,
  });
}

//修改列表选项字典信息
export function ApiSystemUpdate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/optdict/update",
    method: "post",
    middle,
    params,
  });
}

//删除列表选项字典信息
export function ApiSystemDelete(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/optdict/delete",
    method: "post",
    middle,
    params,
  });
}

// export function ApiSecurityUpdateDisabled(params) {
//   const middle = {
//     request(params) {
//       // to do something
//       return params;
//     },
//     response(data) {
//       // to do something
//       return data;
//     }
//   };
//   return api({
//     url: BASE_URL + "/security/resource/updateDisabled",
//     method: "post",
//     middle,
//     params
//   });
// }

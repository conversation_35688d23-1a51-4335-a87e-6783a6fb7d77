<template>
  <div>
    <div class="title">
      <div class="company">
        <span class="companyName">{{ obj.companyName }}</span>
        <div class="tab">
          <div class="eclipse">
            <span>企业标签：</span>
            <span v-for="i in list" :key="i" style="margin-right: 5px">{{
              getName(1, i)
            }}</span>
          </div>
          <a @click="addTags"><a-icon type="edit" class="tagEdit" /></a>
        </div>
        <div class="tab">
          <div class="eclipse">
            <span>行业分布：</span>
            <span v-for="i in types" :key="i" style="margin-right: 5px">{{
              getName(2, i)
            }}</span>
          </div>
          <a @click="addTypes"><a-icon type="edit" class="tagEdit" /></a>
        </div>
      </div>
      <div>
        <div @click="changeColor()" class="left-star">
          <svg-icon
            iconClass="star"
            :style="{ color: svgColor ? '#e6e6e6' : '#f4ea2a' }"
            class="avatar"
          ></svg-icon>
        </div>
        <span>重点关注</span>
      </div>
    </div>
    <table
      class="table table-striped table-bordered"
      align="center"
      valign="center"
    >
      <tr>
        <td class="column">统一社会信用代码</td>
        <td class="value">{{ obj.socialCreditCode }}</td>
        <td class="column">组织机构代码</td>
        <td class="value">{{ obj.orgNo }}</td>
      </tr>
      <tr>
        <td class="column">企业法人</td>
        <td class="value">{{ obj.legalPerson }}</td>
        <td class="column">纳税人识别码</td>
        <td class="value">{{ obj.socialCreditCode }}</td>
      </tr>
      <tr>
        <td class="column">成立时间</td>
        <td class="value">{{ obj.businessRegistrationDate }}</td>
        <td class="column">注册资金（元）</td>
        <td class="value">{{ obj.registeredCapital }}</td>
      </tr>
      <tr>
        <td class="column">营业期限</td>
        <td class="value">
          {{ obj.operatingPeriodBegin }}-{{
            obj.operatingPeriodEnd ? obj.operatingPeriodEnd : "无固定期限"
          }}
        </td>
        <td class="column">纳税人资质</td>
        <td class="value"></td>
      </tr>
      <tr>
        <td class="column">企业分布</td>
        <td class="value">{{ obj.industryCategory }}</td>
        <td class="column">企业性质</td>
        <td class="value">{{ obj.companyNature }}</td>
      </tr>
      <tr>
        <td class="column">人员规模</td>
        <td class="value"></td>
        <td class="column">参保人数</td>
        <td class="value">{{ obj.insuranceNum }}</td>
      </tr>
      <tr>
        <td class="column">所属地区</td>
        <td class="value">{{ obj.residentialArea }}</td>
        <td class="column">登记机关</td>
        <td class="value">{{ obj.belongOrg }}</td>
      </tr>
      <tr>
        <td class="column">进出口企业代码</td>
        <td class="value"></td>
        <td class="column">英文名</td>
        <td class="value"></td>
      </tr>
      <tr>
        <td class="column">重点企业</td>
        <td class="value">{{ obj.keyEnterprises ? "是" : "否" }}</td>
        <td class="column">公司地址</td>
        <!-- 注册地址 -->
        <td class="value">{{ obj.companyAddress }}</td>
      </tr>
      <tr>
        <td class="column">经营范围</td>
        <td class="value" colspan="5" style="text-align: left">
          {{ obj.businessScope }}
          <br />
        </td>
      </tr>
    </table>

    <!-- 选择标签弹框 -->
    <a-modal
      title="添加标签"
      :visible="showTags"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      width="800px"
      @cancel="handleCancel"
    >
      <a-form
        :form="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 17 }"
        @submit="handleSubmit"
      >
        <a-form-item label="标签">
          <a-select
            mode="tags"
            style="width: 100%"
            placeholder="请选择标签"
            @change="handleChangeTags"
            v-decorator="[
              'tags',
              {
                rules: [],
              },
            ]"
          >
            <a-select-option v-for="item in tagList" :key="item.value">
              {{ item.desc }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 选择企业分布弹框 -->
    <a-modal
      title="编辑行业分布"
      :visible="showTypes"
      :confirm-loading="confirmLoading"
      @ok="handleOkType"
      width="800px"
      @cancel="handleCancelType"
    >
      <a-form
        :form="form2"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 17 }"
        @submit="handleSubmit"
      >
        <a-form-item label="标签">
          <a-select
            mode="tags"
            style="width: 100%"
            placeholder="请选择标签"
            @change="handleChangeTypeList"
            v-decorator="[
              'types',
              {
                rules: [],
              },
            ]"
          >
            <a-select-option v-for="item in typeList" :key="item.value">
              {{ item.desc }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { getCompanyById, updateKey } from "@/pages/demo/data/api/api/company";
import {
  ApiGetComponeyTagsById,
  ApiSaveComponeyLabelById,
  ApiGetAllComponeyLabels,
  ApiGetAllComponeyTypes,
  ApiSaveEnterpriseTypes,
} from "@/pages/index/data/api/InfomationQuery/enterpriseDetail";
import { ApiGetDictByParentCode } from "@/pages/index/data/api/Common";
export default {
  data() {
    return {
      svgColor: true,
      id: "",
      obj: {},
      list: [],
      str: "高新技术,专精特新,外资总部,上市公司",
      showTags: false,
      showTypes: false,
      types: [],
      tagList: [],
      confirmLoading: false,
      formLayout: "horizontal",
      form: this.$form.createForm(this, { name: "coordinated" }),
      form2: this.$form.createForm(this, { name: "typeList" }),
      typeList: [],
    };
  },
  created() {
    this.id = this.$route.query.id;
  },
  mounted() {
    this.initData();
  },
  methods: {
    getName(type, value) {
      return type == 1
        ? this.tagList.find((item) => item.value == value)?.desc
        : this.typeList.find((item) => item.value == value)?.desc;
    },
    handleChangeTags(values) {
      console.log("handleChangeTags", values);
      // let arr = [];
      // this.tagList.forEach((e) => {
      //   values.forEach((item) => {
      //     if (item == e.value) {
      //       arr.push(item);
      //     }
      //   });
      // });
      // this.form.getFieldDecorator("tags");
      // this.form.setFieldsValue({ tags: arr });
    },
    handleChangeTypeList(values) {
      console.log("handleChangeTypeList", values);
    },
    handleSubmit() {
      console.log("handleSubmit");
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log("Received values of form: ", values);
          ApiSaveComponeyLabelById({
            companyId: this.id,
            companyLabels: values.tags,
          }).then((res) => {
            if (res.code === 0) {
              this.$message.success("保存成功");
              this.getComponeyLabels();
              this.showTags = false;
            }
          });
        }
      });
    },
    handleOk() {
      console.log("HandleOk");
      this.handleSubmit();
    },
    handleOkType() {
      this.form2.validateFields((err, values) => {
        if (!err) {
          console.log("Received values of form: ", values);
          ApiSaveEnterpriseTypes({
            companyId: this.id,
            companyLabels: values.types,
          }).then((res) => {
            if (res.code === 0) {
              this.$message.success("保存成功");
              this.getComponeyTypes();
              this.showTypes = false;
            }
          });
        }
      });
    },
    handleCancel() {
      console.log("handleCancel");
      this.showTags = false;
      this.form.setFieldsValue({ tags: this.list });
    },
    handleCancelType() {
      this.showTypes = false;
      this.form2.setFieldsValue({ types: this.types });
    },
    addTags() {
      this.showTags = true;
    },
    addTypes() {
      this.showTypes = true;
    },
    async initData() {
      if (!this.id) {
        return;
      }
      //查询企业标签
      this.getComponeyLabels();
      /**
       * 查询企业行业
       */
      this.getComponeyTypes();
      // 获取所有标签
      ApiGetAllComponeyLabels().then((res) => {
        this.tagList = res.data;
      });
      /**
       * 字典表获取所有行业
       */
      ApiGetDictByParentCode({ dictCode: "industryCategory" }).then((res) => {
        this.typeList = res.data;
      });

      // 获取企业信息
      getCompanyById({ id: this.id }).then((res) => {
        this.obj = res.data;
        this.svgColor = this.obj.keyEnterprises == 1;
      });
    },
    getComponeyLabels() {
      // 获取企业标签
      ApiGetComponeyTagsById({
        companyId: this.id,
      }).then((obj) => {
        let companyNature = obj.data;
        this.list = companyNature || [];
        this.form.getFieldDecorator("tags");
        this.form.setFieldsValue({ tags: this.list });
      });
    },
    //获取行业标签
    getComponeyTypes() {
      ApiGetAllComponeyTypes({
        companyId: this.$route.query.id,
      }).then((res) => {
        this.types = res.data;
        this.form2.getFieldDecorator("types");
        this.form2.setFieldsValue({ types: this.types });
      });
    },
    changeColor() {
      let param = {
        id: this.id,
        keyEnterprises: this.svgColor ? 0 : 1, // 0 否 1 是
      };
      updateKey(param).then(() => {
        this.svgColor = !this.svgColor;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  margin-bottom: 10px;
  margin-top: 10px;
}
.companyName {
  font-size: 22px;
  color: #000;
  font-weight: 700;
}
.company {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.left-star {
  padding-top: 10px;
  padding-left: 7px;
}
.tab {
  display: flex;
}
.eclipse {
  max-width: 1000px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.avatar {
  cursor: pointer;
  width: 40px !important;
  height: 40px !important;
}
.table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  display: table;
  width: 98%;
  max-width: 100%;
  margin: 0 auto;
}
.table td {
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  color: #333333;
  padding: 8px 12px;
}
.table-bordered {
  border: 1px solid #ddd;
}
* {
  margin: 0px;
  padding: 0px;
}
.column {
  width: 30px;
  height: 30px;
  border: 1px solid #333;
  background: #eff3fc;
}
.value {
  width: 70px;
  height: 30px;
  border: 1px solid #333;
}
</style>

<!--
 * @dec 表单面板
-->
<template>
  <div class="control-panel">
    <a-card
      size="small"
      :bordered="false"
      :title="data.dataname"
      :headStyle="headStyle"
    >
      <component
        v-bind:is="data.datatype"
        v-bind:data.sync="formData"
      ></component>
    </a-card>
  </div>
</template>
<script>
// 组件库
import * as ControlLibraryForm from "../ControlLibrary/Form";
import * as SuiteLibraryForm from "../SuiteLibrary/Form";
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          datatype: ""
        };
      }
    }
  },
  data() {
    return {
      headStyle: {
        height: "45px",
        lineHeight: "45px",
        boxSizing: "border-box"
      },
      // 表单数据
      formData: undefined
    };
  },
  watch: {
    data: {
      handler: function(data) {
        this.$nextTick(() => {
          this.formData = { ...data.data };
        });
      },
      immediate: true
    },
    formData: {
      handler: function(form) {
        this.$emit("change", { data: form, key: this.data.key });
      },
      deep: true
    }
  },
  components: {
    ...ControlLibraryForm,
    ...SuiteLibraryForm
  },
  methods: {}
};
</script>
<style lang="less">
.control-panel {
  .ant-card-head-title {
    padding: unset !important;
  }
}
</style>

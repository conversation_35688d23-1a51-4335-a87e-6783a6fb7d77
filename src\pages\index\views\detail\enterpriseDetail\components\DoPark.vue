// 信息查询--园区查询
<template>
  <div>
    <table
      class="table table-striped table-bordered"
      align="center"
      valign="center"
      v-for="obj in list"
      :key="obj"
    >
      <tr>
        <td class="column">招商类型</td>
        <td class="value">
          {{ obj.investmentType ? "区域下放" : "非区域下放" }}
        </td>
        <td class="column">园区</td>
        <td class="value">{{ obj.parkName }}</td>
      </tr>
      <tr>
        <td class="column">入驻楼宇</td>
        <td class="value">{{ obj.leaseAddress }}</td>
        <td class="column">租赁期限</td>
        <td class="value">{{ obj.leaseStartTime }}-{{ obj.leaseEndTime }}</td>
      </tr>
      <tr>
        <td class="column">租赁面积</td>
        <td class="value">{{ obj.leaseArea }}㎡</td>
        <td class="column"></td>
        <td class="value"></td>
      </tr>
    </table>
  </div>
</template>

<script>
import { getResidingPark } from "@/pages/demo/data/api/api/company"
export default {
  data() {
    return {
      id: "",
      list: []
    }
  },
  created() {
    this.id = this.$route.query.id
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      if (!this.id) {
        return
      }
      getResidingPark({ id: this.id }).then((res) => {
        this.list = res.data
      })
    }
  }
}
</script>

<style lang="less" scoped>
.table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  display: table;
  width: 98%;
  max-width: 100%;
  margin: 0 auto;
}
.table td {
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  color: #333333;
  padding: 8px 12px;
}
.table-bordered {
  border: 1px solid #ddd;
  margin-bottom: 20px;
}
* {
  margin: 0px;
  padding: 0px;
}
.column {
  width: 15%;
  height: 30px;
  border: 1px solid #333;
  background: #eff3fc;
}
.value {
  width: 35%;
  height: 30px;
  border: 1px solid #333;
}
</style>

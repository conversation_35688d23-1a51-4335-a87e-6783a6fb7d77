<template>
  <a-modal
    :title="title"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
    v-bind="
      data &&
        data.type === 1 && {
          footer: ''
        }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="中文名称">
          <a-input
            autoComplete="off"
            :maxLength="50"
            placeholder="请输入中文名称"
            :disabled="data && data.type === 1"
            v-decorator="[
              'nameCn',
              {
                initialValue: '',
                rules: [{ required: true, message: '请输入中文名称' }]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="编码">
          <a-input
            autoComplete="off"
            :maxLength="50"
            placeholder="请输入编码"
            :disabled="data && data.type === 1"
            v-decorator="[
              'code',
              {
                initialValue: '',
                rules: [
                  {
                    required: true,
                    message: '请输入编码'
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="选项取值">
          <a-input
            autoComplete="off"
            :maxLength="50"
            placeholder="请输入数值"
            :disabled="data && data.type === 1"
            v-decorator="[
              'dictValue',
              {
                initialValue: '',
                rules: [
                  {
                    required: true,
                    message: '请输入数值'
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea
            :maxLength="100"
            placeholder="请输入描述"
            :disabled="data && data.type === 1"
            :rows="4"
            v-decorator="[
              'desc',
              {
                initialValue: ''
              }
            ]"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick";
// Api接口
import {
  ApiSystemCreate,
  ApiSystemUpdate
} from "@/pages/index/data/api/SystemManagement/System";
// 表单字段
const fields = ["nameCn", "code", "dictValue", "desc"];

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      },
      title: "",
      differ: ""
    };
    return {
      loading: false,
      form: this.$form.createForm(this)
    };
  },
  created() {
    // 防止表单未注册
    fields.forEach(v => this.form.getFieldDecorator(v));
    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields));
      this.differ = this.data.type;
      if (this.data.type == 0) {
        this.title = "新增";
      } else if (this.data.type == 1) {
        this.title = "查看";
      } else if (this.data.type == 2) {
        this.title = "修改";
      }
    });
  },
  methods: {
    handleOk() {
      this.loading = true;
      this.form.validateFields((errors, values) => {
        if (!errors) {
          if (this.differ == 2) {
            // 修改
            ApiSystemUpdate(
              Object.assign(values, { id: this.data.id, sort: this.data.sort })
            )
              .then(() => {
                this.$emit("cancel");
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("修改成功");
              })
              .finally(() => {
                this.loading = false;
              });
          } else if (this.differ == 0) {
            // 新增
            ApiSystemCreate(
              Object.assign(values, {
                sort: "0",
                parentId: this.data.parentId || "",
                level: this.data.level + 1
              })
            )
              .then(() => {
                this.$emit("cancel");
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("新增成功");
              })
              .finally(() => {
                this.loading = false;
              });
          }
        } else {
          this.loading = false;
        }
      });
    }
  }
};
</script>

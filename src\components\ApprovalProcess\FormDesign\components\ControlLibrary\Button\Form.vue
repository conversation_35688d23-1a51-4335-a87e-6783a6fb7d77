<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 按钮控件表单配置
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input
        v-model="form.inputTitle"
        placeholder="申请主题"
        maxLength="20"
        allowClear
      ></a-input>
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        v-model="form.placeholder.placeholderText"
        placeholder="请输入"
        maxLength="50"
        allowClear
      ></a-input>
    </a-form-model-item>
    <a-form-model-item label="按钮类型">
      <a-select
        v-model="form.optionsData.type"
        placeholder="请选择按钮类型"
        allowClear
        :getPopupContainer="
          (triggerNode) => {
            return triggerNode.parentNode || document.body
          }
        "
      >
        <a-select-option value="primary">
          基础按钮
        </a-select-option>
        <a-select-option value="dashed">
          虚线边框
        </a-select-option>
        <a-select-option value="danger">
          红色警告
        </a-select-option>
        <a-select-option value="link">
          链接(无边框)
        </a-select-option>
        <a-select-option value="">
          空白
        </a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        placeholder: "",
        optionsData: {}
      }
    }
  },
  watch: {
    data(data) {
      this.form = data
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form)
      },
      deep: true
    }
  }
}
</script>

<template>
  <div class="out">
    <div class="left">
      <div class="left-t">
        <!-- 数据控制显示 -->
        <div class="left-t-con" v-for="item in titList" :key="item.id">
          <a-statistic
            :formatter="formattedValue"
            :title="item.type"
            :value="item.value"
            valueStyle="fontSize:26px;fontWeight:600;text-align:center;"
          >
            <template #suffix>
              <span style="font-weight: 100">{{ item.unit }}</span>
            </template>
          </a-statistic>
        </div>
      </div>
      <div class="left-b">
        <a-tabs v-model="activeKey" @change="handleTabChange">
          <a-tab-pane key="4" tab="待办事项">
            <!-- <div class="to-more" @click="toMore(4)"><span>>>>更多</span></div> -->
            <div class="box-out">
              <div class="box-list">
                <div class="box-tit">
                  <div class="boxTit-name">
                    <!-- <a-icon style="font-size: 14px" type="profile" /> -->
                    <img src="../../../../assets/image/common/u96.png" alt="" />
                    <span>租赁备案申请</span>
                  </div>
                  <span class="more" @click="toMoreFn(1)">>>更多</span>
                </div>
                <div class="box-tabel">
                  <a-table
                    size="small"
                    :columns="columns"
                    :data-source="contractTodoList"
                    :pagination="false"
                    :scroll="{ x: 100, y: 200 }"
                  >
                    <template #tenantry="text, record">
                      <a-tooltip>
                        <template slot="title">
                          {{ text }}
                        </template>
                        <a href="javascript:;" @click="toApprove(1, record)">{{
                          text
                        }}</a>
                      </a-tooltip>
                    </template>
                  </a-table>
                </div>
              </div>
              <div class="box-list">
                <div class="box-tit">
                  <div class="boxTit-name">
                    <!-- <a-icon style="font-size: 14px" type="file-done" /> -->
                    <img src="../../../../assets/image/common/u95.png" alt="" />
                    <span>预申报表申请</span>
                  </div>
                  <span class="more" @click="toMoreFn(2)">>>更多</span>
                </div>
                <div class="box-tabel">
                  <a-table
                    size="small"
                    :columns="columns1"
                    :data-source="contractPreTodoList"
                    :pagination="false"
                    :scroll="{ x: 200, y: 240 }"
                  >
                    <template #companyName="text, record">
                      <a-tooltip>
                        <template slot="title">
                          {{ text }}
                        </template>
                        <a href="javascript:;" @click="toApprove(2, record)">{{
                          text
                        }}</a>
                      </a-tooltip>
                    </template>
                  </a-table>
                </div>
              </div>
              <div class="box-list">
                <div class="box-tit">
                  <div class="boxTit-name">
                    <!-- <a-icon style="font-size: 14px" type="profile" /> -->
                    <img src="../../../../assets/image/common/u45.png" alt="" />
                    <span>重点线索申请</span>
                  </div>

                  <span class="more" @click="toMoreFn(3)">>>更多</span>
                </div>
                <div class="box-tabel">
                  <a-table
                    size="small"
                    :columns="columns2"
                    :data-source="keyClueTodoList"
                    :pagination="false"
                    :scroll="{ x: 200, y: 240 }"
                  >
                    <template #projectName="text, record">
                      <a-tooltip>
                        <template slot="title">
                          {{ text }}
                        </template>
                        <a href="javascript:;" @click="toApprove(3, record)">{{
                          text
                        }}</a>
                      </a-tooltip>
                    </template>
                  </a-table>
                </div>
              </div>
              <div class="box-list">
                <div class="box-tit">
                  <div class="boxTit-name">
                    <!-- <a-icon style="font-size: 14px" type="profile" /> -->
                    <img src="../../../../assets/image/common/u97.png" alt="" />
                    <span>户管信息申请</span>
                  </div>

                  <span class="more" @click="toMoreFn(4)">>>更多</span>
                </div>
                <div class="box-tabel">
                  <a-table
                    size="small"
                    :columns="columns3"
                    :data-source="householeTodoList"
                    :pagination="false"
                    :scroll="{ x: 200, y: 240 }"
                  >
                    <template #enterpriseName="text, record">
                      <a-tooltip>
                        <template slot="title">
                          {{ text }}
                        </template>
                        <a href="javascript:;" @click="toApprove(4, record)">{{
                          text
                        }}</a>
                      </a-tooltip>
                    </template>
                  </a-table>
                </div>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="1" tab="租赁预警">
            <div class="to-more" @click="toMore(2)"><span>>>更多</span></div>
            <a-carousel
              autoplay
              :autoplaySpeed="20000"
              v-if="datalist.length > 0"
            >
              <div
                class="b-con-out"
                v-for="(item, index) in datalist"
                :key="index"
              >
                <div class="left-b-con" v-for="v in item.list" :key="v.id">
                  <div @click="onShow(v, 2)" style="cursor: pointer">
                    <div style="padding: 8px; box-sizing: border-box">
                      <div
                        style="margin-top: 36px; height: 45px"
                        class="con-fontw"
                      >
                        {{ v.tenantry }}
                      </div>
                      <div class="color-b">租赁到期预警</div>
                      <div class="color-9">
                        <div>租赁开始：{{ v.leaseStartTime }}</div>
                        <div>租赁截至：{{ v.leaseEndTime }}</div>
                      </div>
                      <div class="con-icon con-icon-y">预警</div>
                      <div class="con-btn">...</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-carousel>
          </a-tab-pane>
          <a-tab-pane key="2" tab="税收预警" force-render>
            <div class="to-more">
              <div>
                <a-select
                  v-model="taxationWarningYear"
                  style="width: 160px"
                  placeholder="请选择"
                  @change="handleChange"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in taxationWarningArr"
                    :key="item.value"
                    >{{ item.name }}</a-select-option
                  >
                </a-select>
              </div>
              <div style="margin-left: 20px" @click="toMore(1)">
                <span>>>更多</span>
              </div>
            </div>

            <!-- <a-carousel
              autoplay
              :autoplaySpeed="20000"
              v-if="taxationWarningList.length > 0"
            > -->
            <div
              class="b-con-out"
              v-for="(item, index) in taxationWarningList"
              :key="index"
            >
              <div
                class="left-b-con"
                style="height: 220px"
                v-for="v in item.list"
                :key="v.id"
              >
                <div
                  style="padding: 8px; box-sizing: border-box"
                  @click="onShow(v, 1)"
                >
                  <div
                    style="margin-top: 36px; height: 60px"
                    class="con-fontw"
                    @click="toDetail(v.companyName)"
                  >
                    {{ v.companyName }}
                  </div>
                  <div class="color-b">税收异常预警</div>
                  <div class="color-9">
                    <div>
                      去年税收：{{
                        v.taxationOfLastYear
                          ? v.taxationOfLastYear.toFixed(2)
                          : 0
                      }}
                      万元
                    </div>
                    <div>
                      今年税收：{{ v.taxation ? v.taxation.toFixed(2) : 0 }}
                      万元
                    </div>
                    <div v-if="v.warningType == 1">
                      同比增减：<span v-if="v.deltaRate > 0" style="color: red"
                        >{{ v.deltaRate }}%</span
                      >
                      <span v-else style="color: green"
                        >{{ v.deltaRate }}%</span
                      >
                      <a-icon
                        v-if="v.deltaRate > 0"
                        style="color: red"
                        type="arrow-up"
                      />
                      <a-icon v-else type="arrow-down" style="color: green" />
                    </div>
                    <div v-if="v.warningType == 2">
                      同比净值：<span style="color: green"
                        >{{
                          v.deltaTaxation ? v.deltaTaxation.toFixed(2) : 0
                        }}
                        万元</span
                      >
                    </div>
                  </div>
                  <div class="con-icon con-icon-y">预警</div>
                  <div class="con-btn">...</div>
                </div>
              </div>
            </div>
            <!-- </a-carousel> -->
          </a-tab-pane>
          <a-tab-pane key="3" tab="外迁预警" force-render>
            <div class="to-more" @click="toMore(3)"><span>>>更多</span></div>
            <a-carousel
              autoplay
              :autoplaySpeed="20000"
              v-if="migrationWarningList.length > 0"
            >
              <div
                class="b-con-out"
                v-for="(item, index) in migrationWarningList"
                :key="index"
              >
                <div class="left-b-con" v-for="v in item.list" :key="v.id">
                  <!-- <div @click="toMigrationList(v)" style="cursor: pointer">  -->
                  <div @click="onShow(v, 3)" style="cursor: pointer">
                    <div style="padding: 8px; box-sizing: border-box">
                      <div
                        style="margin-top: 36px; height: 50px"
                        class="con-fontw"
                      >
                        {{ v.companyName }}
                      </div>
                      <div class="color-b">外迁预警</div>
                      <div class="color-9">
                        <div v-if="v.keyEnterprises == 1">是否重点企业：是</div>
                        <div v-else>是否重点企业：否</div>
                        <div v-if="v.districtDecentralize == 1">
                          是否属地：是
                        </div>
                        <div v-else>是否属地：否</div>
                      </div>
                      <div class="con-icon con-icon-y">预警</div>
                      <div class="con-btn">...</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-carousel>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div class="right">
      <div class="right-t">
        <a-carousel autoplay :autoplaySpeed="20000">
          <div>
            <div class="tit-out">
              <div class="tit">辖区内重点企业Top10(今年)</div>
              <div class="year">截止至：{{ nowYearMonth }}</div>
            </div>
            <a-divider style="margin: 0; margin-bottom: 10px" />
            <div class="t-con-out">
              <div
                class="r-t-con"
                v-for="item in taxationNowData"
                :key="item.id"
              >
                <a-tooltip>
                  <template #title>{{ item.company }}</template>
                  <div class="ellipsis" style="width: 100%">
                    {{ item.company }}
                  </div>
                </a-tooltip>
              </div>
            </div>
          </div>
          <div>
            <div class="tit-out">
              <div class="tit">辖区内重点企业Top10(去年)</div>
              <div class="year">截止至：{{ lastYearMonth }}-12</div>
            </div>
            <a-divider style="margin: 0; margin-bottom: 10px" />
            <div class="t-con-out">
              <div
                class="r-t-con"
                v-for="item in taxationLastData"
                :key="item.id"
              >
                <a-tooltip>
                  <template #title>{{ item.company }}</template>
                  <div class="ellipsis" style="width: 100%">
                    {{ item.company }}
                  </div>
                </a-tooltip>
                <!-- <a-progress
                  :percent="(item.taxAmount / lasttotal) * 100"
                  strokeWidth="15"
                  :format="() => item.taxAmount + '万元'"
                  style="width: 60%"
                /> -->
              </div>
            </div>
          </div>
        </a-carousel>
      </div>
      <div class="right-b">
        <div>
          <div class="message-title">
            <div class="message">消息通知</div>
            <div class="message-more" @click="gotoMessageList">
              <span>>>>更多</span>
            </div>
          </div>
          <a-divider style="margin: 0; margin-bottom: 15px" />
        </div>
        <div class="r-b-r">
          <div
            class="b-r-con"
            v-for="item in messageList"
            :key="item.id"
            @click="
              () => {
                getoMessageDetail(item);
              }
            "
          >
            <!-- <a-tooltip>
              <template #title>{{ item.theme }}</template> -->
            <div class="con-main">{{ item.theme }}</div>
            <div>{{ item.createTime }}</div>
            <!-- </a-tooltip> -->
            <a-divider style="margin: 10px 0" />
          </div>
        </div>
      </div>
    </div>

    <!-- <InfoModel
      v-on:myself="myDefine"
      :visible="visible"
      :textList="textList"
      @cancel="handleCancel"
    /> -->
    <div v-if="visible">
      <prewarning-processing-modal
        :visible="visible"
        :detailInfo="detailInfo"
        @handleOkAction="handleOKCallback"
        @handleCancelAction="handleCancelCallback"
      />
    </div>
  </div>
</template>

<script>
import moment from "moment";
// import { backGroundPageByCondition } from "@/pages/demo/data/api/api/company";
// import InfoModel from "./components/infoModel.vue";
import PrewarningProcessingModal from "./components/prewarningProcessingModal.vue";
import {
  getStatisticsApi,
  getEarlyWarningApi,
  gettaxationTopOfLastYearApi,
  getTaxationTop20Api,
  companyTaxationWarningApi,
  // geinfoListApi
  APIQueryWarningPageByType,
  findContractTodoList, //租赁备案申请
  findContractPreTodoList, //预申报表申请
  selectBusinessClueTodoList, //重点线索申请
  selectHouseholdTodoList,
} from "@/pages/index/data/api/home";
import {
  ApiNoticeList,
  // ApiNoticeUpdate,
  // ApiNoticeReadCount,
} from "APIs/PortalManagement/NotificationManagement/index.js";
export default {
  name: "myself",
  components: {
    // InfoModel,
    PrewarningProcessingModal,
  },
  data() {
    return {
      contractTodoList: [], //租赁备案申请
      contractPreTodoList: [], //预申报表申请
      keyClueTodoList: [], //重点线索申请
      householeTodoList: [],
      columns: [
        {
          title: "企业名称",
          dataIndex: "tenantry",
          key: "tenantry",
          width: 80,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "tenantry" },
        },
        {
          title: "出租方",
          dataIndex: "abbr",
          key: "abbr",
          width: 60,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "abbr" },
        },
        {
          title: "申请时间",
          dataIndex: "createDateTime",
          key: "createDateTime",
          ellipsis: true,
          align: "center",
          width: 80,
        },
      ],
      columns1: [
        {
          title: "企业名称",
          dataIndex: "companyName",
          key: "companyName",
          width: 80,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "companyName" },
        },
        {
          title: "招商分部",
          dataIndex: "abbr",
          key: "abbr",
          width: 60,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "abbr" },
        },
        {
          title: "申请时间",
          dataIndex: "createTime",
          key: "createTime",
          align: "center",
          ellipsis: true,
          width: 80,
        },
      ],
      columns2: [
        {
          title: "项目名称",
          dataIndex: "projectName",
          key: "projectName",
          width: 80,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "projectName" },
        },
        {
          title: "招商分部",
          dataIndex: "businessDivision",
          key: "businessDivision",
          width: 60,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "businessDivision" },
        },
        {
          title: "申请时间",
          dataIndex: "createdTime",
          key: "createdTime",
          align: "center",
          ellipsis: true,
          width: 80,
        },
      ],
      columns3: [
        {
          title: "企业名称",
          dataIndex: "enterpriseName",
          key: "enterpriseName",
          width: 80,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "enterpriseName" },
        },
        {
          title: "招商分部",
          dataIndex: "businessDivision",
          key: "businessDivision",
          width: 60,
          align: "center",
          ellipsis: true,
          scopedSlots: { customRender: "businessDivision" },
        },
        {
          title: "申请时间",
          dataIndex: "applicationDate",
          key: "applicationDate",
          align: "center",
          ellipsis: true,
          width: 80,
        },
      ],
      visible: false,
      textList: [],
      titList: [],
      taxationNowData: [],
      taxationLastData: [],
      nowYearMonth: "",
      lastYearMonth: "",
      datalist: [], //租赁预警
      taxationWarningList: [], //税收预警
      migrationWarningList: [], //外迁预警
      nowtotal: 0,
      lasttotal: 0,
      contentData: [
        {
          id: 1,
          type: "消息类型1",
          content:
            "“为深入学习贯彻党的二十大精神,大力开展调查研究,促进徐汇区科技创新和重点产业高质量发展,9月4日,全国政协常委、九三学社中央原副主席、上海市政协原副主席、九三学社市委原主委赵雯,全国政协常委、九三学社中央常委、市政协副主席、九三学社市委主委钱锋带队赴徐汇区调研",
          createTime: "2023-08-02 11:02:11",
        },
        {
          id: 2,
          type: "消息类型2",
          content:
            "“为深入学习贯彻党的二十大精神,大力开展调查研究,促进徐汇区科技创新和重点产业高质量发展,9月4日,全国政协常委、九三学社中央原副主席、上海市政协原副主席、九三学社市委原主委赵雯,全国政协常委、九三学社中央常委、市政协副主席、九三学社市委主委钱锋带队赴徐汇区调研",
          createTime: "2023-08-02 11:02:11",
        },
        {
          id: 3,
          type: "消息类型3",
          content:
            "“为深入学习贯彻党的二十大精神,大力开展调查研究,促进徐汇区科技创新和重点产业高质量发展,9月4日,全国政协常委、九三学社中央原副主席、上海市政协原副主席、九三学社市委原主委赵雯,全国政协常委、九三学社中央常委、市政协副主席、九三学社市委主委钱锋带队赴徐汇区调研",
          createTime: "2023-08-02 11:02:11",
        },
      ],
      taxationWarningYear: "",
      taxationWarningArr: [
        {
          name: "十二月",
          value: "12",
        },
        {
          name: "十一月",
          value: "11",
        },
        {
          name: "十月",
          value: "10",
        },
        {
          name: "九月",
          value: "09",
        },
        {
          name: "八月",
          value: "08",
        },
        {
          name: "七月",
          value: "07",
        },
        {
          name: "六月",
          value: "06",
        },
        {
          name: "五月",
          value: "05",
        },
        {
          name: "四月",
          value: "04",
        },
        {
          name: "三月",
          value: "03",
        },
        {
          name: "二月",
          value: "02",
        },
        {
          name: "一月",
          value: "01",
        },
      ],
      messageList: [], //消息列表
      taxationMonthStr: undefined, //税收预警选择的月份
      activeKey: "4",
      detailInfo: {},
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles,
    };
  },
  mounted() {
    /**
     * 获取企业租赁预警
     */
    // this.getEarlyWarning();
    this.getWarningList({ type: 2, pageNum: 1, pageSize: 8, status: 0 }, 2);
    /**
     * 获取下去统计数据
     */
    getStatisticsApi("").then((res) => {
      this.titList = res.data;
    });
    /**
     * 获取今年top10税收企业
     */
    getTaxationTop20Api("").then((res) => {
      this.taxationNowData = res.data.list;
      this.nowYearMonth = res.data?.yearMonth;
      this.nowtotal = this.taxationNowData[0]?.taxAmount;
    });
    /**
     * 获取去年top10税收企业
     */
    gettaxationTopOfLastYearApi("").then((res) => {
      this.taxationLastData = res.data;
      this.lastYearMonth = this.taxationLastData[0]?.year;
      this.lasttotal = this.taxationLastData[0]?.taxAmount;
    });
    // geinfoListApi('').then((res) => {
    // 	this.contentData = res.data
    // })
    /**
     * 获取企业税收预警
     */
    // this.data();
    // this.getCompanyTaxationWarning(this.taxationWarningYear);
    /**
     * 获取企业外迁预警
     */
    // this.getWarningList({ type: 3, pageNum: 1, pageSize: 8 }, 3);
    /**
     * 获取消息通知
     */
    this.getMessageList();
    this.getContractTodoList(); //租赁备案申请
    this.getContractPreTodoList(); //预申报表申请
    this.getSelectBusinessClueTodoList(); //重点线索申请
    this.getSelectHouseholdTodoList();
  },
  methods: {
    //获取待办事项-租赁备案申请
    async getContractTodoList() {
      await findContractTodoList().then((res) => {
        this.contractTodoList = res.data;
        console.log(this.contractTodoList, "租赁备案申请");
      });
    },
    //获取待办事项-预申报表申请
    async getContractPreTodoList() {
      await findContractPreTodoList({}).then((res) => {
        this.contractPreTodoList = res.data;
        console.log(this.contractPreTodoList, "预申报表申请");
      });
    },
    //获取待办事项-重点线索申请
    async getSelectBusinessClueTodoList() {
      await selectBusinessClueTodoList().then((res) => {
        this.keyClueTodoList = res.data;
        console.log(this.keyClueTodoList, "重点线索申请");
      });
    },
    //获取待办事项-户管
    async getSelectHouseholdTodoList() {
      await selectHouseholdTodoList().then((res) => {
        this.householeTodoList = res.data;
        console.log(this.householeTodoList, "huguan");
      });
    },

    handleTabChange: function (key) {
      console.log("Currently the active key is " + key);
      this.$set(this, "activeKey", key);
      if (key == "1") {
        //租赁预警
        // this.getEarlyWarning();
        this.getWarningList({ type: 2, pageNum: 1, pageSize: 8, status: 0 }, 2);
      } else if (key == "2") {
        const year = moment().year();
        //税收预警
        if (this.taxationMonthStr) {
          this.getWarningList(
            {
              type: 1,
              pageNum: 1,
              pageSize: 8,
              year,
              month: this.taxationMonthStr,
              status: 0,
            },
            1
          );
        } else {
          this.data();
          this.getWarningList(
            {
              type: 1,
              pageNum: 1,
              pageSize: 8,
              year,
              month: this.taxationWarningYear,
              status: 0,
            },
            1
          );
        }
      } else if (key == "3") {
        //外迁预警
        this.getWarningList({ type: 3, pageNum: 1, pageSize: 8, status: 0 }, 3);
      }
    },
    getEarlyWarning() {
      this.datalist = [];
      getEarlyWarningApi({}).then((res) => {
        let list = [];
        list = res.data;
        let listLength = list.length;
        let count = 0;
        for (let i = 0; i < listLength / 8; i++) {
          let liList = [];
          list.forEach((e) => {
            count++;
            if (count < 9) {
              liList.push(e);
            }
          });
          this.datalist.push({ list: liList });
          count = 0;
          list.splice(0, 8);
        }
        this.visible = false;
      });
    },
    formattedValue(value) {
      // return value;
      // 检查值是否为整数
      if (Number.isInteger(value.value)) {
        return value.value;
      } else {
        // 如果不是整数，则保留两位小数
        return Number(value.value).toFixed(2);
      }
    },
    getCompanyTaxationWarning(e) {
      this.taxationWarningList = [];
      let parmas = {
        year: 2023,
        month: e,
      };
      companyTaxationWarningApi(parmas).then((res) => {
        let list = [];
        list = res.data;
        let listLength = list.length;
        let count = 0;
        for (let i = 0; i < listLength / 8; i++) {
          let liList = [];
          list.forEach((e) => {
            count++;
            if (count < 9) {
              liList.push(e);
            }
          });
          this.taxationWarningList.push({ list: liList });
          count = 0;
          list.splice(0, 8);
        }
      });
    },
    getWarningList: function (params, type) {
      console.log(type);
      if (type == 1) {
        let tmpTaxationWarningList = [];
        APIQueryWarningPageByType(params).then((res) => {
          console.log(res);
          if (res.code == 0) {
            let list = res.data?.records || [];
            let listLength = list.length;
            let count = 0;
            for (let i = 0; i < listLength / 8; i++) {
              let liList = [];
              list.forEach((e) => {
                count++;
                if (count < 9) {
                  liList.push(e);
                }
              });
              tmpTaxationWarningList.push({ list: liList });
              this.taxationWarningList = tmpTaxationWarningList;
              count = 0;
              list.splice(0, 8);
            }
            console.log(this.taxationWarningList, "lllllllll");
          }
        });
      } else if (type == 2) {
        let tmpDataList = [];
        APIQueryWarningPageByType(params).then((res) => {
          console.log(res);
          if (res.code == 0) {
            let list = res.data?.records || [];
            let listLength = list.length;
            let count = 0;
            for (let i = 0; i < listLength / 8; i++) {
              let liList = [];
              list.forEach((e) => {
                count++;
                if (count < 9) {
                  liList.push(e);
                }
              });
              tmpDataList.push({ list: liList });
              this.datalist = tmpDataList;
              count = 0;
              list.splice(0, 8);
            }
            this.visible = false;
          }
        });
      } else {
        let tmpMigrationWarningList = [];
        APIQueryWarningPageByType(params).then((res) => {
          console.log(res);
          if (res.code == 0) {
            let list = res.data?.records || [];
            let listLength = list.length;
            let count = 0;
            for (let i = 0; i < listLength / 8; i++) {
              let liList = [];
              list.forEach((e) => {
                count++;
                if (count < 9) {
                  liList.push(e);
                }
              });
              tmpMigrationWarningList.push({ list: liList });
              this.migrationWarningList = tmpMigrationWarningList;
              count = 0;
              list.splice(0, 8);
            }
          }
        });
      }
    },
    data() {
      this.taxationWarningYear = moment(new Date())
        .subtract(1, "months")
        .format("MM");
    },
    handleChange(e) {
      this.$set(this, "taxationMonthStr", e);
      this.taxationWarningList = [];
      // this.getCompanyTaxationWarning(e);
      const year = moment().year();
      console.log(year, "year~~~");
      this.getWarningList(
        { type: 1, pageNum: 1, pageSize: 8, year, month: e, status: 0 },
        1
      );
    },
    myDefine(v) {
      this.visible = v;
    },
    // toDetail(record) {
    //   console.log(record);
    //   let requestParameters = {
    //     companyName: record,
    //   };
    //   backGroundPageByCondition(requestParameters).then((res) => {
    //     let dataObjData = res.data.records;
    //     this.$router.push(
    //       `/information-query/enterprise-detail?id=` + dataObjData[0].id
    //     );
    //   });
    // },
    toMigrationList: function (record) {
      console.log(record);
      // let dataObjData = res.data.records;
      this.$router.push({
        path: "/prewarning-management/prewarningInfo",
        query: {
          companyName: record.companyName,
          type: 3,
        },
      });
    },
    toMore: function (type) {
      this.$router.push({
        path: "/prewarning-management/prewarningInfo",
        query: {
          type,
        },
      });
    },
    toMoreFn(val) {
      if (val == 1) {
        this.$router.push({
          path: "/registration-record-review/record",
        });
      } else if (val == 2) {
        this.$router.push({
          path: "/pre-declaration-check/query",
        });
      } else if (val == 3) {
        this.$router.push({
          path: "/key-clue-management",
        });
      } else if (val == 4) {
        this.$router.push({
          path: "/household-infor-managment",
        });
      } else {
        return false;
      }
    },
    toApprove(n, record) {
      if (n == 1) {
        if (record.status == 1) {
          this.$router.push({
            path: "/registration-record-review/record/reviewItem",
            query: {
              type: "1", //view 查看，check 或者空为操作页面否则只是查看
              // status: 2, ////// status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
              filingNumber: record.filingNumber,
              id: record.id,
            },
          });
        } else if (
          record.status == 2 ||
          record.status == 3 ||
          record.status == 5 ||
          record.status == 12
        ) {
          this.$router.push({
            path: "/registration-record-review/record/trial",
            query: {
              viewType: "check",
              status: record.status,
              filingNumber: record.filingNumber,
              id: record.id,
            },
          });
        } else if (record.status == 13 || record.status == 14) {
          this.$router.push({
            path: "/registration-record-review/record",
            query: {
              record: record,
            },
          });
        } else {
          return;
        }
      } else if (n == 2) {
        this.$router.push(
          `/pre-declaration-check/query/add?statu=1` + `&id=` + record.id
        );
      } else if (n == 3) {
        this.$router.push({
          path: "/key-clue-management/viewKeyClue?statu=3" + "&id=" + record.id,
        });
      } else if (n == 4) {
        let status = this.roles.includes("户管信息初审人") ? "1" : "2";
        this.$router.push({
          path:
            "/household-infor-managment/viewHosehold?statu=3" +
            "&id=" +
            record.id +
            "&status=" +
            status,
        });
      }
    },

    onShow(e, type) {
      this.$set(this, "detailInfo", {
        id: e.id,
        type,
        canEdit: 1,
      });
      // this.$set(this, "detailInfo", {
      //   id: e.id,
      //   type: 2,
      //   canEdit: 1,
      // });
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
    },
    //获取消息列表
    getMessageList: function () {
      ApiNoticeList({ pageNo: 1, pageSize: 3 }).then((response) => {
        console.log(response);
        if (response.code == 0) {
          this.$set(this, "messageList", response?.data?.records || []);
        }
      });
    },
    //点击更多消息至消息列表
    gotoMessageList: function () {
      this.$router.push({
        path: "/portal-management/notification-management",
      });
    },
    //点击单行消息至消息详情
    getoMessageDetail: function (item) {
      this.$router.push({
        path: "/notification-management/detail",
        query: {
          id: item.id,
        },
      });
    },
    handleOKCallback: function () {
      this.$set(this, "visible", false);
      this.$set(this, "detailInfo", {});
      // this.$refs.prewarningInfoTable.search(this.form);
      this.handleTabChange(this.activeKey);
    },
    handleCancelCallback: function () {
      this.$set(this, "visible", false);
      this.$set(this, "detailInfo", {});
    },
  },
};
</script>

<style lang="less" scoped>
@import "./index.less";
@import "~@/assets/styles/carousel.css";
.left-b {
  .box-out {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .box-list {
      width: calc(50% - 5px);
      min-height: 320px;
      border-radius: 5px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: rgba(244, 250, 255, 1);
      .box-tit {
        width: 100%;
        height: 47px;
        border-radius: 5px;
        padding: 0 10px;
        margin-bottom: 10px;
        background: rgba(129, 211, 248, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .boxTit-name {
          font-size: 14px;
          color: #1677ff;
          img {
            display: inline-block;
            width: 20px;
            height: 20px;
          }
          span {
            display: inline-block;
            margin-left: 5px;
          }
        }
        .more {
          color: #1677ff;
          font-size: 14px;
          cursor: pointer;
        }
      }
      .box-tabel {
        width: 100%;
        background: #fff;
        padding: 10px 10px;
        border-radius: 5px;
        /deep/.ant-table-thead > tr > th {
          padding: 5px !important;
        }
      }
    }
  }
}

.message-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 20px;
}
.message {
  font-size: 22px;
  font-weight: 600;
}
.message-more {
  cursor: pointer;
}
.to-more {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 12px;
  margin-bottom: 12px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
}
</style>

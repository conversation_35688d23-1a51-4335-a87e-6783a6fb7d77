<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 表单引擎 - 图片控件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <picture-dom :data="formData"></picture-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <picture-form v-bind:data.sync="formData"></picture-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 图片控件 DOM/Form
import {
  PictureDom,
  PictureForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/Picture";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    PictureDom,
    PictureForm
  },
  data() {
    return {
      formData: {
        title: null, //标题
        optionsData: {
          fileSize: null, //文件大小
          fileCompany: "MB", //文件大小的单位
          fileNum: null //文件数量
        },
        isRequired: true //是否必填
      },
      moduleVoList: []
    };
  },
  // mounted() {
  //   this.handCheck();
  // },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.title, //标题
        // placeholder: JSON.stringify(this.formData.placeholder), //提示文字
        optionsData: JSON.stringify(this.formData.optionsData), //文件信息
        notNull: this.formData.isRequired ? 1 : 0, //是否必填
        inputType: "picture",
        inputName: "1"
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "zxTestPicture", //保留字段随便传
        formId: "zxTestPicture", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data).then(() => {
        this.$message.info("保存成功");
      });
    },
    handCheck() {
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        let allData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "picture") {
              let itemData = {
                optionsData: JSON.parse(item.optionsData),
                title: item.inputTitle,
                isRequired: item.notNull
              };
              allData.push(itemData);
            }
          });
        });
        this.formData = allData[allData.length - 1];
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

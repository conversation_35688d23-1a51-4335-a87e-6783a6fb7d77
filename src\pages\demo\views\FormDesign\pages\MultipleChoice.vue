<!--
* <AUTHOR>
* @time 2020-9-1
* @dec 表单引擎 - 多选控件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <multiple-choice-dom :data="formData"></multiple-choice-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <multiple-choice-form v-bind:data.sync="formData"></multiple-choice-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 多选控件 DOM/Form
import {
  MultipleChoiceDom,
  MultipleChoiceForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/MultipleChoice";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    MultipleChoiceDom,
    MultipleChoiceForm
  },
  data() {
    return {
      formData: {
        title: null, //标题
        placeholder: {
          tipsTitleText: null //标题提示
        },
        optionsData: [
          {
            optionContent: "", //选项文字
            placeholder: "选项" //选择提示
          }
        ],
        isRequired: true, //是否必填
        verification: true //是否验证
      },
      moduleVoList: []
    };
  },
  // mounted() {
  //   this.handCheck();
  // },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.title, //标题
        placeholder: JSON.stringify(this.formData.placeholder), //提示文字
        optionsData: JSON.stringify(this.formData.optionsData), //多选选项数据
        notNull: this.formData.isRequired ? 1 : 0, //是否必填
        isRegex: this.formData.verification ? 1 : 0, //是否验证
        inputType: "multipleChoice",
        inputName: "1"
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "zxTestMultipleChoice", //保留字段随便传
        formId: "zxTestMultipleChoice", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data).then(() => {
        this.$message.info("保存成功");
      });
    },
    handCheck() {
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        let allData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "multipleChoice") {
              let itemData = {
                placeholder: JSON.parse(item.placeholder),
                optionsData: JSON.parse(item.optionsData),
                title: item.inputTitle,
                isRequired: item.notNull,
                verification: item.isRegex
              };
              allData.push(itemData);
            }
          });
        });
        this.formData = allData[allData.length - 1];
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

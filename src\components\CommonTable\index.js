import T from "ant-design-vue/es/table/Table";
export default {
  props: {
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    loadData: {
      type: Function,
      required: true,
    },
    showPagination: {
      type: Boolean,
      required: false,
      default: () => true,
    },
  },
  data() {
    return {
      dataSource: [],
      loading: false,
      pagination: {
        showSizeChanger: true,
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共有 ${total} 条数据`,
        onChange: this.onPageChange,
        onShowSizeChange: this.onPageSizeChange,
      },
      slotKeys: [],
    };
  },
  watch: {
    showPagination: {
      handler() {
        this.$nextTick(() => {
          if (!this.showPagination) {
            this.$set(this, "pagination", false);
          }
        });
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.onLoad();
  },
  methods: {
    onLoad: function(queryParams) {
      if (this.loadData) {
        this.loading = true;
        let data = this.loadData({
          ...queryParams,
          pageNo:
            this.showPagination && this.pagination
              ? this.pagination.current
              : 1, //这里如果不需要分页器显示，默认no为1，临时处理
          pageSize:
            this.showPagination && this.pagination
              ? this.pagination.pageSize
              : 10, //这里如果不需要分页器显示，默认size为10，临时处理
        });
        data
          .then((result) => {
            let tmpDataSource = result.records || [];
            if (tmpDataSource.length > 0) {
              tmpDataSource.forEach((element, index) => {
                element["common_table_id"] = (index + 9).toString(36) + index;
              });
            }
            this.dataSource = result.records || [];
            if (this.showPagination) {
              const pager = { ...this.pagination };
              pager.total = result.total || 0;
              this.pagination = pager;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        console.log("没有配置数据源");
      }
    },
    onPageChange: function(current, size) {
      console.log(current, size);
      const pager = { ...this.pagination };
      pager.current = current;
      this.pagination = pager;
      this.onLoad();
    },
    onPageSizeChange: function(current, size) {
      console.log(current, size);
      const pager = { ...this.pagination };
      pager.current = 1;
      pager.pageSize = size;
      this.pagination = pager;
      this.onLoad();
    },
    search: function(searchParams) {
      //点击查询时先重新分页pageNo
      if (this.showPagination) {
        const pager = { ...this.pagination };
        pager.current = 1;
        this.pagination = pager;
      }
      this.onLoad(searchParams);
    },
  },
  render() {
    const props = {};
    const slots = Object.keys(this.$slots).map((slot) => {
      return <template slot={slot}>{this.$slots[slot]}</template>;
    });
    const ATablePropsKeys = Object.keys(this.$attrs) || [];
    Object.keys(T.props).map((key) => {
      if (ATablePropsKeys.includes(key)) {
        props[key] = this.$attrs[key];
      }
    });
    const table = (
      <a-table
        {...{ props }}
        scopedSlots={this.$scopedSlots}
        columns={this.columns}
        dataSource={this.dataSource}
        pagination={this.pagination}
        loading={this.loading}
        rowKey="common_table_id"
      >
        {slots}
      </a-table>
    );
    return (
      <div class="common-table" style="margin:20px">
        {table}
      </div>
    );
  },
};

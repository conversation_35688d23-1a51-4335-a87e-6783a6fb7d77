import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 税收预警分页查询
 */
export function getTaxationWarningPage(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxationWarning/pageByCondition",
    method: "post",
    params,
  });
}
/**
 * 关闭税收信息
 */
export function ApiCloseWarningMessage(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxationWarning/close",
    method: "post",
    params,
  });
}
/**
 * 分页租赁预警查询
 */
export function ApiGetleaseWarning(params) {
  return api({
    url: BASE_URL + "/buildings/lease/queryLeasesWaringPage",
    method: "post",
    params,
  });
}
/**
 * 处理租赁预警
 */
export function ApiResolveWarning(params) {
  return api({
    url: BASE_URL + "/buildings/lease/dealLeasesWaring",
    method: "post",
    params,
  });
}

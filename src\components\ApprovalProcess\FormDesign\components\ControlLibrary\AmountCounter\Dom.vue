<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 金额计数器控件
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '金额'"
      style="margin-bottom:unset"
      help="大写 壹万元整(示例)"
      prop="amountCounter"
      :rules="[
        {
          required: data.notNull,
          message: '请输入',
          trigger: 'blur'
        }
      ]"
      ><a-input
        v-model="form.amountCounter"
        :placeholder="
          data.optionsData.capitalize
            ? numberChinese(data.placeholder.placeholderText)
            : data.placeholder.placeholderText
        "
        @blur="
          () => {
            if (data.optionsData.capitalize) {
              amountCounter = numberChinese(amountCounter);
            }
          }
        "
      ></a-input>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { numberChinese } from "@/common/filters/common";
export default {
  props: {
    data: {
      type: Object,
      default() {
        return { placeholder: "" };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        amountCounter: null
      }
    };
  },
  methods: {
    numberChinese
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

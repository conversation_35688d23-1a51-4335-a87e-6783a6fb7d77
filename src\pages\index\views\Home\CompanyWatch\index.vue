<template>
  <div>
    <div class="top">
      <div class="left">
        <div class="top-chart">
          <ParkArea></ParkArea>
        </div>
        <!-- <div class="l-tit">
          <div class="disp">
            <div class="tit">园区面积</div>
            <div class="disp">
              <div class="disp">
                <div class="tit-item kzmj"></div>
                <span>空置面积</span>
              </div>
              <div class="disp">
                <div class="tit-item zlmj"></div>
                <span>租赁面积</span>
              </div>
              <div class="disp">
                <div class="tit-item lhydq"></div>
                <span>预计6个月到期</span>
              </div>
            </div>
          </div>
          <div class="l-main">
            <progressThird></progressThird>
          </div>
        </div> -->
      </div>
      <div class="right">
        <div class="top-chart">
          <ParkOccupancyRate></ParkOccupancyRate>
        </div>
        <!-- <div class="tit">园区入驻率</div>
        <a-carousel autoplay :autoplaySpeed="20000" arrows>
          <div
            class="r-item"
            v-for="(item1, index) in OccupancyList"
            :key="index"
          >
            <div
              class="r-item-c"
              v-for="(item, index) in item1.list"
              :key="index"
            >
              <a-progress
                type="circle"
                :percent="item.occupancyRate"
                strokeWidth="15"
                strokeColor="#6faaff"
                :format="
                  () =>
                    item.occupancyRate === 100
                      ? '100%'
                      : `${item.occupancyRate}%`
                "
              />
              <div>
                <a-tooltip>
                  <template #title>{{ item.parkName }}</template>
                  <div class="item-font">{{ item.parkName }}</div>
                </a-tooltip>
              </div>
            </div>
          </div>
        </a-carousel> -->
      </div>
    </div>
    <div class="bot">
      <div class="left">
        <div class="tit">载体占比</div>
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div class="left-chart">
            <carrier-proportion></carrier-proportion>
          </div>
          <a-divider type="vertical" dashed="true" />
          <div class="left-font-out">
            <div
              class="left_f-item"
              v-for="item in industrialParkAreaList"
              :key="item.value"
            >
              <a-tooltip>
                <template #title
                  >{{ item.parkName }} {{ item.value }}家</template
                >
                <div class="left-font">
                  <span style="margin-right: 40px;"> {{ item.parkName }}</span>
                  {{ item.value }}家
                </div>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="tit">园区政策</div>
        <div class="right-con" v-for="item in infoData" :key="item.id">
          <div class="r-img-out">
            <div class="r-img">
              <img
                :src="
                  require(`./../../../../../assets/image/common/yqzc` +
                    item.id +
                    `.png`)
                "
                alt=""
              />
            </div>
            <div class="r-c-item">
              <a-tooltip>
                <template #title>{{ item.content }}</template>
                <div class="item-content">{{ item.content }}</div>
                <div class="item-time">{{ item.time }}</div>
              </a-tooltip>
            </div>
          </div>
          <a-divider style="margin: 12px 0" />
        </div>
      </div>
    </div>
    <!-- <park-area-bar /> -->
  </div>
</template>

<script>
import {
  ParkOccupancyRate,
  ParkArea,
  CarrierProportion,
} from "./../components";
// import progressThird from "./components/progressThird";
// import ParkAreaBar from "./components/ParkAreaBar";
import {
  // getAreaPolicyApi,
  getAreaOccupancyApi,
  getCarrierProportionApi,
} from "@/pages/index/data/api/ComponyWatch";
export default {
  components: {
    // progressThird,
    CarrierProportion,
    ParkOccupancyRate,
    ParkArea,
  },
  data() {
    return {
      industrialParkAreaList: [],
      infoData: [
        {
          id: 1,
          time: "2023-06-21 11:02:11",
          content:
            "自2023年1月1日至2023年12月31日，增值税小规模纳税人适用3%征收率的应税销售收入，减按1%征收率征收增值税；适用3%预征率的预缴增值税项目，减按1%预征率预缴增值税。",
        },
        {
          id: 2,
          time: "2023-06-21 11:02:11",
          content:
            "允许生产性服务业纳税人按照当期可抵扣进项税额加计5%抵减应纳税额。生产性服务业纳税人，是指提供邮政服务、电信服务、现代服务、生活服务取得的销售额占全部销售额的比重超过50%的纳税人",
        },
        {
          id: 3,
          time: "2023-06-21 11:02:11",
          content:
            "纳税人适用加计抵减政策的其他有关事项，按照《财政部 税务总局 海关总署关于深化增值税改革有关政策的公告》（财政部 税务总局 海关总署公告2019年第39号）、《财政部 税务总局关于明确生活性服务业增值税加计抵减政策的公告》（财政部 税务总局公告2019年第87号）等有关规定执行。",
        },
      ],
      OccupancyList: [],
    };
  },
  methods: {},
  computed: {},
  mounted() {
    // getAreaPolicyApi('').then((res) => {
    //   this.infoData = res.data
    // })
    getAreaOccupancyApi("").then((res) => {
      let list = [];
      list = res.data;
      let listLength = list.length;
      let count = 0;
      for (let i = 0; i < listLength / 10; i++) {
        let liList = [];
        list.forEach((e) => {
          count++;
          if (count < 11) {
            liList.push(e);
          }
        });
        this.OccupancyList.push({ list: liList });
        count = 0;
        list.splice(0, 10);
      }
    });

    getCarrierProportionApi("").then((res) => {
      this.industrialParkAreaList = res.data;
    });
  },
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/carousel.css";

.top {
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .top-chart {
    width: 98%;
    margin: 2px auto;
    height: 375px;
  }

  .left {
    width: 49%;
    padding: 15px;
    border: 1px solid #fff;
    border-radius: 8px;
    background-color: #fff;
    box-sizing: border-box;

    .l-tit {
      .tit {
        font-size: 22px;
        font-weight: 600;
      }

      .disp {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .tit-item {
        width: 30px;
        height: 14px;
        line-height: 14px;
        border-radius: 35px;
        margin: 0 5px;
      }

      .kzmj {
        background-color: #9079fd;
        border: 1px solid #9079fd;
      }

      .zlmj {
        background-color: #2777e4;
        border: 1px solid #2777e4;
      }

      .lhydq {
        background-color: #2fcbbe;
        border: 1px solid #2fcbbe;
      }
    }

    .l-main {
      height: 365px;
      overflow-x: hidden;
      overflow-y: auto;

      .l-m-item {
        width: 90%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
      }
    }
  }

  .right {
    width: 49%;
    padding: 15px;
    border: 1px solid #fff;
    border-radius: 8px;
    background-color: #fff;
    box-sizing: border-box;

    .tit {
      font-size: 22px;
      font-weight: 600;
    }

    .r-item {
      width: 100%;
      display: flex !important;
      flex-wrap: wrap;
      margin-top: 30px;
      height: 365px !important;

      .r-item-c {
        width: 20%;
        margin-top: 5px;
        text-align: center;

        .item-font {
          width: 120px;
          margin: 10px auto;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }
  }
}

.bot {
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;

  .left {
    width: 49%;
    padding: 15px;
    border: 1px solid #fff;
    border-radius: 8px;
    background-color: #fff;
    box-sizing: border-box;

    .tit {
      font-size: 22px;
      font-weight: 600;
    }

    .left-chart {
      width: 350px;
      height: 315px;
    }

    .left-font-out {
      width: 48%;
      height: 330px;
      overflow-x: hidden;
      overflow-y: auto;

      .left_f-item {
        padding-right: 10px;
        margin-bottom: 10px;
        .left-font {
          font-weight: 500;
          margin-left: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          span {
            display: inline-block;
            width: 100px;
          }
        }
      }
    }
  }

  .right-con {
    margin-top: 20px;
  }

  .right {
    width: 49%;
    padding: 15px;
    border: 1px solid #fff;
    border-radius: 8px;
    background-color: #fff;
    box-sizing: border-box;

    .tit {
      font-size: 22px;
      font-weight: 600;
    }

    .r-img-out {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .r-img {
      width: 72px;
      height: 70px;
      margin-right: 12px;
    }

    .item-content {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }

    .item-time {
      text-align: right;
    }
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 5px;
  /* 滚动条宽度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #ffffff;
  /* 轨道背景颜色 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0);
  /* 滑块背景颜色 */
}

/* 滚动条滑块hover状态 */
::-webkit-scrollbar-thumb:hover {
  background-color: #e6e6e6;
  /* hover状态下滑块背景颜色 */
}
</style>

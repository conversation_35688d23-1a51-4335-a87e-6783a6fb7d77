<!-- 载体查询 -->
<template>
  <div class="carrier">
    <div class="carrierFrom">
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="归并园区">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                v-model="queryParam.mergedParkName"
                @change="changeIndustryComponey"
                placeholder="全部"
              >
                <a-select-option
                  :value="item"
                  v-for="item in enumerateObj.industryComponey"
                  :key="item.item"
                  >{{ item }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="载体地址">
              <a-input
                allowClear
                v-model="queryParam.roomAddress"
                placeholder="园区名称/xx号楼/xx层/xx室"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="状态">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                allowClear
                v-model="queryParam.roomStatus"
                placeholder="全部"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in stateArr"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="房间面积">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                allowClear
                v-model="queryParam.area"
                placeholder="全部"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in parkArr"
                  :key="item.value"
                  >{{ item.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="租赁到期时间">
              <a-range-picker
                allowClear
                style="width: 100%"
                v-model="leaseEndTime"
                @change="onChange"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8" align="right">
            <a-form-item :wrapper-col="{ span: 24 }">
              <a-button
                type="primary"
                @click="search"
                style="margin-right: 20px"
                >查询</a-button
              >
              <a-button type="default" @click="reset">重置</a-button>
              <a-button
                type="primary"
                class="export"
                style="margin-left: 15px"
                @click="deduced()"
                >导出</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-card style="width: 100%; margin-top: 20px">
      <div style="display: flex; justify-content: end; margin-bottom: 20px">
        <a-button
          type="primary"
          @click="add()"
          v-if="!roles.includes('实业公司')"
          >新增</a-button
        >
      </div>
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        :rowKey="(record) => record.floorRoomId"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </span>
        <span slot="parkName" slot-scope="text, record">
          <a
            href="javascript:;"
            @click="toDetail(text, record)"
            v-if="!roles.includes('实业公司')"
            >{{ record.parkName }}</a
          >
          <span v-else>{{ record.parkName }}</span>
        </span>
        <span slot="tenantry" slot-scope="text, record">
          <a
            href="javascript:;"
            @click="toCompany(text, record)"
            v-if="!roles.includes('实业公司')"
            >{{ record.tenantry }}</a
          >
          <span v-else>{{ record.tenantry }}</span>
        </span>
        <span
          slot="action"
          slot-scope="text, record"
          v-if="!roles.includes('实业公司')"
        >
          <a
            href="javascript:;"
            style="margin-right: 10px"
            @click="edit(text, record)"
            >编辑</a
          >
          <a-popconfirm
            title="是否要删除该载体？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="remove(text, record)"
            @cancel="cancel"
          >
            <a href="#">删除</a>
          </a-popconfirm>
        </span>
      </s-table>
    </a-card>
    <!-- 添加和编辑载体的弹框 -->
    <a-modal
      v-model="visible"
      :title="title"
      @ok="handleOk(isEdit)"
      @cancel="handleClose()"
    >
      <a-form-model :model="modalForm" ref="ruleForm" :rules="rules">
        <a-form-model-item
          prop="mergedParkName"
          label="归并园区"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-select
            :disabled="disabled"
            v-model="modalForm.mergedParkName"
            placeholder="全部"
            @change="handleMergeParkChange"
          >
            <a-select-option
              :value="item"
              v-for="item in enumerateObj.mergeParkObj"
              :key="item.item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          label="园区"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          prop="parkName"
        >
          <a-select
            :disabled="disabled"
            v-model="modalForm.parkName"
            placeholder="全部"
            @change="handleParkChange"
          >
            <a-select-option
              :value="item"
              v-for="item in enumerateObj.parkObj"
              :key="item.item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          label="楼号"
          prop="buildingNumber"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-select
            :disabled="disabled"
            v-model="modalForm.buildingNumber"
            placeholder="全部"
            @change="handlebuildingNoChange"
          >
            <a-select-option
              :value="item"
              v-for="item in enumerateObj.buildingNoObj"
              :key="item.item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          prop="floorNumber"
          label="楼层"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            :disabled="disabled"
            allowClear
            v-model="modalForm.floorNumber"
            placeholder="请输入楼层"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item
          prop="roomNumber"
          label="房间号"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            allowClear
            v-model="modalForm.roomNumber"
            placeholder="请输入房间号"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item
          prop="roomArea"
          label="房间面积"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            type="number"
            @input="handleInput(modalForm.roomArea)"
            v-model="modalForm.roomArea"
            placeholder="请输入房间面积"
          />
          <!-- <a-input
            allowClear
            v-model="modalForm.roomArea"
            placeholder="请输入房间面积"
          ></a-input> -->
        </a-form-model-item>
        <!-- <a-form-model-item
          label="楼层状态"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-radio-group v-model="modalForm.floorStatus">
            <a-radio value="0"> 空置 </a-radio>
            <a-radio value="1"> 占用 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          label="房间状态"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-radio-group v-model="modalForm.roomStatus">
            <a-radio value="0"> 空置 </a-radio>
            <a-radio value="1"> 占用 </a-radio>
          </a-radio-group>
        </a-form-model-item> -->
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { queryCarrierInformation } from "@/pages/demo/data/api/api/park";
import {
  ApiGetBuildFloorNumberByPark,
  ApiSearchAllMergePark,
  ApiFindParkNamesByMergedName,
  ApiExportCarryQuerry,
  ApiDeleteHousingCarriers,
  ApiAddHousingCarriers,
  ApiEditHousingCarriers,
  ApiGetHousingCarriers,
} from "@/pages/index/data/api/InfomationQuery";
import STable from "@/components/Table";
import moment from "moment";
import { parseNumFloat } from "@/common/utils/utils.js";
export default {
  components: {
    STable,
  },
  data() {
    return {
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles,
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: "80px",
          fixed: "left",
          align: "center",
        },
        {
          title: "归并园区",
          dataIndex: "mergedParkName",
          width: 120,
          align: "center",
          fixed: "left",
        },
        {
          title: "园区",
          dataIndex: "parkName",
          scopedSlots: { customRender: "parkName" },
          width: 220,
          align: "center",
        },
        {
          title: "出租方",
          dataIndex: "lessor",
          width: 120,
          align: "center",
        },
        {
          title: "承租方",
          dataIndex: "tenantry",
          align: "center",
          width: 220,
          scopedSlots: { customRender: "tenantry" },
        },
        {
          title: "楼号",
          dataIndex: "buildingNumber",
          width: 120,
          align: "center",
        },
        {
          title: "楼层",
          dataIndex: "floorNumber",
          width: 120,
          align: "center",
        },
        {
          title: "房间号",
          dataIndex: "roomNumber",
          width: 120,
          align: "center",
        },
        {
          title: "房间面积(平米)",
          dataIndex: "roomArea",
          align: "center",
          width: 160,
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "状态",
          dataIndex: "roomStatus",
          width: 60,
          align: "center",
          customRender: (text) => {
            return text == 1 ? "占用" : "空置";
          },
        },
        {
          title: "属地情况",
          dataIndex: "territorialSituation",
          align: "center",
          width: 220,
        },
        {
          title: "租赁单价(元/天/平米)",
          dataIndex: "rentalUnitPrice",
          align: "center",
          width: 120,
          customRender(text) {
            return parseNumFloat(text);
          },
        },
        {
          title: "合同起始时间",
          dataIndex: "leaseStartTime",
          align: "center",
          width: 160,
          customRender: (text) => {
            return text ? moment(new Date(text)).format("YYYY-MM-DD") : "";
          },
        },
        {
          title: "租赁到期时间",
          dataIndex: "leaseEndTime",
          align: "center",
          width: 160,
          customRender: (text) => {
            return text ? moment(new Date(text)).format("YYYY-MM-DD") : "";
          },
        },
        // {
        //   title: "注册情况",
        //   dataIndex: "territorialSituation",
        //   align: "center",
        //   width: 220
        // },
        {
          title: "关联企业",
          dataIndex: "entRelationName",
          align: "center",
          width: 220,
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          width: 150,
          fixed: "right",
          scopedSlots: { customRender: "action" },
        },
      ],
      isFlag: true,
      title: "", //定义弹框的标题
      visible: false, //控制弹框显示的变量，false不显示
      disabled: false,
      isEdit: false, //标识是编辑页面还是新增，true表示编辑，false表示新增
      //弹框中的表单数据
      modalForm: {
        parkName: undefined,
        mergedParkName: undefined,
        buildingNumber: undefined,
        floorNumber: undefined,
        roomNumber: undefined,
        roomArea: undefined,
        floorStatus: "0",
        roomStatus: "0",
      },
      rules: {
        mergedParkName: [
          { required: true, message: "请选择归并园区", trigger: "blur" },
        ],
        parkName: [{ required: true, message: "请选择园区", trigger: "blur" }],
        buildingNumber: [
          { required: true, message: "请选择楼号", trigger: "blur" },
        ],
        floorNumber: [
          { required: true, message: "请输入楼层", trigger: "blur" },
        ],
        roomNumber: [
          { required: true, message: "请输入房间号", trigger: "blur" },
        ],
        roomArea: [
          { required: true, message: "请输入房间面积", trigger: "blur" },
        ],
      },
      timeList: [],
      leaseEndTime: "",
      queryParam: {
        mergedParkName: undefined,
        parkName: undefined,
        // floor: "",
        // area: "",
        // status: "",
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 10,
      },
      parkArr: [
        {
          desc: "200平米以内",
          value: "0",
        },
        {
          desc: "201平米-500平米",
          value: "1",
        },
        {
          desc: "201平米-500平米",
          value: "2",
        },
        {
          desc: "201平米-500平米",
          value: "3",
        },
        {
          desc: "501平米-800平米",
          value: "4",
        },
        {
          desc: "800平米-1000平米",
          value: "4",
        },
        {
          desc: "1000平米以上",
          value: "4",
        },
      ],
      stateArr: [
        {
          name: "空置",
          value: "0",
        },
        {
          name: "占用",
          value: "1",
        },
      ],
      //枚举对象存放后台返回的各类数据
      enumerateObj: {
        buildingNums: [],
        parkList: [],
        mergeParkObj: [], //存放所有归并园区数据
        parkObj: [], //存放根据归并园区查出来的园区数据
        buildingNoObj: [], //存放根据园区查出来的楼号数据
      }, // 查询条件对象
    };
  },
  created() {
    // this.init()
    this.getRoomArea();
    if (this.roles.includes("实业公司")) {
      this.columns.pop();
    }
  },
  methods: {
    deduced() {
      ApiExportCarryQuerry(this.queryParam);
      //帮我写一个防抖函数ts
    },
    // 加载数据方法 必须为 Promise 对象
    async loadData({ pageNo, pageSize }) {
      console.log("pageNo", pageNo, "pageSize", pageSize, "loadData");
      this.pageNo = pageNo;
      this.pageSize = pageSize;
      let industryComponey;

      if (this.queryParam.mergedParkName) {
        console.log(111);
      } else {
        industryComponey = await ApiSearchAllMergePark();
        industryComponey.data = industryComponey.data.filter(
          (item) => item != null
        );
        this.enumerateObj.industryComponey = industryComponey.data.map(
          (item) => item
        );
        this.queryParam.mergedParkName = industryComponey.data[0];
        const requestParameters = Object.assign(this.queryParam, {
          pageNum: pageNo,
          pageSize,
        });
        console.log("---***", requestParameters, this.queryParam);
      }
      let queryParam = Object.assign({}, this.queryParam, {
        pageNum: pageNo,
        pageSize: pageSize,
      });
      return queryCarrierInformation(queryParam).then((res) => {
        let dataObj = res.data;
        dataObj.data = res.data.records;
        dataObj.totalCount = res.data.total;
        dataObj.pageSize = res.data.size;
        dataObj.pageNo = res.data.current;
        return dataObj;
      });
    },
    handleInput(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.modalForm.roomArea = val;
    },
    changeIndustryComponey(value) {
      console.log("changeIndustryComponey", value);
      if (!value) {
        return;
      }
      this.queryParam.mergedParkName = value;
      // ApiFindParkNamesByMergedName({
      //   mergedParkName: value
      // }).then((res) => {
      //   console.log(res, "**园区名称**")
      //   this.enumerateObj.parkList = res.data
      //   if (!res.data || res.data.length == 0) return
      //   this.queryParam.parkName = res.data[0]

      //   ApiGetBuildFloorNumberByPark({
      //     parkName: res.data[0] || this.queryParam.parkName
      //   }).then((res) => {
      //     this.enumerateObj.buildingNums = res.data.buildingNumbers
      //   })
      // })
    },
    changeParkName(value) {
      this.queryParam.buildingNumber = undefined;
      if (!value) {
        return;
      }
      ApiGetBuildFloorNumberByPark({
        parkName: value || this.queryParam.parkName,
      }).then((res) => {
        this.enumerateObj.buildingNums = res.data.buildingNumbers;
      });
    },
    reset() {
      this.queryParam = {
        parkName: "",
        pageNum: 1,
        pageSize: 10,
        mergedParkName: this.queryParam.mergedParkName,
      };
      this.leaseEndTime = [];
      this.$refs.table.refresh(true);
    },
    async getRoomArea() {
      this.parkArr = await this.$getDictByType({ dictCode: "roomArea" });
    },

    async init() {
      if (this.queryParam.mergedParkName) {
        return;
      }
      let industryComponey = await ApiSearchAllMergePark();
      industryComponey.data = industryComponey.data.filter(
        (item) => item != null
      );
      this.enumerateObj.industryComponey = industryComponey.data.map(
        (item) => item
      );
      this.queryParam.industryComponey = industryComponey.data[0];
      ApiFindParkNamesByMergedName({
        mergedParkName: industryComponey.data[0],
      }).then((res) => {
        console.log(res, "**园区名称**");
        this.enumerateObj.parkList = res.data;
        this.queryParam.parkName = res.data[0];
        if (!res.data || res.data.length == 0) return;
        ApiGetBuildFloorNumberByPark({
          parkName: res.data[0] || this.queryParam.parkName,
        }).then((res) => {
          this.enumerateObj.buildingNums = res.data.buildingNumbers;
        });
      });
    },
    search() {
      console.log(this.queryParam);
      this.$refs.table.refresh(true);
    },
    onChange(date, dateString) {
      this.queryParam.startTime = dateString[0];
      this.queryParam.endTime = dateString[1];
    },
    toDetail(text, record) {
      this.$router.push(
        `/information-query/park-detail?parkName=${record.parkName}`
      );
    },
    toCompany(text, record) {
      console.log(record, "record");
      if (record.id) {
        this.$router.push(
          `/information-query/enterprise-detail?id=${record.id}`
        );
      }
    },
    //删除载体的方法
    remove(text, record) {
      console.log(record, 2222);
      ApiDeleteHousingCarriers({ floorRoomId: record.floorRoomId }).then(
        (res) => {
          if (res.code == 0) {
            console.log(res);
            this.$message.success("删除成功");
            //刷新列表
            this.$refs.table.refresh();
          }
        }
      );
    },
    //编辑载体的方法
    edit(text, record) {
      this.title = "编辑载体";
      this.visible = true;
      this.disabled = true;
      this.isEdit = true;
      console.log(record, "00000");
      //获取信息显示
      ApiGetHousingCarriers({ floorRoomId: record.floorRoomId }).then((res) => {
        if (res.code == 0) {
          this.modalForm = res.data[0];
        }
      });
    },
    //点击添加按钮调用的方法
    add() {
      this.visible = true;
      this.title = "添加载体";
      this.isEdit = false;
      this.modalForm = {
        parkName: undefined,
        mergedParkName: undefined,
        buildingNumber: undefined,
        floorNumber: undefined,
        roomNumber: undefined,
        roomArea: undefined,
        floorStatus: "0",
        roomStatus: "0",
      };
      //获取所有归并园区
      ApiSearchAllMergePark().then((res) => {
        this.enumerateObj.mergeParkObj = res.data;
      });
    },
    //当选择归并园区或者改变选择时调用方法，value是选中的option值
    handleMergeParkChange(value) {
      console.log(value, ",,,,,");
      ApiFindParkNamesByMergedName({ mergedParkName: value }).then((res) => {
        this.enumerateObj.parkObj = res.data;
      });
    },
    //当选择园区或者改变选择时调用方法，value是选中的option值
    handleParkChange(value) {
      console.log(value, ",,,,,");
      ApiGetBuildFloorNumberByPark({ parkName: value }).then((res) => {
        this.enumerateObj.buildingNoObj = res.data.buildingNumbers;
        console.log(res.data, "ttt");
      });
    },
    handlebuildingNoChange(value) {
      console.log(value);
    },
    //点击关闭弹框的方法
    handleClose() {
      console.log("nihao");
      this.modalForm = {
        parkName: undefined,
        mergedParkName: undefined,
        buildingNumber: undefined,
        floorNumber: undefined,
        roomNumber: undefined,
        roomArea: undefined,
        floorStatus: "0",
        roomStatus: "0",
      };
      this.visible = false;
      this.disabled = false;
      console.log(this.modalForm, "5555555");
      this.$refs.ruleForm.resetFields();
    },
    //弹框点击确定按钮调用的方法
    handleOk(temp) {
      //true表示编辑页面
      if (this.isFlag) {
        this.isFlag = false;
        if (temp === true) {
          console.log(this.modalForm, "12345");
          //将数据传递给后台
          this.$refs.ruleForm.validate((valid) => {
            if (valid) {
              ApiEditHousingCarriers(this.modalForm).then((res) => {
                if (res.code == 0) {
                  console.log(res);
                  this.$message.success("编辑成功");
                  this.visible = false;
                  this.disabled = false;
                  this.$refs.table.refresh(true);
                }
              });
            }
          });
          //false表示新增页面
        } else if (temp === false) {
          //将数据传递给后台
          this.$refs.ruleForm.validate((valid) => {
            if (valid) {
              ApiAddHousingCarriers(this.modalForm).then((res) => {
                if (res.code == 0) {
                  console.log(res);
                  this.$message.success("新增成功");
                  this.visible = false;
                  this.disabled = false;
                  //刷新列表
                  this.$refs.table.refresh();
                  this.modalForm = {
                    parkName: undefined,
                    mergedParkName: undefined,
                    buildingNumber: undefined,
                    floorNumber: undefined,
                    roomNumber: undefined,
                    roomArea: undefined,
                    floorStatus: "0",
                    roomStatus: "0",
                  };
                }
              });
            }
          });
        }
        setTimeout(() => {
          this.isFlag = true;
        }, 2000);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.carrier {
  display: flex;
  flex-wrap: wrap;
  .carrierFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }
  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
</style>

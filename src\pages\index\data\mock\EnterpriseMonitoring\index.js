/*
 * @Description:
 * @Author: cyw
 * @Date: 2023-09-25 13:12:13
 * @LastEditTime: 2023-10-12 13:24:50
 * @LastEditors: cyw
 */
import Mock from "mockjs"

//获取企业信息的数据的接口
const companyMessage = {
  code: 0,
  data: {
    totalEnterprise: 1437,
    territorial: 63,
    nonTerritorial: 965,
    xuhui: 225,
    noXuhui: 184
  },
  msg: null
}
Mock.mock("/Mock/enterpriseMonitoring/companyMessage", companyMessage)

//获取税收信息的数据的接口
const taxMessage = {
  code: 0,
  data: {
    totalTax: 321677.54,
    territorial: 12186,
    nonTerritorial: 292104.77,
    xuhui: 17386.71,
    noXuhui: 0
  },
  msg: null
}
Mock.mock("/Mock/enterpriseMonitoring/taxMessage", taxMessage)

//获取不同标签企业的总税收和企业数量
const labelCompany = {
  code: 0,
  data: {
    records: [
      {
        highTechNum: 0,
        highTechTax: 660
      },
      {
        specializedNewNum: 0,
        specializedNewTax: 760
      },
      {
        foreignCapitalNum: 0,
        foreignCapitalTax: 860
      },
      {
        listedCompaniesNum: 0,
        listedCompaniesTax: 960
      }
    ]
  },
  msg: null
}
Mock.mock("/Mock/enterpriseMonitoring/labelCompany", labelCompany)

//获取不同类型的企业基本信息渲染表格
const companyTable = {
  code: 0,
  data: {
    records: [
      {
        id: 1,
        companyName: "上海传卓电子有限公司",
        taxAmount: 24.04,
        taxArea: "华泾镇",
        settle: "星联科技园",
        industry: "软件科技",
        scale: "小型企业",
        quality: "私营企业",
        label: "无"
      },
      {
        id: 2,
        companyName: "高力建设项目管理（上海）有限公司 ",
        taxAmount: 143.56,
        taxArea: "华泾镇",
        settle: "星联科技园",
        industry: "软件科技",
        scale: "大型企业",
        quality: "外资总部",
        label: "无"
      }
    ]
  },
  msg: null
}
Mock.mock("/Mock/enterpriseMonitoring/companyTable", (params) => {
  console.log(params, "companyTable")
  companyTable.data.records[0].companyName += JSON.parse(params.body).typeLogo
  return companyTable
})

//获取违规记录
const violationHistory = {
  code: 0,
  data: {
    records: [
      {
        id: 1,
        company: "上海传卓电子有限公司",
        msg: "违规内容",
        level: 1,
        time: "2023-7-19",
        content: "长期拖欠房屋的租金"
      },
      {
        id: 1,
        company: "上海柏飞电子科技有限公司",
        msg: "违规内容",
        level: 3,
        time: "2023-8-20",
        content: "房屋到期没有及时的搬离"
      },
      {
        id: 1,
        company: "上海琪朔信息科技有限公司",
        msg: "违规内容",
        level: 2,
        time: "2023-10-5",
        content: "破坏房屋公共设施"
      }
    ]
  },
  msg: null
}
Mock.mock("/Mock/enterpriseMonitoring/violationHistory", violationHistory)

//获取辖区每个月的税收金额
const taxMoney = {
  code: 0,
  data: {
    records: [50, 80, 120, 150, 200, 220, 260, 280, 300, 390, 430, 450],
    targetTax: 470
  },
  msg: null
}
Mock.mock("/Mock/enterpriseMonitoring/taxMoney", taxMoney)

// Mock.mock("/mapi/manager/companyRelatedRelation/pageList", {
//   code: 0,
//   msg: "查询成功！",
//   data: {
//     records: [
//       {
//         id: "00b411eeb5dc4ea8bfdd8bfb28e911c8",
//         companyName: "上海华理置业有限公司",
//         companyNature: null,
//         legalPerson: "王昀",
//         legalPersonPhone: null,
//         companyAddress: null,
//         businessScope:
//           "许可项目：住宅室内装饰装修；建设工程施工。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）一般项目：停车场服务；商业综合体管理服务；音响设备销售；办公服务；计算机及办公设备维修；信息咨询服务（不含许可类信息咨询服务）；技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）",
//         operatingPeriodBegin: null,
//         operatingPeriodEnd: null,
//         registeredCapital: "400 万元人民币",
//         currency: null,
//         exchangeRate: null,
//         industryCategory: null,
//         socialCreditCode: "9131010476720819XR",
//         industryCategoryRemark: null,
//         foreignHeadquarters: null,
//         keyEnterprises: null,
//         attractInvestment: null,
//         licenseNumber: null,
//         businessRegistrationAddress: null,
//         investmentType: null,
//         businessRegistrationDate: null,
//         districtDecentralize: 0,
//         businessMigration: null,
//         businessChangeDate: null,
//         originalDomicile: null,
//         introductionDate: null,
//         cooperationIntroducer: null,
//         handlingType: null,
//         handledBy: null,
//         occupyResources: null,
//         virtualAddressProcess: null,
//         residentialArea: null,
//         crossFunctionalArea: null,
//         occupationBuilding: null,
//         declarationTime: null,
//         listedCompany: null,
//         newThirdBoard: null,
//         taxRelocation: null,
//         taxCancellation: null,
//         companyStarRating: null,
//         remark: null,
//         status: 1,
//         createTime: null,
//         creator: null,
//         updateTime: null,
//         updator: null,
//         dateOfImmigration: null,
//         dateOfRelocation: null,
//         totalPatents: null,
//         companyType: 5,
//         territorialType: null
//       },
//       {
//         id: "00be4fdc535b4f3588db6e770e5d4f77",
//         companyName: "华腾地毯有限公司",
//         companyNature: null,
//         legalPerson: "郜宏",
//         legalPersonPhone: null,
//         companyAddress: null,
//         businessScope:
//           "地毯及原料、辅料的生产加工、销售、装璜材料、地毯工具的加工及销售、地毯辅装清洗及配套装璜、地毯相关的教育培训、设计及技术咨询、设备及房屋租赁服务，本企业自产产品的出口业务和本企业所需的机械设备、零部件、原材料的进出口业务；买卖、生产、分销，进出口地毯、小地毯、油毡窗帘、照明设备、家居用品以及各种类型的装饰材料和面料（依法须经批准的项目，经相关部门批准后，方可开展经营活动）*",
//         operatingPeriodBegin: null,
//         operatingPeriodEnd: null,
//         registeredCapital: "6000 万元人民币",
//         currency: null,
//         exchangeRate: null,
//         industryCategory: null,
//         socialCreditCode: "91360500763355520R",
//         industryCategoryRemark: null,
//         foreignHeadquarters: null,
//         keyEnterprises: null,
//         attractInvestment: null,
//         licenseNumber: null,
//         businessRegistrationAddress: null,
//         investmentType: null,
//         businessRegistrationDate: null,
//         districtDecentralize: 0,
//         businessMigration: null,
//         businessChangeDate: null,
//         originalDomicile: null,
//         introductionDate: null,
//         cooperationIntroducer: null,
//         handlingType: null,
//         handledBy: null,
//         occupyResources: null,
//         virtualAddressProcess: null,
//         residentialArea: null,
//         crossFunctionalArea: null,
//         occupationBuilding: null,
//         declarationTime: null,
//         listedCompany: null,
//         newThirdBoard: null,
//         taxRelocation: null,
//         taxCancellation: null,
//         companyStarRating: null,
//         remark: null,
//         status: 1,
//         createTime: null,
//         creator: null,
//         updateTime: null,
//         updator: null,
//         dateOfImmigration: null,
//         dateOfRelocation: null,
//         totalPatents: null,
//         companyType: 5,
//         territorialType: null
//       }
//     ],
//     total: 11,
//     size: 2,
//     current: 3,
//     searchCount: true,
//     pages: 6
//   }
// })

<!--
* <AUTHOR>
* @time 2021-1-4
* @dec 示意图控件
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      label="示意图"
      style="margin-bottom:unset"
      prop="fileList"
      :rules="[
        {
          required: data.notNull,
          message: '请上传图片！',
          trigger: 'change'
        }
      ]"
    >
      <div class="mapTitle">
        {{ data.inputTitle || "示意图标题" }}
      </div>
      <img
        style="width: 100%"
        alt="暂无图片或未正确显示"
        :src="
          data.optionsData.thumbnailUrl ||
            require('./image/defaultSketchMap.png')
        "
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      previewVisible: false,
      previewImage: "",
      fileList: [],
      uploadTip: [] //文件上传提示
    };
  }
};
</script>
<style lang="less" scoped>
@import "../index.less";
.form-design-controlLib-field-label {
  width: unset !important;
}
.mapTitle {
  width: 100%;
  text-align: center;
}
</style>

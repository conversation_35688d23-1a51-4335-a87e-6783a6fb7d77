/**
 * <AUTHOR>
 * @time 2020-8-14
 * @dec 页面集仓库，在每个页面集仓库都要倒入全局库，以便个栏目做公用数据通信
 */
import Vue from "vue";
import Vuex from "vuex";
// 核心库
import common from "@/data/store/common";
// 给全局store赋值，以便全局方法调用
import { setCommonStore } from "@/data/store";
import getters from "./getters";
Vue.use(Vuex);
const store = new Vuex.Store({
  modules: {
    ...common
  },
  getters
});
setCommonStore(store);
export default store;

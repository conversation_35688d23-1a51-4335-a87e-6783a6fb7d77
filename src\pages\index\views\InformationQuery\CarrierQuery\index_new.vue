<!-- 载体查询 -->
<template>
  <div class="carrier">
    <div class="carrierFrom">
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
        @submit="handleSubmit"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="归并园区|园区|楼号">
              <a-tree-select
                v-model="queryParam.mergedParkIdlike"
                tree-data-simple-mode
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="enumerateObj.industryComponeyArray"
                placeholder="请选择归并园区或园区或楼号"
                @select="changeIndustryComponeyArray"
                :load-data="onLoadData"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="房间面积">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
                allowClear
                v-model="queryParam.area"
                placeholder="全部"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in parkArr"
                  :key="item.value"
                  >{{ item.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="状态">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
                allowClear
                v-model="queryParam.roomStatus"
                placeholder="全部"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in stateArr"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="租赁到期时间">
              <a-range-picker
                allowClear
                style="width:100%"
                v-model="leaseEndTime"
                @change="onChange"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="载体地址">
              <a-input
                v-model="queryParam.roomAddress"
                placeholder="xx号楼/xx层/xx室"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8" align="right">
            <a-form-item :wrapper-col="{ span: 24 }">
              <a-button type="primary" @click="search" style="margin-right:20px"
                >查询</a-button
              >
              <a-button type="default" @click="reset">重置</a-button>
              <a-button
                type="primary"
                class="export"
                style="margin-left:15px"
                @click="deduced()"
                >导出</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-card style="width:100%;margin-top:20px">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        :rowKey="(record) => record.floorRoomId"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </span>
        <span slot="parkName" slot-scope="text, record">
          <a href="javascript:;" @click="toDetail(text, record)">{{
            record.parkName
          }}</a>
        </span>
        <span slot="tenantry" slot-scope="text, record">
          <a href="javascript:;" @click="toCompany(text, record)">{{
            record.tenantry
          }}</a>
        </span>
      </s-table>
    </a-card>
  </div>
</template>

<script>
import { queryCarrierInformation } from "@/pages/demo/data/api/api/park"
import {
  ApiGetBuildFloorNumberByPark,
  ApiSearchAllMergePark,
  ApiFindParkNamesByMergedName,
  ApiExportCarryQuerry
} from "@/pages/index/data/api/InfomationQuery"
import STable from "@/components/Table"
import moment from "moment"
import { parseNumFloat } from "@/common/utils/utils.js"
export default {
  components: {
    STable
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        }
      },
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: "60px",
          fixed: "left",
          align: "center"
        },
        {
          title: "园区",
          dataIndex: "parkName",
          scopedSlots: { customRender: "parkName" },
          width: 220,
          fixed: "left",
          align: "center"
        },
        {
          title: "出租方",
          dataIndex: "lessor",
          width: 120,
          align: "center"
        },
        {
          title: "归并园区",
          dataIndex: "mergedParkName",
          width: 120,
          align: "center"
        },

        {
          title: "楼号",
          dataIndex: "buildingNumber",
          width: 120,
          align: "center"
        },
        {
          title: "楼层",
          dataIndex: "floorNumber",
          width: 120,
          align: "center"
        },
        {
          title: "房间号",
          dataIndex: "roomNumber",
          width: 120,
          align: "center"
        },
        {
          title: "房间面积(平米)",
          dataIndex: "roomArea",
          align: "center",
          width: 160,
          customRender(text) {
            return parseNumFloat(text)
          }
        },
        {
          title: "状态",
          dataIndex: "roomStatus",
          width: 60,
          align: "center",
          customRender: (text) => {
            return text == 1 ? "占用" : "空置"
          }
        },
        {
          title: "承租方",
          dataIndex: "tenantry",
          align: "center",
          width: 220,
          scopedSlots: { customRender: "tenantry" }
        },
        {
          title: "关联企业",
          dataIndex: "entRelationName",
          align: "center",
          width: 220
        },
        {
          title: "注册情况",
          dataIndex: "territorialSituation",
          align: "center",
          width: 220
        },
        {
          title: "租赁单价(元/天/平米)",
          dataIndex: "rent",
          align: "center",
          width: 120,
          customRender(text) {
            return parseNumFloat(text)
          }
        },
        {
          title: "租赁到期时间",
          dataIndex: "leaseEndTime",
          align: "center",
          width: 160,
          customRender: (text) => {
            return text ? moment(new Date(text)).format("YYYY-MM-DD") : ""
          }
        }
      ],
      timeList: [],
      leaseEndTime: "",
      queryParam: {
        mergedParkIdlike: "",
        mergedParkName: undefined,
        parkName: undefined,
        // floor: "",
        // area: "",
        // status: "",
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 10
      },
      labelCol: { span: 4 },
      parkArr: [
        {
          desc: "200平米以内",
          value: "0"
        },
        {
          desc: "201平米-500平米",
          value: "1"
        },
        {
          desc: "201平米-500平米",
          value: "2"
        },
        {
          desc: "201平米-500平米",
          value: "3"
        },
        {
          desc: "501平米-800平米",
          value: "4"
        },
        {
          desc: "800平米-1000平米",
          value: "4"
        },
        {
          desc: "1000平米以上",
          value: "4"
        }
      ],
      stateArr: [
        {
          name: "空置",
          value: "0"
        },
        {
          name: "占用",
          value: "1"
        }
      ],
      enumerateObj: {
        buildingNums: [],
        parkList: [],
        industryComponeyArray: []
      } // 查询条件对象
    }
  },
  created() {
    // this.onLoadData()
    this.getRoomArea()
  },
  methods: {
    deduced() {
      ApiExportCarryQuerry(this.queryParam)
      //帮我写一个防抖函数ts
    },
    changeIndustryComponeyArray(value, treeNode, key) {
      this.queryParam.mergedParkName = ""
      this.queryParam.parkName = ""
      this.queryParam.buildingNums = ""
      console.log(value, treeNode, key)
      const level = treeNode?.dataRef?.level
      const title = treeNode?.dataRef?.title
      this.queryParam.mergedParkIdlike = value
      if (level === 0) {
        return
      } else if (level === 1) {
        this.queryParam.mergedParkName = title
      } else if (level == 2) {
        this.queryParam.parkName = title
      } else {
        let parentTitle = treeNode?.$parent?.dataRef?.title
        this.queryParam.buildingNums = title
        this.queryParam.parkName = parentTitle
      }
    },
    async onLoadData(treeNode) {
      console.log(treeNode)
      const title = treeNode?.dataRef?.title
      const id = treeNode?.dataRef?.id
      const level = treeNode?.dataRef?.level
      console.log(title, level, treeNode, "treeNode")
      if (!level) {
        let industryComponey = await ApiSearchAllMergePark()
        industryComponey = industryComponey.data
          .filter((item) => item != null)
          .map((item) => {
            const random = Math.random()
              .toString(36)
              .substring(2, 6)
            return {
              level: 1,
              id: random,
              pId: id ? id : "all",
              value: random,
              title: item,
              isLeaf: !!treeNode
            }
          })

        this.enumerateObj.industryComponeyArray = industryComponey
        console.log(
          "industryComponeyArray",
          this.enumerateObj.industryComponeyArray
        )
        return await this.enumerateObj.industryComponeyArray.concat(
          industryComponey.data
        )
      } else if (level === 1) {
        let industryComponey = await ApiFindParkNamesByMergedName({
          mergedParkName: title
        })
        industryComponey.data = industryComponey.data
          .filter((item) => item != null)
          .map((item) => {
            const random = Math.random()
              .toString(36)
              .substring(2, 6)
            return {
              level: 2,
              id: random,
              pId: id ? id : "all",
              value: random,
              title: item,
              isLeaf: false
            }
          })
        debugger
        let beforeArray = this.enumerateObj.industryComponeyArray
        console.log("beforeArray", beforeArray)
        let industryComponeyArray = beforeArray.concat(industryComponey.data)
        this.$set(
          this.enumerateObj,
          "industryComponeyArray",
          industryComponeyArray
        )
        console.log("industryComponeyArray", industryComponeyArray)
        return await this.enumerateObj.industryComponeyArray.concat(
          industryComponey.data
        )
      } else if (level === 2) {
        let industryComponey = await ApiGetBuildFloorNumberByPark({
          parkName: title
        })
        industryComponey.data = industryComponey.data.buildingNumbers
          .filter((item) => item != null)
          .map((item) => {
            const random = Math.random()
              .toString(36)
              .substring(2, 6)
            return {
              level: 3,
              id: random,
              pId: id ? id : "all",
              value: random,
              title: item,
              isLeaf: true
            }
          })
        let industryComponeyArray = this.enumerateObj.industryComponeyArray.concat(
          industryComponey.data
        )
        this.$set(
          this.enumerateObj,
          "industryComponeyArray",
          industryComponeyArray
        )
        console.log("industryComponeyArray", industryComponeyArray)
        return await this.enumerateObj.industryComponeyArray.concat(
          industryComponey.data
        )
      }
    },
    // 加载数据方法 必须为 Promise 对象
    async loadData({ pageNo, pageSize }) {
      console.log("pageNo", pageNo, "pageSize", pageSize, "loadData")
      this.pageNo = pageNo
      this.pageSize = pageSize
      let industryComponey
      if (
        this.queryParam.mergedParkName ||
        this.queryParam.parkName ||
        this.queryParam.buildingNumber
      ) {
        console.log("已初始化")
      } else {
        console.log("初始化数据")
        industryComponey = await this.onLoadData()

        this.queryParam.mergedParkName = industryComponey[0].title
        this.queryParam.mergedParkIdlike = industryComponey[0].id
      }
      let queryParam = Object.assign({}, this.queryParam, {
        pageNum: pageNo,
        pageSize: pageSize
      })
      return queryCarrierInformation(queryParam).then((res) => {
        let dataObj = res.data
        dataObj.data = res.data.records
        dataObj.totalCount = res.data.total
        dataObj.pageSize = res.data.size
        dataObj.pageNo = res.data.current
        return dataObj
      })
    },
    changeIndustryComponey(value) {
      console.log("changeIndustryComponey", value)
      this.queryParam.parkName = ""
      if (!value) {
        return
      }
      ApiFindParkNamesByMergedName({
        mergedParkName: value
      }).then((res) => {
        console.log(res, "**园区名称**")
        this.enumerateObj.parkList = res.data
        if (!res.data || res.data.length == 0) return
        this.queryParam.parkName = res.data[0]

        ApiGetBuildFloorNumberByPark({
          parkName: res.data[0] || this.queryParam.parkName
        }).then((res) => {
          this.enumerateObj.buildingNums = res.data.buildingNumbers
        })
      })
    },
    changeParkName(value) {
      this.queryParam.buildingNumber = undefined
      if (!value) {
        return
      }
      ApiGetBuildFloorNumberByPark({
        parkName: value || this.queryParam.parkName
      }).then((res) => {
        this.enumerateObj.buildingNums = res.data.buildingNumbers
      })
    },
    reset() {
      this.queryParam = {
        mergedParkName:
          this.enumerateObj.industryComponeyArray?.[0]?.title || "远中产业园",
        mergedParkIdlike: this.enumerateObj.industryComponeyArray?.[0]?.id,
        industryComponey:
          this.enumerateObj.industryComponeyArray?.[0]?.title || "远中产业园",
        pageNum: 1,
        pageSize: 10
      }
      this.leaseEndTime = []
      this.$refs.table.refresh(true)
    },
    async getRoomArea() {
      this.parkArr = await this.$getDictByType({ dictCode: "roomArea" })
    },
    search() {
      console.log(this.queryParam)
      this.$refs.table.refresh(true)
    },
    onChange(date, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    toDetail(text, record) {
      this.$router.push(
        `/information-query/park-detail?parkName=${record.parkName}`
      )
    },
    toCompany(text, record) {
      this.$router.push(`/information-query/enterprise-detail?id=${record.id}`)
    }
  }
}
</script>

<style lang="less" scoped>
.carrier {
  display: flex;
  flex-wrap: wrap;
  .carrierFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }
  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
</style>

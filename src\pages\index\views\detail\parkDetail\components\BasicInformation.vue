<template>
  <div>
    <table
      class="table table-striped table-bordered"
      align="center"
      valign="center"
      v-if="obj.parkName"
    >
      <tr>
        <td class="column">园区</td>
        <td class="value">{{ obj.parkName }}</td>
        <td class="column">总面积</td>
        <td class="value" v-if="Number(obj.parkTotalArea).toFixed(2) != NaN">
          {{ parseFloat(obj.parkTotalArea).toFixed(2) }}平米
        </td>
      </tr>
      <tr>
        <td class="column">楼宇数</td>
        <td class="value">{{ obj.buildingNumbers }}栋</td>
        <td class="column">空置面积</td>
        <td class="value" v-if="Number(obj.parkVacantArea).toFixed(2) != NaN">
          {{ Number(obj.parkVacantArea).toFixed(2) }}平米
        </td>
      </tr>
      <tr>
        <td class="column">入驻率</td>
        <td class="value">{{ Number(obj.parkOccupancyRate).toFixed(2) }}%</td>
        <td class="column">空置率</td>
        <td class="value">{{ Number(obj.parkVacancyRate).toFixed(2) }}%</td>
      </tr>
      <tr>
        <td class="column">税收金额</td>
        <td class="value">{{ obj.parkTaxAmount }}万元</td>
        <td class="column">属地率</td>
        <td class="value">
          {{ obj.territorialRate === "" ? "0.00%" : obj.territorialRate }}
        </td>
      </tr>
      <tr>
        <td class="column">园区物业费</td>
        <td class="value">
          {{ obj.propertyFee === null || "" ? "0.00" : obj.propertyFee }}元/平米/月
        </td>
        <td class="column">园区租金</td>
        <td class="value">{{ obj.rent }}元/平米/天</td>
      </tr>
      <tr>
        <td class="column">面积排名</td>
        <td class="value">{{ obj.rankingParkArea }}</td>
        <td class="column">空置面积排名</td>
        <td class="value">{{ obj.rankingVacancyArea }}</td>
      </tr>
      <tr>
        <td class="column">税收排名</td>
        <td class="value">{{ obj.taxRanking }}</td>
        <td class="column">每平米收益率</td>
        <td class="value">{{ obj.perSquareMeterYield }}元/平米</td>
      </tr>
      <tr>
        <td class="column">园区介绍</td>
        <td class="value" colspan="5" style="text-align:left;">
          {{ obj.parkDescription }}
          <br />
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
import { queryParkBasicInformation } from "@/pages/demo/data/api/api/park";
export default {
  data() {
    return {
      obj: {},
      parkName: ""
    };
  },
  created() {
    this.parkName = this.$route.query.parkName;
  },
  activated() {
    console.log("BasicInformation activated");
  },
  mounted() {
    if (!this.$route.query.parkName) {
      return;
    }
    let params = {
      parkName: this.$route.query.parkName
    };
    queryParkBasicInformation(params).then(res => {
      this.obj = res.data;
    });
  }
};
</script>

<style lang="less">
.table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  display: table;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}
.table td {
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  color: #333333;
  padding: 8px 12px;
}
.table-bordered {
  border: 1px solid #ddd;
}
* {
  margin: 0px;
  padding: 0px;
}
.column {
  width: 30px;
  height: 30px;
  border: 1px solid #333;
  background: #eff3fc;
}
.value {
  width: 70px;
  height: 30px;
  border: 1px solid #333;
}
</style>

<!-- 企业监控 -->
<template>
  <a-card class="carrier_monitoring">
    <headerContent></headerContent>
    <MiddleContent></MiddleContent>
    <footerContent></footerContent>
  </a-card>
</template>

<script>
import headerContent from "./components/Header/Header.vue";
import MiddleContent from "./components/Middle/Middle.vue";
import footerContent from "./components/Footer/index.vue";
export default {
  components: {
    headerContent,
    MiddleContent,
    footerContent,
  },
  data() {
    return {};
  },
  methods: {},
  computed: {},
  mounted() {},
};
</script>

<style scoped lang="less">
.carrier_monitoring {
}
</style>

<template>
  <div :class="module_name + '-container'">
    <div :class="module_name + '-icon1'" v-if="BType == 1">
      <!-- <a-icon type="warning" theme="filled" style="color:#eee5ff" /> -->
      <img
        src="@/assets/image/common/warning1.png"
        style="width:38px;height:38px;"
      />
    </div>
    <div :class="module_name + '-icon2'" v-if="BType == 2">
      <img
        src="@/assets/image/common/warning2.png"
        style="width:38px;height:38px;"
      />
    </div>
    <div :class="module_name + '-icon3'" v-if="BType == 3">
      <img
        src="@/assets/image/common/warning3.png"
        style="width:38px;height:38px;"
      />
    </div>
    <div :class="module_name + '-icon4'" v-if="BType == 4">
      <img
        src="@/assets/image/common/warning4.png"
        style="width:38px;height:38px;"
      />
    </div>
    <div :class="module_name + '-icon5'" v-else-if="BType == 5">
      <img
        src="@/assets/image/common/warning5.png"
        style="width:38px;height:38px;"
      />
    </div>
    <div :class="module_name + '-statistics'">
      <div :class="module_name + '-quantity'">
        <span :class="module_name + '-num'">{{ BQuantity }}</span>
        <span :class="module_name + '-unit'">{{ BUnit }}</span>
      </div>
      <div :class="module_name + '-title'">{{ BTitle }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: Number,
    title: String,
    quantity: Number,
    unit: String,
    extra: Object,
  },
  components: {},
  data() {
    return {
      module_name: "reactangle",
      BTitle: "",
      BQuantity: 0,
      BUnit: "",
      BType: undefined,
    };
  },
  watch: {
    type: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "BType", this.type);
          console.log(this.BType, "~~~~~");
        });
      },
      immediate: true,
      deep: true,
    },
    title: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "BTitle", this.title);
        });
      },
      immediate: true,
      deep: true,
    },
    quantity: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "BQuantity", this.quantity);
        });
      },
      immediate: true,
      deep: true,
    },
    unit: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "BUnit", this.unit);
        });
      },
      immediate: true,
      deep: true,
    },
    extra: {
      handler() {
        this.$nextTick(() => {
          console.log("Nothing to do currently.");
        });
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.reactangle-container {
  position: relative;
  width: 20%;
  height: 80px;
  // background-color: #fff;
  // border: 1px solid #f00;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reactangle-icon1 {
  width: 68px;
  height: 68px;
  background-color: #eee5ff;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reactangle-icon2 {
  width: 68px;
  height: 68px;
  background-color: #e1f0ff;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reactangle-icon3 {
  width: 68px;
  height: 68px;
  background-color: #d7f9ef;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reactangle-icon4 {
  width: 68px;
  height: 68px;
  background-color: #ffe8bb;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reactangle-icon5 {
  width: 68px;
  height: 68px;
  background-color: #ffe2e5;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reactangle-num {
  font-size: 28px;
  font-weight: 600;
  color: #464e5f;
}
.reactangle-statistics {
  margin-left: 20px;
}
.reactangle-title {
  font-size: 18px;
  font-weight: 500;
  color: #b5b5c3;
}
</style>

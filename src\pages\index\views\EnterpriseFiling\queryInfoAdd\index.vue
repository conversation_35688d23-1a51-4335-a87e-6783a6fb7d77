<!-- 企业建档表 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <div class="detail">
      <a-form-model
        class="ant-advanced-search-form"
        ref="ruleForm"
        :model="decisionParmas"
        :rules="rules"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <!-- 企业基础信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业基础信息</div>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="companyName" label="企业名称">
                  <a-input
                    v-model="decisionParmas.companyName"
                    @blur="onSearch"
                    @change="onChange"
                    :disabled="type == 2"
                    placeholder="请输入企业名称"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="econKind" label="企业性质">
                  <a-input
                    disabled
                    v-model="decisionParmas.econKind"
                    placeholder="请输入企业性质"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="registCapi" label="注册资金">
                  <a-input
                    disabled
                    placeholder="请输入注册资金"
                    v-model="decisionParmas.registCapi"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="startDate" label="创立时间">
                  <a-date-picker
                    disabled
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    v-model="decisionParmas.startDate"
                    style="width: 100%"
                    placeholder="请输入创立时间"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="belongOrg" label="工商登记机关">
                  <a-input
                    disabled
                    v-model="decisionParmas.belongOrg"
                    placeholder="请输入工商登记机关"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="address" label="注册地址">
                  <a-input
                    disabled
                    v-model="decisionParmas.address"
                    placeholder="请输入注册地址"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8"> </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="officeAddress" label="办公地址">
                  <a-input
                    :title="decisionParmas.officeAddress"
                    v-model="decisionParmas.officeAddress"
                    placeholder="请输入办公地址"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8"> </a-col>
            </a-row>
          </div>
        </div>
        <!-- 企业资质评估信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业资质评估信息</div>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="insuranceNum" label="参保人数">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.insuranceNum"
                    placeholder="请输入参保人数"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="employeesInfo" label="主要人员及职位">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.employeesInfo"
                    placeholder="请输入主要人员及职位"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item
                  prop="partnersInfo"
                  label="主要股东及持股比例"
                >
                  <a-textarea
                    disabled
                    v-model="decisionParmas.partnersInfo"
                    placeholder="请输入主要股东及持股比例"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="affiliate" label="主要关联公司">
                  <a-select disabled v-model="affiliate">
                    <a-select-option
                      :value="item2.value"
                      v-for="item2 in affiliateArr"
                      :key="item2.item"
                      >{{ item2.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="16">
                <a-form-model-item prop="branchesInfo" label="">
                  <a-input
                    :title="decisionParmas.branchesInfo"
                    disabled
                    v-model="decisionParmas.branchesInfo"
                    placeholder="请输入分公司及子公司"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="affiliate1" label=" ">
                  <a-select disabled v-model="affiliate1">
                    <a-select-option
                      :value="item2.value"
                      v-for="item2 in affiliateArr1"
                      :key="item2.item"
                      >{{ item2.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="16">
                <a-form-model-item prop="investsInfo" label="">
                  <a-input
                    :title="decisionParmas.investsInfo"
                    disabled
                    v-model="decisionParmas.investsInfo"
                    placeholder="请输入对外投资公司"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
        <!-- 专利情况 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">专利情况</div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="patentNum" label="发明专利申请">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.patentNum"
                    placeholder="请输入发明专利申请"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="trademarkNum" label="商标信息">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.trademarkNum"
                    placeholder="请输入商标信息"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item
                  prop="copyrightNum"
                  label="著作权（不含软著）"
                >
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    placeholder="请输入著作权（不含软著）"
                    v-model="decisionParmas.copyrightNum"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="certNum" label="资质认证">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.certNum"
                    placeholder="请输入资质认证"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="softwareNum" label="软件著作权">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.softwareNum"
                    placeholder="请输入软件著作权"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
        <!-- 资质情况 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">资质情况</div>
            <div>
              <a-checkbox-group
            
                v-model="decisionParmas.aptitudeInfos"
                :options="plainOptions"
              />
            </div>
            <!-- <div style="display: flex">
              <a-checkbox-group
                disabled
                v-model="decisionParmas.aptitudeOther"
                :options="plainOptions1"
              />
              <a-input
                disabled
                :title="decisionParmas.aptitudeOtherDescription"
                v-model="decisionParmas.aptitudeOtherDescription"
                placeholder="请输入其他资质描述"
                style="width: 56%; margin-bottom: 30px; margin-left: 26px"
              />
            </div> -->
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="entPortraitInfo" label="主营业务">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.entPortraitInfo"
                    placeholder="请输入主营业务 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="projectInfo" label="品牌产品">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.projectInfo"
                    placeholder="请输入品牌产品 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="awardsAchievements" label="获奖及成就">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.awardsAchievements"
                    placeholder="请输入获奖及成就 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="financeInfo" label="融资情况">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.financeInfo"
                    placeholder="请输入融资情况 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="entProfileInfo" label="上市情况">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.entProfileInfo"
                    placeholder="请输入上市情况 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form-model>
    </div>
    <div style="text-align: center">
      <a-button class="back" @click="toBack">返回</a-button>
      <a-button v-if="type != 3" type="primary" @click="save(0)" class="back"
        >保存</a-button
      >
      <!-- <a-button v-if="type != 3" type="primary" @click="save(1)" class="back"
        >提交申请</a-button
      > -->
    </div>
  </div>
</template>

<script>
import {
  queryContractInfoByIdApi,
  addContractInfoApi,
  updateContractInfoApi,
  getCompanyDetailApi,
} from "@/pages/index/data/api/EnterpriseFiling";
export default {
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      plainOptions: [
        { label: "国家科技型中小企业", value: "1" },
        { label: "国家高新技术企业", value: "2" },
        { label: "市专精特新中小企业", value: "3" },
        { label: "国家专精特新小巨人企业", value: "4" },
        { label: "区企业技术中心", value: "5" },
        { label: "市企业技术中心", value: "6" },
      ],
      plainOptions1: [{ label: "其他", value: "0" }],
      affiliateArr: [{ label: "分公司及子公司", value: "0" }], //分公司及子公司
      affiliateArr1: [{ label: "对外投资公司", value: "0" }], //对外投资公司
      rules: {
        companyName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
      },
      type: null, //1：新增，2：编辑，3：查看
      isFlag: true,
      isSaveFlag: true,
      isShow: false,

      affiliate: "0", //关联公司
      affiliate1: "0", //关联公司
      decisionParmas: {
        companyName: "", //企业名称
        econKind: "", //企业性质
        registCapi: "", //注册资金
        startDate: "", //创立时间
        belongOrg: "", //工商登记机关
        address: "", //注册地址
        officeAddress: "", //办公地址
        insuranceNum: "", //参保人数
        employeesInfo: "", //主要人员及职位
        partnersInfo: "", //主要股东及持股比例
        branchesInfo: "", //分公司及子公司
        investsInfo: "", //对外投资公司
        patentNum: 0, //发明专利申请
        trademarkNum: 0, //有效注册商标
        copyrightNum: 0, //著作权（不含软著）
        certNum: 0, //资质认证
        softwareNum: 0, //软件著作权
        aptitudeInfos: [], //资质情况
        aptitudeOther: ["1"], //资质情况其他
        aptitudeOtherDescription: "", //其他资质描述
        entPortraitInfo: "", //主营业务
        projectInfo: "", //品牌产品
        awardsAchievements: "", //获奖及成就
        financeInfo: "", //融资情况
        entProfileInfo: "", //上市情况
      },
    };
  },
  created() {
    this.type = this.$route.query.statu;
    if (this.type == 2) {
      this.getContractId();
    }
  },
  methods: {
    handleInput1(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.rentalUnitPrice = val;
    },
    onSearch() {
      if (this.isFlag) {
        this.isFlag = false;
        if (this.decisionParmas.companyName) {
          let parmas = {
            enterpriseName: this.decisionParmas.companyName,
          };

          getCompanyDetailApi(parmas).then((res) => {
            if (res.data) {
              let obj1 = this.decisionParmas;
              let obj2 = res.data;
              Object.keys(obj1).forEach((key) => {
                obj1[key] = obj2[key] == undefined ? obj1[key] : obj2[key];
              });
              this.decisionParmas.companyName = res.data.enterpriseName;
            } else {
              return;
            }
          });
        }
        setTimeout(() => {
          this.isFlag = true;
        }, 3000);
      }
    },
    onChange(e) {
      console.log(e.target.value);
      this.$refs.ruleForm.resetFields();
      this.decisionParmas.companyName = e.target.value;
    },
    getContractId() {
      //根据id查询所有数据
      if (this.$route.query.id) {
        let parmas = {
          id: this.$route.query.id,
        };
        queryContractInfoByIdApi(parmas).then((res) => {
          this.decisionParmas = res.data;
          let arr = [];
          arr.push(res.data.aptitudeOther.toString());
          this.decisionParmas.aptitudeOther = arr;
        });
      }
    },
    save(i) {
      if (this.isSaveFlag) {
        this.isSaveFlag = false;
        if (this.type == 1) {
          this.comfirBtn(i); //新增
        } else if (this.type == 2) {
          this.editSubmit(i); //编辑
        }
        setTimeout(() => {
          this.isSaveFlag = true;
        }, 3000);
      }
    },
    comfirBtn(v) {
      const parmas = this.decisionParmas;
      parmas.openType = v; //0：保存，1：提交
      console.log(this.decisionParmas);
      if (!parmas.aptitudeOther) {
        parmas.aptitudeOther = "1";
      } else {
        parmas.aptitudeOther = this.decisionParmas.aptitudeOther[0];
      }

      if (parmas.openType == 1) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            addContractInfoApi(parmas)
              .then(() => {
                this.$message.success("提交申请成功");
                this.$router.push(`/enterprise-filing/query`);
              })
              .catch((error) => {
                this.$message.warning(error.data.data);
              });
          } else {
            return this.$message.warning("有必填项未填写，请检查！");
          }
        });
      } else {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            addContractInfoApi(parmas)
              .then(() => {
                this.$message.success("保存成功");
                this.$router.push(`/enterprise-filing/query`);
              })
              .catch((error) => {
                this.$message.warning(error.data.data);
              });
          } else {
            return this.$message.warning("有必填项未填写，请检查！");
          }
        });
      }
    },
    editSubmit(v) {
      const parmas = this.decisionParmas;
      parmas.openType = v; //0：保存，1：提交
      if (!parmas.aptitudeOther) {
        parmas.aptitudeOther = "1";
      } else {
        parmas.aptitudeOther = this.decisionParmas.aptitudeOther[0];
      }
      if (parmas.openType == 1) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            updateContractInfoApi(parmas)
              .then(() => {
                this.$message.success("提交申请成功");
                this.$router.push(`/enterprise-filing/query`);
              })
              .catch((error) => {
                this.$message.warning(error.data.data);
              });
          } else {
            return this.$message.warning("有必填项未填写，请检查！");
          }
        });
      } else {
        updateContractInfoApi(parmas)
          .then(() => {
            this.$message.success("保存成功");
            this.$router.push(`/enterprise-filing/query`);
          })
          .catch((error) => {
            this.$message.warning(error.data.data);
          });
      }
    },
    warning(v) {
      this.$warning({
        title: "提示",
        content: v,
      });
    },
    toBack() {
      this.$router.push(`/enterprise-filing/query`);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}

.ant-advanced-search-form /deep/ .ant-checkbox-group {
  .ant-checkbox-group-item {
    font-size: 17px !important;
    margin-left: 68px;
    margin-bottom: 30px;
  }
}
.ant-advanced-search-form /deep/ .ant-col-8 {
  .ant-col-sm-8 {
    width: 31% !important;
  }
  .ant-col-sm-16 {
    width: 69% !important;
  }
}
.ant-advanced-search-form /deep/ .ant-col-16 {
  .ant-col-sm-8 {
    width: 15% !important;
  }
  .ant-col-sm-16 {
    width: 85% !important;
  }
}
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  // width: 166px !important;
}
</style>

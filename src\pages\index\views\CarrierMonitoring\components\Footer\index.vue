<template>
  <div class="footerContent">
    <div class="left">
      <Echarts></Echarts>
    </div>
    <div class="right">
      <ViolationHistory></ViolationHistory>
    </div>
  </div>
</template>

<script>
import Echarts from "./Echarts/index.vue";
import ViolationHistory from "./ViolationHistory/index.vue";
export default {
  components: {
    Echarts,
    ViolationHistory,
  },
  data() {
    return {};
  },
  methods: {},
  computed: {},
  mounted() {},
};
</script>

<style scoped lang="less">
.footerContent {
  display: flex;
  margin-top: 10px;
  height: 223px;
  .left {
    width: 60%;
    background-color: white;
    border-radius: 10px;
  }
  .right {
    width: 40%;
    margin-left: 20px;
    background-color: white;
    border-radius: 10px;
  }
}
</style>

<template>
  <div>
    <a-modal
      :width="1400"
      :visible="visible"
      @cancel="handleCancel"
      :footer="null"
    >
      <div id="printPage">
        <div class="content">
          <div class="content-header">{{ form.title }}</div>
          <div class="content-basic-header">{{ form.basicHeader }}</div>
          <div class="content-basic-info">
            <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;"
            >
              <a-col
                class="content-col"
                style="border: 1px solid #1d2129; margin-left: 4px; margin-top: 4px;width: 192px;min-height: 60px;"
              >
                <div>申请单位</div>
              </a-col>
              <a-col
                class="content-col"
                style="border-top: 1px solid #1d2129;margin-top: 4px;width: 490px;min-height: 60px; justify-content:flex-start;padding-left:40px;padding-right:40px;"
              >
                <div>{{ lessorName }}</div>
              </a-col>
              <a-col
                class="content-col"
                style="border-top: 1px solid #1d2129;border-left: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-top: 4px;width: 192px;min-height: 60px;"
              >
                <div>审核流转表编号</div>
              </a-col>
              <a-col
                class="content-col"
                style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-top: 4px;margin-right: 4px;width: 287px;min-height: 60px;"
              >
                <div>{{ contractInfo.filingNumber }}-1</div>
              </a-col>
            </a-row>
             <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border-left: 1px solid #1d2129;border-right: 1px solid #1d2129;border-bottom: 1px solid #1d2129;margin-left:4px;width: 192px;margin-bottom: 4px;min-height: 60px;height:100%;"
                >
                  申请事项
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;border-bottom: 1px solid #1d2129; width: 969px;min-height: 64px; justify-content:flex-start;padding-left:40px;padding-right:40px;padding-top:10px;padding-bottom:10px;text-align:left;"
                >
                  {{ contractInfo.tenantry }}承租{{
                    contractInfo.rentalAddress
                  }}面积：{{ contractInfo.leaseArea }}㎡；租赁用途：{{
                    contractInfo.leasePurpose
                  }}；
                </div>
              </a-col>
            </a-row>
             <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;margin-bottom:4px;"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border-left: 1px solid #1d2129;border-right: 1px solid #1d2129;border-bottom: 1px solid #1d2129;margin-left:4px;width: 192px;margin-bottom: 4px;min-height: 60px;height:100%;"
                >
                  申请类型
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-right: 1px solid #1d2129;border-bottom: 1px solid #1d2129; width: 969px;min-height: 64px; justify-content:flex-start;padding-left:40px;padding-right:40px;padding-top:10px;padding-bottom:10px;text-align:left;"
                >
                  {{ contractInfo.contractType==0?'新租':contractInfo.contractType==1?'续租':contractInfo.contractType==2?'变更':contractInfo.contractType==3?'提前终止':'' }}
                  {{contractInfo.contentChangeType==0?'（主体变更）':contractInfo.contentChangeType==1?'（内容变更）':''}}
                </div>
              </a-col>
            </a-row>
          </div>
          <div class="content-preliminary-header">
            {{ form.preliminaryHeader }}
          </div>
          <div class="content-preliminary-info">
            <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;height:65px;"
            >
              <a-col
                class="content-col"
                style="border: 1px solid #1d2129; margin-left: 4px; margin-top: 4px;width: 64px;border-bottom:none;"
              >
                <div>编号</div>
              </a-col>
              <a-col
                class="content-col"
                style="border-top: 1px solid #1d2129;margin-top: 4px;width: 260px;"
              >
                <div>审核项</div>
              </a-col>
              <a-col
                class="content-col"
                style="border-top: 1px solid #1d2129;border-left: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-top: 4px;width: 522px;"
              >
                <div>申请单位提供材料</div>
              </a-col>
              <a-col
                class="content-col"
                style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-top: 4px;margin-right: 4px;width: 315px;"
              >
                <div>经发公司初审意见</div>
              </a-col>
            </a-row>

            <template v-for="(item, index) in preliminaryList">
              <div :key="index" v-if="item.index != 0">
                <a-row
                  type="flex"
                  justify="start"
                  style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;"
                >
                  <a-col
                    class="content-col"
                    style="border-top: 1px solid #1d2129;border-left: 1px solid #1d2129;border-right: 1px solid #1d2129; margin-left: 4px; width: 64px;min-height: 60px;"
                  >
                    <div>{{ item.index }}</div>
                  </a-col>
                  <a-col
                    class="content-col"
                    style="border-top: 1px solid #1d2129;width: 260px;min-height: 60px;"
                  >
                    <div>{{ item.titleName }}</div>
                  </a-col>
                  <a-col
                    class="content-col"
                    style="border-top: 1px solid #1d2129;border-left: 1px solid #1d2129;border-right: 1px solid #1d2129;width: 522px;min-height: 60px;justify-content:flex-start;padding-left:40px;padding-right:40px;text-align:left"
                  >
                    <div v-html="item.titleValue"></div>
                  </a-col>
                  <a-col
                    class="content-col"
                    style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-right: 4px;width: 315px;min-height: 60px;text-align:left;padding:10px;"
                  >
                    <div>{{ item.advice }}</div>
                  </a-col>
                </a-row>
              </div>
            </template>
            <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border-left: 1px solid #1d2129;border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-left:4px;width: 325px;min-height: 64px;height:100%;"
                >
                  发起
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;width: 836px;margin-right: 4px;min-height: 64px;padding:10px 40px;justify-content:flex-start;text-align:left;"
                >
                  {{ contractInfo.remark }}
                </div>
              </a-col>
            </a-row>
            <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border-left: 1px solid #1d2129;border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-left:4px;width: 325px;min-height: 100px;height:100%;"
                >
                  小结
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;width: 836px;margin-right: 4px;min-height: 100px;padding:10px 40px;justify-content:flex-start;text-align:left; position:relative;"
                >
                  <!-- {{ auditOptionByType(contractInfo, 1) }} -->
                  <div style="width: 100%;height:60px;">
                    <div style="margin-bottom: 10px;">
                      {{ auditOptionByType(contractInfo, 1) }}
                    </div>
                    <div
                      style="display: flex; align-items: center; justify-content: flex-end;position:absolute;bottom:5px;right:5px;"
                    >
                      <div style="margin-right: 80px; width: 140px;">{{`审核人：${contractInfo.approver}`}}</div>
                      <div style="margin-right: 80px;">{{`审核日期：${formatDate(contractInfo.approverTime)}`}}</div>
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
            <a-row
              type="flex"
              justify="start"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border-left: 1px solid #1d2129;border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;margin-left:4px;width: 325px;min-height: 120px;height:100%;"
                >
                  初审意见
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;width: 836px;margin-right: 4px;min-height: 120px;padding:10px 40px;justify-content:flex-start;text-align:left; position:relative;"
                >
                  <div style="width: 100%;height:80px;">
                    <div style="margin-bottom: 10px;">
                      {{ auditOptionByType(contractInfo, 2) }}
                    </div>
                    <div
                      style="display: flex; align-items: center; justify-content: flex-end;position:absolute;bottom:5px;right:5px;"
                    >
                      <div style="margin-right: 80px; width: 140px;">{{`审核人：${contractInfo.preReviewer}`}}</div>
                      <div style="margin-right: 80px;">{{`审核日期：${formatDate(contractInfo.preReviewerTime)}`}}</div>
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
            <!-- <a-row
              type="flex"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;margin-bottom:4px"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border: 1px solid #1d2129;margin-left:4px;width: 325px;margin-bottom: 4px;min-height: 65px;height:100%;padding:10px 40px;"
                >
                  初审意见
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;border-bottom: 1px solid #1d2129; width: 836px;margin-bottom: 4px;min-height: 65px;height:100%;padding:10px 40px;text-align:left;justify-content: flex-start;"
                >
                  {{ contractInfo.preliminaryReviewOpinion }}
                </div>
              </a-col>
            </a-row> -->
            <a-row
              type="flex"
              style="text-align: center;width: 1170px !important; font-size: 20px; line-height: 32px;margin-bottom:4px"
            >
              <a-col>
                <div
                  class="content-col"
                  style="border: 1px solid #1d2129;margin-left:4px;width: 325px;margin-bottom: 4px;min-height: 160px;height:100%;padding:10px 40px;"
                >
                  经发公司意见
                </div>
              </a-col>
              <a-col>
                <div
                  class="content-col"
                  style="border-top: 1px solid #1d2129;border-right: 1px solid #1d2129;border-bottom: 1px solid #1d2129; width: 836px;margin-bottom: 4px;min-height: 160px;height:100%;padding:10px 40px;text-align:left;justify-content: flex-start; position:relative;"
                >
                  <!-- {{ auditOptionByType(contractInfo, 3) }} -->
                  <div style="width: 100%; height:120px;">
                    <div style="margin-bottom: 10px;">
                      {{ auditOptionByType(contractInfo, 3) }}
                    </div>
                    <div
                      style="display: flex; align-items: center; justify-content: flex-end;position:absolute;bottom:5px;right:5px;"
                    >
                      <div style="margin-right: 80px; width: 140px;">
                        {{`审核人：${contractInfo.reviewer}`}}
                      </div>
                      <div style="margin-right: 80px;">{{`审核日期：${formatDate(contractInfo.reviewerTime)}`}}</div>
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
          <div class="preliminary-footer">{{ form.footer }}</div>
        </div>
        <div class="content-footer" id="footer">
          <div>
            <a-button class="footer-item" type="primary" @click="handleCancel"
              >返回</a-button
            >
          </div>
          <div class="right">
            <a-button class="footer-item" type="primary" @click="handleExport"
              >导出</a-button
            >
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
/**
 * props:modalVisible
 * event:closeModal
 */
import JsPdfImg from "html2pdf-2img";
import { ApiLeaseReviewAuditQuery } from "APIs/Template/index.js";
import moment from 'moment';
export default {
  props: {
    modalVisible: Boolean,
    businessInfo: Object,
    isDownload: Boolean,
  },
  data() {
    return {
      visible: false,
      isDown: false,
      dataInfo: {
        filingNumber: "",
      },
      form: {
        title: "初审表",
        basicHeader: "一、基本情况",
        preliminaryHeader: "二、初审情况",
        footer: "上海华泾经济发展有限公司",
      },
      preliminaryList: [],
      contractInfo: {},
      lessorName: "",
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      isIndustry: false, //登录角色是否是实业公司
    };
  },
  mounted() {
    const userInfo = localStorage.getItem("USER_KEY");

    console.log(JSON.parse(userInfo).roles);
    const roles = JSON.parse(userInfo).roles;
    if (roles && roles.includes("实业公司")) {
      this.$set(this, "isIndustry", true);
    } else {
      this.$set(this, "isIndustry", false);
    }
  },
  watch: {
    modalVisible: {
      handler() {
        this.$nextTick(() => {
          this.modalVisibleWatch();
        });
      },
      immediate: true,
    },
    isDownload: {
      handler() {
        this.$nextTick(() => {
          this.isDownloadWatch();
        });
      },
      immediate: true,
    },
    businessInfo: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "dataInfo", this.businessInfo);
          this.QueryBusinessInfoByFilingNumber();
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    formatDate: function(source) {
      let target = "";
      if (source) {
        // target = moment(source).format("YYYY-MM-DD HH:mm:ss");
        target = moment(source).format("YYYY-MM-DD");
      }
      return target;
    },
    QueryBusinessInfoByFilingNumber: function() {
      const { filingNumber } = this.businessInfo;
      if (!filingNumber) {
        return;
      }
      if (filingNumber) {
        const params = {
          filingNumber,
        };
        ApiLeaseReviewAuditQuery(params).then((res) => {
          if (res.code == 0) {
            console.log(res);
            const contractReviews = res.data?.contractReviews || [];
            const contractInfo = res.data?.contractInfo;
            this.$set(this, "preliminaryList", contractReviews);
            this.$set(this, "contractInfo", contractInfo);
            this.$set(this, "lessorName", res.data?.lessorName);
          }
        });
      }
    },
    modalVisibleWatch: function() {
      this.$set(this, "visible", this.modalVisible);
    },
    isDownloadWatch: function() {
      this.$set(this, "isDown", this.isDownload);
    },
    handleCancel: function() {
      console.log("close modal action.");
      this.$emit("closeModal");
    },
    handleExport: function() {
      console.log("close modal and export current page content as pdf");
      let footer = document.getElementById("footer");
      footer.style.display = "none";
      new JsPdfImg("#printPage", "初审表", {
        pageBreak: [".title", "#area", "li", "h3"], // 当导出pdf时候，这个参数必填
        pageStartOffset: 200, // 每个页头的留空距离
        watermarkOption: {
          watermark_txt: "上海华泾经济发展有限公司",
          z_index: 97,
          watermark_x: 0,
          watermark_y: 0,
          watermark_x_space: 80,
          watermark_y_space: 80,
          watermark_width: 480,
          watermark_fontsize: "30px",
          watermark_alpha: 0.08,
        },
      }).outPdf(() => {
        console.log("结束");
        footer.style.display = "flex";
        this.$emit("closeModal");
      });
    },
    auditOptionByType: function(contractInfo, type) {
      const {
        // approver,
        approvalSummary,
        // preReviewer,
        preliminaryReviewOpinion,
        // reviewer,
        reviewOpinions,
      } = contractInfo;
      if (type == 1) {
        //小结
        return approvalSummary;
        // return approver
        //   ? approvalSummary + "(" + approver + ")"
        //   : approvalSummary;
      } else if (type == 2) {
        //初审意见
        return preliminaryReviewOpinion;
        // return preReviewer
        //   ? preliminaryReviewOpinion + "(" + preReviewer + ")"
        //   : preliminaryReviewOpinion;
      } else if (type == 3) {
        //经发公司意见
        return reviewOpinions;
        // return reviewer
        //   ? reviewOpinions + "(" + reviewer + ")"
        //   : reviewOpinions;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  width: 1170px;
  padding-top: 56px;
  margin-left: 90px;
  margin-right: 90px;
  margin-bottom: 60px;

  &-col {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-header {
    text-align: center;
    color: #1d2129;
    font-size: 32px;
    font-weight: 600;
  }
  &-basic-header {
    margin-top: 32px;
    color: #1d2129;
    font-size: 22px;
    font-weight: 500;
  }
  &-basic-info {
    margin-top: 18px;
    width: 100%;
    border: 1px solid #1d2129;
  }
  &-preliminary-header {
    margin-top: 32px;
    color: #1d2129;
    font-size: 22px;
    font-weight: 500;
  }
  &-preliminary-info {
    margin-top: 18px;
    width: 100%;
    border: 1px solid #1d2129;
  }
  .preliminary-footer {
    font-size: 18px;
    font-weight: 400;
    color: #1d2129;
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &-footer {
    margin-top: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    .right {
      margin-left: 36px;
    }
    .footer-item {
      width: 112px;
      height: 40px;
      border-radius: 6px;
    }
  }
}
</style>

<!-- 载体查询 -->
<template>
  <div class="carrier">
    <div class="carrierFrom">
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
        @submit="handleSubmit"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="园区名称">
              <a-select v-model="queryParam.parkName" placeholder="全部">
                <a-select-option
                  :value="item"
                  v-for="item in enumerateObj.parkList"
                  :key="item"
                  >{{ item }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="载体地址">
              <a-input
                v-model="queryParam.roomAddress"
                placeholder="xx号楼/xx层/xx室"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="房间面积">
              <a-select v-model="queryParam.area" placeholder="全部">
                <a-select-option
                  :value="item.value"
                  v-for="item in parkArr"
                  :key="item.value"
                  >{{ item.desc }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="状态">
              <a-select v-model="queryParam.floorStatus" placeholder="全部">
                <a-select-option
                  :value="item.value"
                  v-for="item in stateArr"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="租赁到期时间">
              <a-range-picker
                v-model="leaseEndTime"
                @change="onChange"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8" align="right">
            <a-form-item>
              <a-button type="primary" @click="search">查询</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="tablePart">
      <div class="table">
        <s-table
          ref="table"
          size="default"
          :columns="columns"
          :data="loadData"
          :scroll="{ x: 1000 }"
          rowKey="key"
        >
          <span slot="serial" slot-scope="text, record, index">
            {{ index + 1 }}
          </span>
          <span slot="parkName" slot-scope="text, record">
            <a href="javascript:;" @click="toDetail(text, record)">{{
              record.parkName
            }}</a>
          </span>
          <span slot="tenantry" slot-scope="text, record">
            <a href="javascript:;" @click="toCompany(text, record)">{{
              record.tenantry
            }}</a>
          </span>
        </s-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  queryCarrierInformation,
  queryCondition
} from "@/pages/demo/data/api/api/park"
import STable from "@/components/Table"
export default {
  components: {
    STable
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        }
      },
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 60,
          fixed: "left"
        },
        {
          title: "园区",
          dataIndex: "parkName",
          scopedSlots: { customRender: "parkName" },
          width: 120,
          fixed: "left"
        },
        {
          title: "楼号",
          dataIndex: "buildingNumber",
          width: 60
        },
        {
          title: "楼层",
          dataIndex: "floorNumber",
          width: 60
        },
        {
          title: "房间号",
          dataIndex: "roomNumber",
          width: 80
        },
        {
          title: "房间面积(平米)",
          dataIndex: "roomArea"
        },
        {
          title: "状态",
          dataIndex: "floorStatus",
          width: 70,
          customRender: (text) => {
            return text == 1 ? "占用" : "空置"
          }
        },
        {
          title: "承租方",
          dataIndex: "tenantry",
          scopedSlots: { customRender: "tenantry" }
        },
        {
          title: "租赁单价(元/天/平米)",
          dataIndex: "rent"
        },
        {
          title: "租赁到期时间",
          dataIndex: "leaseEndTime"
        }
      ],
      timeList: [],
      leaseEndTime: "",
      queryParam: {
        parkName: "星联科技园",
        // floor: "",
        // area: "",
        // status: "",
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 10
      },
      labelCol: { span: 4 },
      parkArr: [
        {
          desc: "200平米以内",
          value: "0"
        },
        {
          desc: "201平米-500平米",
          value: "1"
        },
        {
          desc: "201平米-500平米",
          value: "2"
        },
        {
          desc: "201平米-500平米",
          value: "3"
        },
        {
          desc: "501平米-800平米",
          value: "4"
        },
        {
          desc: "800平米-1000平米",
          value: "4"
        },
        {
          desc: "1000平米以上",
          value: "4"
        }
      ],
      stateArr: [
        {
          name: "空置",
          value: "0"
        },
        {
          name: "占用",
          value: "1"
        }
      ],
      enumerateObj: {}, // 查询条件对象
      // 加载数据方法 必须为 Promise 对象
      loadData: ({ pageNo, pageSize }) => {
        console.log("pageNo", pageNo, "pageSize", pageSize)
        const requestParameters = Object.assign(this.queryParam, {
          pageNum: pageNo,
          pageSize
        })
        console.log("---***", requestParameters, this.queryParam)
        return queryCarrierInformation(requestParameters).then((res) => {
          let dataObj = res.data
          dataObj.data = res.data.records
          dataObj.totalCount = res.data.total
          dataObj.pageSize = res.data.size
          dataObj.pageNo = res.data.current
          return dataObj
        })
      }
    }
  },
  mounted() {
    this.init()
    this.getRoomArea()
  },
  methods: {
    async getRoomArea() {
      this.parkArr = await this.$getDictByType({ dictCode: "roomArea" })
    },
    async init() {
      const request = await queryCondition()
      this.enumerateObj = request.data
      console.log(request, this.enumerateObj)
    },
    search() {
      console.log(this.queryParam)
      this.$refs.table.refresh(true)
    },
    onChange(date, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    toDetail(text, record) {
      this.$router.push(
        `/information-query/park-detail?parkName=${record.parkName}`
      )
    },
    toCompany(text, record) {
      this.$router.push(`/information-query/enterprise-detail?id=${record.id}`)
    }
  }
}
</script>

<style lang="less" scoped>
.carrier {
  display: flex;
  flex-wrap: wrap;
  .carrierFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }
  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
</style>

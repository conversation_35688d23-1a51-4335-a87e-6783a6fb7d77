<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 座机控件 
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '座机'"
      style="margin-bottom:unset"
      prop="LandLine"
      :rules="[
        {
          required: data.notNull,
          message: '请输入座机号！',
          trigger: 'blur'
        }
      ]"
    >
      <!-- <a-input-group compact>
        <a-input style="width: 40%" default-value="" v-model="form.phoneNo1" />
        <a-input style="width: 60%" default-value="" v-model="form.phoneNo2" />
      </a-input-group> -->
      <a-input
        v-model="form.LandLine"
        :placeholder="data.placeholder.tipsTitleText || '请输入'"
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        LandLine: null
      }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

<!-- 报表管理--园区分析报表 TODELETE -->
<template>
  <div class="park">
    <div class="parkFrom">
      <a-form
        class="all"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-row :gutter="100" align="center">
          <a-col :span="8">
            <a-form-item label="统计周期" class="label">
              <a-space direction="vertical" :size="12">
                <a-range-picker
                  style="width: 100%;"
                  v-model="queryParams.period"
                  @change="date()"
                />
              </a-space>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="园区名称" class="label">
              <a-select
                v-model="queryParams.parkName"
                style="width: 100%;"
                placeholder="请选择"
                allowClear
                :filterMultiple="false"
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
              >
                <a-select-option
                  :value="item.name"
                  v-for="item in parkArr"
                  :key="item.value"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <div class="btnGroup">
              <a-button type="primary" class="query" @click="search">
                查询
              </a-button>
              <a-button type="primary" class="export" @click="reset"
                >重置</a-button
              >
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="tablePart">
      <a-card style="width:100%">
        <s-table
          ref="table"
          size="default"
          rowKey="key"
          :columns="columns"
          :scroll="{ x: 1200, y: 800 }"
          :data="loadData"
        >
        </s-table>
      </a-card>
    </div>
  </div>
</template>

<script>
// import ParkPublicFrom from "../compontents/ParkPublicFrom.vue";
// import PublicTable from "../compontents/PublicTable.vue";
import { ApiGetTaxAnalysis } from "@/pages/index/data/api/ReportManagement/ParkAnalysisReport"
// import moment from "moment"
import STable from "@/components/Table"
import { parseNumFloat } from "@/common/utils/utils.js"
export default {
  components: {
    // ParkPublicFrom,
    // PublicTable
    STable
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      parkArr: [
        {
          name: "星联科技园",
          value: "星联科技园"
        },
        {
          name: "桂林科技园",
          value: "桂林科技园"
        },
        {
          name: "虹三产业园",
          value: "虹三产业园"
        },
        {
          name: "华泾广场",
          value: "华泾广场"
        }
      ],
      queryParams: {
        period: [],
        parkName: undefined
      },
      defaultValue: "排序",
      sortArr: [
        {
          value: "1",
          name: "按园区面积排序"
        },
        {
          value: "2",
          name: "按楼宇数排序"
        }
      ],
      tableData: [],
      columns: [
        {
          title: "序号",
          width: 80,
          dataIndex: "ordinal",
          align: "center",
          customRender: function(text, record, index) {
            return index + 1
          }
        },
        {
          title: "企业名称",
          dataIndex: "componeyName",

          align: "center"
        },
        {
          title: "园区名称",

          dataIndex: "parkName",
          align: "center"
        },
        {
          title: "是否属地企业",
          dataIndex: "isLocal",
          width: 220,
          align: "center",
          customRender: function(text) {
            return text == 1 ? "是" : "否"
          }
        },
        {
          title: "纳税金额(万元)",
          dataIndex: "taxAmount",
          width: 220,
          align: "center",
          customRender(text) {
            return parseNumFloat(text)
          }
        }
      ],
      data: []
    }
  },
  methods: {
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        period: [],
        createDateStart: "",
        createDateEnd: ""
      }
      this.leaseEndTime = []
      this.$refs.table.refresh(true)
    },
    search() {
      console.log(this.queryParam)
      if (this.queryParams.period.length > 0) {
        this.queryParams.createDateStart = this.queryParams.period[0].format(
          "YYYY-MM-DD hh:mm:ss"
        )
        this.queryParams.createDateEnd = this.queryParams.period[1].format(
          "YYYY-MM-DD hh:mm:ss"
        )
      }
      this.$refs.table.refresh(true)
    },
    date(value) {
      console.log(value, "value")

      if (this.queryParams.period.length > 0) {
        this.queryParams.createDateStart = this.queryParams.period[0].format(
          "YYYY-MM-DD hh:mm:ss"
        )
        this.queryParams.createDateEnd = this.queryParams.period[1].format(
          "YYYY-MM-DD hh:mm:ss"
        )
      } else {
        this.queryParams.createDateStart = ""
        this.queryParams.createDateEnd = ""
      }
    },
    getOption(e) {
      this.defaultValue = e
    },
    //导出按钮
    deduced() {},
    async loadData({ pageNo, pageSize }) {
      console.log(pageNo, pageSize)
      const requestParameters = Object.assign(this.queryParams, {
        pageNum: pageNo,
        pageSize
      })
      return ApiGetTaxAnalysis(requestParameters).then((res) => {
        let dataObj = res.data
        return dataObj
      })
      // return new Promise((resolve) => {
      //   setTimeout(() => {
      //     resolve({
      //       pageNo: 1,
      //       pageSize: 10,
      //       total: 0,
      //       records: [
      //         {
      //           id: 1,
      //           parkName: "星联企业园", //园区名称
      //           isLocal: 1, //是否属地
      //           taxAmount: 1000, //纳税金额
      //           componeyName: "金星科技"
      //         },
      //         {
      //           id: 2,
      //           parkName: "星联企业园", //园区名称
      //           isLocal: 2, //是否属地
      //           taxAmount: 1200, //纳税金额
      //           componeyName: "中国移动"
      //         },
      //         {
      //           id: 3,
      //           parkName: "星联企业园", //园区名称
      //           isLocal: 1, //是否属地
      //           taxAmount: 2000, //纳税金额
      //           componeyName: "长城股份"
      //         },
      //         {
      //           id: 4,
      //           parkName: "星联企业园", //园区名称
      //           isLocal: 2, //是否属地
      //           taxAmount: 2600, //纳税金额
      //           componeyName: "星光乐园"
      //         }
      //       ]
      //     })
      //   }, 1000)
      // }).then((res) => {
      //   return res
      // })
    }
  }
}
</script>

<style lang="less" scoped>
.park {
  display: flex;
  flex-wrap: wrap;
  .parkFrom {
    width: 100%;
    height: 4rem;
    border-width: 0px;
    position: absolute;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    border: none;
    border-radius: 2px;
    .all {
      // display: flex;
      // flex-wrap: nowrap;
      margin-top: 0.8rem;
      // margin-left: 0.5rem;
      .label {
        // display: flex;
        // flex-wrap: nowrap;
        // margin-right: 30px;
      }
      .btnGroup {
        // margin-left: auto;
        margin-right: 20px;
        float: right;
        margin-top: 2px;
        .query {
          margin-right: 10px;
        }
      }
    }
  }
  .tablePart {
    margin-top: 80px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;
    .sort {
      margin-left: auto;
      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }
  .table {
    width: 100%;
    margin-top: 10px;
  }
}
</style>

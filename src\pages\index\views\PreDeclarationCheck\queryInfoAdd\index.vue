<!-- 预申报表审核 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <div class="detail">
      <a-form-model
        class="ant-advanced-search-form"
        ref="ruleForm"
        :model="decisionParmas"
        :rules="rules"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <div class="lessorInfo">
          <div
            class="lessorInfo-tit"
            style="
              height: 100px;
              display: flex;
              align-items: center;
              border-bottom: 1px solid #ece5e5;
            "
          >
            <div class="title">申请人</div>
            <div class="title-font">{{ decisionParmas.initiateName }}</div>
          </div>
        </div>
        <!-- 企业基础信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业基础信息</div>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="companyName" label="企业名称">
                  <a-input
                    disabled
                    placeholder="请输入企业名称"
                    v-model="decisionParmas.companyName"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="territorialSituation" label="属地情况">
                  <a-select
                    disabled
                    v-model="decisionParmas.territorialSituation"
                    placeholder="请选择属地情况"
                    allowClear
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in territorialArr"
                      :key="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="registCapi" label="注册资金">
                  <a-input
                    disabled
                    placeholder="请输入注册资金"
                    v-model="decisionParmas.registCapi"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="startDate" label="创立时间">
                  <a-date-picker
                    disabled
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    v-model="decisionParmas.startDate"
                    style="width: 100%"
                    placeholder="请输入创立时间"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="newStatus" label="经营状态">
                  <a-input
                    disabled
                    v-model="decisionParmas.newStatus"
                    placeholder="请输入经营状态"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" style="display: flex">
              <a-col :span="16">
                <a-form-model-item prop="scope" label="经营范围">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.scope"
                    placeholder="请输入经营范围"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="40"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="econKind" label="企业性质">
                  <a-input
                    disabled
                    v-model="decisionParmas.econKind"
                    placeholder="请输入企业性质"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="belongOrg" label="工商登记机关">
                  <a-input
                    disabled
                    v-model="decisionParmas.belongOrg"
                    placeholder="请输入工商登记机关"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="address" label="工商注册地址">
                  <a-input
                    disabled
                    v-model="decisionParmas.address"
                    placeholder="请输入工商注册地址"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8"> </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="contactPerson" label="联系人">
                  <a-input
                    disabled
                    v-model="decisionParmas.contactPerson"
                    placeholder="请输入联系人"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="duty" label="职务">
                  <a-input
                    disabled
                    v-model="decisionParmas.duty"
                    placeholder="请输入职务"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="contactWay" label="联系方式">
                  <a-input
                    disabled
                    v-model="decisionParmas.contactWay"
                    placeholder="请输入联系方式"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 租赁意向信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">租赁意向信息</div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="leasingDemand" label="现租赁需求">
                  <a-radio-group
                    disabled
                    v-model="decisionParmas.leasingDemand"
                  >
                    <a-radio :value="'0'"> 新设 </a-radio>
                    <a-radio :value="'1'"> 扩租 </a-radio>
                    <a-radio :value="'2'"> 搬迁 </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>

              <a-col :span="8">
                <a-form-model-item prop="leasePurpose" label="租赁用途">
                  <!-- <a-input
                    disabled
                    placeholder="请选择租赁用途"
                    v-model="decisionParmas.leasePurpose"
                    maxLength="20"
                  /> -->
                  <a-select
                    disabled
                    placeholder="请选择租赁用途"
                    v-model="decisionParmas.leasePurpose"
                    maxLength="20"
                  >
                    <a-select-option
                      :value="item.value"
                      v-for="item in leasePurposeArr"
                      :key="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>

              <a-col
                :span="8"
                v-if="
                  decisionParmas.leasePurpose == '商业' ||
                  decisionParmas.leasePurpose == '其他'
                "
              >
                <a-form-model-item prop="leasePurposeInfo" label="">
                  <a-input
                    disabled
                    placeholder="请输入内容"
                    v-model="decisionParmas.leasePurposeInfo"
                    maxLength="20"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseDate" label="起租日期">
                  <a-input
                    disabled
                    placeholder="请输入起租日期"
                    v-model="decisionParmas.leaseDate"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" v-if="decisionParmas.leasingDemand == '2'">
              <a-col :span="16">
                <a-form-model-item prop="officeAddress" label="原办公地址">
                  <a-input
                    disabled
                    :title="decisionParmas.officeAddress"
                    v-model="decisionParmas.officeAddress"
                    placeholder="请输入原办公地址"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8"> </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="rentFree" label="免租期">
                  <a-input
                    disabled
                    placeholder="请输入免租期"
                    v-model="decisionParmas.rentFree"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseArea" label="租赁面积">
                  <a-input
                    disabled
                    type="number"
                    v-model="decisionParmas.leaseArea"
                    addon-after="㎡"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="rentalUnitPrice" label="租赁单价">
                  <a-input
                    disabled
                    type="number"
                    @input="handleInput1(decisionParmas.rentalUnitPrice)"
                    placeholder="请输入租赁单价"
                    v-model="decisionParmas.rentalUnitPrice"
                    addon-after="元/㎡/天"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="rentalAddress" label="租赁地址">
                  <a-input
                    disabled
                    placeholder="请输入租赁地址"
                    :title="decisionParmas.rentalAddress"
                    v-model="decisionParmas.rentalAddress"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseTerm" label="租期">
                  <a-input
                    disabled
                    placeholder="请输入租期"
                    v-model="decisionParmas.leaseTerm"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <!-- <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="specialTerms" label="特殊条款">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.specialTerms"
                    placeholder="请输入特殊条款"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="40"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="negotiateProgress" label="洽谈进度">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.negotiateProgress"
                    placeholder="请输入洽谈进度"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="40"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="solveProblem" label="需协调解决">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.solveProblem"
                    placeholder="请输入需协调解决"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="40"
                  />
                </a-form-model-item>
              </a-col>
            </a-row> -->
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="remark" label="备注">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.remark"
                    placeholder="请输入备注"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="40"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 企业资质评估信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业资质评估信息</div>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="insuranceNum" label="参保人数">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.insuranceNum"
                    placeholder="请输入参保人数"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="employeesInfo" label="主要人员及职位">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.employeesInfo"
                    placeholder="请输入主要人员及职位"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item
                  prop="partnersInfo"
                  label="主要股东及持股比例"
                >
                  <a-textarea
                    disabled
                    v-model="decisionParmas.partnersInfo"
                    placeholder="请输入主要股东及持股比例"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="affiliate" label="主要关联公司">
                  <a-select disabled v-model="affiliate">
                    <a-select-option
                      :value="item2.value"
                      v-for="item2 in affiliateArr"
                      :key="item2.item"
                      >{{ item2.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="16">
                <a-form-model-item prop="branchesInfo" label="">
                  <a-input
                    :title="decisionParmas.branchesInfo"
                    disabled
                    v-model="decisionParmas.branchesInfo"
                    placeholder="请输入分公司及子公司"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="affiliate1" label=" ">
                  <a-select disabled v-model="affiliate1">
                    <a-select-option
                      :value="item2.value"
                      v-for="item2 in affiliateArr1"
                      :key="item2.item"
                      >{{ item2.label }}</a-select-option
                    >
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="16">
                <a-form-model-item prop="investsInfo" label="">
                  <a-input
                    :title="decisionParmas.investsInfo"
                    disabled
                    v-model="decisionParmas.investsInfo"
                    placeholder="请输入对外投资公司"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
        <!-- 专利情况 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">专利情况</div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="patentNum" label="发明专利申请">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.patentNum"
                    placeholder="请输入发明专利申请"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="trademarkNum" label="商标信息">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.trademarkNum"
                    placeholder="请输入商标信息"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item
                  prop="copyrightNum"
                  label="著作权（不含软著）"
                >
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    placeholder="请输入著作权（不含软著）"
                    v-model="decisionParmas.copyrightNum"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="certNum" label="资质认证">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.certNum"
                    placeholder="请输入资质认证"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="softwareNum" label="软件著作权">
                  <a-input
                    disabled
                    type="number"
                    oninput="value=value.replace('.', '',)"
                    v-model="decisionParmas.softwareNum"
                    placeholder="请输入软件著作权"
                    addon-after="个"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
        <!-- 资质情况 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">资质情况</div>
            <div>
              <a-checkbox-group
                disabled
                v-model="decisionParmas.aptitudeInfos"
                :options="plainOptions"
              />
            </div>
            <!-- <div style="display: flex">
              <a-checkbox-group
                disabled
                v-model="decisionParmas.aptitudeOther"
                :options="plainOptions1"
              />
              <a-input
                disabled
                :title="decisionParmas.aptitudeOtherDescription"
                v-model="decisionParmas.aptitudeOtherDescription"
                placeholder="请输入其他资质描述"
                style="width: 56%; margin-bottom: 30px; margin-left: 26px"
              />
            </div> -->
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="entPortraitInfo" label="主营业务">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.entPortraitInfo"
                    placeholder="请输入主营业务 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="projectInfo" label="品牌产品">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.projectInfo"
                    placeholder="请输入品牌产品 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="awardsAchievements" label="获奖及成就">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.awardsAchievements"
                    placeholder="请输入获奖及成就 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="financeInfo" label="融资情况">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.financeInfo"
                    placeholder="请输入融资情况 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="entProfileInfo" label="上市情况">
                  <a-textarea
                    disabled
                    v-model="decisionParmas.entProfileInfo"
                    placeholder="请输入上市情况 "
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
        <!-- 审批意见 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">审批意见</div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="approvalResult" label="审批结果">
                  <a-radio-group
                    :disabled="isShow"
                    v-model="decisionParmas.approvalResult"
                  >
                    <a-radio :value="'0'"> 通过 </a-radio>
                    <a-radio :value="'1'"> 不通过 </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="approvalOpinion" label="审批意见">
                  <a-textarea
                    :disabled="isShow"
                    v-model="decisionParmas.approvalOpinion"
                    placeholder="请输入审批意见"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="50"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form-model>
    </div>

    <div style="text-align: center">
      <a-button
        v-if="this.$route.query.statu != 2"
        type="danger"
        @click="comfirBtn(0)"
        class="back"
        >驳回</a-button
      >
      <a-button class="back" @click="toBack">返回</a-button>
      <a-button
        v-if="this.$route.query.statu != 2"
        type="primary"
        @click="comfirBtn(1)"
        class="back"
        >保存</a-button
      >
      <a-button
        v-if="this.$route.query.statu != 2"
        type="primary"
        @click="comfirBtn(2)"
        class="back"
        >提交审核</a-button
      >
    </div>
  </div>
</template>

<script>
import {
  queryContractInfoByIdApiNew,
  approvalContractApi,
} from "@/pages/index/data/api/PreDeclarationQuery";
export default {
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      rules: {
        companyName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
        territorialSituation: [
          { required: true, message: "请选择属地情况", trigger: "change" },
        ],
        leasingDemand: [
          { required: true, message: "请选择现租赁需求", trigger: "change" },
        ],
        leasePurpose: [
          { required: true, message: "请选择租赁用途", trigger: "blur" },
        ],
        leasePurposeInfo: [
          { required: true, message: "请输入内容", trigger: "blur" },
        ],
        contactPerson: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        duty: [{ required: true, message: "请输入职务", trigger: "blur" }],
        contactWay: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
        ],
        officeAddress: [
          { required: true, message: "请输入原办公地址", trigger: "blur" },
        ],
        approvalResult: [
          { required: true, message: "请选择审批结果", trigger: "change" },
        ],
        approvalOpinion: [
          { required: false, message: "请输入审批意见", trigger: "blur" },
        ],
      },
      isFlag: true,
      isShow: false,
      territorialArr: [
        { label: "镇属", value: "镇属" },
        { label: "区属", value: "区属" },
        { label: "关联属地", value: "关联属地" },
        { label: "未属地", value: "未属地" },
      ],
      leasePurposeArr: [
        { label: "办公", value: "办公" },
        { label: "商业", value: "商业" },
        { label: "其他", value: "其他" },
      ],
      plainOptions: [
        { label: "国家科技型中小企业", value: "1" },
        { label: "国家高新技术企业", value: "2" },
        { label: "市专精特新中小企业", value: "3" },
        { label: "国家专精特新小巨人企业", value: "4" },
        { label: "区企业技术中心", value: "5" },
        { label: "市企业技术中心", value: "6" },
      ],
      plainOptions1: [{ label: "其他", value: "0" }],
      affiliateArr: [{ label: "分公司及子公司", value: "0" }], //分公司及子公司
      affiliateArr1: [{ label: "对外投资公司", value: "0" }], //对外投资公司
      affiliate: "0", //关联公司
      affiliate1: "0", //关联公司
      officeAddressParam: "", //原办公地址参数
      decisionParmas: {
        approvalOpenType: null, //0:保存  1:提交
        initiateName: "", //申请人
        //企业基础信息
        companyName: "", //企业名称
        territorialSituation: undefined, //属地情况
        registCapi: "", //注册资金
        startDate: "", //创立时间
        contactWay: "", //联系方式
        newStatus: "", //经营状态
        scope: "", //经营范围
        address: "", //工商注册地址
        officeAddress: "", //原办公地址
        contactPerson: "", //联系人
        duty: "", //职务
        //租赁意向信息
        leasingDemand: "0", //现租赁需求
        leasePurpose: "", //租赁用途
        leasePurposeInfo: undefined, //租赁用途内容
        leaseDate: "", //起租日期
        rentFree: "", //免租期
        leaseArea: 0, //租赁面积
        rentalUnitPrice: 0, //租赁单价
        rentalAddress: "", //租赁地址
        leaseTerm: "", //租期
        // specialTerms: "", //特殊条款
        // negotiateProgress: "", //洽谈进度
        // solveProblem: "", //需协调解决问题
        remark: "", //备注
        //审批意见
        approvalResult: "0", //审批结果
        approvalOpinion: "", //审批意见
        econKind: "", //企业性质
        belongOrg: "", //工商登记机关
        insuranceNum: "", //参保人数
        employeesInfo: "", //主要人员及职位
        partnersInfo: "", //主要股东及持股比例
        branchesInfo: "", //分公司及子公司
        investsInfo: "", //对外投资公司
        patentNum: 0, //发明专利申请
        trademarkNum: 0, //有效注册商标
        copyrightNum: 0, //著作权（不含软著）
        certNum: 0, //资质认证
        softwareNum: 0, //软件著作权
        aptitudeInfos: [], //资质情况
        aptitudeOther: ["1"], //资质情况其他 是否其他资质 0 是 1 否
        aptitudeOtherDescription: "", //其他资质描述
        entPortraitInfo: "", //主营业务
        projectInfo: "", //品牌产品
        awardsAchievements: "", //获奖及成就
        financeInfo: "", //融资情况
        entProfileInfo: "", //上市情况
      },
    };
  },
  watch: {
    "decisionParmas.leasingDemand": {
      // deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        console.log(val, oldVal, this.decisionParmas.officeAddress);
        if (val == "2") {
          this.decisionParmas.officeAddress = this.officeAddressParam;
        } else {
          this.decisionParmas.officeAddress = "";
        }
      },
    },
  },
  created() {
    this.getContractId();
    if (this.$route.query.statu == 2) {
      // 2 :查看
      this.isShow = true;
    } else {
      // 1 :处理
      this.isShow = false;
    }
  },
  methods: {
    handleInput1(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.rentalUnitPrice = val;
    },
    getContractId() {
      //根据id查询所有数据
      if (this.$route.query.id) {
        let parmas = {
          id: this.$route.query.id,
        };
        queryContractInfoByIdApiNew(parmas).then((res) => {
          this.officeAddressParam = res.data.officeAddress || "";
          let obj1 = this.decisionParmas;
          let obj2 = res.data;
          Object.keys(obj1).forEach((key) => {
            obj1[key] = obj2[key] == undefined ? obj1[key] : obj2[key];
          });
          if (res.data.aptitudeOther) {
            let arr = [];
            arr.push(res.data.aptitudeOther.toString());
            this.decisionParmas.aptitudeOther = arr;
          }
        });
      }
    },
    comfirBtn(v) {
      if (this.isFlag && this.$route.query.id) {
        this.isFlag = false;
        const parmas = this.decisionParmas;
        parmas.approvalOpenType = v; //0：保存，1：提交
        parmas.id = this.$route.query.id;
        if (!parmas.aptitudeOther) {
          parmas.aptitudeOther = "1";
        } else {
          parmas.aptitudeOther = this.decisionParmas.aptitudeOther[0];
        }
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            approvalContractApi(parmas)
              .then(() => {
                this.$router.push(`/pre-declaration-check/query`);
              })
              .catch((error) => {
                this.$message.warning(error.data.data);
              });
          } else {
            return this.$message.warning("有必填项未填写，请检查！");
          }
        });
        setTimeout(() => {
          this.isFlag = true;
        }, 3000);
      }
    },
    toBack() {
      this.$router.push(`/pre-declaration-check/query`);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}
.title {
  width: 70px;
  height: 38px;
  line-height: 38px;
  border-radius: 45%;
  background-color: #e8e4e4;
  color: #cbc5c5;
  font-size: 16px;
  text-align: center;
  margin-right: 20px;
}
.title-font {
  font-size: 21px;
  font-weight: bold;
}
.ant-advanced-search-form /deep/ .ant-checkbox-group {
  .ant-checkbox-group-item {
    font-size: 17px !important;
    margin-left: 68px;
    margin-bottom: 30px;
  }
}
.ant-advanced-search-form /deep/ .ant-col-8 {
  .ant-col-sm-8 {
    width: 31% !important;
  }
  .ant-col-sm-16 {
    width: 69% !important;
  }
}
.ant-advanced-search-form /deep/ .ant-col-16 {
  .ant-col-sm-8 {
    width: 15% !important;
  }
  .ant-col-sm-16 {
    width: 85% !important;
  }
}
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  // width: 166px !important;
}
</style>

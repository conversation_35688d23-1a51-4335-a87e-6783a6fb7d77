<!--
* <AUTHOR>
* @time 2020-9-1
* @dec 多选控件 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '多选'"
      style="margin-bottom:unset"
      prop="multipleChoiceVal"
      :rules="[
        {
          required: data.notNull,
          message: '请选择',
          trigger: 'change'
        }
      ]"
    >
      <a-select
        :getPopupContainer="
          (triggerNode) => {
            return triggerNode.parentNode || document.body
          }
        "
        mode="multiple"
        v-model="form.multipleChoiceVal"
        :placeholder="data.placeholder.tipsTitleText || '请选择'"
      >
        <a-select-option
          v-for="(item, index) in data.optionsData"
          :key="index"
          :value="item.optionContent"
        >
          {{ item.optionContent }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          placeholder: ""
        }
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        multipleChoiceVal: undefined
      }
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
</style>

<!--
* <AUTHOR>
* @time 2020-12-22
* @dec 系统管理-消息模板管理
-->
<template>
  <a-card :bordered="false">
    <!-- 搜索栏 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="标题">
              <a-input
                v-model="queryParam.title"
                placeholder="请输入姓名"
              /> </a-form-item
          ></a-col>

          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="() => (this.queryParam = {})"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作栏 -->
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd">新建</a-button>
    </div>
    <!-- 列表 -->
    <s-table
      ref="table"
      size="default"
      rowKey="key"
      :columns="columns"
      :data="loadData"
      class="table-page"
    >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * 10 + index + 1 }}
      </span>

      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record)">修改</a>
          <a-divider type="vertical" />
          <a-popconfirm
            title="请认真核对，选中数据是否删除!"
            @confirm="() => handleDel(record)"
          >
            <a href="javascript:;">删除</a>
          </a-popconfirm>
        </template>
      </span>
    </s-table>
    <add-form
      ref="addModal"
      :visible="visible"
      :data="mdl"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>
<script>
// 表格组件
import STable from "@/components/Table"
// import Ellipsis from "@/components/Ellipsis";
import AddForm from "./compontents/AddForm"

import {
  ApiSystemPageByCondition,
  ApiSystemRemoveById
} from "@/pages/index/data/api/SystemManagement/Message"

const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "serial" }
  },
  {
    title: "标题",
    dataIndex: "title"
  },
  {
    title: "流程id",
    dataIndex: "templateId"
  },
  {
    title: "节点id",
    dataIndex: "nodeId"
  },
  {
    title: "类型",
    dataIndex: "type"
  },
  {
    title: "文案",
    dataIndex: "content"
  },

  {
    title: "操作",
    dataIndex: "action",
    width: "120px",
    scopedSlots: { customRender: "action" }
  }
]
export default {
  components: { STable, AddForm },
  data() {
    this.columns = columns
    return {
      visible: false,
      mdl: null,
      // 查询参数
      queryParam: {
        title: undefined, // 标题
        pageSize: 10,
        ascs: "",
        currentPage: 1,
        descs: ""
        // pageSize: 10
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        this.pageNo = parameter.pageNo
        this.queryParam.currentPage = parameter.pageNo
        this.queryParam.current = this.queryParam.currentPage
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        console.log("loadData request parameters:", requestParameters)
        return ApiSystemPageByCondition(requestParameters).then((res) => {
          console.log("12321", res.data)
          return res.data
        })
      },
    }
  },
  mounted() {},
  watch: {},
  methods: {
    //添加
    handleAdd() {
      this.visible = true
      this.mdl = {}
    },
    //修改
    handleEdit(record) {
      this.visible = true
      this.mdl = { ...record }
      console.log("this.mdl", this.mdl)
    },
    //删除
    handleDel(record) {
      console.log("删除事件", record.id)
      this.loading = true
      const params = {
        id: record.id
      }
      ApiSystemRemoveById(params)
        .then(() => {
          this.$message.info("删除成功")
          this.$refs.table.refresh()
        })
        .finally(() => {
          this.loading = false
        })
    },
    //弹框关闭
    handleCancel() {
      this.visible = false
      this.lookDtvisible = false
    }
  }
}
</script>
<style lang="less"></style>

<!--
* <AUTHOR>
* @time 2020-9-10
* @dec 门户管理-首页管理-轮播图片-添加/修改
-->
<template>
  <a-modal
    :title="modelTitle"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="标题">
          <a-input
            autoComplete="off"
            v-decorator="[
              'bannerName',
              {
                rules: [{ required: true, message: '请输入标题!' }]
              }
            ]"
            placeholder="请输入标题"
          />
        </a-form-item>
        <a-form-item label="排序">
          <a-input-number
            :style="{ width: '110px' }"
            v-decorator="[
              'sort',
              {
                rules: [{ required: true, message: '请输入排序!' }]
              }
            ]"
            placeholder="请输入排序"
            :min="1"
            :max="999"
          />
        </a-form-item>
        <a-form-item label="是否启用">
          <!-- <a-switch v-decorator="['status', { valuePropName: 'checked' }]" /> -->
          <a-radio-group v-decorator="['status', { initialValue: 1 }]">
            <a-radio :value="1">
              是
            </a-radio>
            <a-radio :value="0">
              否
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="图片">
          <div
            class="clearfix"
            v-if="!form.id || (from.id && !form.bannerImage)"
          >
            <a-upload
              action=""
              list-type="picture-card"
              :file-list="fileList"
              :before-upload="beforeUpload"
              @preview="handlePreview"
              @change="handleUpload"
            >
              <div v-if="this.fileList < 1">
                <a-icon type="plus" />
                <div class="ant-upload-text">
                  选择上传
                </div>
              </div>
            </a-upload>
            <span v-if="this.fileList < 1"
              >图片最佳尺寸：1125*675，比例为5：3，支持jpg、png、jpeg</span
            >
            <a-modal
              :visible="previewVisible"
              :footer="null"
              @cancel="handleCancel"
            >
              <img alt="" style="width: 100%" :src="previewImage" />
            </a-modal>
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick";
import { Apicommonupload } from "@/data/api/components/ApprovalProcess";
import {
  ApiBusinessCreate,
  ApiBusinessUpdate
} from "@/pages/index/data/api/PortalManagement/HomePage/Rotation";
// 表单字段
const fields = ["bannerName", "sort", "status", "bannerImage"];

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      loading: false,
      form: this.$form.createForm(this),
      fileList: [],
      previewVisible: false,
      previewImage: "",
      thumbnailUrl: "",
      modelTitle: "" //弹框标题
    };
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          this.form.setFieldsValue({
            bannerName: null,
            sort: "",
            status: 1,
            bannerImage: ""
          });
          this.fileList = [];
          this.modelTitle = this.data.id ? "修改" : "添加";
        }
      },
      deep: true
    }
  },
  created() {
    // 防止表单未注册
    fields.forEach(v => this.form.getFieldDecorator(v));

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      if (this.data.bannerImage) {
        this.fileList.push({
          uid: "-1",
          // name: "image.png",
          status: "done",
          url: this.data.bannerImage
        });
        this.thumbnailUrl = this.data.bannerImage;
      }
      this.data && this.form.setFieldsValue(pick(this.data, fields));
    });
  },
  methods: {
    //确定提交
    handleOk(e) {
      this.loading = true;
      e.preventDefault();
      this.form.validateFields((errors, values) => {
        if (!errors) {
          let params = {
            bannerName: values.bannerName,
            sort: values.sort,
            status: values.status ? 1 : 0
            // bannerImage: this.thumbnailUrl
          };
          if (this.fileList.length) {
            params.bannerImage = this.thumbnailUrl;
          } else {
            params.bannerImage = "";
          }

          if (this.data.id) {
            params.id = this.data.id;
            ApiBusinessUpdate(params)
              .then(res => {
                if (res.code == 0) {
                  this.$emit("cancel");
                  // 重置表单数据
                  this.form.resetFields();
                  // 刷新表格
                  this.fileList = [];
                  this.thumbnailUrl = "";
                  this.$emit("ok");
                  this.$message.info("修改成功！");
                }
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            ApiBusinessCreate(params)
              .then(res => {
                if (res.code == 0) {
                  this.$emit("cancel");
                  // 重置表单数据
                  this.form.resetFields();
                  // 刷新表格
                  this.fileList = [];
                  this.thumbnailUrl = "";
                  this.$emit("ok");
                  this.$message.info("添加成功！");
                }
              })
              .finally(() => {
                this.loading = false;
              });
          }
        } else {
          this.loading = false;
        }
      });
    },
    // 上传图片调用接口
    handleUpload({ file, fileList }) {
      if (file.name) {
        let fileType = file.name.substr(file.name.lastIndexOf(".") + 1);
        let checkFileType = this.isAssetTypeAnImage(fileType);
        if (!checkFileType) {
          this.$message.error("请上传指定的图片类型！");
          return false;
        }
      }

      this.fileList = fileList;
      if (fileList.length > 1 || fileList.length < 1) {
        return false;
      }
      this.loading = true;
      let params = {
        objectId: "5", //待处理
        name: "1",
        type: "jpg",
        file: file
      };
      Apicommonupload(params)
        .then(res => {
          this.thumbnailUrl = res.data.thumbnailUrl;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //上传文件之前的钩子
    beforeUpload() {
      return false;
    },
    //取消查看图片
    handleCancel() {
      this.previewVisible = false;
    },
    //点击文件链接或预览图标时的回调
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    //判断图片后缀名
    isAssetTypeAnImage(ext) {
      return ["png", "jpg", "jpeg"].indexOf(ext.toLowerCase()) !== -1;
    }
  }
};
</script>

<template>
  <div class="all">
    <div class="mainContent">
      <!-- direction步骤条的方向水平还是垂直，size指定大小普通和迷你 -->
      <a-steps direction="vertical">
        <a-step
          :title="item.reviewNodes"
          v-for="(item, index) in recordData"
          :key="index"
          :status="getStatus(item)"
          :subTitle="item.approvalTime.substring(0, 10)"
        >
          <template #description>
            <div>{{ judgment(item)[0] }}</div>
            <div>{{ judgment(item)[1] }}</div>
            <div></div>
          </template>
          <!-- <a-icon
            slot="icon"
            :type="getIconType(item.state)"
            theme="filled"
          ></a-icon> -->
        </a-step>
      </a-steps>
    </div>
  </div>
</template>
<script>
import { ApiGetApprovalRecords } from "../../../data/api/RegistrationRecordReview/Trial";
export default {
  props: {
    show: <PERSON><PERSON><PERSON>,
    id: String
  },
  data() {
    return {
      recordData: [] //定义用来接收后台传过来的数据
    };
  },
  created() {
    this.getRecords(this.show + 22222);
  },
  computed: {},
  methods: {
    //获取记录的信息
    getRecords() {
      ApiGetApprovalRecords({ contractInfoId: this.id }).then(res => {
        this.recordData = res.data;
        console.log(res.data, "pppppp");
      });
    },
    //显示记录的相关信息
    judgment(item) {
      let temp = item?.reviewNodes;
      // indexOf()返回某个指定的字符串值在字符串中首次出现的位置.如果要检索的字符串值没有出现，则该方法返回 -1
      if (temp.indexOf("初审") != -1) {
        return [
          `初审人：${item.approver}`,
          `审批意见：${item.comments !== null ? item.comments : ""}`
        ];
      } else if (temp.indexOf("复核") != -1) {
        return [
          `复审人：${item.approver} `,
          `审批意见：${item.comments !== null ? item.comments : ""}`
        ];
      } else if (temp.indexOf('镇领导审核节点') != -1) {
        return [
          `复审人：${item.approver} `,
          `审批意见：${item.comments !== null ? item.comments : ""}`
        ];
      } 
      else {
        return [`提交人：${item.approver}`];
      }
    },
    //获取每个节点的显示状态
    getStatus(item) {
      console.log(item);
      if (item.remark && item.remark === "提交") {
        return "finish";
      } else if (item.remark && item.remark === "驳回") {
        return "error";
      } else if (item.reviewNodes === "发起备案登记") {
        return "finish";
      }
    }
    //获取审批记录圆点的图标
    // getIconType(state) {
    //   let icon = null;
    //   switch (state) {
    //     case 1:
    //       icon = "check-circle";
    //       break;
    //     case 2:
    //       icon = "check-circle";
    //       break;
    //     case 3:
    //       icon = "close-circle";
    //       break;
    //     case 4:
    //       icon = "circle";
    //       break;
    //   }
    //   return icon;
    // }
  }
};
</script>
<style scoped lang="less">
/deep/.ant-steps-item-subtitle {
  display: block;
  margin-left: 0px;
}
/deep/.ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-500;
  font-weight: 500;
  color: #262626;
  line-height: 20px;
}
/deep/.ant-steps-vertical
  > .ant-steps-item
  > .ant-steps-item-container
  > .ant-steps-item-tail {
  position: absolute;
  top: 30px;
  left: 16px;
  width: 1px;
  height: 100%;
  padding: 22px 0px 6px;
}
/deep/ .ant-steps-item-container {
  outline: none;
  margin-bottom: 40px;
}
/deep/.ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: rgba(0, 0, 0, 0.45);
  // width: 114px;
}
</style>

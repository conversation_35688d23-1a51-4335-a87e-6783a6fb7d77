<template>
  <div class="common-layout" id="particles-js-test">
    <div class="content_box">
      <div class="content"><slot></slot></div>
      <!-- <page-footer
        :link-list="footerLinks"
        :copyright="copyright"
      ></page-footer> -->
    </div>
  </div>
</template>

<script>
// import PageFooter from "./Footer/PageFooter";
import { mapState } from "vuex";
// import configJson from "@/assets/libs/particles.json";
export default {
  name: "CommonLayout",
  components: {
    // PageFooter
  },
  computed: {
    ...mapState("setting", ["footerLinks", "copyright"]),
  },
  mounted() {
    // window.particlesJS("particles-js", configJson, function() {
    //   console.log("callback - particles.js config loaded");
    // });
  },
};
</script>

<style scoped lang="less">
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background-color: @layout-body-background;
  // background-image: url("https://gw.alipayobjects.com/zos/rmsportal/TVYTbAXWheQpRcWDaDMu.svg");
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
  > .content_box {
    // position: absolute;
    // left: 50%;
    // top: 50%;
    // transform: translate(-50%, -349px);
  }
  .content {
    padding: 32px 0;
    flex: 1;
    @media (min-width: 768px) {
      padding: 300px 0 24px;
    }
  }
}
</style>

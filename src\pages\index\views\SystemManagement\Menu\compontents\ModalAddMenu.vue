<!--
  * <AUTHOR>
  * @dec 添加菜单
  * @time 2020-08-23
-->
<template>
  <a-modal
    :title="type === 'edit' ? '修改菜单' : '添加菜单'"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel')
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <!-- 主键ID -->
        <a-form-item v-show="false" label="主键ID">
          <a-input v-decorator="['id']" disabled />
        </a-form-item>
        <a-form-item v-show="false" label="父级ID">
          <a-input v-decorator="['parentId']" disabled />
        </a-form-item>
        <a-form-item label="菜单类型">
          <a-select
            v-show="false"
            disabled
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
            v-decorator="[
              'appType',
              {
                rules: [
                  {
                    required: true,
                    message: '请选择菜单类型！'
                  }
                ]
              }
            ]"
          />
          {{
            $store.getters["dictionaries/getNameFromTypeCode"]({
              type: "APPTYPE",
              code: this.data.appType
            })
          }}
        </a-form-item>
        <a-form-item label="菜单名称">
          <a-input
            autoComplete="off"
            :maxLength="250"
            placeholder="请输入菜单名称"
            v-decorator="[
              'name',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入菜单名称！'
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="授权码">
          <a-input
            autoComplete="off"
            :maxLength="250"
            placeholder="请输入授权码"
            v-decorator="[
              'permissionString',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入授权码！'
                  },
                  {
                    validator: checkPressmissonStr
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="链接">
          <a-input
            autoComplete="off"
            placeholder="请输入链接"
            :maxLength="250"
            v-decorator="[
              'url',
              {
                rules: [
                  {
                    message: '请输入图片格式！'
                  },
                  {
                    validator: checkPressmissonStr
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="图标">
          <a-input
            autoComplete="off"
            placeholder="请输入图标"
            :maxLength="250"
            v-decorator="[
              'icon',
              {
                rules: [
                  {
                    message: '请输入图片格式！'
                  },
                  {
                    validator: checkPressmissonStr
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="排序">
          <a-input
            autoComplete="off"
            placeholder="请输入排序号码"
            :maxLength="3"
            v-decorator="[
              'sortIndex',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入排序号码！'
                  },
                  {
                    validator: checkPositiveNum
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="菜单状态">
          <a-radio-group
            v-decorator="[
              'disabled',
              { initialValue: false, rules: [{ required: true, message: '' }] }
            ]"
          >
            <a-radio :value="false">
              启用
            </a-radio>
            <a-radio :value="true">
              停用
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick"
/**
 * @api ApiSecurityCreate 新增菜单
 * @api ApiSecurityUpdate 修改菜单
 * @api ApiSecurityFindById 根据ID查找菜单数据
 */
import {
  ApiSecurityCreate,
  ApiSecurityUpdate,
  ApiSecurityFindById
} from "@/pages//index/data/api/SystemManagement/Menu"
// 校验
import { checkPositiveNum, checkPressmissonStr } from "@/common/validate"

// 表单字段
const fields = [
  "appType",
  "disabled",
  "icon",
  "id",
  "name",
  "parentId",
  "permissionString",
  "sortIndex",
  "url"
]

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    },
    // type=edit为修改菜单
    type: {
      type: String,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    }
    return {
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v))
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.type === "edit") {
          this.getMenuData()
        }
      }
    },
    data(data) {
      data && this.form.setFieldsValue(pick(data, ["id", "appType"]))
    }
  },
  methods: {
    // 正整数校验
    checkPositiveNum,
    // 大小写英文数字符号校验
    checkPressmissonStr,
    // 获取菜单数据
    getMenuData() {
      this.loading = true
      ApiSecurityFindById({ id: this.data.id })
        .then((res) => {
          res.data && this.form.setFieldsValue(pick(res.data, fields))
        })
        .catch()
        .finally(() => {
          this.loading = false
        })
    },
    // 提交
    handleOk() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          this.loading = true
          const requestParameters = Object.assign({}, values)
          if (this.type === "edit") {
            ApiSecurityUpdate(requestParameters)
              .then((res) => {
                console.log("res", res)
                this.$emit("cancel")
                // 重置表单数据
                this.form.resetFields()
                // 刷新表格
                this.$emit("ok")
                this.$message.info("修改成功")
              })
              .catch()
              .finally(() => {
                this.loading = false
              })
          } else {
            requestParameters.parentId = requestParameters.id
            delete requestParameters.id
            ApiSecurityCreate(requestParameters)
              .then((res) => {
                console.log("res", res)
                this.$emit("cancel")
                // 重置表单数据
                this.form.resetFields()
                // 刷新表格
                this.$emit("ok")
                this.$message.info("新增菜单")
              })
              .catch()
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    }
  }
}
</script>

import VueI18nPlugin from "./i18n-extend";
import AuthorityPlugin from "./authority-plugin";
import TabsPagePlugin from "./tabs-page-plugin";
// 全局vue模板fiters过滤器
import filters from "@/common/filters";
// 全局directive指令
import directives from "@/common/directives";

const Plugins = {
  install: function(Vue) {
    Vue.use(VueI18nPlugin);
    Vue.use(AuthorityPlugin);
    Vue.use(TabsPagePlugin);
    // 注册vue模板过滤器
    Object.keys(filters).forEach(key => {
      Vue.filter(key, filters[key]);
    });
    // 注册全局指令方法
    Object.keys(directives).forEach(key => {
      Vue.directive(key, directives[key]);
    });
  }
};
export default Plugins;

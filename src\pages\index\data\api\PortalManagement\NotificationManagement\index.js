import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 通知列表
 * @param {*} params
 * @returns
 */
export function ApiNoticeList(params) {
  return api({
    url: BASE_URL + "/notice/pageByCondition",
    method: "post",
    params,
  });
}

/**
 * 通知状态更新
 * @param {*} params
 * @returns
 */
export function ApiNoticeUpdate(params) {
  return api({
    url: BASE_URL + "/notice/update",
    method: "post",
    params,
  });
}

/**
 * 通知详情
 * @param {*} params
 * @returns
 */
export function ApiNoticeDetail(params) {
  return api({
    url: BASE_URL + "/notice/selectNoticeById",
    method: "post",
    params,
  });
}

/**
 * 通知数量
 * @param {*} params
 * @returns
 */
export function ApiNoticeReadCount(params) {
  return api({
    url: BASE_URL + "/notice/getReadCount",
    method: "post",
    params,
  });
}

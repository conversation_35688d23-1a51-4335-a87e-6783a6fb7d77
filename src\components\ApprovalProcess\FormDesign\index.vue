<!--
 * @dec 设计主页
-->
<template>
  <div class="form-design-page">
    <a-row :gutter="16">
      <a-col :span="6">
        <control-tabs @addComponent="haddleAddComponent"></control-tabs>
      </a-col>
      <a-col :span="10" class="page-row-mid">
        <control-panel
          @changeActive="changeActive"
          ref="controlPanel"
        ></control-panel>
      </a-col>
      <a-col :span="8">
        <control-form
          v-if="refreshForm"
          :data="active"
          @change="handleControlForm"
        ></control-form>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import ControlTabs from "@/components/ApprovalProcess/FormDesign/components/ControlTabs";
import ControlForm from "@/components/ApprovalProcess/FormDesign/components/ControlForm";
import ControlPanel from "@/components/ApprovalProcess/FormDesign/components/ControlPanel";
// import { mapGetters } from "vuex";
export default {
  components: {
    ControlTabs,
    ControlForm,
    ControlPanel
  },
  // watch: {
  // formDesign(data) {
  //   this.setData(data);
  // }
  // },
  destroyed() {},
  // computed: {
  //   // 监听vuex数据变化
  //   ...mapGetters("approvalProcess", ["formDesign"])
  // },
  mounted() {},
  data() {
    return {
      active: {},
      refreshForm: true
    };
  },
  methods: {
    // setData(data) {
    //   this.active = data[0];
    // },
    changeActive(active) {
      this.refreshForm = false;
      this.$nextTick(() => {
        this.refreshForm = true;
        this.active = active;
      });
    },
    /**
     * 获取数据
     */
    getData() {
      return this.$refs.controlPanel.getData();
    },
    /**
     * 添加组件
     */
    haddleAddComponent(datatype, dataname) {
      this.$refs.controlPanel.handleCopyNodeAt({
        datatype,
        dataname,
        data: {
          placeholder: {},
          optionsData: {}
        }
      });
    },
    /**
     * 表单数据发生变化
     */
    handleControlForm({ data, key }) {
      this.$nextTick(() => {
        this.$refs.controlPanel.setComponentData({ data, key });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.form-design-page {
  height: calc(100vh - 65px);
  padding: 0 16px;
  .ant-row {
    display: flex;
    height: 100%;
    justify-content: space-between;
    .ant-col {
      background: @white;
      overflow: auto;
    }
    .page-row-mid {
      border-left: 1px solid #d9d9d9;
      border-right: 1px solid #d9d9d9;
    }
  }
}
</style>

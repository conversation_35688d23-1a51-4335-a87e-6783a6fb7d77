<template>
  <a-card style="width: 100%; margin-top: 20px" :bordered="false">
    <s-table
      ref="table"
      size="default"
      :columns="columns"
      :data="loadData"
      :scroll="{ x: 1000 }"
      rowKey="id"
    >
      <template slot="title"
        ><a-button type="primary" @click="fnShowEnterprise"
          >添加关联企业</a-button
        ></template
      >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * pageSize + index + 1 }}
      </span>
      <template slot="operate" slot-scope="text, record">
        <a-popconfirm
          title="确认删除?"
          ok-text="确认"
          cancel-text="取消"
          @confirm="deleteComponey(record)"
          @cancel="cancelDelete"
        >
          <a>删除</a>
        </a-popconfirm>
      </template>
    </s-table>
    <a-modal
      title="新增关联企业"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      width="1600px"
      height="800px"
      destroyOnClose
      @cancel="handleCancel"
    >
      <EnterPriseList ref="enterPriseList" selected="selected"></EnterPriseList>
    </a-modal>
  </a-card>
</template>

<script>
import {
  ApiGetRelationComponeyInfo,
  ApiAddRelationComponeyInfo,
  ApiDeleteRelationComponeyInfo,
} from "@/pages/index/data/api/InfomationQuery";
import STable from "@/components/Table";

import EnterPriseList from "./EnterPriseList";
export default {
  components: { STable, EnterPriseList },
  data() {
    this.lastFetchId = 0;
    return {
      isFlag: true,
      queryParam: {},
      chooseData: [],
      data: [],
      value: [],
      selected: [],
      fetching: false,
      formModel: {},
      visible: false,
      confirmLoading: false,
      id: "",
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 60,
          fixed: "left",
          align: "center",
        },
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center",
        },
        {
          title: "关联人",
          dataIndex: "legalPerson",
          align: "center",
        },
        {
          title: "注册资金",
          dataIndex: "registeredCapital",
          align: "center",
        },
        {
          title: "税收金额(万元)",
          dataIndex: "taxAmount",
          align: "center",
        },
        {
          title: "操作",
          dataIndex: "registeredCapital",
          scopedSlots: { customRender: "operate" },
          align: "center",
        },
      ],
    };
  },
  computed: {
    formItemLayout() {
      return {
        labelCol: { span: 8 },
        wrapperCol: { span: 14 },
      };
    },
  },
  created() {
    this.id = this.$route.query.id;
  },
  methods: {
    cancelDelete(e) {
      console.log(e);
    },
    async deleteComponey(record) {
      console.log(record);
      let resp = await ApiDeleteRelationComponeyInfo({
        companyId: this.$route.query.id,
        relatedCompanyId: record.id,
      });
      console.log(resp);
      if (resp.code == 0) {
        this.$refs.table.refresh(true);
      }
    },
    fnShowEnterprise() {
      //TODO fetch all enterprise
      this.visible = true;
    },
    async handleOk() {
      if (this.isFlag) {
        this.isFlag = false;
        let resp = await ApiAddRelationComponeyInfo({
          companyId: this.$route.query.id,
          relatedCompanyIds: this.$refs.enterPriseList.selectedRows.map(
            (item) => item.id
          ),
        });
        console.log(resp, "添加企业关系");
        if (resp.code === 0) {
          this.$message.success("添加企业关系成功");
          this.$refs.table.refresh(true);
        }
        this.confirmLoading = false;
        this.visible = false;
        setTimeout(() => {
          this.isFlag = true;
        }, 2000);
      }
    },
    handleCancel() {
      this.confirmLoading = false;
      this.visible = false;
    },
    loadData(values) {
      if (!this.id) {
        return;
      }
      this.pageNo = values.pageNo;
      this.pageSize = values.pageSize;
      const requestParameters = Object.assign(
        {},
        {
          companyId: this.id,
          currentPage: values.pageNo,
          pageSize: values.pageSize,
        },
        this.queryParam
      );
      console.log("---", this.queryParam, values);

      return ApiGetRelationComponeyInfo(requestParameters)
        .then((res) => {
          let dataObj = res.data;
          dataObj.data = res.data.records;
          return dataObj;
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped></style>

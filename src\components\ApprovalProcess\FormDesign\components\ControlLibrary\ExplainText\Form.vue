<!--
* <AUTHOR>
* @time 2020-11-24
* @dec 说明文字控件表单配置
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input
        v-model="form.inputTitle"
        placeholder="申请主题"
        maxLength="20"
        allowClear
      ></a-input>
    </a-form-model-item>
    <a-form-model-item help="说明文字最多200字" label="说明内容">
      <a-input
        type="textarea"
        v-model="form.optionsData.content"
        placeholder="请输入"
        maxLength="200"
      ></a-input>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
// import { VueEditor } from "vue2-editor";

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  components: {},
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        optionsData: {}
      }
    };
  },
  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  }
};
</script>
<style lang="less" scoped></style>

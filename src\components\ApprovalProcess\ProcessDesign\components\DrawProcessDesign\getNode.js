class FlowNode {
  constructor(data) {
    if (data.type === "1") {
      return {
        id: "root",
        groupId: null,
        type: "1",
        title: "发起人",
        content: data.content || "所有人",
        isRow: true,
        isRoot: true,
        data: data.data.roleAndUserAuth
          ? data.data
          : { ...data.data, roleAndUserAuth: { authType: 1, tags: [] } }
      };
    }
    if (data.type === "5") {
      return null;
    }
    // this.data = data;
    // if (data.nodeType === "3") {
    //   this.data = {
    //     ruleInfo: data.ruleInfo
    //   };
    //   let { formData, conditionRule, value } = data.ruleInfo.conditions[0];
    //   this.content =
    //     formData && formData.inputTitle
    //       ? (formData.inputTitle || "") +
    //         (conditionRule || "") +
    //         (value || "") +
    //         "..."
    //       : "请输入";
    // }
    return data;
    // this.id = nodeId;
    // this.isRow = isRow === "true" ? true : false;
    // this.title = nodeName;
    // this.type = nodeType;
    // this.groupId = groupId;
    // this.groupPid = parentGroupId;
  }
}
export { FlowNode };

<!--
* <AUTHOR>
* @time 2021-04-22
* @dec 机电机房线下套件
-->

<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>机电机房线下套件</h3>
      <a-form-model-item label="申请主题">
        <a-input
          placeholder="[申请单位]关于用途的机电机房线下申请单-申请人-申请日期"
        />
      </a-form-model-item>
      <a-form-model-item label="航站楼">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="1">
            T1 机房
          </a-select-option>
          <a-select-option value="2">
            T2 机房
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="机房">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="A">
            A
          </a-select-option>
          <a-select-option value="B">
            B
          </a-select-option>
          <a-select-option value="C">
            C
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="机房作业类别">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="A">
            配置变更和维护
          </a-select-option>
          <a-select-option value="B">
            资源变更
          </a-select-option>
          <a-select-option value="C">
            携带笔记本电脑
          </a-select-option>
          <a-select-option value="C">
            日常巡查
          </a-select-option>
          <a-select-option value="C">
            参观观察
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="资源申请单号">
        <a-input placeholder="请输入申请单号" />
      </a-form-model-item>
      <a-form-model-item label="进入机房日期">
        <a-date-picker @change="onChange" />
      </a-form-model-item>
      <a-form-model-item label="进入机房时间">
        <a-time-picker use12-hours @change="onChange" />
      </a-form-model-item>
    </a-form-model>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        machineroom: null
      }
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
.non-aviation-title-button {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 10px 0;
}
</style>

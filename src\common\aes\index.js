import CryptoJS from "crypto-js";
//秘钥
const CRYPTOJSKEY = "#uy7U*iwheYtrBi_";
// 加密
export function encrypt(keyString) {
  let plaintText = keyString;
  var options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  };
  var key = CryptoJS.enc.Utf8.parse(CRYPTOJSKEY);
  var encryptedData = CryptoJS.AES.encrypt(plaintText, key, options);
  var encryptedBase64Str = encryptedData.toString();
  // encryptedBase64Str = encryptedBase64Str;
  return encryptedBase64Str;
}
//解密
export function decrypt(encryptedBase64Str) {
  var vals = encryptedBase64Str.replace(/\/-/g, "+").replace(/_/g, "/");
  var options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  };
  var key = CryptoJS.enc.Utf8.parse(CRYPTOJSKEY);
  var decryptedData = CryptoJS.AES.decrypt(vals, key, options);
  var decryptedStr = CryptoJS.enc.Utf8.stringify(decryptedData);
  return decryptedStr;
}

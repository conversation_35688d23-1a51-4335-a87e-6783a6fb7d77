.out {
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}
.out .tit {
  width: 96%;
  margin: 0 auto;
  height: 55px;
  line-height: 55px;
  font-size: 18px;
  font-weight: 600;
}
.out .left {
  width: 65%;

}
.out .left .left-t {
  display: flex;
  justify-content: space-between;
  height: 120px;
}
.out .left .left-t .left-t-con {
  width: 19%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  border: 1px solid #fff;
  border-radius: 8px;
  background-color: #fff;
  box-sizing: border-box;
}
.out .left .left-t .left-t-con p {
  width: 100%;
  font-size: 16px;
}
.out .left .left-t .left-t-con .con-font {
  font-size: 26px;
}
.out .left .left-t .con-img {
  width: 46px;
  height: 48px;
}
.out .left .left-b {
  margin-top: 12px;
  height: 657px;
  padding: 7.5px;
  border: 1px solid #fff;
  border-radius: 8px;
  box-sizing: border-box;
  background-color: #fff;
}
.out .left .left-b .b-con-out {
  height: 550px;
  display: flex !important;
  flex-wrap: wrap;
  overflow-x: hidden;
  overflow-y: auto;
}
.out .left .left-b .left-b-con {
  width: 22%;
  height: 200px;
  text-align: center;
  border: 1px solid #fff;
  border-radius: 8px;
  background-color: #F4FAFF;
  box-sizing: border-box;
  position: relative;
  margin: 8px;
  margin-left: 10px;
}
.out .left .left-b .left-b-con .con-icon {
  width: 50px;
  height: 23px;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  position: absolute;
  left: 5px;
  top: 5px;
  border-radius: 4px;
}
.out .left .left-b .left-b-con .con-btn {
  font-weight: 600;
  position: absolute;
  right: 8px;
  top: 3px;
  cursor: pointer;
}
.out .left .left-b .left-b-con .con-border-y {
  width: 190px;
  height: 200px;
  box-sizing: border-box;
  border: 1px solid #FFA32E;
  border-radius: 8px;
}
.out .left .left-b .left-b-con .con-icon-y {
  color: #fff;
  background-color: #FFA32E;
  border: 1px solid #FFA32E;
}
.out .left .left-b .left-b-con .con-fontw {
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  cursor: pointer;
}
.out .right {
  width: 35%;
 

}
.out .right .right-t {
  height: 450px;
  width: 96%;
  margin: 0 auto;
  border: 1px solid #fff;
  border-radius: 8px;
  background-color: #fff;
  margin-bottom: 24px;
}
.out .right .right-t .tit-out {
  position: relative;
}
.out .right .right-t .tit-out .year {
  position: absolute;
  top: 25px;
  right: 16px;
  color: #1677ff;
  font-weight: bolder;
}
.out .right .right-t .t-con-out {
  height: 375px;
  overflow-x: hidden;
  overflow-y: auto;
}
.out .right .right-t .r-t-con {
  width: 90%;
  margin: 0 auto;
  display: flex;
  margin-bottom: 15px;
}
.out .right .right-b {
  width: 96%;
  margin: 0 auto;
  height: 315px;
  border: 1px solid #fff;
  border-radius: 8px;
  background-color: #fff;
}
.out .right .right-b .r-b-r {
  width: 95%;
  margin: 0 auto;
}
.out .right .right-b .r-b-r .b-r-con {
  width: 96%;
  margin: 0 auto ;
}
.out .right .right-b .r-b-r .b-r-con .con-main {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.out .color-b {
  color: #29A2FF;
}
.out .color-9 {
  margin-top: 10px;
  color: #a7a2a2;
}
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 5px;
  /* 滚动条宽度 */
}
/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #ffffff;
  /* 轨道背景颜色 */
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #e6e6e6;
  /* 滑块背景颜色 */
}
/* 滚动条滑块悬停状态 */
::-webkit-scrollbar-thumb:hover {
  background-color: #e6e6e6;
  /* 悬停状态下滑块背景颜色 */
}

<!--
* <AUTHOR>
* @time 2020-8-27
* @dec 系统管理 - 操作日志页面
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="操作内容">
              <a-input
                v-model="queryParam.operatorModule"
                placeholder="请输入操作内容"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="用户">
              <a-input
                v-model="queryParam.operatorName"
                placeholder="请输入用户"
              />
            </a-form-item>
          </a-col>
          <template v-if="advanced">
            <a-col :md="8" :sm="24">
              <a-form-item label="操作时间" has-feedback>
                <a-range-picker v-model="queryParam.date" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="
                  () =>
                    (this.queryParam = {
                      startDate: undefined,
                      endDate: undefined,
                      date: undefined
                    })
                "
                >重置</a-button
              >
              <a @click="toggleAdvanced" style="margin-left: 8px">
                {{ advanced ? "收起" : "展开" }}
                <a-icon :type="advanced ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 批量操作 -->
    <div class="table-operator">
      <a-dropdown>
        <a-menu slot="overlay" @click="handleDownloadClick">
          <a-menu-item key="1">
            一月内
          </a-menu-item>
          <a-menu-item key="2">
            三月内
          </a-menu-item>
          <a-menu-item key="3">
            半年内
          </a-menu-item>
          <a-menu-item key="4">
            全部
          </a-menu-item>
        </a-menu>
        <a-button type="primary"><a-icon type="download" /> 导出 </a-button>
      </a-dropdown>
    </div>
    <s-table
      ref="table"
      size="default"
      rowKey="id"
      :columns="columns"
      :data="loadData"
      :rowSelection="rowSelection"
    >
      <span slot="description" slot-scope="text">
        <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
      </span>
    </s-table>
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table";
import Ellipsis from "@/components/Ellipsis";
import { exportDownload } from "@/common/utils/index.js";

// API接口
import {
  ApiSecurityPageByCondition,
  ApiSecurityDownloadLog
} from "@/pages/index/data/api/SystemManagement/Operator";

const columns = [
  {
    title: "用户",
    dataIndex: "operatorName"
  },
  // {
  //   title: "请求ip",
  //   dataIndex: "reqIp"
  // },
  {
    title: "操作内容",
    dataIndex: "operationContent"
  },
  {
    title: "操作时间",
    dataIndex: "operatorTime"
  }
];

export default {
  name: "SystemManagementOperator",
  components: {
    STable,
    Ellipsis
  },
  data() {
    this.columns = columns;
    return {
      // create model
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        ascs: "",
        currentPage: 0,
        descs: "",
        endDate: "",
        operatorModule: "",
        operatorName: "",
        // pageSize: 10,
        startDate: ""
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        if (this.queryParam.date) {
          this.queryParam.startDate = this.queryParam.date[0].format(
            "YYYY-MM-DD"
          );
          this.queryParam.endDate = this.queryParam.date[1].format(
            "YYYY-MM-DD"
          );
        }
        // console.log("parameter", this.queryParam);
        this.queryParam.currentPage = parameter.pageNo;
        this.queryParam.current = this.queryParam.currentPage;
        const requestParameters = Object.assign({}, parameter, this.queryParam);
        return ApiSecurityPageByCondition(requestParameters)
          .then(res => {
            return res.data;
          })
          .finally(() => {
            this.loading = false;
          });
      },
    };
  },
  created() {},
  methods: {
    //查询条件收起/展开
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    //导出
    handleDownloadClick(e) {
      console.log("Download", e.key);
      let params = {
        type: e.key
      };
      ApiSecurityDownloadLog(params)
        .then(res => {
          let fileName = "操作日志.xls";
          exportDownload(res, fileName);
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

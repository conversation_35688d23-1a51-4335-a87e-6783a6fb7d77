<!--
* <AUTHOR>
* @time 2020-12-23
* @dec 作业人员套件
-->
<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>作业人员套件</h3>
      <a-form-model-item label="申请主题">
        <a-input
          placeholder="[申请单位]关于用途的作业人员申请单-申请人-申请日期"
        />
      </a-form-model-item>
      <div class="personnel-record-title-button">
        <div>作业人员列表</div>
        <div>
          <a-button type="primary" size="small">
            <a-icon type="plus" />
            添加
          </a-button>
        </div>
      </div>
      <a-table
        :scroll="{ x: 1000 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * 10 + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
    </a-form-model>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    dataIndex: "serial",
    key: "serial",
    scopedSlots: { customRender: "serial" }
  },
  {
    title: "作业人员",
    dataIndex: "userName",
    key: "userName"
  },
  {
    title: "身份证号",
    dataIndex: "idCard",
    key: "idCard"
  },
  {
    title: "操作证名称",
    dataIndex: "operationName",
    key: "operationName"
  },
  {
    title: "操作证号",
    dataIndex: "operationNumber",
    key: "operationNumber"
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "120px",
    scopedSlots: { customRender: "action" }
  }
]

const values = [
  {
    userName: "王二", //姓名
    idCard: "320902198408085030", //身份证号
    operationName: "操作证", //操作证名称
    operationNumber: "9dasd" //操作证号
  }
]
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columns,
      values,
      form: {
        operator: null
      }
    }
  },
  watch: {
    value(val) {
      console.log(`selected:`, val)
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
.personnel-record-title-button {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 10px 0;
}
</style>

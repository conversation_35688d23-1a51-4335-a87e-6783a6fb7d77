/**
 * <AUTHOR>
 * @time 2020-8-14
 * @dec 前端MOCK，可查阅mockjs官方文档
 */
import Mock from "mockjs";
const ApiSecurityPageByCondition = {
  msg: "",
  code: 0,
  data: {
    current: 1,
    pageNo: 10,
    total: 30,
    records: [
      {
        appType: "pc",
        type: "菜单",
        name: "1212",
        resourceString: "323",
        permissionString: "1"
      },
      {
        appType: "pc",
        type: "菜单",
        name: "42141",
        resourceString: "323",
        permissionString: "2"
      },
      {
        appType: "pc",
        type: "菜单",
        name: "53252",
        resourceString: "323",
        permissionString: "3"
      },
      {
        appType: "pc",
        type: "菜单",
        name: "5324234",
        resourceString: "323",
        permissionString: "4"
      },
      {
        appType: "pc",
        type: "菜单",
        name: "4324234",
        resourceString: "323",
        permissionString: "5"
      }
    ]
  }
};
Mock.mock(/\/security\/resource\/pageByCondition/, ApiSecurityPageByCondition);

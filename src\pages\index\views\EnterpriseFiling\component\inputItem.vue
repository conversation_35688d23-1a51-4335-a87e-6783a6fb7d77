<template>
  <div>
    <div class="tableContent">
      <div class="name">{{ name }}</div>
      <div class="value" :style="{ width: widthVal + 'px' }">
        {{ value }}
        <span v-if="value != 0"> {{ unit }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    widthVal: Number, //宽度
    name: String,
    value: String,
    unit: String,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.tableContent {
  display: flex;
  .name {
    width: 200px;
    min-height: 66px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f9ff;
    border: 1px solid #deeafb;
    font-size: 16px;
    color: #262626;
  }
  .value {
    min-height: 66px;
    display: flex;
    align-items: center;
    text-indent: 1em;
    border: 1px solid #deeafb;
    color: #262626;
  }
}
</style>

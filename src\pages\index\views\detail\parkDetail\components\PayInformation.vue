// 信息查询--园区查询
<template>
  <a-card style="width:100%">
    <s-table
      ref="table"
      size="default"
      :columns="columns"
      :data="loadData"
      rowKey="key"
    >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * 10 + index + 1 }}
      </span>
    </s-table>
  </a-card>
</template>

<script>
import { queryCurrencyTaxation } from "@/pages/demo/data/api/api/park"
// import parkTable from "../../../InformationQuery/ParkQuery/components/parkTable.vue";
import STable from "@/components/Table"
export default {
  components: {
    // parkTable,
    STable
  },
  data() {
    return {
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 60,
          fixed: "left"
        },
        {
          title: "企业名称",
          dataIndex: "companyName",
          align:'center'
        },
        {
          title: "是否属地企业",
          dataIndex: "districtDecentralize",
          align:'center',
          customRender: (text, row) => {
            return row.districtDecentralize ? "非属地企业" : "属地企业"
          }
        },
        {
          title: "纳税金额(万元)",
          dataIndex: "currencyYearTaxation",
          align:'center'
        }
      ],
      // 查询条件参数
      queryParam: { parkName: this.$route.query.parkName },
      // 加载数据方法 必须为 Promise 对象
      loadData: (values) => {
        if (!this.$route.query.parkName) return
        this.pageNo = values.pageNo
        const requestParameters = Object.assign(
          {
            pageNum: values.pageNo,
            pageSize: values.pageSize
          },
          this.queryParam
        )
        return queryCurrencyTaxation(requestParameters).then((res) => {
          let dataObj = res.data
          dataObj.data = res.data.companyTaxationBos
          return dataObj
        })
      },
      defaultValue: "排序",
      sortArr: [
        {
          value: "1",
          name: "按园区面积排序"
        },
        {
          value: "2",
          name: "按楼宇数排序"
        }
      ]
    }
  },
  methods: {
    getOption(e) {
      this.defaultValue = e
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    }
  }
}
</script>

<style lang="less" scoped>
.sort {
  text-align: right;
}
/deep/.ant-select {
  width: 10rem;
}
</style>

<!--
  * <AUTHOR>
  * @dec 添加资源
  * @time 2020-08-23
-->
<template>
  <a-modal
    title="配置资源"
    :width="1000"
    :visible="visible"
    id="modalAddResources"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="资源名称">
                <a-input
                  v-model="queryParam.name"
                  placeholder="请输入资源名称"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="链接地址">
                <a-input
                  v-model="queryParam.resourceString"
                  placeholder="请输入链接地址"
                >
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :md="(!advanced && 8) || 24" :sm="24">
              <span
                class="table-page-search-submitButtons"
                :style="
                  (advanced && { float: 'right', overflow: 'hidden' }) || {}
                "
              >
                <a-button type="primary" @click="$refs.table.refresh(true)"
                  >查询</a-button
                >
                <a-button
                  style="margin-left: 8px"
                  @click="() => (this.queryParam = {})"
                  >重置</a-button
                >
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <a-row :gutter="48">
        <a-col :md="16" :sm="24">
          <s-table
            ref="table"
            size="small"
            rowKey="id"
            :columns="columns"
            :data="loadData"
            :rowSelection="rowSelection"
            :showPagination="true"
          >
            <span slot="appType" slot-scope="text">
              {{
                $store.getters["dictionaries/getNameFromTypeCode"]({
                  type: "APPTYPE",
                  code: text
                })
              }}
            </span>
          </s-table>
        </a-col>
        <a-col :md="8" :sm="24">
          <template v-for="tag in selectedRows">
            <a-tag :key="tag.id" closable @close="() => handleTagsClose(tag)">
              {{ tag.name }}
            </a-tag>
          </template>
          <div v-if="selectedRows.length < 1">请选择资源</div>
        </a-col>
      </a-row>
    </a-spin>
  </a-modal>
</template>

<script>
// 表格组件
import STable from "@/components/Table";
// API接口
import {
  ApiSecurityPageByCondition,
  ApiSecuritySaveMenuWithResource,
  ApiSecurityFindResourceByMenuId
} from "@/pages/index/data/api/SystemManagement/Menu";
const columns = [
  {
    title: "应用端",
    dataIndex: "appType",
    width: 100,
    scopedSlots: { customRender: "appType" }
  },
  {
    title: "资源名称",
    dataIndex: "name",
    scopedSlots: { customRender: "name" }
  },
  {
    title: "授权码",
    width: "150px",
    dataIndex: "permissionString"
  }
];
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  components: {
    STable
  },
  data() {
    this.columns = columns;
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      loading: false,
      // 查询参数
      queryParam: {
        appType: "",
        currentPage: 1,
        enabled: "",
        name: "",
        // pageSize: "10",
        resourceString: "",
        used: true
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        this.queryParam.currentPage = parameter.pageNo;
        this.queryParam.current = this.queryParam.currentPage;
        const requestParameters = Object.assign(
          {},
          parameter,
          this.queryParam,
          {
            appType: this.data.appType
          }
        );
        return ApiSecurityPageByCondition(requestParameters).then(res => {
          this.pageDataKeys = res.data.data.map(item => item.id);
          return res.data;
        });
      },
      pageDataKeys: [],
      selectedRowKeys: [],
      selectedRows: []
    };
  },
  created() {
    console.log("custom modal created");

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {});
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      };
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.table.refresh(true);
          this.getResourceData();
        });
      }
    }
  },
  methods: {
    // 获取已有资源数据
    getResourceData() {
      this.loading = true;
      ApiSecurityFindResourceByMenuId({
        id: this.data.id,
        appType: this.data.appType
      })
        .then(res => {
          this.selectedRows = res.data;
          this.selectedRowKeys = res.data.map(item => item.id);
        })
        .catch(() => {
          this.$emit("cancel");
          this.$message.error("数据加载失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 提交
    handleOk() {
      this.loading = true;
      ApiSecuritySaveMenuWithResource({
        menuId: this.data.id,
        resourceIds: this.selectedRowKeys
      })
        .then(() => {
          this.$emit("ok");
          this.$emit("cancel");
          this.$message.info("资源添加成功");
        })
        .catch()
        .finally(() => {});
    },
    // 移除标签
    handleTagsClose(data) {
      this.selectedRowKeys = this.selectedRowKeys.filter(
        item => item !== data.id
      );
      this.selectedRows = this.selectedRows.filter(item => item.id !== data.id);
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = this.selectedRowKeys.filter(item => {
        return !this.pageDataKeys.some(a => a === item);
      });
      this.selectedRows = this.selectedRows.filter(item => {
        return !this.pageDataKeys.some(a => a === item.id);
      });
      selectedRows.forEach(item => {
        if (!this.selectedRowKeys.includes(item.id)) {
          this.selectedRowKeys.push(item.id);
          this.selectedRows.push(item);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#modalAddResources {
  /deep/ .ant-tag {
    padding: 5px 10px;
    margin-bottom: 10px;
    font-size: 14px;
    max-width: 100%;
    white-space: normal;
    word-break: break-all;
  }
}
</style>

//实业公司备案登记接口
import api from "@/common/api";
import { BASE_URL } from "Config";

//分页查询预申报表 /mapi/manager/contractPreDeclarationInfo/pageByCondition
export function getListApi(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/pageByCondition",
        method: "post",
        params,
    });
}

//新增预申报表 /manager/contractPreDeclarationInfo/addContractPreDeclaration
export function addContractInfoApi(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/addContractPreDeclaration",
        method: "post",
        params,
    });
}
// 新增预申报表新接口
export function addContractInfoApiNew(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/addContractPreDeclarationNew",
        method: "post",
        params,
    });
}

//编辑预申报表 /mapi/manager/contractPreDeclarationInfo/updateContractPreDeclaration
export function updateContractInfoApi(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/updateContractPreDeclaration",
        method: "post",
        params,
    });
}

//编辑预申报表新接口
export function updateContractInfoApiNew(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/updateContractPreDeclarationNew",
        method: "post",
        params,
    });
}

//删除预申报表 /mapi/manager/contractPreDeclarationInfo/deleteById
export function deleteContractInfoApi(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/deleteById",
        method: "post",
        params,
    });
}

//根据Id查询预申报表 /mapi/manager/contractPreDeclarationInfo/selectPreDeclarationById
export function queryContractInfoByIdApi(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/selectPreDeclarationById",
        method: "post",
        params,
    });
}

//根据Id查询预申报表新接口
export function queryContractInfoByIdApiNew(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/selectPreDeclarationByIdNew",
        method: "post",
        params,
    });
}

//模糊搜索承租方 -- /mapi/manager/ContractInfo/queryTenantry
export function queryTenantryApi(params) {
    return api({
        url: BASE_URL + "/manager/ContractInfo/queryTenantry",
        method: "post",
        params,
    });
}

//企业全名查询企业工商照面信息 -- /mapi/qiXinBao/getCompanyDetail
export function getCompanyDetailApi(params) {
    return api({
        url: BASE_URL + "/qiXinBao/getCompanyDetail",
        method: "post",
        params,
    });
}

//企业全名查询企业工商照面信息新接口
export function getCompanyDetailApiNew(params) {
    return api({
        url: BASE_URL + "/qiXinBao/getCompanyDetailAll",
        method: "post",
        params,
    });
}
//审批预申报表 -- /mapi/manager/contractPreDeclarationInfo/approvalContract
export function approvalContractApi(params) {
    return api({
        url: BASE_URL + "/manager/contractPreDeclarationInfo/approvalContract",
        method: "post",
        params,
    });
}
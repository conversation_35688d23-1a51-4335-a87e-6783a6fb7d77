/**
 * <AUTHOR>
 * @time 2020-09-10
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

//分页查询
export function ApiBusinessPageByCondition(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/business/flowProcess/pageByCondition",
    method: "post",
    middle,
    params,
  });
}
// 获取流程模板列表信息
export function ApiBusinessFindByTemplateId(params) {
  return api({
    url: BASE_URL + "/business/flowProcess/findByTemplateId",
    method: "post",
    params,
  });
}
// 修改模板信息
export function ApiBusinessUpdate(params) {
  return api({
    url: BASE_URL + "/business/flowProcess/update",
    method: "post",
    params,
  });
}
// 修改模板信息
export function ApiBusinessDeleteByFlowProcessRecordId(params) {
  return api({
    url: BASE_URL + "/business/flowProcess/deleteByFlowProcessRecordId",
    method: "post",
    params,
  });
}

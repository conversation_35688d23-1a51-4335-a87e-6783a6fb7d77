/**
 * <AUTHOR>
 * @time 2020-9-2
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 */
import api from "@/common/api";
import { BASE_URL } from "Config";
import { formatResponse, formatSubmitData } from "./utils";

//控件查询
export function ApiFormFindAllFormComponent(params) {
  return api({
    url: BASE_URL + "/form/formComponent/findAllFormComponent",
    method: "post",
    params,
  });
}
//查询分组接口
export function ApiactivitifindAllAtGroupTemplate(params) {
  return api({
    url: BASE_URL + "/activiti/atgrouptemplate/findAllAtGroupTemplate",
    method: "post",
    params,
  });
}
//新增保存接口(调用新增流程的接口)
export function Apiactiviticreate(params) {
  return api({
    url: BASE_URL + "/activiti/atregistertemplate/create",
    method: "post",
    params,
  });
}
//附件上传
export function Apicommonupload(params) {
  let formdata = new FormData();
  for (let key in params) {
    formdata.append(key, params[key]);
  }
  return api({
    url: BASE_URL + "/common/attachment/upload",
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    params: formdata,
  });
}
//发布
export function ApiActivitiDeploy(params) {
  const middle = {
    request(params) {
      // to do something
      return formatSubmitData(params);
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/activiti/deploy",
    method: "post",
    middle,
    params,
  });
}
//修改
export function ApiActivitiModifyDeploy(params) {
  const middle = {
    request(params) {
      // to do something
      return formatSubmitData(params);
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/activiti/modifyDeploy",
    method: "post",
    middle,
    params,
  });
}

//获取人员列表数据
export function ApiSecurityFindUserByOrgType(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/user/findUserByOrgType",
    method: "post",
    middle,
    params,
  });
}

//获取 角色
export function ApiSecurityFindByAppType(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/role/findByAppType",
    method: "post",
    middle,
    params,
  });
}

//获取 组织结构
export function ApiSecurityFindGroup(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/group/findGroup",
    method: "post",
    middle,
    params,
  });
}

//查询详情
export function ApiActivitiSearchById(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data = formatResponse(data.data);
      return data;
    },
  };
  return api({
    url: BASE_URL + "/activiti/queryDeploy",
    method: "post",
    middle,
    params,
  });
}

// 单选多选控件字典项配置
export function ApiSystemListParentValues(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/system/sysdict/listParentValues",
    method: "post",
    middle,
    params,
  });
}

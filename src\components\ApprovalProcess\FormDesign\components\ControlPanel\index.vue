<!--
 * <AUTHOR>
 * @time 2020-9-4
 * @dec 控件面板
-->
<template>
  <div class="control-tabs-panel" ref="panel">
    <div
      class="form-design-controlLib"
      v-for="(item, index) of componentList"
      :key="item.key"
      :datakey="item.key"
      :class="{ active: item.key === componentActive.key }"
      @click.stop="handleActive(item)"
    >
      <div class="form-design-controlLib-btns">
        <a-tooltip placement="top" title="复制">
          <a-icon type="copy" @click="handleCopyNodeAt(item, index)" />
        </a-tooltip>
        <a-divider type="vertical" />
        <a-tooltip placement="top" title="删除">
          <a-icon type="delete" @click="handleRemoveNodeAt(item, index)" />
        </a-tooltip>
      </div>
      <div class="form-design-controlLib-overlay"></div>
      <div class="form-design-controlLib-view">
        <component v-bind:is="item.datatype" :data.sync="item.data"></component>
      </div>
    </div>
  </div>
</template>
<script>
import sortablejs from "sortablejs";
// 组件库
import * as ControlLibraryDom from "../ControlLibrary/Dom";
// 组件库
import { mapGetters } from "vuex";
import * as SuiteLibraryDom from "../SuiteLibrary/Dom";
import { deepClone } from "@/common/utils";
export default {
  data() {
    return {
      // 组件列表
      componentList: [],
      // 当前聚焦
      componentActive: {}
    };
  },
  components: {
    ...ControlLibraryDom,
    ...SuiteLibraryDom
  },
  watch: {
    // form: {
    //   handler: function(form) {
    //     throttled(form, this);
    //   },
    //   deep: true
    // },
    formDesign(data) {
      this.setData(data);
    }
  },
  destroyed() {},
  computed: {
    // 监听vuex数据变化
    ...mapGetters("approvalProcess", ["formDesign"])
  },
  mounted() {
    this.initSortable();
  },
  methods: {
    /**
     * 放置数据
     */
    setData(data) {
      this.componentList = data;
    },
    /**
     * 获取数据
     */
    getData() {
      return deepClone(this.componentList);
    },
    /**
     * 初始化方法
     */
    initSortable() {
      new sortablejs(this.$refs.panel, {
        group: {
          name: "shared",
          pull: "clone"
        },
        // ghostClass: "", // drop placeholder的css类名
        // chosenClass: "", // 被选中项的css 类名
        animation: 150,
        onAdd: evt => {
          this.insertNodeAt(evt.item, evt.newIndex);
          this.removeNode(evt.item);
        },
        onStart: evt => {
          evt.to.classList.add("sortable-moving");
        },
        onEnd: evt => {
          evt.to.classList.remove("sortable-moving");
        },
        onUpdate: evt => {
          // this.removeNode(evt.item);
          // this.insertNodeAt(evt.item, evt.newIndex);
          this.updatePosition(evt.oldIndex, evt.newIndex);
          // let item = this.componentList.splice(evt.oldDraggableIndex, 1);
          // this.componentList.splice(evt.newDraggableIndex, 0, item[0]);
        }
      });
    },
    /**
     * 聚焦
     */
    handleActive(item) {
      this.componentActive = { ...item };
      this.$emit("changeActive", { ...item });
    },
    /**
     * 插入组件
     */
    insertNodeAt(node, position) {
      let datatype = node.getAttribute("datatype");
      let dataname = node.getAttribute("dataname");
      let item = {
        datatype,
        dataname,
        data: {
          placeholder: {},
          optionsData: {}
        },
        key: datatype + "_" + String(Math.random()).slice(3)
      };
      this.componentList.splice(position, 0, item);
      this.handleActive(item);
    },
    /**
     * 复制组件
     */
    handleCopyNodeAt(item, position) {
      let o = {
        ...item,
        data: {
          placeholder: {},
          optionsData: {}
        },
        key: item.datatype + "_" + String(Math.random()).slice(3)
      };
      if (!position) {
        if (this.componentActive.key) {
          position =
            this.componentList.findIndex(
              item => item.key === this.componentActive.key
            ) + 1;
        } else {
          position = this.componentList.length;
        }
      }
      this.componentList.splice(position, 0, o);

      setTimeout(() => {
        this.handleActive({ ...this.componentList[position] });
      });
    },
    /**
     * 删除组件
     */
    handleRemoveNodeAt(item, position) {
      this.componentList.splice(position, 1);
      let active = this.componentList[position - 1];
      if (!active) {
        active = this.componentList[0];
      }
      setTimeout(() => {
        this.handleActive({ ...active });
      });
    },
    /**
     * 删除DOM
     */
    removeNode(node) {
      if (node.parentElement !== null) {
        node.parentElement.removeChild(node);
      }
    },

    /**
     * 操作控件列表数据
     */
    alterList(onList) {
      const newList = this.componentList;
      onList(newList);
    },

    /**
     * 截取控件列表数据
     */
    spliceList() {
      const spliceList = list => list.splice(...arguments);
      this.alterList(spliceList);
    },

    /**
     * 调换控件列表数据位置
     */
    updatePosition(oldIndex, newIndex) {
      const updatePosition = list =>
        list.splice(newIndex, 0, list.splice(oldIndex, 1)[0]);
      this.alterList(updatePosition);
    },

    /**
     * 设置属性数据
     */
    setComponentData({ data, key }) {
      if (key) {
        let _active = this.componentList.find(item => item.key === key);
        if (_active) _active.data = data;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.control-tabs-panel {
  min-height: 100%;
  padding: 15px 15px;
  /deep/ .sortable-chosen {
    width: 100%;
    float: none;
    color: @primary-color;
    .control-flex {
      & > span {
        flex: 1;
      }
    }
    .control-flex .anticon {
      margin-left: 10px;
    }
  }
}
/**
 * 控件DOM 拖拽样式
 */
// .sortable-moving {
//   .form-design-controlLib:hover .form-design-controlLib-overlay {
//     border: 1px dashed transparent;
//   }
//   .form-design-controlLib:hover .form-design-controlLib-btns {
//     display: none;
//   }
// }
.form-design-controlLib {
  margin: 15px 0;
  overflow: hidden;
  position: relative;
  border: 1px solid transparent;
  padding: 0 15px;
  cursor: grab;
  .form-design-controlLib-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9;
  }
  &.active .form-design-controlLib-overlay {
    &:after {
      content: "";
      position: absolute;
      top: 0;
      bottom: 0;
      left: 5px;
      width: 3px;
      background: @primary-color;
    }
  }
  .form-design-controlLib-btns {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    font-size: 16px;
    line-height: 1;
    color: @text-color-secondary;
    z-index: 10;
    padding: 5px 10px;
    background: #f4f4f4;
    border-radius: 26px;
    .ant-divider {
      margin: 0 5px;
      background: #ddd;
    }
    .anticon:hover {
      cursor: pointer;
      color: @primary-color;
    }
  }
  &.active .form-design-controlLib-btns {
    display: block;
  }
  .form-design-controlLib-field {
    display: flex;
    align-items: center;
    position: relative;
    background: @white;
    font-size: 14px;
    padding: 14px 0;
    .form-design-controlLib-field-label {
      color: @text-color;
      white-space: normal;
      word-break: break-word;
      padding: 0 16px;
      color: #222;
      flex: none;
      width: 120px;
    }
    .form-design-controlLib-field-placeholde {
      text-align: right;
      color: @text-color;
      padding-right: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .anticon {
      width: 26px;
      flex: none;
      font-size: 18px;
      line-height: 1;
      color: @text-color;
    }
  }
}
</style>

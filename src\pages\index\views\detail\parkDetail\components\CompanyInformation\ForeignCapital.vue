<template>
  <a-card style="width:100%">
    <s-table
      ref="sTable"
      :data="loadData"
      :columns="columns"
      :scroll="{ x: 800 }"
      :rowKey="record => record.id"
    >
      <span slot="serialNo" slot-scope="text">
        {{ (pageNo - 1) * pageSize + parseInt(text) }}
      </span>
    </s-table>
  </a-card>
</template>

<script>
import sTable from "@/components/Table/index";
// import { queryEnterpriseInformation } from "@/pages/demo/data/api/api/park"
export default {
  components: {
    sTable
  },
  props: {
    loadData: {
      type: Array,
      default: () => []
    },
    pageNo: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      columns: [
        {
          title: "序号",
          width: 60,
          fixed: "left",
          dataIndex: "serialNo",
          scopedSlots: { customRender: "serialNo" },
          align: "center"
        },
        {
          title: "企业名称",
          width: 200,
          dataIndex: "companyName",
          fixed: "left",
          align: "center"
        },
        {
          title: "租赁楼层",
          dataIndex: "buildingSignFloor",
          align: "center"
        },
        {
          title: "企业性质",
          dataIndex: "companyNature",
          align: "center"
        },
        {
          title: "企业分布",
          dataIndex: "industryCategory",
          align: "center"
        },
        {
          title: "是否重点企业",
          dataIndex: "keyEnterprises",
          customRender: (text, row) => {
            return row.keyEnterprises ? "是" : "否";
          },
          align: "center"
        },
        {
          title: "常驻/新增",
          dataIndex: "maintenanceStatus",
          customRender: (text, row) => {
            return row.maintenanceStatus ? "常驻" : "新增";
          },
          align: "center"
        }
      ]
    };
  },
  methods: {
    // loadData(values) {
    //   if (!this.$route.query.parkName) return
    //   const params = Object.assign(
    //     { pageNum: values.pageNo, pageSize: values.pageSize },
    //     {
    //       parkName: this.$route.query.parkName,
    //       companyNature: "外资总部"
    //     }
    //   )
    //   return queryEnterpriseInformation(params).then((res) => {
    //     this.pageData = res.data.parkBuildingBos.records
    //     res.data.parkBuildingBos.data = res.data.parkBuildingBos.records
    //     return res.data.parkBuildingBos
    //   })
    // }
  },
  created() {
    this.parkName = this.$route.query.parkName;
  },
  mounted() {}
};
</script>

<style></style>

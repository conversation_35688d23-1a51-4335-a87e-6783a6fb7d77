<!--
* <AUTHOR>
* @time 2020-8-24
* @dec 系统管理 - 部门管理页面
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <!-- <a-col :md="8" :sm="24">
            <a-form-item label="部门名称">
              <a-input v-model="queryParam.name" placeholder="请输入" />
            </a-form-item>
          </a-col> -->
          <a-col :md="8" :sm="24">
            <a-form-item label="公司类型">
              <a-select
                allowClear
                v-model="queryParam.companyType"
                placeholder="请选择公司类型"
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
              >
                <a-select-option :value="0">
                  内部
                </a-select-option>
                <a-select-option :value="1">
                  外部
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="() => (this.queryParam = {})"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator" v-if="$auth('mapi:security:department:create')">
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd(record)"
        v-auth="'mapi:security:department:create'"
        >新建部门</a-button
      >
    </div>

    <s-table
      ref="table"
      size="default"
      rowKey="key"
      :columns="columns"
      :data="loadData"
    >
      <!-- <span slot="serial" slot-scope="text, record, index">
        {{ index + 1 }}
      </span> -->
      <span slot="description" slot-scope="text">
        <!-- <ellipsis :length="4" tooltip>{{ text }}</ellipsis> -->
        <span>{{ text }}</span>
      </span>
      <span slot="action" slot-scope="text, record">
        <template>
          <a
            @click="handleAddChild(record, 'addChild')"
            v-auth="'mapi:security:department:create'"
            >新建子部门</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:department:update')"
          />
          <a
            @click="handleEdit(record, 'edit')"
            v-auth="'mapi:security:department:update'"
            >修改</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:department:deleteById')"
          />
          <a-popconfirm
            title="确认删除?"
            @confirm="() => handleDelete(record)"
            v-auth="'mapi:security:department:deleteById'"
          >
            <a href="javascript:;">删除</a>
          </a-popconfirm>
        </template>
      </span>
    </s-table>
    <!-- 新增部门 -->
    <create-form
      ref="createModal"
      :visible="modal.createForm.visible"
      :data="modal.createForm.data"
      :type="modal.createForm.type"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table"
// import Ellipsis from "@/components/Ellipsis";
// API接口
import {
  ApiSecurityfindDepartmentTree,
  ApiSecuritydeleteById
} from "@/pages/index/data/api/SystemManagement/Department"
import CreateForm from "./compontents/CreateForm"
import { translateDataToTree } from "@/common/utils"
const columns = [
  // {
  //   title: "序号",
  //   width: "100px",
  //   scopedSlots: { customRender: "serial" }
  // },
  {
    title: "部门名称",
    dataIndex: "name",
    width: "200px",
    scopedSlots: { customRender: "description" }
  },
  {
    title: "部门描述",
    dataIndex: "remark",
    width: "400px",
    scopedSlots: { customRender: "status" }
  },
  {
    title: "修改时间",
    width: "200px",
    dataIndex: "updateTime"
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "200px",
    scopedSlots: { customRender: "action" }
  }
]
export default {
  name: "SystemManagementDepartment",
  components: {
    STable,
    // Ellipsis,
    CreateForm
  },
  data() {
    this.columns = columns
    return {
      // 视窗状态
      modal: {
        // 新增部门
        createForm: {
          visible: false,
          data: {},
          type: ""
        }
      },
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        companyType: undefined
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return ApiSecurityfindDepartmentTree(requestParameters).then((res) => {
          res.data = res.data.map((item) => {
            item.key = item.id
            return item
          })
          console.log(res.data)
          res.data = translateDataToTree(res.data)
          console.log(res.data)
          return res
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    onChange(checked) {
      console.log(`a-switch to ${checked}`)
    },
    // 新增部门 开启窗口
    handleAdd() {
      this.modal.createForm.visible = true
    },
    // 新增部门 关闭窗口
    handleCancel() {
      this.modal.createForm.visible = false
      this.modal.createForm.data = {}
      this.modal.createForm.type = {}
      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    // 修改数据
    handleEdit(record, type) {
      let data = Object.assign(record, type)
      this.modal.createForm.visible = true
      this.modal.createForm.data = data
      this.modal.createForm.type = type
    },
    // 新增子部门
    handleAddChild(record, type) {
      let data = Object.assign(record, type)
      this.modal.createForm.visible = true
      this.modal.createForm.data = data
      this.modal.createForm.type = type
    },
    // 删除数据
    handleDelete(record) {
      console.log("删除事件", record.id)
      this.loading = true
      const params = {
        id: record.id
      }
      ApiSecuritydeleteById(params)
        .then((res) => {
          console.log("loadData", res)
          this.$message.info("删除成功")
          this.$refs.table.refresh()
        })
        .finally(() => {
          this.loading = false
        })
    },

    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    }
  }
}
</script>

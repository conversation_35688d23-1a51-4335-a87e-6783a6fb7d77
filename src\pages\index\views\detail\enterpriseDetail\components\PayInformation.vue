// 信息查询--园区查询
<template>
  <div>
    <table
      class="table table-striped table-bordered"
      align="center"
      valign="center"
    >
      <tr>
        <td class="column">纳税人识别号</td>
        <td class="value">{{ obj.socialCreditCode }}</td>
        <td class="column">是否属地纳税</td>
        <td class="value">
          {{ obj.districtDecentralize ? "否" : "是" }}
        </td>
      </tr>
      <tr>
        <td class="column">纳税排名</td>
        <td class="value">{{ obj.rank }}</td>
        <!-- <td class="column">纳税人信用级别</td>
        <td class="value">{{ obj.leaseAddress }}</td> -->
        <td class="column">总纳税额(万元)</td>
        <td class="value">{{ obj.totalTaxation }}</td>
      </tr>
      <!-- <tr>
        <td class="column">纳税排名</td>
        <td class="value">{{ obj.leaseArea }}</td>
        <td class="column"></td>
        <td class="value"></td>
      </tr> -->
    </table>
  </div>
</template>

<script>
import { getCompanyTaxationBasic } from "@/pages/demo/data/api/api/company"
export default {
  data() {
    return {
      id: "",
      obj: {}
    }
  },
  created() {
    this.id = this.$route.query.id
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      if (!this.id) {
        return
      }
      getCompanyTaxationBasic({ id: this.id }).then((res) => {
        this.obj = res.data || {}
      })
    }
  }
}
</script>

<style lang="less" scoped>
.table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  display: table;
  width: 98%;
  max-width: 100%;
  margin: 0 auto;
}
.table td {
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  color: #333333;
  padding: 8px 12px;
}
.table-bordered {
  border: 1px solid #ddd;
}
* {
  margin: 0px;
  padding: 0px;
}
.column {
  width: 15%;
  height: 30px;
  border: 1px solid #333;
  background: #eff3fc;
}
.value {
  width: 35%;
  height: 30px;
  border: 1px solid #333;
}
</style>

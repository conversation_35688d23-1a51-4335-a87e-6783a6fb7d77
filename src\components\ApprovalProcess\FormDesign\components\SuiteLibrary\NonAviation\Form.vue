<!--
 * <AUTHOR>
 * @time 2020-9-4
 * @dec 非航资源套件表单配置 
-->
<template>
  <div>
    <!-- <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item label="标题">
        <a-input
          v-model="form.inputTitle"
          placeholder="请输入套件名称"
        ></a-input>
      </a-form-model-item>
    </a-form-model> -->
    <a-table
      :columns="columns"
      :data-source="form.optionsData.regex"
    >
      <span slot="isRegex" slot-scope="text, record">
        <template>
          <a-switch v-model="record.isRegex" />
        </template>
      </span>
    </a-table>
  </div>
</template>
<script>
const columns = [
  {
    title: "字段",
    dataIndex: "name",
    key: "name",
    scopedSlots: { customRender: "name" }
  },
  {
    title: "是否验证",
    dataIndex: "isRegex",
    key: "isRegex",
    width: 100,
    scopedSlots: { customRender: "isRegex" }
  }
];

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      columns,
      form: {
        inputTitle: "非航资源套件",
        optionsData: {
          // 参与校验逻辑
          regex: [
            {
              name: "申请资源",
              isRegex: true
            }
          ]
        }
      }
    };
  },
  watch: {
    data: {
      handler: function(data) {
        if (data.optionsData && data.optionsData.regex) this.form = data;
      }
    },
    form: {
      handler: function() {
        setTimeout(() => {
          this.$emit("update:data", this.form);
        }, 50);
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="less" scoped></style>

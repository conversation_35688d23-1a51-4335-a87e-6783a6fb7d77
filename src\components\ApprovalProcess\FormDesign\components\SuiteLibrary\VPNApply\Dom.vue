<!--
* <AUTHOR>
* @time 2021-4-7
* @dec VPN可访问主机套件
-->
<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>VPN可访问主机套件</h3>
      <a-form-model-item
        label="申请使用时间"
        :rules="[
          {
            message: '请选择申请使用时间！',
            trigger: 'change'
          }
        ]"
      >
        <template>
          <a-range-picker @change="onChangeDate" />
        </template>
      </a-form-model-item>
      <div class="personnel-record-title-button">
        <div>VPN可访问主机</div>
        <div>
          <a-button type="primary" size="small">
            添加VPN信息
          </a-button>
        </div>
      </div>
      <a-table
        :scroll="{ x: 1000 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * 10 + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
    </a-form-model>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    dataIndex: "serial",
    key: "serial",
    scopedSlots: { customRender: "serial" },
    width: "80px"
  },
  {
    title: "设备信息",
    dataIndex: "visitedName",
    key: "visitedName",
    width: "100px"
  },
  {
    title: "IP地址/网段",
    dataIndex: "ipAddress",
    key: "ipAddress",
    width: "80px"
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "120px",
    scopedSlots: { customRender: "action" }
  }
]
const values = [
  {
    visitedName: "Windows",
    ipAddress: "************"
  }
]
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columns,
      values
    }
  },
  watch: {
    value(val) {
      console.log(`selected:`, val)
    }
  }
}
</script>

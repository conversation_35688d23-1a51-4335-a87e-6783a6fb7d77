<!--
* <AUTHOR>
* @time 2020-9-4
* @dec 按钮控件
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '按钮'"
      style="margin-bottom:unset"
      ><a-button :type="data.optionsData.type">按钮</a-button>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        button: null
      }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

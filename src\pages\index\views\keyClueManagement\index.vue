<template>
  <router-view v-if="$route.meta.level == 2"> </router-view>
  <div class="enterprise" v-else>
    <div class="enterpriseFrom">
      <div class="carrierFrom">
        <a-form
          :form="form"
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
          @submit="handleSubmit"
        >
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="项目名称">
                <a-input
                  allowClear
                  v-model="queryParam.projectName"
                  placeholder="请输入项目名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="税收规模">
                <a-select
                  allowClear
                  v-model="queryParam.taxScale"
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  placeholder="请选择税收规模"
                >
                  <a-select-option value="百万级">百万级</a-select-option>
                  <a-select-option value="千万级">千万级</a-select-option>
                  <a-select-option value="亿万级">亿万级</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="招商类型">
                <a-select
                  allowClear
                  v-model="queryParam.investmentTypeCategory"
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  placeholder="请选择招商类型"
                >
                  <a-select-option value="1">新设</a-select-option>
                  <a-select-option value="2">迁入</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="营商分部">
                <a-select
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  allowClear
                  v-model="queryParam.businessDivision"
                  placeholder="请选择营商营商分部"
                >
                  <a-select-option
                    :value="item.abbr"
                    v-for="item in attractInvestmentArr"
                    :key="item.abbr"
                    >{{ item.abbr }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="审批状态">
                <!-- <a-range-picker v-model="queryParam.approvalStatus" /> -->
                <a-select
                  allowClear
                  v-model="queryParam.status"
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  placeholder="请选择审批状态"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in approvalStatusArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" align="right">
              <a-form-item :wrapper-col="{ span: 24 }">
                <a-button
                  type="primary"
                  @click="showPopup()"
                  style="margin-right: 20px"
                  v-if="roles.includes('重点线索发起人')"
                  >新增</a-button
                >
                <a-button
                  type="primary"
                  @click="search"
                  style="margin-right: 20px"
                  >查询</a-button
                >
                <a-button type="default" @click="reset">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
    <a-card style="width: 100%; margin-top: 20px">
      <div class="list-tit">
        <a-tabs
          v-model="activeKey"
          size="small"
          @change="callbackfn"
          style="width: 100%"
        >
          <a-tab-pane key="0" tab="待办事项"></a-tab-pane>
          <a-tab-pane key="1" tab="流转中事项"></a-tab-pane>
          <a-tab-pane key="2" tab="已完结事项"></a-tab-pane>
        </a-tabs>
        <a-button
          class="btnSty"
          type="primary"
          @click="batchExport()"
          :disabled="selectedRowKeys.length == 0"
          v-if="activeKey == 2"
          >批量导出{{
            selectedRowKeys.length ? `(${selectedRowKeys.length})` : ""
          }}</a-button
        >
      </div>
      <div>
        <a-table
          ref="table"
          size="default"
          :pagination="false"
          :columns="columns"
          :loading="loading"
          :data-source="loadData"
          :scroll="{ x: 1000 }"
          :row-selection="
            activeKey == 2
              ? {
                  selectedRowKeys: selectedRowKeys,
                  onChange: onChangeFn,
                }
              : undefined
          "
          row-key="id"
        >
          <span slot="serial" slot-scope="text, record, index">
            {{ (pages.pageNo - 1) * pages.pageSize + index + 1 }}
          </span>
          <template slot="projectName" slot-scope="text, record">
            <a-tooltip>
              <template slot="title">
                {{ text }}
              </template>
              <div
                @click="toDetail(record, 2)"
                style="
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  color: #1677ff;
                  cursor: pointer;
                "
              >
                {{ text }}
              </div>
            </a-tooltip>
          </template>
          <span slot="action" slot-scope="text, record">
            <a
              v-if="record.status == 0 || record.status == 4"
              href="javascript:;"
              @click="toDetail(record, 1)"
              >编辑</a
            >
            <a
              href="javascript:;"
              style="margin: 0 5px"
              v-if="record.status != 0 && record.status != 4"
              @click="toDetail(record, 2)"
              >查看</a
            >
            <a
              href="javascript:;"
              v-if="
                (record.status == 1 && roles.includes('重点线索初审人')) ||
                (record.status == 2 && roles.includes('重点线索复审人')) ||
                (record.status == 5 && roles.includes('重点线索终审人'))
              "
              @click="toDetail(record, 3)"
              >处理</a
            >
          </span>
        </a-table>
        <a-pagination
          style="display: flex; justify-content: flex-end"
          show-size-changer
          :total="pages.total"
          @change="onShowSizeChange"
        />
      </div>
    </a-card>
    <a-modal
      centered="true"
      v-model="visible"
      title="请选择税收规模"
      ok-text="确认"
      width="60%"
      @ok="hideModal"
    >
      <a-select
        v-model="taxScale"
        :getPopupContainer="
          (triggerNode) => {
            return triggerNode.parentNode || document.body;
          }
        "
        style="width: 100%"
        placeholder="请选择税收规模"
        allow-clear
        v-decorator="[
          'taxScale',
          {
            rules: [{ required: true, message: '请选择税收规模' }],
          },
        ]"
      >
        <a-select-option value="1">百万级</a-select-option>
        <a-select-option value="2">千万级</a-select-option>
        <a-select-option value="3">亿万级</a-select-option>
      </a-select>
    </a-modal>
  </div>
</template>

<script>
import moment from "moment";
import {
  pageByCondition,
  getCompanyAbbrList,
  exportBusinessClub,
} from "@/pages/index/data/api/keyAndHouse/index";
// import { ApiExportEnterSearch } from "@/pages/index/data/api/InfomationQuery/index";

export default {
  data() {
    return {
      taxScale: undefined, //税收规模
      visible: false,
      approvalStatusArr: [
        {
          value: "0",
          label: "待提交",
        },
        {
          value: "1",
          label: "待初审",
        },
        {
          value: "2",
          label: "待复审",
        },
        {
          value: "5",
          label: "待终审",
        },
        {
          value: "3",
          label: "已完成",
        },
        {
          value: "4",
          label: "已驳回",
        },
      ], //审批状态枚举

      attractInvestmentArr: [], //招商分布
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 80,
          fixed: "left",
          align: "center",
        },
        {
          title: "项目名称",
          dataIndex: "projectName",
          scopedSlots: { customRender: "projectName" },
          width: 180,
          ellipsis: true,
          align: "center",
        },
        {
          title: "招商类型",
          dataIndex: "investmentTypeCategory",
          scopedSlots: { customRender: "investmentTypeCategory" },
          width: 180,
          ellipsis: true,
          customRender: (text) => {
            return text == 1 ? "新设" : text == 2 ? "迁入" : "";
          },
          align: "center",
        },
        {
          title: "营商分部",
          dataIndex: "businessDivision",
          width: 120,
          ellipsis: true,
          align: "center",
        },
        {
          title: "税收规模",
          dataIndex: "estimatedTaxRevenue",
          width: 120,
          ellipsis: true,
          // customRender: (text) => {
          //   return text == 1 ? "百万级" : text == 2 ? "千万级" : "亿万级";
          // },
          align: "center",
        },

        {
          title: "申请时间",
          dataIndex: "createdTime",
          width: 180,
          ellipsis: true,

          align: "center",
        },

        {
          title: "审批状态",
          dataIndex: "status",
          width: 100,
          align: "center",
          scopedSlots: { customRender: "status" },
          customRender: (text) => {
            return text == 0
              ? "待提交"
              : text == 1
              ? "待初审"
              : text == 2
              ? "待复审"
              : text == 5
              ? "待终审"
              : text == 3
              ? "已完成"
              : "已驳回";
          },
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          scopedSlots: { customRender: "action" },
          width: 120,
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      queryParam: {
        projectName: undefined, //项目名称
        taxScale: undefined, //税收规模
        investmentTypeCategory: undefined, //招商类型
        businessDivision: undefined, //招商分布
        status: undefined, //审批状态
      },
      loadData: [],
      loading: false,
      pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      activeKey: "0",
      selectedRowKeys: [],
      selectedRows: [],
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
    };
  },
  watch: {
    $route(to, from) {
      // 路由变化时执行的代码
      console.log("Route changed:", to.path, from.path);
      if (to.path == "/key-clue-management") {
        this.loadDataFn();
      }
    },
  },
  mounted() {
    this.loadDataFn();
    this.getCompanyAbbr(); //获取营商分布
    console.log(this.roles);
  },
  methods: {
    moment,
    //获取营商分布
    getCompanyAbbr() {
      getCompanyAbbrList({}).then((res) => {
        console.log(res, "formData");
        if (res.code == 0) {
          // this.formData.businessDivision = res.data;
          // this.$set(this.formData, "businessDivision", res.data); //设置营商分布
          this.attractInvestmentArr = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    showPopup() {
      this.visible = true;
      this.taxScale = undefined;
    },
    hideModal() {
      if (this.taxScale == undefined) {
        this.$message.error("请选择税收规模");
        return;
      }
      this.visible = false;
      this.add();
    },
    //切换tab
    callbackfn() {
      console.log(this.activeKey, "3333");
      this.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      };
      this.loadData = [];
      this.loadDataFn();
    },
    //获取列表数据
    loadDataFn() {
      this.loading = true;
      const requestParameters = Object.assign(
        {
          currentPage: this.pages.pageNo,
          pageSize: this.pages.pageSize,
          queryType: this.activeKey,
        },
        this.queryParam
      );
      pageByCondition(requestParameters)
        .then((res) => {
          console.log(res);
          this.loading = false;
          this.loadData = res.data.records;
          this.pages.total = res.data.total;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    //复选框选中行
    onChangeFn(selectedRowKeys, selectedRows) {
      console.log(Object.prototype.toString.call(selectedRowKeys));

      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;

      console.log(this.selectedRowKeys, selectedRows, "selectedRowKeys");
    },

    //重置
    reset() {
      this.queryParam = {
        projectName: undefined, //项目名称
        taxScale: undefined, //税收规模
        investmentTypeCategory: undefined, //招商类型
        businessDivision: undefined, //招商分布
        sstatus: undefined, //审批状态
      };
      this.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      };
      this.loadData = [];
      this.loadDataFn();
    },
    //查询
    search() {
      console.log(this.queryParam);
      this.pages.pageNo = 1;
      this.pages.pageSize = 10;
      this.loadData = [];
      this.selectedRowKeys = [];
      this.selectedRows = [];
      this.loadDataFn();
      // this.$refs.table.refresh(true);
    },
    //翻页
    onShowSizeChange(current, pageSize) {
      console.log(current, pageSize);
      this.pages.pageNo = current;
      this.pages.pageSize = pageSize;
      this.loadData = [];
      this.loadDataFn();
    },
    //新增
    add() {
      console.log(this.taxScale, "taxScale");
      this.$router.push(
        `/key-clue-management/addKeyClue?taxScale=` + this.taxScale
      );
    },

    //编辑、查看、处理
    toDetail(record, n) {
      // 1:编辑、2:查看、3:处理
      if (n == 1) {
        this.$router.push(
          `/key-clue-management/editKeyClue?statu=` + n + `&id=` + record.id
        );
      } else {
        this.$router.push(
          `/key-clue-management/viewKeyClue?statu=` + n + `&id=` + record.id
        );
      }
    },
    // 批量导出
    batchExport() {
      console.log(JSON.stringify(this.selectedRowKeys), "selectedRowKeys");
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请至少选择一条数据进行导出！");
        return;
      }
      exportBusinessClub({ idList: this.selectedRowKeys });
    },
  },
};
</script>

<style lang="less" scoped>
.enterprise {
  display: flex;
  flex-wrap: wrap;

  .enterpriseFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }

  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;

    .sort {
      margin-left: auto;

      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }

  .table {
    width: 100%;
    margin-top: 10px;
  }

  .list-tit {
    position: relative;
    display: flex;
    justify-content: space-between;
    .btnSty {
      position: absolute;
      right: 0;
      top: 0;
    }

    p {
      font-size: 20px;
    }
  }
}

.ellipse {
  width: 100%;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 这里是超出几行省略 */
}
</style>

<!--
* <AUTHOR>
* @time 2020-8-24
* @dec 系统管理 -- 角色管理 --权限配置
-->
<template>
  <a-modal
    title="权限配置"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        checkedKeys = [];
        treeData = [];
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-tree
        v-if="treeData.length"
        checkable
        v-model="checkedKeys"
        @check="handleChange"
        :selectable="false"
        :defaultExpandAll="true"
        :tree-data="treeData"
      />
    </a-spin>
  </a-modal>
</template>

<script>
import {
  ApiSecurityFindPermissionTree,
  ApiSecuritysaveRoleWithPermission,
} from "@/pages/index/data/api/SystemManagement/Role";

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      checkedKeys: [],
      halfCheckedKeys: [],
      treeData: [],
    };
  },
  created() {},
  watch: {
    visible(value) {
      if (value) {
        this.treeData = [];
        this.checkedKeys = [];
        this.halfCheckedKeys = [];
        this.getData();
      }
    },
  },
  methods: {
    /**
     * change
     */
    handleChange(checkedKeys, checkedNodes) {
      this.halfCheckedKeys = checkedNodes.halfCheckedKeys;
    },
    /**
     * 获取权限配置数据
     */
    getData() {
      this.loading = true;
      ApiSecurityFindPermissionTree(this.data).then((res) => {
        this.treeData = this.formatChildren(res.data);
        this.setCahecked(this.treeData);
        this.loading = false;
      });
    },
    setCahecked(list) {
      list.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.childrenList = [];
          this.getAllChildren(item.children);
          const nowList = JSON.parse(JSON.stringify(this.childrenList));
          if (nowList.every((subItem) => subItem.checked)) {
            // 所有子级都选中
            this.checkedKeys.push(item.key);
          } else if (
            nowList.some((subItem) => subItem.checked) &&
            item.checked
          ) {
            // 部分子级选中
            this.halfCheckedKeys.push(item.key);
          }
          this.setCahecked(item.children);
        } else {
          if (item.checked) {
            this.checkedKeys.push(item.key);
          }
        }
      });
    },
    getAllChildren(list) {
      list.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.getAllChildren(item.children);
        } else {
          this.childrenList.push(item);
        }
      });
    },
    formatChildren(data) {
      data = data.map((value) => {
        value.title = value.name;
        value.key = value.parentId + "-" + value.id + "-" + value.type;
        // if (value.checked) {
        //   this.checkedKeys.push(value.key);
        // }
        // // 将半勾选状态数据存储
        // if (value.checked) {
        //   this.halfCheckedKeys.push(value.key);
        // }
        return value;
      });
      let parents = data.filter(
        (value) =>
          value.parentId == "undefined" ||
          value.parentId == null ||
          value.parentId == ""
      );
      let children = data.filter(
        (value) =>
          (value.parentId !== "undefined" && value.parentId != null) ||
          value.parentId == ""
      );
      let translator = (parents, children) => {
        parents.forEach((parent) => {
          children.forEach((current, index) => {
            if (current.parentId === parent.id) {
              let temp = JSON.parse(JSON.stringify(children));
              temp.splice(index, 1);
              translator([current], temp);
              typeof parent.children !== "undefined"
                ? parent.children.push(current)
                : (parent.children = [current]);
            }
          });
        });
      };

      translator(parents, children);

      return parents;
    },
    handleOk() {
      this.loading = true;
      // 保存角色与权限关联
      const requestParameters = {
        menuIds: [],
        resourceIds: [],
        roleId: this.data.id,
      };
      this.checkedKeys.forEach((item) => {
        if (item) {
          let data = item.split("-");
          if (data[2] === "menu") {
            requestParameters.menuIds.push(data[1]);
          } else if (data[2] === "resource") {
            requestParameters.resourceIds.push(data[1]);
          }
        }
      });
      this.halfCheckedKeys.forEach((item) => {
        if (item) {
          let data = item.split("-");
          if (data[2] === "menu") {
            requestParameters.menuIds.push(data[1]);
          } else if (data[2] === "resource") {
            requestParameters.resourceIds.push(data[1]);
          }
        }
      });
      console.log("提交参数", requestParameters);
      ApiSecuritysaveRoleWithPermission(requestParameters)
        .then((res) => {
          console.log("loadData", res);
          this.checkedKeys = [];
          this.$emit("cancel");
          // 刷新表格
          this.$emit("ok");
          this.$message.info("关联成功");
        })
        .finally(() => {
          this.loading = false;
        })
        .catch(() => {
          this.checkedKeys = [];
          this.loading = false;
        });
    },
  },
};
</script>

<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 单选控件 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input v-model="form.inputTitle" placeholder="请输入" maxLength="20" />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        v-model="form.placeholder.tipsTitleText"
        placeholder="请选择"
        maxLength="50"
      />
    </a-form-model-item>
    <a-form-model-item label="选项" class="addDelBtns">
      <a-tabs
        :default-active-key="key"
        v-model="key"
        @change="onChange"
        :animated="false"
      >
        <a-tab-pane key="1" tab="自定义添加选项配置">
          <div
            class="btnItem"
            v-for="(item, index) in optionsData"
            :key="index"
          >
            <a-input
              v-model="item.optionContent"
              :placeholder="item.placeholder"
            />
            <a-button
              shape="circle"
              icon="minus"
              size="small"
              @click="delOption(index)"
            />
            <a-button
              shape="circle"
              icon="plus"
              size="small"
              @click="addOption(index)"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="使用字典选项配置" force-render>
          <a-select
            v-model="form.placeholder.dictKey"
            @change="getDict"
            placeholder="请选择"
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
          >
            <a-select-option
              v-for="(item, index) in dictionaries"
              :key="index"
              :value="item.code"
              >{{ item.nameCn }}
            </a-select-option>
          </a-select>
          <p
            v-for="(item, index) in optionsData"
            :key="index"
            style="margin-top:20px"
          >
            {{ item.optionContent }}
          </p>
        </a-tab-pane>
      </a-tabs>
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
    <a-form-model-item label="验证" help="勾选后可作为流程条件">
      <a-switch v-model="form.isRegex" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { ApiSystemListParentValues } from "@/data/api/components/ApprovalProcess"
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      dictType: [],
      dictionaries: [],
      form: {
        inputTitle: null, //标题
        placeholder: {
          tipsTitleText: null, //标题提示
          key: "",
          dictKey: undefined
        },
        notNull: false, //是否必填
        isRegex: false //是否验证
      },
      optionsData: [
        {
          optionContent: "", //选项文字
          placeholder: "选项" //选择提示
        }
      ],
      key: "1"
    }
  },
  watch: {
    data(data) {
      this.form = data
      if (this.form.optionsData.length > 0) {
        this.optionsData = this.form.optionsData
      }
      if (this.form.placeholder.key) {
        this.key = this.form.placeholder.key
      }
      console.log("form1", this.form)
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form)
      },
      deep: true
    },
    optionsData: {
      handler: function(optionsData) {
        this.form.optionsData = optionsData
      },
      deep: true
    },
    key: {
      handler: function(key) {
        this.form.placeholder.key = key
      },
      deep: true
    }
  },
  mounted() {
    this.getApiSystemListParentValues()
  },
  methods: {
    // 获取字典项下的值
    getDict(value) {
      this.dictType = this.$store.getters["dictionaries/getType"](value)
      this.optionsData = this.dictType.map((item) => {
        return {
          // code: item.dictValue,
          optionContent: item.nameCn
        }
      })
    },
    //tab页切换
    onChange(key) {
      // console.log("key", key);
      this.form.placeholder.key = key
      this.form.placeholder.dictKey = undefined
      this.form.optionsData = []
      this.optionsData = [
        {
          optionContent: "", //选项文字
          placeholder: "选项" //选择提示
        }
      ]
    },
    // 单选多选控件字典项配置
    getApiSystemListParentValues() {
      this.loading = true
      let params = {}
      ApiSystemListParentValues(params)
        .then((res) => {
          this.dictionaries = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    //删除选项
    delOption(index) {
      if (this.optionsData.length > 1) {
        this.optionsData.splice(index, 1)
      } else {
        this.$message.error("至少保留一个选项")
        return false
      }
    },
    //添加选项
    addOption(index) {
      let item = {
        optionContent: "",
        // placeholder: "选项" + (index + 2),
        // placeholder: "选项" + (this.optionsData.length + 1),
        placeholder: "选项"
      }
      this.optionsData.splice(index + 1, 0, item)
    }
  }
}
</script>
<style lang="less">
.addDelBtns {
  .ant-form-item-control {
    margin-top: 4px;
  }
  .btnItem {
    display: flex;
    margin-top: 10px;
    button {
      margin: 4px 0 0 5px;
    }
  }
  .btnItem:first-child {
    margin-top: unset;
  }
}
</style>

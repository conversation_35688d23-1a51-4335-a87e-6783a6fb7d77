/*
 * @Author: 王正勇 <EMAIL>
 * @Date: 2025-03-31 17:00:08
 * @LastEditors: 王正勇 <EMAIL>
 * @LastEditTime: 2025-04-20 19:39:13
 * @FilePath: \zhys-admin\src\pages\index\data\api\home\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 首页展示
 */
export function getStatisticsApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/statistics",
    method: "get",
    params
  });
}

//辖区内重点企业Top10(去年)  /home/<USER>/taxationTop
export function getTaxationTop20Api(params) {
  return api({
    url: BASE_URL + "/home/<USER>/taxationTop",
    method: "get",
    params
  });
}

//辖区内重点企业Top10(去年)  /home/<USER>/taxationTopOfLastYear
export function gettaxationTopOfLastYearApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/taxationTopOfLastYear",
    method: "get",
    params
  });
}

//消息队列
export function geinfoListApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/infoList",
    method: "get",
    params
  });
}

//租赁预警  /mapi/buildings/lease/queryLeasesWaring
export function getEarlyWarningApi(params) {
  return api({
    url: BASE_URL + "/buildings/lease/queryLeasesWaring",
    method: "post",
    params
  });
}

//处理租赁预警信息  /mapi/buildings/lease/dealLeasesWaring
export function dealLeasesWaringApi(params) {
  return api({
    url: BASE_URL + "/buildings/lease/dealLeasesWaring",
    method: "post",
    params
  });
}

//税收预警 /mapi/manager/companyTaxationWarning/allList
export function companyTaxationWarningApi(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxationWarning/allList",
    method: "post",
    params
  });
}

/**
 * 外迁预警
 * @param {*} params
 * @returns
 */
export function APIQueryWarningPageByType(params) {
  return api({
    url: BASE_URL + "/manage/warning/queryWarningPageByType",
    method: "post",
    params
  });
}
/**
 * 待办事项-租赁备案申请
 * @param {*} params
 * @returns
 */

export function findContractTodoList() {
  return api({
    url: BASE_URL + "/manager/ContractInfo/findContractTodoList",
    method: "get"
  });
}
/**
 * 待办事项-查询预申报表
 * @param {*} params
 * @returns
 */
export function findContractPreTodoList(params) {
  return api({
    url:
      BASE_URL + "/manager/contractPreDeclarationInfo/findContractPreTodoList",
    method: "post",
    params
  });
}
/**
 * 待办事项-重点线索
 * @param {*} params
 * @returns
 */
export function selectBusinessClueTodoList() {
  return api({
    url: BASE_URL + "/manager/BusinessClue/selectBusinessClueTodoList",
    method: "get"
  });
}

export function selectHouseholdTodoList() {
  return api({
    url: BASE_URL + "/manager/household/selectHouseholdTodoList",
    method: "get"
  });
}

<template>
  <div class="keyClueForm">
    <a-form-model
      :model="formData"
      layout="horizontal"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="申请人" props="initiateName">
            <a-input
              disabled="true"
              placeholder="请输入"
              v-model="formData.initiateName"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="招商分部">
            <a-input
              disabled="true"
              v-model="formData.businessDivision"
              placeholder="请选择招商分部"
            >
            </a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="申请日期">
            <a-input
              disabled="true"
              placeholder="请输入申请日期"
              v-model="formData.applicationDate"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="编号">
            <a-input
              disabled="true"
              placeholder="请输入编号"
              v-model="formData.applicationNumber"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="企业名称"
            disabled="true"
            :label-col="{ span: 4 }"
          >
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入企业名称"
              v-model="formData.enterpriseName"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="企业性质">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.enterpriseNature"
              placeholder="请选择企业性质"
            >
              <a-select-option
                :value="item.value"
                :key="item.value"
                v-for="item in enterpriseNatureArr"
              >
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="法人代表">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入法人代表"
              v-model="formData.legalRepresentative"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="法人代表手机号">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入法人代表手机号"
              v-model="formData.legalPhone"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="办公经营地址">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入办公经营地址"
              v-model="formData.officeAddress"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item label="经营范围" :labelCol="{ span: 4 }">
            <a-input
              :disabled="pageType === 'view'"
              type="textarea"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入经营范围"
              v-model="formData.businessScope"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="经营期限(起)">
            <a-date-picker
              :disabled="pageType === 'view'"
              v-model="formData.startDate"
              placeholder="请选择经营期限(起)"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="经营期限(止)">
            <a-date-picker
              v-model="formData.endDate"
              :disabled="pageType === 'view'"
              placeholder="请选择经营期限(止)"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="注册资金(万)">
            <a-input-number
              :disabled="pageType === 'view'"
              placeholder="请输入注册资金(万)"
              v-model="formData.registeredCapital"
              style="width: 100%"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="币种(注册资金)">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.currency"
              placeholder="请选择币种(注册资金)"
            >
              <a-select-option value="1"> 人民币</a-select-option>
              <a-select-option value="2"> 美元 </a-select-option>
              <a-select-option value="3">欧元 </a-select-option>
              <a-select-option value="4"> 日元 </a-select-option>
              <a-select-option value="5">港币 </a-select-option>
              <a-select-option value="6">英镑 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="行业类别">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.industryCategory"
              placeholder="请选择行业类别"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in industryCategoryArr"
                :key="item.value"
                >{{ item.desc }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="统一社会信用代码">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入统一社会信用代码"
              v-model="formData.creditCode"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="行业类别备注">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入行业类别备注"
              v-model="formData.industryCategoryRemark"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="重点企业">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isKeyEnterprise"
              placeholder="请选择重点企业"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="总部经济">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isHeadquartersEconomy"
              placeholder="请选择总部经济"
              allowClear
            >
              <a-select-option value="1"> 跨国公司</a-select-option>
              <a-select-option value="2"> 民营 </a-select-option>
              <a-select-option value="3"> 研发中心 </a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-model-item label="招商企业">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.investmentCompany"
              placeholder="请选择招商企业"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col> -->
        <a-col :span="12">
          <a-form-model-item label="500强企业">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isTop500"
              placeholder="请选择500强企业"
              allowClear
            >
              <a-select-option value="1"> 世界五百强 </a-select-option>
              <a-select-option value="2"> 中国五百强 </a-select-option>
              <a-select-option value="3"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否4大产业集群">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isFourClusters"
              placeholder="请选择是否4大产业集群"
              allowClear
            >
              <a-select-option value="1"> 人工智能 </a-select-option>
              <a-select-option value="2"> 生命健康 </a-select-option>
              <a-select-option value="3"> 科技金融 </a-select-option>
              <a-select-option value="4"> 艺术传媒 </a-select-option>
              <a-select-option value="5"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否行业标杆">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isIndustryBenchmark"
              placeholder="请选择是否行业标杆"
              allowClear
            >
              <a-select-option value="1"> 央企投资 </a-select-option>
              <a-select-option value="2"> 上市公司 </a-select-option>
              <a-select-option value="3"> 优质外资</a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="科创引领">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isTechLeader"
              placeholder="请选择是否科创引领"
              allowClear
            >
              <a-select-option value="1"> 高新技术企业 </a-select-option>
              <a-select-option value="2"> 专精特新 </a-select-option>
              <a-select-option value="3"> 科技小巨人 </a-select-option>
              <a-select-option value="4"> 专精特新小巨人 </a-select-option>
              <a-select-option value="5"> 独角兽 </a-select-option>
              <a-select-option value="6"> 瞪羚企业 </a-select-option>
              <a-select-option value="7"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="工商注册地址">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入工商注册地址"
              v-model="formData.businessRegistrationAddress"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="招商类型">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.investmentTypeCategory"
              placeholder="请选择招商类型"
              allowClear
            >
              <a-select-option value="1"> 新设 </a-select-option>
              <a-select-option value="2"> 迁入 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="工商登记日期">
            <a-input
              v-model="formData.businessRegistrationDate"
              :disabled="pageType === 'view'"
              placeholder="请选择工商登记日期"
              style="width: 100%"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="原区属下放">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isOriginalDistrict"
              placeholder="请选择原区属下放"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否工商迁移">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isBusinessTransfer"
              placeholder="请选择是否工商迁移"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="迁入企业原注册地">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入迁入企业原注册地"
              v-model="formData.transferOriginalAddress"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="工商变更日期">
            <a-input
              v-model="formData.businessChangeDate"
              :disabled="pageType === 'view'"
              placeholder="请选择工商变更日期"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- <a-row :gutter="24">
       
        <a-col :span="12">
          <a-form-model-item label="引进日期(工商)">
            <a-input
              v-model="formData.introductionDate"
              :disabled="pageType === 'view'"
              placeholder="请选择引进日期(工商)"
              style="width: 100%"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row> -->
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label=" 合作引进方">
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.cooperationIntroducer"
              placeholder="请填写合作引进方"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="经办类型">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.handlingType"
              placeholder="请选择经办类型"
              allowClear
            >
              <a-select-option value="1"> 企业自办</a-select-option>
              <a-select-option value="2"> 分部代办</a-select-option>
              <a-select-option value="3"> 经发代办 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="经办人">
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.handlingPerson"
              placeholder="请填写经办人"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否占用资源">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isResourceOccupation"
              placeholder="请选择是否占用资源"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="虚拟地址申请流程">
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.virtualAddressApplicationProcess"
              placeholder="请填写虚拟地址申请流程"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="所属区域">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.belongingArea"
              showSearch
              placeholder="请选择所属区域"
              allowClear
            >
              <!-- @change="changeBelongingArea()" -->
              <a-select-option
                :value="item.value"
                v-for="item in belongingAreaArr"
                :key="item.value"
              >
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label=" 交叉功能区">
            <!-- <a-input
              :disabled="pageType === 'view'"
              v-model="formData.crossFunctionalArea"
              placeholder="请填写交叉功能区"
              allowClear
            ></a-input> -->

            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.crossFunctionalArea"
              placeholder="请选择交叉功能区"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="item in crossFunctionalAreaTypeArr"
                :key="item.value"
              >
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="入住楼宇">
            <!-- <a-select
              :disabled="pageType === 'view'"
              v-model="formData.buildingEntry"
              placeholder="请选择入住楼宇"
              showSearch
              allowClear
            >
              <a-select-option
                :value="item"
                v-for="item in buildingEntryArr"
                :key="item.id"
                >{{ item }}</a-select-option
              >
            </a-select> -->
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.buildingEntry"
              placeholder="请填写入住楼宇"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label=" 申报时间">
            <a-input
              disabled="true"
              v-model="formData.declarationDate"
              placeholder="自动带出"
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="税务外迁">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.taxRelocation"
              placeholder="请选择税务外迁"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="税务注销">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.taxCancellation"
              placeholder="请选择税务注销"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="企业星级">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.starRating"
              placeholder="请选择企业星级"
              allowClear
            >
              <a-select-option value="1"> ★</a-select-option>
              <a-select-option value="2"> ★ ★</a-select-option>
              <a-select-option value="2"> ★ ★ ★</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item label="营业执照" :label-col="{ span: 4 }">
            <FileAttachmentList
              v-if="formData.attachmentList.length > 0"
              title=""
              pagesType="view"
              :ifNeedPreviewOnline="true"
              marked="true"
              @deleteFile="(file, fileList) => deleteConFile(file, fileList)"
              :fileList="formData.attachmentList"
            >
            </FileAttachmentList>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item label="备注" :label-col="{ span: 4 }">
            <a-input
              :disabled="pageType === 'view'"
              type="textarea"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入备注"
              v-model="formData.remarks"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <div class="box1">
      <div class="box1Title">
        <span>归属单位</span>
      </div>
      <a-table
        :columns="columns"
        :data-source="householdAffiliationDTOList"
        bordered
        :pagination="false"
      >
        <!-- :row-selection="{
          selectedRowkeys: selectedRowkeys,
          onChange: onChange,
        }" -->
        <template slot="index" slot-scope="text, record, index">
          {{ (pages.pageNo - 1) * pages.pageSize + index + 1 }}
        </template>
        <template slot="department" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false" style="display: block; width: 100%">
              <a-select
                v-model="record.department"
                placeholder="请选择归属分部"
                style="width: 100%"
                showSearch
              >
                <a-select-option
                  :value="item.abbr"
                  v-for="item in businessDivisionArr"
                  :key="item.abbr"
                >
                  {{ item.abbr }}
                </a-select-option>
              </a-select>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="ratio" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.ratio"
                placeholder="请填写比例"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="type" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-select
                v-model="record.type"
                placeholder="请选择类型"
                width="100%"
              >
                <a-select-option value="1">单一</a-select-option>
                <a-select-option value="2"> 分享 </a-select-option>
              </a-select>
            </span>
            <span v-else>{{ text }} </span>
          </div>
        </template>
        <template slot="remark" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.remark"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else> {{ text }}</span>
          </div>
        </template>
      </a-table>
      <a-pagination
        v-if="householdAffiliationDTOList.length > 0"
        style="display: flex; justify-content: flex-end; margin: 20px 0"
        show-size-changer
        :total="householdAffiliationDTOList.length"
        @showSizeChange="onShowSizeChange"
      />
    </div>
    <div class="box1">
      <div class="box1Title">
        <span>企业联系人</span>
      </div>
      <a-table
        :columns="columns1"
        :pagination="false"
        :data-source="householdContactDTOList"
        bordered
      >
        <template slot="index" slot-scope="text, record, index">
          {{ (pages1.pageNo - 1) * pages1.pageSize + index + 1 }}
        </template>
        <template slot="contactName" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.contactName"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="position" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.position"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="contactPhone" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.contactPhone"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="contactEmail" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.contactEmail"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else>{{ text }}</span>
          </div>
        </template>
        <template slot="otherPhone" slot-scope="text, record">
          <div class="editable-row-operations">
            <span v-if="false">
              <a-input
                v-model="record.otherPhone"
                placeholder="请填写"
                width="100%"
              ></a-input>
            </span>
            <span v-else> {{ text }}</span>
          </div>
        </template>
      </a-table>
      <a-pagination
        v-if="householdContactDTOList.length > 0"
        style="display: flex; justify-content: flex-end; margin: 20px 0"
        show-size-changer
        :total="householdContactDTOList.length"
        @showSizeChange="onShowSizeChange1"
      />
    </div>
    <!-- //审批详情 -->
    <template v-for="item in householdApprovalHistory">
      <div class="summarizeBox" :key="item.id">
        <div class="comTitle">
          {{
            item.reviewNodes == "初审节点"
              ? "初审小结"
              : item.reviewNodes == "复审节点"
              ? "复审小结"
              : item.reviewNodes == "终审节点"
              ? "终审小结"
              : item.reviewNodes == "预审节点"
              ? "预审小结"
              : ""
          }}
        </div>
        <div style="margin-bottom: 20px; padding-left: 20px">
          <span style="margin-right: 50px"
            >{{ item.approver }}：{{ item.remark }}
          </span>
          <span style="margin-right: 50px">审批意见：{{ item.comments }} </span>
          <span>审批时间：{{ item.approvalTime }} </span>
        </div>
      </div>
    </template>
    <!-- 审批（初审、复审）意见填写 -->
    <div class="approveBox" v-if="pageState == 3">
      <div class="comTitle">
        {{
          roles.includes("户管信息初审人")
            ? "初审小结"
            : roles.includes("户管信息复审人")
            ? "复审小结"
            : roles.includes("户管信息终审人")
            ? "终审小结"
            : roles.includes("户管信息预审人")
            ? "预审小结"
            : ""
        }}
      </div>
      <a-form-model
        ref="examineApprove"
        :model="examineApproveData"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="approveRules"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="审批处理" prop="approveStatue">
              <a-select
                v-model="examineApproveData.approveStatue"
                placeholder="请选择"
              >
                <a-select-option value="0"
                  >{{ roles.includes("户管信息终审人") ? "同意" : "拟同意" }}
                </a-select-option>
                <a-select-option value="1"> 驳回 </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="审批意见">
              <a-input
                type="textarea"
                :auto-size="{ minRows: 3, maxRows: 5 }"
                placeholder="请输入审批意见"
                v-model="examineApproveData.comments"
                :maxlength="200"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      "
    >
      <a-button @click="toBack" style="margin: 0 10px">返回</a-button>
      <a-button type="primary" @click="onSubmit()" v-if="pageState == 3"
        >审批</a-button
      >
    </div>
  </div>
</template>
<script>
import FileAttachmentList from "@/components/fileView";
import {
  getCompanyAbbrList,
  getHouseholdDetail,
  // findAllParkName,
  // queryAllBuilding,
  approvalHousehold,
  updateHousehold,
} from "@/pages/index/data/api/keyAndHouse/index";
// import moment from "moment";
export default {
  components: {
    FileAttachmentList,
  },
  data() {
    return {
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
      pageState: "2", //页面状态：2-查看，3-处理
      pageType: "view", //页面类型
      status: "",
      id: "", //户管信息主id
      // view: 查看，edit: 编辑，add: 新增
      examineApproveData: {
        approveStatue: undefined, //审批处理
        comments: "", //审批意见
      },
      approveRules: {
        approveStatue: [
          { required: true, message: "请选择审批处理", trigger: "change" },
        ],
      },
      householdAffiliationDTOList: [], //户管归属单位
      householdContactDTOList: [], //户管企业联系人
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "id",
          width: 100,
          scopedSlots: { customRender: "index" },
        },
        {
          title: "归属分部",
          dataIndex: "department",
          key: "department",
          width: 150,
          scopedSlots: { customRender: "department" },
        },
        {
          title: "比例（%）",
          dataIndex: "ratio",
          key: "ratio",
          width: 150,
          scopedSlots: { customRender: "ratio" },
        },
        {
          title: "类型",
          dataIndex: "type",
          key: "type",
          width: 150,
          scopedSlots: { customRender: "type" },
        },
        {
          title: "备注",
          dataIndex: "remark",
          key: "remark",
          width: 150,
          scopedSlots: { customRender: "remark" },
        },
      ],
      columns1: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          width: 80,
          scopedSlots: { customRender: "index" },
        },
        {
          title: "联系人",
          dataIndex: "contactName",
          key: "contactName",
          width: 100,
          scopedSlots: { customRender: "contactName" },
        },
        {
          title: "职务",
          dataIndex: "position",
          key: "position",
          width: 100,
          scopedSlots: { customRender: "position" },
        },
        {
          title: "手机号码",
          dataIndex: "contactPhone",
          key: "contactPhone",
          width: 100,
          scopedSlots: { customRender: "contactPhone" },
        },
        {
          title: "邮箱地址",
          dataIndex: "contactEmail",
          key: "contactEmail",
          width: 100,
          scopedSlots: { customRender: "contactEmail" },
        },
        {
          title: "其他联系电话",
          dataIndex: "otherPhone",
          key: "otherPhone",
          width: 150,
          scopedSlots: { customRender: "otherPhone" },
        },
      ],
      selectedRowkeys: [],
      selectedRows: [],
      selected: [],
      selected1: [],
      selectedRowkeys1: [],
      pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      pages1: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      formData: {
        initiateName: "", //申请人
        businessDivision: undefined, //招商分部
        applicationDate: "", //申请日期
        applicationNumber: "", //申请编号
        enterpriseName: "", //企业名称
        enterpriseNature: undefined, //企业性质
        legalRepresentative: "", //法定代表人
        legalPhone: "", //法定代表人电话
        officeAddress: "", //办公经营地址
        businessScope: "", //经营范围
        startDate: "", //经营期限起
        endDate: "", //经营期限止
        registeredCapital: "", //注册资本
        currency: undefined, //币种
        industryCategory: undefined, //行业类别
        creditCode: "", //统一社会信用代码
        industryCategoryRemark: "", //行业类别备注
        isKeyEnterprise: undefined, //重点企业
        isHeadquartersEconomy: undefined, //总部经济
        // investmentCompany: undefined, //招商企业
        isTop500: undefined, //500强企业
        isFourClusters: undefined, //4大产业集群
        isIndustryBenchmark: undefined, //行业标杆
        isTechLeader: undefined, //科创引领
        businessRegistrationAddress: "", //工商注册地址
        investmentTypeCategory: undefined, //招商类型
        businessRegistrationDate: "", //工商登记日期
        isOriginalDistrict: undefined, //原区属下放
        isBusinessTransfer: undefined, //是否工商迁移
        transferOriginalAddress: "", //迁入企业原注册地
        businessChangeDate: "", //工商变更日期
        introductionDate: "", //引进日期(工商)
        cooperationIntroducer: "", //合作引进方
        handlingType: undefined, //经办类型
        handlingPerson: "", //经办人
        isResourceOccupation: undefined, //是否占用资源
        virtualAddressApplicationProcess: "", //虚拟地址申请流程
        belongingArea: undefined, //所属区域
        crossFunctionalArea: "", //交叉功能区
        buildingEntry: "", //入住楼宇
        declarationTime: "", //申报时间
        taxRelocation: undefined, //税务外迁
        taxCancellation: undefined, //税务注销
        starRating: undefined, //企业星级
        attachmentList: [], //营业执照
        remarks: "", //备注
      },
      businessDivisionArr: [],
      openType: "", //0保存、1提交
      businessLicenseInfoFile: false,
      enterpriseNatureArr: [], //企业性质:
      industryCategoryArr: [], //行业类别
      belongingAreaArr: [], //区域
      buildingEntryArr: "", //楼宇
      crossFunctionalAreaTypeArr: [],
      householdApprovalHistory: [], //审批详情
    };
  },
  created() {
    this.getCompanyAbbrData();
    this.getEnterpriseNature();
    this.getIndustryCategory();
    this.getFindAllParkName();
    if (
      this.$route.query.id &&
      this.$route.query.statu &&
      this.$route.query.status
    ) {
      this.id = this.$route.query.id; //获取路由参数
      this.pageState = this.$route.query.statu; //获取路由参数
      this.status = this.$route.query.status;

      this.getHouseholdDetail();
    }
  },

  methods: {
    //获取营商分布枚举
    async getCompanyAbbrData() {
      await getCompanyAbbrList().then((res) => {
        if (res.code == 0) {
          this.businessDivisionArr = res.data;
        } else {
          this.businessDivisionArr = [];
        }
      });
    },
    //企业性质
    async getEnterpriseNature() {
      this.enterpriseNatureArr = await this.$getDictByType({
        dictCode: "enterprise_nature",
      });
    },
    //行业类别
    async getIndustryCategory() {
      this.industryCategoryArr = await this.$getDictByType({
        dictCode: "industry_category",
      });
    },
    //获取所属区域
    async getFindAllParkName() {
      this.belongingAreaArr = await this.$getDictByType({
        dictCode: "belonging_area_type",
      });
      // await findAllParkName().then((res) => {
      //   if (res.code == 0) {
      //     this.belongingAreaArr = res.data;
      //   }
      // });
    },
    //获取交叉功能区
    async getcrossFunctionalAreaType() {
      this.crossFunctionalAreaTypeArr = await this.$getDictByType({
        dictCode: "cross_functional_area_type",
      });
    },
    //改变所属区域，获取不同楼宇list
    // changeBelongingArea() {
    //   this.buildingEntryArr = [];
    //   if (this.formData.belongingArea) {
    //     queryAllBuilding({ parkName: this.formData.belongingArea }).then(
    //       (res) => {
    //         if (res.code == 0) {
    //           this.buildingEntryArr = res.data.buildingNumbers;
    //         }
    //       }
    //     );
    //   }
    // },
    //获取户管信息详情
    async getHouseholdDetail() {
      await getHouseholdDetail({ id: this.id }).then((res) => {
        if (res.code == 0) {
          console.log(res.data, "formData");
          this.formData = res.data;
          this.formData.attachmentList = this.formData.attachmentList
            ? this.formData.attachmentList
            : [];
          this.householdAffiliationDTOList =
            res.data.householdAffiliationDTOList;
          this.householdContactDTOList = res.data.householdContactDTOList;
          this.householdApprovalHistory = res.data.householdApprovalHistory;
          // this.changeBelongingArea();
        }
      });
    },
    //更新信息
    updataInfo() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formData));
          params.openType = this.openType;
          params.householdAffiliationDTOList = this.householdAffiliationDTOList;
          params.householdContactDTOList = this.householdContactDTOList;
          updateHousehold(params).then((res) => {
            if (res.code == 0) {
              this.$message.success(
                `${this.openType == 0 ? "保存" : "提交"}成功`
              );
              this.$router.push(`/household-infor-managment`);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //提交审批
    onSubmit() {
      console.log("submit!", this.form);
      this.$refs.examineApprove.validate((valid) => {
        if (valid) {
          approvalHousehold({
            id: this.id,
            approveStatue: this.examineApproveData.approveStatue,
            comments: this.examineApproveData.comments,
          }).then((res) => {
            if (res.code == 0) {
              this.$message.success(`审批成功`);
              this.$router.push(`/household-infor-managment`);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //返回
    toBack() {
      let that = this;
      that.$router.push(`/household-infor-managment`);
      // this.$confirm({
      //   title: "当前内容尚未保存，请确认是否返回？",
      //   content: "",
      //   onOk() {
      //     that.$router.push(`/household-infor-managment`);
      //   },
      //   onCancel() {
      //     console.log("Cancel");
      //   },
      // });
    },
    //新增户管归属单位、户管联系人
    addTabel(type) {
      if (type == 1) {
        this.dataSource.push({
          belongingDivision: "",
          proportion: "",
          type: "",
          notes: "",
        });
      } else if (type == 2) {
        this.dataSource1.push({
          contacts: "",
          position: "",
          phoneNumber: "",
          email: "",
          otherPhone: "",
        });
      }
    },
    //选中要删除的户管单位
    onChangeFn(selectedRowKeys, selectedRows) {
      console.log(Object.prototype.toString.call(selectedRowKeys));

      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;

      console.log(this.selectedRowKeys, selectedRows, "selectedRowKeys");
    },

    // 选中户管联系人
    onChangeFn1(selectedRowKeys, selectedRows) {
      console.log(Object.prototype.toString.call(selectedRowKeys));
      this.selectedRowKeys1 = selectedRowKeys;
      this.selectedRows1 = selectedRows;
      console.log(this.selectedRowKeys, selectedRows, "selectedRowKeys");
    },
    //翻页
    onShowSizeChange(current, pageSize) {
      console.log(current, pageSize);
      this.pages.pageNo = current;
      this.pages.pageSize = pageSize;
      this.pages.total = this.dataSource.length;
    },
    onShowSizeChange1(current, pageSize) {
      console.log(current, pageSize);
      this.pages1.pageNo = current;
      this.pages1.pageSize = pageSize;
      this.pages1.total = this.dataSource1.length;
    },
  },
};
</script>
<style lang="less" scoped>
.keyClueForm {
  width: 100%;
  padding: 20px;
  background: #fff;
  .comTitle {
    position: relative;
    width: 100%;
    margin: 10px 0;
    padding: 0 10px;
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    &::before {
      content: "";
      width: 4px;
      height: 20px;
      background: #1777ff;
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translateY(-10px);
    }
  }
  .approveBox {
  }
  .box1 {
    width: 100%;
    background: #fff;
    padding: 10px;
    .box1Title {
      height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      font-size: 14px;
      font-weight: bold;
    }
  }
  .boxWidth {
    /deep/.ant-form-item-control-wrapper {
      width: 100%;
    }
  }
  .label-box {
    width: 80%; // 建议80%，太长就会超出内容
    display: inline-block;
    height: auto !important;
    white-space: break-spaces;
    line-height: 18px;
    text-align: right;
    vertical-align: bottom; // 这是为了让整体的字往下移动一点
  }
  .ant-form-item {
    margin-bottom: 16px;
  }
  .ant-form-item-label {
    text-align: left;
  }
  .ant-form-item-control {
    margin-left: 0px;
  }
}
</style>

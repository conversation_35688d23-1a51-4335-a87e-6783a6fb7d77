<template>
  <div class="uploadModal">
    <a-modal
      :title="title"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      :width="1000"
    >
      <slot></slot>
      <Importer
        :useCustom="true"
        :businessId="businessId"
        :accept="accept"
        @handleFileCallback="file => handleFileCallback(file, record)"
      >
        <template #import>
          <a-button type="primary" :disabled="disabled == true">
            <a-icon type="upload" /> {{ title }}</a-button
          >
        </template>
      </Importer>
      <FileAttachmentList
        :fileList="fileList"
        :ifNeedPreviewOnline="true"
        @deleteFile="fnDeleteFile"
      ></FileAttachmentList>
    </a-modal>
  </div>
</template>
<script>
import Importer from "@/components/Importer";
import FileAttachmentList from "@/components/FileAttachmentList";
export default {
  components: {
    Importer,
    FileAttachmentList
  },
  props: {
    accept: {
      type: String,
      default: ".doc,.docx,.pdf,.xls,.xlsx,.png,.jpg,.jpeg",
    }, //接受上传的文件类型
    title: {
      type: String,
      default: "上传合同"
    },
    record: {
      type: Object,
      default: () => ({})
    },
    businessId: {
      type: String,
      default: "review_completed_contract"
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      fileList: []
    };
  },
  methods: {
    fnDeleteFile() {
      this.fileList = [];
    },
    handleFileCallback(file) {
      console.log(file);
      let fileList = [file];
      this.fileList = fileList;
      this.$emit("handleFileCallback", fileList);
    },
    handleOk() {
      //将上传的文件传递给父组件
      this.$emit("ok", this.fileList)
      this.fileList = []
    },
    handleCancel() {
      this.fileList = [];
      this.$emit("cancel");
    }
  }
};
</script>

<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 上传控件表单
-->
<template>
  <a-form-model
    :model="form"
    :label-col="labelCol"
    :rules="rules"
    :wrapper-col="wrapperCol"
  >
    <a-form-model-item label="标题">
      <a-input
        v-model="form.inputTitle"
        placeholder="最多20字"
        maxLength="20"
      />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        maxLength="20"
        v-model="form.placeholder.placeholderText"
        placeholder="最多20字"
      />
    </a-form-model-item>
    <a-form-model-item label="每个文件大小" prop="fileSize">
      <a-input
        v-model="form.optionsData.fileSize"
        suffix="MB"
        placeholder="请输入大小"
        oninput="value=value.replace(/[^\d]/g,'')"
      >
      </a-input>
    </a-form-model-item>
    <a-form-model-item label="文件个数">
      <a-input-number v-model="form.optionsData.fileNum" min="1" max="100" />
    </a-form-model-item>
    <a-form-model-item label="支持文件类型">
      <a-checkbox
        :indeterminate="form.optionsData.indeterminate"
        v-model="form.optionsData.checkAll"
        @change="handCheckAllChange"
        :checked="form.optionsData.checkAll"
      >
        全选
      </a-checkbox>
      <a-checkbox-group
        v-model="form.optionsData.checkedList"
        :options="plainOptions"
        @change="handCheckList"
      />
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { checkPositiveNum } from "@/common/validate";

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        optionsData: {},
        placeholder: {}
      },
      plainOptions: [
        "PDF",
        "Word",
        "Excel",
        "TXT",
        "图片",
        "压缩文件",
        "音频",
        "视频"
      ],
      checkAll: false,
      rules: {
        fileSize: [
          {
            validator: checkPositiveNum,
            trigger: "blur"
          }
        ]
      }
    };
  },

  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  },
  methods: {
    // handSelectType(value) {
    //   // this.form.optionsData.fileSize = value;
    //   this.$set(this.form.optionsData, "fileSize", value);
    // },
    checkPositiveNum,
    handCheckList(checkedList) {
      this.form.optionsData.indeterminate =
        !!checkedList.length && checkedList.length < this.plainOptions.length;
      this.form.optionsData.checkAll =
        checkedList.length === this.plainOptions.length;
    },
    handCheckAllChange(e) {
      Object.assign(this.form.optionsData, {
        checkedList: e.target.checked ? this.plainOptions : [],
        indeterminate: !this.form.optionsData.indeterminate,
        checkAll: e.target.checked
      });
    }
  }
};
</script>

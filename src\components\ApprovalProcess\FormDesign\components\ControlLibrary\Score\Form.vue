<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 评分Dom
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input v-model="form.inputTitle" placeholder="申请主题" />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        maxLength="20"
        v-model="form.placeholder.placeholderText"
        placeholder="最多20字"
      />
    </a-form-model-item>
    <!-- <a-form-model-item label="满分">
      <a-input v-model="form.optionsData.score" placeholder="请输入分数" />
    </a-form-model-item>
    <a-form-model-item label="几颗星">
      <a-input-number
        id="inputNumber"
        v-model="form.starNumber"
        :min="1"
        :max="5"
        @change="handScore"
      />
    </a-form-model-item> -->
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
    <a-form-model-item label="显示分数">
      <a-switch v-model="form.optionsData.showScore" />
    </a-form-model-item>
    <a-form-model-item label="允许半选">
      <a-switch v-model="form.optionsData.needOption" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        placeholder: {},
        optionsData: {}
      }
    };
  },

  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  }
};
</script>

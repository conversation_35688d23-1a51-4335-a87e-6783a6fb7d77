import Mock from "mockjs";

//查询接口
const query = {
  code: 0,
  data: {
    current: 1,
    pages: 1,
    records: [
      {
        birthdayTime: "2020-10-19",
        businessScope:
          "计算机系统服务；数据处理服务；互联网数据服务；软件开发；科技中介服务；新材料技术研发",
        companyAddress:
          "上海市徐汇区漕河泾新兴技术开发区桂平路315号1幢3楼301室",
        companyDistribute: "信息传输、软件科技与信息技术服务",
        companyName: "星臻科技（上海）有限公司",
        companyNature: "高新技术,其他",
        createBy: "xiaoming",
        createTime: "2023-09-20 14:30:32",
        dependencyCompany: 1,
        id: 1,
        investmentType: "高新技术企业",
        isDelete: 0,
        keypointCompany: 0,
        legalPerson: "陈曲",
        manageStatus: "开业",
        parkName: "桂林科技园",
        registerCapital: 1000,
        taxAmount: 5744.98,
        updateBy: "xiaoming",
        updateTime: "2023-09-20 14:30:32"
      }
    ],
    searchCount: true,
    size: 10,
    total: 4
  },
  msg: null
};
Mock.mock("/Mock/EnterpriseAnalysisReport/query", query);

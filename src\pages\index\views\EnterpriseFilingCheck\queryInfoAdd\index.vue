<!-- 企业建档表 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <a-form-model
      class="ant-advanced-search-form"
      ref="ruleForm"
      :model="decisionParmas"
      :rules="rules"
      :label-col="formItemLayout.labelCol"
      :wrapper-col="formItemLayout.wrapperCol"
    >
      <div class="lessorInfo">
        <div
          class="lessorInfo-tit"
          style="
            height: 100px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #ece5e5;
          "
        >
          <div class="title">申请人</div>
          <div class="title-font">{{ decisionParmas.initiateName }}</div>
        </div>
      </div>
      <div class="detail">
        <!-- 企业基础信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业基础信息</div>
            <div
              style="
                width: 1360px;
                margin-left: 20px;
                border: 1px solid #deeafb;
              "
            >
              <div style="display: flex">
                <InputItem
                  widthVal="480"
                  name="企业名称"
                  :value="decisionParmas.companyName"
                ></InputItem>
                <InputItem
                  widthVal="480"
                  name="企业性质"
                  :value="decisionParmas.econKind"
                ></InputItem>
              </div>
              <div style="display: flex">
                <InputItem
                  widthVal="480"
                  name="注册资金"
                  :value="decisionParmas.registCapi"
                ></InputItem>
                <InputItem
                  widthVal="480"
                  name="创立时间"
                  :value="decisionParmas.startDate"
                ></InputItem>
              </div>
              <InputItem
                widthVal="1160"
                name="工商登记机关"
                :value="decisionParmas.belongOrg"
              ></InputItem>
              <InputItem
                widthVal="1160"
                name="注册地址"
                :value="decisionParmas.address"
              ></InputItem>
              <InputItem
                widthVal="1160"
                name="办公地址"
                :value="decisionParmas.officeAddress"
              ></InputItem>
            </div>
          </div>
        </div>
        <!-- 企业资质评估信息 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业资质评估信息</div>
            <div
              style="
                width: 1360px;
                margin-left: 20px;
                border: 1px solid #deeafb;
              "
            >
              <div style="display: flex">
                <div class="name">参保人数</div>
                <div
                  style="
                    padding-left: 15px;
                    width: 1160px;
                    display: flex;
                    flex-wrap: wrap;
                    border: 1px solid #deeafb;
                  "
                >
                  <div
                    class="employeesInfoOut"
                    v-for="(item, index) in oldinsuranceNum"
                    :key="index"
                  >
                    <div
                      class="item-out"
                      v-if="oldinsuranceNum[index][0]"
                      style="width: 160px"
                    >
                      <div class="item">
                        {{ oldinsuranceNum[index][0] }}
                      </div>
                      <div style="padding-top: 2px; color: #262626">
                        {{ oldinsuranceNum[index][1] }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex">
                <div class="name">主要人员及职位</div>
                <div
                  style="
                    padding-left: 15px;
                    width: 1160px;
                    display: flex;
                    flex-wrap: wrap;
                    border: 1px solid #deeafb;
                  "
                >
                  <div
                    class="employeesInfoOut"
                    v-for="(item, index) in oldemployeesInfo"
                    :key="index"
                  >
                    <div class="item-out" v-if="oldemployeesInfo[index][0]">
                      <div class="item">
                        {{ oldemployeesInfo[index][0] }}
                      </div>
                      <div style="padding-top: 2px">
                        {{ oldemployeesInfo[index][1] }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex">
                <div class="name">主要股东及持股比例</div>
                <div
                  style="
                    padding-left: 15px;
                    width: 1160px;
                    display: flex;
                    flex-wrap: wrap;
                    border: 1px solid #deeafb;
                  "
                >
                  <div
                    class="partnersInfoOut"
                    v-for="(item, index) in oldpartnersInfo"
                    :key="index"
                  >
                    <div
                      v-if="oldpartnersInfo[index][0]"
                      style="display: flex; height: 30px; padding-top: 8px"
                    >
                      <div class="item">
                        {{ oldpartnersInfo[index][0] }}
                      </div>
                      <div class="item1">
                        {{ (oldpartnersInfo[index][1] * 100).toFixed(2) }}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex">
                <div class="name">主要关联公司</div>
                <div style="width: 1160px">
                  <div style="display: flex; min-height: 40px">
                    <div class="componyType">分公司及子公司</div>
                    <div
                      style="
                        width: 100%;
                        display: flex;
                        flex-wrap: wrap;
                        border: 1px solid #deeafb;
                        padding-left: 15px;
                      "
                    >
                      <div
                        class="partnersInfoOut"
                        v-for="(item, index) in oldbranchesInfo"
                        :key="index"
                      >
                        <div
                          v-if="oldbranchesInfo[index][0]"
                          style="display: flex; height: 30px; padding-top: 8px"
                        >
                          <div class="item">
                            {{ oldbranchesInfo[index][0] }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="display: flex; min-height: 40px">
                    <div class="componyType">对外投资公司</div>
                    <div
                      style="
                        width: 100%;
                        display: flex;
                        flex-wrap: wrap;
                        border: 1px solid #deeafb;
                        padding-left: 15px;
                      "
                    >
                      <div
                        class="partnersInfoOut"
                        v-for="(item, index) in oldinvestsInfo"
                        :key="index"
                      >
                        <div
                          v-if="oldinvestsInfo[index][0]"
                          style="display: flex; height: 30px; padding-top: 8px"
                        >
                          <div class="item">
                            {{ oldinvestsInfo[index][0] }}
                          </div>
                          <div class="item1">
                            {{ oldinvestsInfo[index][1] }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 专利情况 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">专利情况</div>
            <div
              style="
                width: 1360px;
                margin-left: 20px;
                border: 1px solid #deeafb;
              "
            >
              <div style="display: flex">
                <InputItem
                  widthVal="250"
                  name="发明专利申请"
                  :value="decisionParmas.patentNum"
                  unit="个"
                ></InputItem>
                <InputItem
                  widthVal="250"
                  name="商标信息"
                  :value="decisionParmas.trademarkNum"
                  unit="个"
                ></InputItem>
                <InputItem
                  widthVal="260"
                  name="著作权（不含软著）"
                  :value="decisionParmas.copyrightNum"
                  unit="个"
                ></InputItem>
              </div>
              <div style="display: flex">
                <InputItem
                  widthVal="250"
                  name="资质认证"
                  :value="decisionParmas.certNum"
                  unit="个"
                ></InputItem>
                <InputItem
                  widthVal="710"
                  name="软件著作权"
                  :value="decisionParmas.softwareNum"
                  unit="个"
                ></InputItem>
              </div>
            </div>
          </div>
        </div>
        <!-- 资质情况 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">资质情况</div>
            <div
              style="
                width: 1360px;
                margin-left: 20px;
                border: 1px solid #deeafb;
              "
            >
              <div style="display: flex">
                <div
                  class="aptitudeStyle"
                  v-for="item in aptitudeInfos"
                  :key="item.value"
                >
                  <p>{{ item.label }}</p>
                </div>
              </div>

              <div style="display: flex">
                <div class="name">主营业务</div>
                <div
                  style="
                    padding-left: 15px;
                    width: 1160px;
                    display: flex;
                    flex-wrap: wrap;
                    border: 1px solid #deeafb;
                  "
                >
                  <div style="width: 100%; display: flex; flex-wrap: wrap">
                    <div
                      class="partnersInfoOut"
                      v-for="(item, index) in oldentPortraitInfo"
                      :key="index"
                    >
                      <div
                        v-if="oldentPortraitInfo[index][0]"
                        style="display: flex"
                      >
                        <div class="item">
                          {{ item }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <InputItem
                widthVal="1160"
                name="品牌产品"
                :value="decisionParmas.projectInfo"
              ></InputItem>
              <div style="display: flex">
                <div class="name">获奖及成就</div>
                <div
                  style="
                    padding-left: 15px;
                    width: 1160px;
                    display: flex;
                    flex-wrap: wrap;
                    border: 1px solid #deeafb;
                  "
                >
                  <div style="width: 100%; display: flex; flex-wrap: wrap">
                    <div
                      class="partnersInfoOut"
                      v-for="(item, index) in oldawardsAchievements"
                      :key="index"
                    >
                      <div
                        v-if="oldawardsAchievements[index][0]"
                        style="display: flex"
                      >
                        <div class="item">
                          {{ item }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex">
                <div class="name">融资情况</div>
                <div
                  style="
                    padding-left: 15px;
                    width: 1160px;
                    display: flex;
                    flex-wrap: wrap;
                    border: 1px solid #deeafb;
                  "
                >
                  <div style="width: 100%; display: flex; flex-wrap: wrap">
                    <div
                      class="partnersInfoOut"
                      v-for="(item, index) in oldfinanceInfo"
                      :key="index"
                    >
                      <div
                        v-if="oldfinanceInfo[index][0]"
                        style="display: flex"
                      >
                        <div class="item">
                          {{ item }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <InputItem
                widthVal="1160"
                name="上市情况"
                :value="decisionParmas.entProfileInfo"
              ></InputItem>
            </div>
          </div>
        </div>
      </div>
      <div class="detail">
        <!-- 企业评级 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">企业评级</div>
            <div style="display: flex">
              <div class="echart-item">
                <EnterpriseRating></EnterpriseRating>
              </div>
              <div class="echart-item">
                <EnterpriseLevel></EnterpriseLevel>
              </div>
            </div>
          </div>
        </div>
        <!-- 客户评级 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">客户评级</div>
            <div class="customer">
              <div class="customer-box">
                <div class="customer-item">
                  <img
                    :src="
                      decisionParmas.customerRating == 1
                        ? require(`./../../../../../assets/image/common/customer-start1.png`)
                        : require(`./../../../../../assets/image/common/customer-start2.png`)
                    "
                    alt=""
                  />
                  <span>重要客户</span>
                </div>
                <div class="customer-item">
                  <img
                    :src="
                      decisionParmas.customerRating == 2
                        ? require(`./../../../../../assets/image/common/customer-start1.png`)
                        : require(`./../../../../../assets/image/common/customer-start2.png`)
                    "
                    alt=""
                  /><span>非常重要</span>
                </div>
                <div class="customer-item">
                  <img
                    :src="
                      decisionParmas.customerRating == 3
                        ? require(`./../../../../../assets/image/common/customer-start1.png`)
                        : require(`./../../../../../assets/image/common/customer-start2.png`)
                    "
                    alt=""
                  /><span>普通客户</span>
                </div>
                <div class="customer-item">
                  <img
                    :src="
                      decisionParmas.customerRating == 4
                        ? require(`./../../../../../assets/image/common/customer-start1.png`)
                        : require(`./../../../../../assets/image/common/customer-start2.png`)
                    "
                    alt=""
                  /><span>伙伴客户</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail">
        <!-- 审批意见 -->
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">审批意见</div>
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-model-item prop="approvalResult" label="审批结果">
                  <a-radio-group
                    :disabled="isShow"
                    v-model="decisionParmas.approvalResult"
                  >
                    <a-radio :value="'0'"> 通过 </a-radio>
                    <a-radio :value="'1'"> 不通过 </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="16">
                <a-form-model-item prop="approvalOpinion" label="审批意见">
                  <a-textarea
                    :disabled="isShow"
                    v-model="decisionParmas.approvalOpinion"
                    placeholder="请输入审批意见"
                    :auto-size="{ minRows: 3, maxRows: 6 }"
                    maxLength="50"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </a-form-model>
    <div style="text-align: center">
      <div style="text-align: center">
        <a-button
          v-if="this.$route.query.statu != 2"
          type="danger"
          @click="comfirBtn(0)"
          class="back"
          >驳回</a-button
        >
        <a-button class="back" @click="toBack">返回</a-button>
        <a-button
          v-if="this.$route.query.statu != 2"
          type="primary"
          @click="comfirBtn(1)"
          class="back"
          >保存</a-button
        >
        <a-button
          v-if="this.$route.query.statu != 2"
          type="primary"
          @click="comfirBtn(2)"
          class="back"
          >提交审核</a-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { EnterpriseRating, EnterpriseLevel } from "./../components";
import InputItem from "../../EnterpriseFiling/component/inputItem.vue";
import {
  queryContractInfoByIdApi,
  approvalContractApi,
} from "@/pages/index/data/api/EnterpriseFiling";
export default {
  components: {
    EnterpriseRating,
    EnterpriseLevel,
    InputItem,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      plainOptions: [
        { label: "国家科技型中小企业", value: "1" },
        { label: "国家高新技术企业", value: "2" },
        { label: "市专精特新中小企业", value: "3" },
        { label: "国家专精特新小巨人企业", value: "4" },
        { label: "区企业技术中心", value: "5" },
        { label: "市企业技术中心", value: "6" },
      ],
      rules: {
        companyName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
      },
      type: null, //1：新增，2：编辑，3：查看
      isFlag: true,
      isShow: false,
      decisionParmas: {
        companyName: "", //企业名称
        econKind: "", //企业性质
        registCapi: "", //注册资金
        startDate: "", //创立时间
        belongOrg: "", //工商登记机关
        address: "", //注册地址
        officeAddress: "", //办公地址
        insuranceNum: "", //参保人数
        employeesInfo: "", //主要人员及职位
        partnersInfo: "", //主要股东及持股比例
        branchesInfo: "", //分公司及子公司
        investsInfo: "", //对外投资公司
        patentNum: 0, //发明专利申请
        trademarkNum: 0, //有效注册商标
        copyrightNum: 0, //著作权（不含软著）
        certNum: 0, //资质认证
        softwareNum: 0, //软件著作权
        aptitudeInfos: [], //资质情况
        aptitudeOther: ["1"], //资质情况其他
        aptitudeOtherDescription: "", //其他资质描述
        entPortraitInfo: "", //主营业务
        projectInfo: "", //品牌产品
        awardsAchievements: "", //获奖及成就
        financeInfo: "", //融资情况
        entProfileInfo: "", //上市情况
        customerRating: "", //客户评级
      },
    };
  },
  created() {
    this.type = this.$route.query.statu;
    if (this.type == 2) {
      this.isShow = true;
      this.getContractId();
    } else {
      this.getContractId();
      this.isShow = false;
    }
  },
  methods: {
    handleInput1(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.decisionParmas.rentalUnitPrice = val;
    },
    getContractId() {
      //根据id查询所有数据
      if (this.$route.query.id) {
        let parmas = {
          id: this.$route.query.id,
        };
        queryContractInfoByIdApi(parmas).then((res) => {
          this.decisionParmas = res.data;

          let insuranceNumArr = this.decisionParmas.insuranceNum?.split(";");
          this.oldinsuranceNum = [];
          insuranceNumArr.forEach((e) => {
            this.oldinsuranceNum.push(e.split(":"));
          });

          let employeesInfoArr = this.decisionParmas.employeesInfo?.split(";");
          this.oldemployeesInfo = [];
          employeesInfoArr.forEach((e) => {
            this.oldemployeesInfo.push(e.split(":"));
          });

          let partnersInfoArr = this.decisionParmas.partnersInfo?.split(";");
          this.oldpartnersInfo = [];
          partnersInfoArr.forEach((e) => {
            this.oldpartnersInfo.push(e.split(":"));
          });

          let investsInfoArr = this.decisionParmas.investsInfo?.split(";");
          this.oldinvestsInfo = [];
          investsInfoArr.forEach((e) => {
            this.oldinvestsInfo.push(e.split(":"));
          });

          let branchesInfoArr = this.decisionParmas.branchesInfo?.split(";");
          this.oldbranchesInfo = [];
          branchesInfoArr.forEach((e) => {
            this.oldbranchesInfo.push(e.split(":"));
          });
          //investsInfo  对外投资公司   branchesInfo 分公司及子公司

          //融资情况
          let financeInfoArr = this.decisionParmas.financeInfo?.split(";");
          this.oldfinanceInfo = financeInfoArr;
          //主营业务
          let entPortraitInfoArr =
            this.decisionParmas.entPortraitInfo?.split(";");
          this.oldentPortraitInfo = entPortraitInfoArr;
          //获奖及成就
          let awardsAchievementsArr =
            this.decisionParmas.awardsAchievements?.split(";");
          this.oldawardsAchievements = awardsAchievementsArr;
          //资质
          this.aptitudeInfos = this.plainOptions.filter((item1) =>
            this.decisionParmas.aptitudeInfos.some(
              (item2) => item2 === item1.value
            )
          );
        });
      }
    },
    comfirBtn(v) {
      if (this.isFlag) {
        this.isFlag = false;
        const parmas = this.decisionParmas;
        parmas.approvalOpenType = v; //0：保存，1：提交
        if (!parmas.aptitudeOther) {
          parmas.aptitudeOther = "1";
        } else {
          parmas.aptitudeOther = this.decisionParmas.aptitudeOther[0];
        }
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            approvalContractApi(parmas)
              .then(() => {
                this.$router.push(`/enterprise-filing-check/query`);
              })
              .catch((error) => {
                this.$message.warning(error.data.data);
              });
          } else {
            return this.$message.warning("有必填项未填写，请检查！");
          }
        });
        setTimeout(() => {
          this.isFlag = true;
        }, 3000);
      }
    },
    warning(v) {
      this.$warning({
        title: "提示",
        content: v,
      });
    },
    toBack() {
      this.$router.push(`/enterprise-filing-check/query`);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
  margin-bottom: 30px;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}

.ant-advanced-search-form /deep/ .ant-checkbox-group {
  .ant-checkbox-group-item {
    font-size: 17px !important;
    margin-left: 68px;
    margin-bottom: 30px;
  }
}
.ant-advanced-search-form /deep/ .ant-col-8 {
  .ant-col-sm-8 {
    width: 31% !important;
  }
  .ant-col-sm-16 {
    width: 69% !important;
  }
}
.ant-advanced-search-form /deep/ .ant-col-16 {
  .ant-col-sm-8 {
    width: 15% !important;
  }
  .ant-col-sm-16 {
    width: 85% !important;
  }
}
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  // width: 166px !important;
}
</style>

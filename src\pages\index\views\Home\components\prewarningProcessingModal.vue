<template>
  <div>
    <a-modal
      :width="1200"
      title="预警处理"
      :visible="canShow"
      :confirm-loading="confirmLoading"
      @cancel="handleCancel"
      :footer="null"
    >
      <div>
        <div v-if="info.type == 1">
          <a-table
            ref="taxTable"
            :columns="taxColumns"
            :dataSource="detail.companyTaxationWarningList"
            :pagination="false"
            bordered
            :scroll="{ x: 1000 }"
            :rowKey="(record) => record.id"
          >
            <template slot="yearOfTaxation"
              >今年{{ monthOfTaxation }}月税收(万元)
            </template>
            <template slot="latYearOfTaxation">
              去年{{ monthOfTaxation }}月税收(万元)
            </template>
            <template slot="districtDecentralize" slot-scope="text, record">
              <div v-if="record.districtDecentralize == 1">属地企业</div>
              <div v-else-if="record.districtDecentralize == 0">非属地企业</div>
              <div v-else></div>
            </template>
          </a-table>
        </div>
        <div v-if="info.type == 2">
          <a-table
            ref="leaseTable"
            :columns="columns"
            :dataSource="detail.leaseWarningList"
            :pagination="false"
            bordered
            :scroll="{ x: 1000 }"
            :rowKey="(record) => record.uid"
          >
          </a-table>
        </div>
        <div v-if="info.type == 3">
          <a-table
            ref="migrationTable"
            :columns="migrationColumns"
            :dataSource="detail.moveOutWarningList"
            :pagination="false"
            bordered
            :scroll="{ x: 1000 }"
            :rowKey="(record) => record.id"
          >
            <template slot="keyEnterprises" slot-scope="text, record">
              <div v-if="record.keyEnterprises == 0">非重点企业</div>
              <div v-else-if="record.keyEnterprises == 1">重点企业</div>
              <div v-else></div>
            </template>
            <template slot="districtDecentralize" slot-scope="text, record">
              <div v-if="record.districtDecentralize == 0">非属地企业</div>
              <div v-else-if="record.districtDecentralize == 1">属地企业</div>
              <div v-else></div>
            </template>
          </a-table>
        </div>
      </div>
      <div>
        <a-form :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row gutter="24">
            <a-col :span="24">
              <a-form-item
                label=" "
                :colon="false"
                :label-col="{ span: 1 }"
                :wrapper-col="{ span: 18 }"
              >
                <div class="handle-category">
                  <a-radio-group
                    @change="handleChange"
                    v-model="operateType"
                    :disabled="!canEdit"
                  >
                    <a-radio :value="1">处理</a-radio>
                    <a-radio :value="2" v-if="!isIndustry">派发</a-radio>
                  </a-radio-group>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="24" v-if="operateType == 2 && !isIndustry">
            <a-col :span="8">
              <a-form-item label="实业公司名称" required>
                <a-select
                  style="width:330px"
                  palceholder="请选择"
                  allowClear
                  @change="handleIndustryChange"
                  :disabled="!canEdit"
                  v-model="industryId"
                >
                  <a-select-option
                    v-for="(item, index) in industryArr"
                    :key="index"
                    :value="item.id"
                    >{{ item.name }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="24" v-if="operateType == 2 && !isIndustry">
            <a-col :span="8">
              <a-form-item label="接收人" required>
                <a-select
                  style="width:330px"
                  palceholder="请选择"
                  allowClear
                  @change="handleReceiverChange"
                  :disabled="!canEdit"
                  v-model="distributeId"
                >
                  <a-select-option
                    v-for="(item, index) in receiverArr"
                    :key="index"
                    :value="item.id"
                    >{{ item.name }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="24" v-if="operateType == 2 && !isIndustry">
            <a-col :span="8">
              <a-form-item label="备注">
                <a-textarea
                  style="width:330px"
                  placeholder="请输入"
                  allow-clear
                  :rows="4"
                  @change="onRemarkChange"
                  :disabled="!canEdit"
                  v-model="remark"
                  :maxLength="100"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row
            gutter="24"
            v-if="(operateType == 1 && !isIndustry) || isIndustry"
          >
            <a-col :span="8">
              <a-form-item label="处理意见" required>
                <a-textarea
                  style="width:330px"
                  placeholder="请输入"
                  allow-clear
                  :rows="4"
                  @change="onChange"
                  :disabled="!canEdit"
                  v-model="operateSuggestion"
                  :maxLength="100"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="button-group">
        <a-button type="primary" @click="handleCancel">关闭</a-button>
        <a-button
          v-if="canEdit"
          class="submit-and-close"
          type="primary"
          @click="handleOk"
          >提交并关闭</a-button
        >
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  ApiGetWarningByIdAndType,
  ApiUpdateWarningByType,
  ApiQueryDepartmentShiye,
  ApiQueryDepartmentOperatePerson,
  ApiIndustryUpdateWarningByType,
} from "APIs/PrewarningManagement";
// import STable from "@/components/Table";
import * as utils from "@/common/utils/index.js";
export default {
  props: {
    visible: Boolean,
    detailInfo: Object,
  },
  components: {
    // STable,
  },
  data() {
    return {
      labelCol: {
        span: 10,
      },
      wrapperCol: {
        span: 14,
      },
      canShow: false,
      type: -1,
      columns: [
        {
          title: "企业名称",
          dataIndex: "tenantry",
          align: "center",
        },
        {
          title: "租赁面积(㎡)",
          dataIndex: "leaseArea",
          align: "center",
        },
        {
          title: "租赁开始日期",
          dataIndex: "leaseStartTime",
          align: "center",
        },
        {
          title: "租赁结束日期",
          dataIndex: "leaseEndTime",
          align: "center",
        },
        {
          title: "租赁地址",
          dataIndex: "leaseAddress",
          align: "center",
        },
      ], //租赁列表
      taxColumns: [
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center",
        },
        {
          // title: "今年税收(万元)",
          dataIndex: "taxation",
          align: "center",
          slots: { title: "yearOfTaxation" },
        },
        {
          // title: "去年税收(万元)",
          dataIndex: "taxationOfLastYear",
          align: "center",
          slots: { title: "latYearOfTaxation" },
        },
        {
          title: "税收异常类型",
          dataIndex: "warningTypeName",
          align: "center",
        },
        {
          title: "是否属地",
          // dataIndex: "districtDecentralize",
          align: "center",
          scopedSlots: { customRender: "districtDecentralize" },
        },
      ], //税收列表
      migrationColumns: [
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center",
        },
        {
          title: "税收总额(万元)",
          dataIndex: "taxation",
          align: "center",
        },
        {
          title: "是否重点企业",
          // dataIndex: "", //0否，1是
          align: "center",
          scopedSlots: { customRender: "keyEnterprises" },
        },
        {
          title: "是否属地",
          // dataIndex: "",//原区属下放：1属地企业、0非属地企业
          align: "center",
          scopedSlots: { customRender: "districtDecentralize" },
        },
        {
          title: "租赁地址",
          dataIndex: "leaseAddress",
          align: "center",
        },
      ], //外迁列表
      info: {}, //父类传递的信息
      detail: {
        leaseWarningList: [], //租赁预警列表
        companyTaxationWarningList: [], //税收预警列表
        moveOutWarningList: [], //外迁预警列表
      }, //根据ID，type获取的详细信息
      isIndustry: false, //是否为实业公司
      operateType: 1, //处理方式：1.给出处理方案 2.派发
      operateSuggestion: "", //处理意见
      remark: "", //备注
      industryArr: [], //实业公司列表
      receiverArr: [], //派发接收人列表
      industryId: undefined, //派发时选择的实业公司ID
      distributeId: undefined, //派发时选择的接收人ID
      enable: false, //处理分类选择了4派发时，设置为true否则为false
      monthOfTaxation: "", //税收预警时查询的当前税收预警月份
    };
  },
  computed: {
    canEdit: function() {
      console.log(this.info.canEdit == 2 ? true : false, "isEdit~~~");
      return this.info.canEdit == 2 ? false : true;
    },
  },
  mounted() {
    const userInfo = localStorage.getItem("USER_KEY");
    const roles = JSON.parse(userInfo).roles;
    if (roles && roles.includes("实业公司")) {
      this.$set(this, "isIndustry", true);
    } else {
      this.$set(this, "isIndustry", false);
      this.getInustryList();
    }
  },
  watch: {
    visible: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "canShow", this.visible);
        });
      },
      immediate: true,
      deep: true,
    },
    detailInfo: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "info", this.detailInfo);
          const id = this.detailInfo.id;
          const type = this.detailInfo.type;
          ApiGetWarningByIdAndType({ id, type }).then((res) => {
            console.log(res, "modal");
            if (res.code == 0) {
              let detail = res.data;
              let tmpLeaseWarningList = [];
              detail.leaseWarningList;
              if (this.info.type == 3) {
                tmpLeaseWarningList = detail.moveOutWarningList;
                tmpLeaseWarningList &&
                  tmpLeaseWarningList.forEach((element) => {
                    element["uid"] = utils.getRandomNum("w", 8);
                  });
                detail["moveOutWarningList"] = tmpLeaseWarningList;
              } else if (this.info.type == 2) {
                tmpLeaseWarningList = detail.leaseWarningList || [];
                tmpLeaseWarningList &&
                  tmpLeaseWarningList.forEach((element) => {
                    element["uid"] = utils.getRandomNum("w", 8);
                  });
                detail["leaseWarningList"] = tmpLeaseWarningList;
              } else if (this.info.type == 1) {
                tmpLeaseWarningList = detail.companyTaxationWarningList;
                tmpLeaseWarningList &&
                  tmpLeaseWarningList.forEach((element) => {
                    element["uid"] = utils.getRandomNum("w", 8);
                  });
                detail["companyTaxationWarningList"] = tmpLeaseWarningList;
                this.$set(
                  this,
                  "monthOfTaxation",
                  tmpLeaseWarningList && tmpLeaseWarningList[0].month
                );
              }

              this.$set(this, "detail", detail);
              const tmpDetail = this.detail;
              const tmpOperateType = tmpDetail.operateType
                ? tmpDetail.operateType
                : 1;
              console.log(tmpDetail);
              console.log(tmpDetail.operateType, "tmpDetail.operateType");
              const tmpIndustryId = tmpDetail.industryId;
              const tmpDistributeId = tmpDetail.distributeId;
              const tmpOperateSuggestion = tmpDetail.operateSuggestion;
              const tmpRemark = tmpDetail.remark;
              if (this.isIndustry) {
                this.$set(this, "operateType", 1);
              } else {
                this.$set(this, "operateType", tmpOperateType);
              }
              this.$set(this, "industryId", tmpIndustryId);
              this.$set(this, "distributeId", tmpDistributeId);
              this.$set(this, "operateSuggestion", tmpOperateSuggestion);
              this.$set(this, "remark", tmpRemark);
              if (tmpDistributeId) {
                //更新接收人
                this.getPersonListUnderIndustry();
              }
            }
          });
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getInustryList: function() {
      ApiQueryDepartmentShiye({}).then((res) => {
        console.log(res);
        if (res.code == 0) {
          this.$set(this, "industryArr", res.data);
        }
      });
    },
    //派发选择实业
    handleIndustryChange: function(e) {
      console.log(e);
      this.$set(this, "industryId", e);
      //派发时，选择完实业公司后，更新接收人
      this.getPersonListUnderIndustry();
    },
    getPersonListUnderIndustry: function() {
      const deptId = this.industryId;
      if (!deptId) {
        return;
      }
      ApiQueryDepartmentOperatePerson({ deptId }).then((res) => {
        console.log(res);
        if (res.code == 0) {
          this.$set(this, "receiverArr", res.data || []);
        }
      });
    },
    handleReceiverChange: function(e) {
      console.log(e);
      this.$set(this, "distributeId", e);
    },
    //处理类型
    handleChange: function(e) {
      console.log(e.target.value);
      this.$set(this, "operateType", e.target.value);
      if (e?.target?.value && e.target.value == 4) {
        this.$set(this, "enable", true);
      } else {
        this.$set(this, "enable", false);
      }
    },
    //备注
    onRemarkChange: function(e) {
      this.$set(this, "remark", e.target.value);
    },
    //处理人意见
    onChange: function(e) {
      this.$set(this, "operateSuggestion", e.target.value);
    },
    //处理人
    onProcessorChange: function(e) {
      console.log(e.target.value);
      // this.$set(this, "operateBy", e.target.value)
    },
    handleOk: function() {
      const id = this.info.id;
      const type = this.info.type;
      const operateType = this.operateType;
      const operateSuggestion = this.operateSuggestion;
      const industryId = this.industryId;
      const distributeId = this.distributeId;
      const remark = this.remark;
      if (this.operateType == 1) {
        if (!operateSuggestion) {
          this.$message.info("您未填写处理意见");
          return;
        }
        const requestParam = {
          id,
          type,
          operateType,
          operateSuggestion,
          // remark,
        };
        ApiIndustryUpdateWarningByType(requestParam).then((response) => {
          console.log(response);
          if (response.code == 0) {
            this.$emit("handleOkAction");
          }
        });
      } else {
        if (!industryId) {
          this.$message.info("您未选择实业公司");
          return;
        }
        if (!distributeId) {
          this.$message.info("您未选择分派对象");
          return;
        }
        const params = {
          id,
          type,
          operateType,
          // operateSuggestion,
          industryId,
          distributeId,
          remark,
        };
        ApiUpdateWarningByType(params).then((res) => {
          console.log(res);
          if (res.code == 0) {
            this.$emit("handleOkAction");
          }
        });
      }
    },
    handleCancel: function() {
      // this.$set(this, "canShow", false);
      this.$emit("handleCancelAction");
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.grid-title {
  height: 48px;
  background-color: #e3e3e3;
  display: flex;
  align-items: center;
  justify-content: center;
}
.handle-category {
  margin-top: 20px;
  margin-bottom: 20px;
}
.processor,
.industry,
.receiver {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.processor-input,
.industry-input {
  margin-left: 10px;
}
.button-group {
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-and-close {
  margin-left: 20px;
}
</style>

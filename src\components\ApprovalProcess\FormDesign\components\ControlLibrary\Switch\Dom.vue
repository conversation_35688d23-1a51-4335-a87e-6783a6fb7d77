<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 开关控件DOM
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '开关'"
      style="margin-bottom:unset"
    >
      <a-switch :checked="data.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

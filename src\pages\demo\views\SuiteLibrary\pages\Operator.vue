<!--
* <AUTHOR>
* @time 2020-12-23
* @dec 表单引擎 - 作业人员备案套件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="12">
        <a-row :gutter="[50, 50]">
          <a-col>
            <operator-dom :data="formData"></operator-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="12">
        <operator-form v-bind:data.sync="formData"></operator-form
      ></a-col>
    </a-row>
  </a-card>
</template>
<script>
// 日期选择控件 DOM/Form
import {
  OperatorDom,
  OperatorForm
} from "@/components/ApprovalProcess/FormDesign/components/SuiteLibrary/Operator";

export default {
  components: {
    OperatorDom,
    OperatorForm
  },
  data() {
    return {
      formData: {}
    };
  }
};
</script>
<style scoped lang="less"></style>

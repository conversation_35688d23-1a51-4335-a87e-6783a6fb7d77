<!-- 新增备案登记 -->
<template>
  <div>
    <router-view v-if="$route.meta.level == 3"> </router-view>
    <div class="detail">
      <LessorInfo
        :totalParmas="totalParmas"
        @lessorInfo="handleLessor"
      ></LessorInfo>
      <div>
        <div class="lessorInfo">
          <div class="lessorInfo-tit">
            <div class="tit">合同信息</div>
            <div class="titItem" style="padding-left: 26px">
              <p style="margin-right: 16px">流程选择</p>
              <a-radio-group v-model="decisionParmas.processFlow">
                <a-radio :value="0"> 正常申报流程 </a-radio>
                <a-radio :value="1"> 特殊申报流程 </a-radio>
              </a-radio-group>
            </div>

            <div v-if="decisionParmas.processFlow == 1">
              <div
                style="
                  width: 100%;
                  color: #f5222d;
                  padding-left: 26px;
                  margin-bottom: 10px;
                  font-size: 16px;
                "
              >
                影响年终内控考核
              </div>
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="特殊通道情况说明(pdf盖章版)"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="
                      (file, fileList) => deleteFile8(file, fileList)
                    "
                    :fileList="fileList8"
                  >
                  </FileAttachmentList>
                </div>
                <div v-if="!showInfo">
                  <my-upload
                    businessId="special_channel_description"
                    @handleFileCallback="handleFileCallback8"
                  />
                </div>
              </div>
            </div>

            <div
              style="display: flex; justify-content: left; padding-left: 26px"
            >
              <div class="titItem">
                <p style="margin-right: 16px">合同类型</p>
                <a-radio-group
                  v-model="decisionParmas.contractType"
                  @change="onChange"
                >
                  <a-radio :value="0"> 新租 </a-radio>
                  <a-radio :value="1"> 续租 </a-radio>
                  <a-radio :value="3"> 提前终止 </a-radio>
                  <a-radio :value="2"> 变更 </a-radio>
                </a-radio-group>
              </div>
              <div class="titItem" v-if="decisionParmas.contractType == 2">
                <p style="margin-right: 16px">是否主体变更</p>
                <a-radio-group
                  v-model="decisionParmas.contentChangeType"
                  @change="onChange2"
                >
                  <a-radio :value="0"> 是 </a-radio>
                  <a-radio :value="1"> 否 </a-radio>
                </a-radio-group>
              </div>
            </div>
            <div
              v-if="decisionParmas.contractType == 0"
              class="upload_file_item"
            >
              <div class="Item">
                <FileAttachmentList
                  title="租赁合同原件（word版）"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="
                    (file, fileList) => deleteConFile(file, fileList)
                  "
                  :fileList="contractFileList"
                >
                </FileAttachmentList>
              </div>
              <div>
                <my-upload
                  :accept="accept"
                  businessId="contract_info"
                  @handleFileCallback="handleConFileCallback"
                />
              </div>
            </div>
          </div>
          <OldRentalInfo
            ref="childRef"
            :contractType="decisionParmas.contractType"
            :processFlow="decisionParmas.processFlow"
            :relationContractId="decisionParmas.relationContractId"
            @oldRental="handleOldRental"
            :isChange="decisionParmas.contentChangeType"
            @oldRentalInfoRules="getoldRentalInfoRules"
            v-if="decisionParmas.contractType != 0"
          ></OldRentalInfo>
          <a-form-model
            class="ant-advanced-search-form"
            ref="ruleForm"
            :model="decisionParmas"
            :rules="rules"
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
          >
            <div class="lessorInfo-tit" v-if="decisionParmas.contractType == 3">
              <div class="tit">终止信息</div>
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-model-item prop="cessationTime" label="提前终止日期">
                    <a-date-picker
                      v-model="decisionParmas.cessationTime"
                      style="width: 100%; height: 40px"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="16">
                  <a-form-model-item
                    prop="cessationReason"
                    label="提前终止原因"
                  >
                    <a-textarea
                      compact="true"
                      style="width: 100%"
                      v-model="decisionParmas.cessationReason"
                      placeholder="请输入提前终止原因"
                      :auto-size="{ minRows: 2, maxRows: 5 }"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="16">
                  <a-form-model-item prop="cessationTerm" label="特殊约定">
                    <a-textarea
                      compact="true"
                      style="width: 100%"
                      placeholder="如：交付状态等"
                      v-model="decisionParmas.cessationTerm"
                      :auto-size="{ minRows: 3, maxRows: 6 }"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="6">
                  <a-form-model-item prop="isDedit" label="是否有违约金">
                    <a-radio-group
                      v-model="decisionParmas.isDedit"
                      @change="isDeditChange"
                      style="width: 100%"
                    >
                      <a-radio :value="1"> 是 </a-radio>
                      <a-radio :value="0"> 否 </a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item prop="dedit">
                    <a-input
                      v-model="decisionParmas.dedit"
                      :placeholder="
                        decisionParmas.isDedit == 0
                          ? '请输入原因'
                          : '请输入金额'
                      "
                      style="width: 92%; height: 40px"
                      maxLength="50"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </div>
          </a-form-model>
        </div>
        <div class="lessorInfo" v-if="decisionParmas.contractType == 1">
          <div class="lessorInfo-tit">
            <div class="tit">续租合同信息</div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="租赁合同原件（word版）"
                  :ifNeedPreviewOnline="true"
                  marked="true"
                  @deleteFile="
                    (file, fileList) => deleteConFile(file, fileList)
                  "
                  :fileList="contractFileList"
                >
                </FileAttachmentList>
              </div>
              <div>
                <my-upload
                  :accept="accept"
                  businessId="contract_info"
                  @handleFileCallback="handleConFileCallback"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="lessorInfo" v-if="decisionParmas.contractType == 3">
          <div class="lessorInfo-tit">
            <div class="tit">提前终止合同信息</div>
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="提前终止合同原件（word版）"
                  :ifNeedPreviewOnline="true"
                  marked="true"
                  @deleteFile="
                    (file, fileList) => deleteConFile1(file, fileList)
                  "
                  :fileList="terminationFileList"
                >
                </FileAttachmentList>
              </div>
              <div>
                <my-upload
                  :accept="accept"
                  businessId="termination_agreement"
                  @handleFileCallback="handleConFileCallback1"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <RentalInfo
        ref="rentalInfoRef"
        v-if="decisionParmas.contractType != 3"
        :contractType="decisionParmas.contractType"
        :processFlow="decisionParmas.processFlow"
        :isChange="decisionParmas.contentChangeType"
        :oldRentalParmas="oldRentalParmas"
        @rentalInfo="handleRental"
        @rentalInfoRules="getRentalInfoRules"
        @rentalFileRules="getRentalFileRules"
        @rentFileRequireStatus="getRentFileRequireStatus"
        @call="handleRentalFile"
      >
      </RentalInfo>
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit">决策程序</div>
          <div class="titItem" style="padding-left: 50px">
            <p style="margin-right: 16px">是否法律顾问审核</p>
            <a-radio-group
              v-model="decisionParmas.legalAdvisorReview"
              @change="legalAdvisorReviewChange"
            >
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </div>
          <div class="titItem" style="padding-left: 50px">
            <p style="margin-right: 16px">是否经过董事会决议</p>
            <a-radio-group
              v-model="decisionParmas.boardResolution"
              @change="boardResolutionChange"
            >
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </div>
          <div class="upload_file_item" style="padding-left: 20px">
            <div class="Item">
              <FileAttachmentList
                key="11"
                title="董事会决议（复印件）"
                :ifNeedPreviewOnline="true"
                :marked="decMarked"
                :fileList="fileList"
                @deleteFile="(file, fileList) => deleteFile(file, fileList)"
              >
              </FileAttachmentList>
            </div>
            <div>
              <my-upload
                businessId="board_resolution"
                @handleFileCallback="handleFileCallback"
              />
            </div>
          </div>
          <div class="titItem" style="padding-left: 50px">
            <p style="margin-right: 16px">是否经过三重一大</p>
            <a-radio-group
              v-model="decisionParmas.isSignificant"
              @change="isSignificantChange"
            >
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </div>
          <div class="upload_file_item" style="padding-left: 20px">
            <div class="Item">
              <FileAttachmentList
                key="22"
                title="三重一大（复印件）"
                :ifNeedPreviewOnline="true"
                :marked="decMarked1"
                :fileList="tbFileList"
                @deleteFile="(file, fileList) => deleteTbFile(file, fileList)"
              >
              </FileAttachmentList>
            </div>
            <div>
              <my-upload
                businessId="triple_and_big"
                @handleFileCallback="handleTBFileCallback"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="lessorInfo">
        <div class="lessorInfo-tit">
          <div class="tit"> <span style="color:#f5222d">*</span>备注</div>
          <div class="titItem1" style="padding-left: 50px">
            <a-textarea
              compact="true"
              style="width: 58%; line-height: 20px"
              v-model="decisionParmas.remark"
              placeholder="请输入备注"
              maxLength="50"
              :auto-size="{ minRows: 3, maxRows: 6 }"
            />
          </div>
        </div>
      </div>
    </div>
    <div style="text-align: center">
      <a-button class="back" @click="toBack">返回</a-button>
      <a-button type="primary" @click="comfirBtn(0)" class="back"
        >保存</a-button
      >
      <a-button type="primary" @click="comfirBtn(1)" class="back"
        >提交申请</a-button
      >
    </div>
  </div>
</template>

<script>
import FileAttachmentList from "@/components/FileAttachmentList";
import myUpload from "@/components/Importer";
import LessorInfo from "./../common/lessorInfo";
import RentalInfo from "./../common/rentalInfo";
import OldRentalInfo from "./../common/oldRentalInfo";
import { saveContractInfo } from "@/pages/index/data/api/RegistrationRecordInquery";
export default {
  components: {
    OldRentalInfo,
    myUpload,
    LessorInfo,
    RentalInfo,
    FileAttachmentList,
  },
  data() {
    return {
      accept: ".doc,.docx",
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      rules: {
        cessationTime: [
          { required: true, message: "请选择提前终止日期", trigger: "change" },
        ],
        cessationReason: [
          { required: true, message: "请输入提前终止原因", trigger: "blur" },
        ],
        cessationTerm: [
          { required: true, message: "请输入特殊约定", trigger: "blur" },
        ],
        dedit: [{ required: true, message: "请输入", trigger: "blur" }],
        isDedit: [{ required: true, message: "", trigger: "blur" }],
      },

      rentalInfoRules: false,
      rentalFileRules: false,
      oldRentalInfoRules: false,
      isEmpty: true,
      isUpload: false,
      contractInfoFile: false,
      terminationFile: false, //提前终止
      specialChannelPeriodFile: false, //特殊规则状态
      boardResolutionFile: false,
      tripleAndBigFile: false,
      isUploadFlie: true,
      decMarked: true,
      decMarked1: true,
      isFlag: true,
      visible: false,
      lessorParmas: {},
      clauseParmas: {},
      rentalParmas: {},
      oldRentalParmas: {},
      contractFileList: [],
      terminationFileList: [],
      fileList8: [], //特殊规则文件
      fileList: [],
      tbFileList: [],
      decisionFile: [],
      totalParmas: {},
      decisionParmas: {
        processFlow: 0, //流程类型 0：正常申报流程 1：异常申报流程
        contractType: 0,
        contentChangeType: null,
        relationContractId: "",
        cessationTime: "",
        cessationReason: "",
        cessationTerm: "",
        remark: "",
        isDedit: 1,
        dedit: undefined,
        attachments: [],
        boardResolution: 1, //董事会决议 0：否 1：是
        isSignificant: 1, //三重一大 0：否 1：是
        legalAdvisorReview: 1, //法律顾问审核 0：否 1：是
        attachmentList: [],
        openType: 1,
      },
      isFreePeriod: 1, //是否需要免租期超过租期的10%或超过90天情况说明:0是1否 //contractFreePeriodStatu true=>0,false =>1
      isLeaseDeposit: 1, //是否需要租赁保证金低于3个月租金情况说明:0是 1否//leaseDepositStatu true=>0,false =>1
      showInfo: false,
    };
  },
  created() {},
  methods: {
    handleOldRental: function (parmas) {
      this.oldRentalParmas = parmas;
      console.log(this.oldRentalParmas, "原合同信息oldRentalParmas");
      this.$set(this.decisionParmas, "relationContractId", parmas.id);
    },
    handleRental(v) {
      console.log(v, "保存提交页面输出");
      this.rentalParmas = v;
    },
    handleLessor(v) {
      console.log(v);
      this.lessorParmas = v;
    },
    onChange(e) {
      console.log(112221);

      this.$nextTick(function () {
        if (e.target.value != 3) {
          this.$refs.rentalInfoRef.onClear();
        }
        if (e.target.value != 0) {
          this.$refs.childRef.onClear();
        }
      });
      if (e.target.value == 2) {
        this.decisionParmas.contentChangeType = 0;
      }
      if (e.target.value == 0 || e.target.value == 1 || e.target.value == 3) {
        this.decisionParmas.contentChangeType = null;
        this.$set(this.oldRentalParmas, "leasePurpose", undefined);
        this.$set(this.oldRentalParmas, "leasePurposeInfo", "");
        console.log(this.oldRentalParmas, "this.oldRentalParmas");
      }
      this.onClearAdd();
    },
    onChange2(e) {
      this.$nextTick(function () {
        this.$refs.rentalInfoRef.onClear();
        if (this.decisionParmas.contractType != 0) {
          this.$refs.childRef.onClear();
        }
      });
      this.onClearAdd();
      this.decisionParmas.contentChangeType = e.target.value;
    },
    onClearAdd() {
      this.fileList = [];
      this.fileList8 = [];
      this.tbFileList = [];
      this.contractFileList = [];
      this.terminationFileList = [];
      this.decisionFile = [];
      this.decisionParmas.boardResolution = 1;
      this.decisionParmas.isSignificant = 1;
      this.decisionParmas.legalAdvisorReview = 1;
      this.decMarked = true;
      this.decMarked1 = true;
      this.decisionParmas.cessationTime = "";
      this.decisionParmas.cessationReason = "";
      this.decisionParmas.cessationTerm = "";
      this.decisionParmas.isDedit = 1;
      this.decisionParmas.dedit = undefined;
      this.decisionParmas.relationContractId = "";
      this.decisionParmas.remark = "";
      this.oldRentalInfoRules = false;
      this.rentalInfoRules = false;
      this.boardResolutionFile = false;
      this.tripleAndBigFile = false;
    },
    getRentalInfoRules(v) {
      this.rentalInfoRules = v;
    },
    getRentalFileRules(v) {
      this.rentalFileRules = v;
    },
    getRentFileRequireStatus(isLeaseDeposit, isFreePeriod) {
      this.isLeaseDeposit = isLeaseDeposit ? 0 : 1;
      this.isFreePeriod = isFreePeriod ? 0 : 1;
      console.log(
        "租赁保证金低于3个月租金:0是 1否",
        this.isLeaseDeposit,
        "免租期超过租期的10%或超过90天:0是 1否",
        this.isFreePeriod
      );
    },
    getoldRentalInfoRules(v) {
      this.oldRentalInfoRules = v;
    },
    comfirBtn(v) {
      if (this.decisionParmas.contractType != 3) {
        this.$refs.rentalInfoRef.onChange();
        this.$refs.rentalInfoRef.onFileRequireStatus();
        if (
          this.decisionParmas.contractType == 2 &&
          this.decisionParmas.contentChangeType == 0
        ) {
          console.log(this.$refs.childRef.rulesForm, "0000");
          this.rentalParmas.leaseTermStart =
            this.$refs.childRef.rulesForm.leaseTermStart;
          this.rentalParmas.leaseTermEnd =
            this.$refs.childRef.rulesForm.leaseTermEnd;
          this.rentalParmas.leaseTerm = this.$refs.childRef.rulesForm.leaseTerm;
        }
      } else {
        this.isLeaseDeposit = 1;
        this.isFreePeriod = 1;
        console.log(
          "else===租赁保证金低于3个月租金:0是 1否",
          this.isLeaseDeposit,
          "免租期超过租期的10%或超过90天:0是 1否",
          this.isFreePeriod
        );
      }
      this.decisionParmas.attachmentList = this.decisionFile.concat(
        this.fileList,
        this.tbFileList,
        this.contractFileList,
        this.terminationFileList,
        this.fileList8
      );
      this.decisionParmas.attachmentList = [
        ...new Set(this.decisionParmas.attachmentList),
      ];
      const parmas = Object.assign(
        this.lessorParmas,
        this.rentalParmas,
        this.decisionParmas
      );
      parmas.isFreePeriod = this.isFreePeriod;
      parmas.isLeaseDeposit = this.isLeaseDeposit;
      parmas.openType = v; //1提交，0保存
     
      if (this.isFlag) {
        this.isFlag = false;
 console.log(parmas, "提交参数parmas223123213213");
        if (parmas.contractType == 0 || parmas.contractType == 1) {
          if (
            this.contractInfoFile &&
            this.boardResolutionFile &&
            this.tripleAndBigFile
          ) {
            this.isUploadFlie = true;
          } else {
            this.isUploadFlie = false;
          }
        } else if (parmas.contractType == 3) {
          if (
            this.terminationFile &&
            this.boardResolutionFile &&
            this.tripleAndBigFile
          ) {
            this.isUploadFlie = true;
          } else {
            this.isUploadFlie = false;
          }
        } else {
          if (this.boardResolutionFile && this.tripleAndBigFile) {
            this.isUploadFlie = true;
          } else {
            this.isUploadFlie = false;
          }
        }
        if (parmas.processFlow == 1 && !this.specialChannelPeriodFile) {
          this.isUploadFlie = false;
        }
        if (parmas.contractType != 0) {
          parmas.contractBuildingInfo = [];
        }
        if (parmas.contractType == 2 && parmas.contentChangeType == 0) {
          parmas.contractFreePeriod = [];
        }
        if (parmas.legalAdvisorReview == 0 && parmas.openType == 1) {
          this.warning(
            "您提交的信息未经过法律顾问审核，请您通过法律顾问审核后提交"
          );
        } else if (parmas.essentialSituation == 0 && parmas.openType == 1) {
          this.warning(
            "您提交的信息未进行必备条款约束，请您先进行必备条款约束后提交"
          );
        } else {
          if (parmas.lessor) {
            console.log(parmas, "走了这里");
            if (parmas.openType == 1) {
              if (parmas.contractType == 3) {
                this.rentalFileRules = true;
              } else {
                this.$refs.rentalInfoRef.onFileUp();
              }
              if (this.rentalFileRules && this.isUploadFlie) {
                if(this.decisionParmas.remark == undefined || this.decisionParmas.remark == ""){
                  this.$message.warning("请填写备注信息！");
                  return;
                }
                if (parmas.contractType == 3) {
                  this.$refs.childRef.onSubmit();
                  this.$refs.ruleForm.validate((valid) => {
                    if (valid && this.oldRentalInfoRules&&this.decisionParmas.remark) {
                      saveContractInfo(parmas)
                        .then(() => {
                          this.$message.success("提交申请成功");
                          this.$router.push(
                            `/registration-record-inquiry/query`
                          );
                        })
                        .catch((error) => {
                          this.$message.warning(error.data.data);
                        });
                    } else {
                      this.$message.warning("有必填项未填写，请检查！");
                    }
                  });
                } else if (parmas.contractType == 0) {
                  this.$refs.rentalInfoRef.onSubmit();
                  if (this.rentalInfoRules) {
                    let isRoom = this.isRoomName(parmas);
                    let isMultTenancy = this.isMultTenancy(parmas);
                    if (isRoom) {
                      this.warning("请选择出租地址");
                    } else if (isMultTenancy) {
                      this.warning("请填写分段租期");
                    } else {
                      saveContractInfo(parmas)
                        .then(() => {
                          this.$message.success("提交申请成功");
                          this.$router.push(
                            `/registration-record-inquiry/query`
                          );
                        })
                        .catch((error) => {
                          this.$message.warning(error.data.data);
                        });
                    }
                  } else {
                    this.$message.warning("有必填项未填写，请检查！");
                  }
                } else {
                  this.$refs.rentalInfoRef.onSubmit();
                  this.$refs.childRef.onSubmit();
                  if (this.rentalInfoRules && this.oldRentalInfoRules) {
                    let isMultTenancy = this.isMultTenancy(parmas);
                    if (parmas.contentChangeType == 1 && !parmas.alterReason) {
                      this.warning("请输入变更具体条款及原因！");
                    } else if (isMultTenancy) {
                      this.warning("请填写分段租期");
                    } else {
                      saveContractInfo(parmas)
                        .then(() => {
                          this.$message.success("提交申请成功");
                          this.$router.push(
                            `/registration-record-inquiry/query`
                          );
                        })
                        .catch((error) => {
                          this.$message.warning(error.data.data);
                        });
                    }
                  } else {
                    this.$message.warning("有必填项未填写，请检查！");
                  }
                }
              } else {
                this.$message.warning("有附件未上传，请检查！");
              }
            } else {
              saveContractInfo(parmas).then((res) => {
                console.log(res.data);
                this.$message.success("保存成功");
                this.$router.push(`/registration-record-inquiry/query`);
              });
            }
          } else {
            if (parmas.openType == 0) {
              this.$message.warning("请选择出租方后，再进行保存");
            } else {
              this.$message.warning("请选择出租方后，再进行提交");
            }
          }
        }

        setTimeout(() => {
          this.isFlag = true;
        }, 2000);
      }
    },
    isDeditChange() {
      this.decisionParmas.dedit = undefined;
    },
    isRoomName(parmas) {
      for (var key in parmas.contractBuildingInfo) {
        if (
          parmas.contractBuildingInfo[key].roomName == "" ||
          parmas.contractBuildingInfo[key].roomName == undefined
        ) {
          return true;
        }
      }
      return false;
    },
    isMultTenancy(parmas) {
      if (parmas.priceIncrease == "1") {
        for (var key in parmas.multTenancyInfoList) {
          if (
            parmas.multTenancyInfoList[key].rentalUnitPrice == "" ||
            parmas.multTenancyInfoList[key].annualRent == "" ||
            parmas.multTenancyInfoList[key].startTime == "" ||
            parmas.multTenancyInfoList[key].endTime == ""
          ) {
            return true;
          }
        }
        return false;
      } else {
        return false;
      }
    },
    warning(v) {
      this.$warning({
        title: "提示",
        content: v,
      });
    },
    toBack() {
      this.$router.push(`/registration-record-inquiry/query`);
    },
    legalAdvisorReviewChange(v) {
      this.decisionParmas.legalAdvisorReview = v.target.value;
    },
    boardResolutionChange(v) {
      if (v.target.value == 0) {
        this.decMarked = false;
        this.boardResolutionFile = true;
      } else {
        this.decMarked = true;
        if (this.boardFileList.length > 0) {
          this.boardResolutionFile = true;
        } else {
          this.boardResolutionFile = false;
        }
      }
    },
    isSignificantChange(v) {
      if (v.target.value == 0) {
        this.decMarked1 = false;
        this.tripleAndBigFile = true;
      } else {
        this.decMarked1 = true;
        if (this.tbFileList.length > 0) {
          this.tripleAndBigFile = true;
        } else {
          this.tripleAndBigFile = false;
        }
      }
    },
    //特殊通道
    handleFileCallback8: function (file) {
      this.specialChannelPeriodFile = true;
      let tmpList = this.fileList8;
      tmpList.push(file);
      this.$set(this, "fileList8", tmpList);
    },
    deleteFile8: function (file, fileList) {
      console.log(
        this.specialChannelPeriodFile,
        this.fileList8,
        "删除特殊通道"
      );
      if (fileList.length == 0) {
        this.specialChannelPeriodFile = false;
      }
      this.fileList8 = fileList;

      // this.rentalFlie = this.rentalFlie.filter(
      //   (item) => item.fileId !== file.fileId
      // );
    },
    //租赁合同原件（盖章版）
    handleConFileCallback: function (file) {
      this.contractInfoFile = true;
      let tmpList = this.contractFileList;
      tmpList.push(file);
      this.$set(this, "contractFileList", tmpList);
    },

    deleteConFile: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.contractInfoFile = false;
      }
      this.contractFileList = fileList;
    },
    //提前终止合同原件（盖章版）
    handleConFileCallback1: function (file) {
      this.terminationFile = true;
      let tmpList = this.terminationFileList;
      tmpList.push(file);
      this.$set(this, "terminationFileList", tmpList);
    },
    deleteConFile1: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.terminationFile = false;
      }
      this.terminationFileList = fileList;
    },
    //董事会决议（复印件）
    handleFileCallback: function (file) {
      this.boardResolutionFile = true;
      let tmpList = this.fileList;
      tmpList.push(file);
      this.$set(this, "fileList", tmpList);
    },
    deleteFile: function (file, fileList) {
      console.log(file, fileList);
      if (this.decisionParmas.boardResolution == 1) {
        if (fileList.length == 0) {
          this.boardResolutionFile = false;
        }
      }
      this.fileList = fileList;
    },
    //三重一大（复印件）
    handleTBFileCallback: function (file) {
      this.tripleAndBigFile = true;
      let tmpList = this.tbFileList;
      tmpList.push(file);
      this.$set(this, "tbFileList", tmpList);
    },
    deleteTbFile: function (file, fileList) {
      if (this.decisionParmas.isSignificant == 1) {
        if (fileList.length == 0) {
          this.tripleAndBigFile = false;
        }
      }
      console.log(file, fileList);
      this.tbFileList = fileList;
    },
    //合同+条款附件
    handleRentalFile(v) {
      this.decisionFile = v;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./../common/common.less";
.detail {
  width: 100%;
  padding: 15px 0;
  background-color: #fff;
}

.back {
  width: 112px;
  margin: 15px auto;
  height: 40px;
  border-radius: 6px;
  margin-right: 32px;
}

.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  width: 166px !important;
}
</style>

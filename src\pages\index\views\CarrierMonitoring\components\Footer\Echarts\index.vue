<template>
  <div>
    <div ref="TaxTrends" class="contain"></div>
  </div>
</template>

<script>
import { ApiGetTax } from "../../../../../data/api/EnterpriseMonitoring";
import * as echarts from "echarts";
export default {
  components: {},
  data() {
    return {
      chartData: [],
      targetData: [],
    };
  },
  mounted() {},
  created() {
    this.getEchartsData();
  },
  methods: {
    async initData() {
      this.drawLine();
    },
    //获取后端传过来渲染在图标里面的数据
    getEchartsData() {
      ApiGetTax().then((res) => {
        this.chartData = res.data.records;
        for (let i = 0; i < 12; i++) {
          this.targetData.push(res.data.targetTax);
        }
        this.initData();
        console.log(res.data);
      });
    },
    drawLine() {
      console.log(this.chartData, "6666");
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(this.$refs.TaxTrends);
      let option = {
        title: {
          text: "税收趋势",
          textStyle: {
            color: "black",
            fontSize: 16,
            fontWeight: 600,
          },
        },
        legend: {
          show: true,
          type: "plain",
          right: "10%",
          top: "5%",
          data: [
            {
              name: "目标税收",
            },
            {
              name: "纳税金额",
            },
          ],
        },
        tooltip: {
          trigger: "axis",
          textStyle: {
            color: "#000",
          },
          backgroundColor: "#fff",
        },
        grid: {
          top: "30%",
          bottom: "20%",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              interval: 0,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#adadad",
              },
            },
            axisTick: {
              show: false,
            },
            data: [
              "1月",
              "2月",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
          },
        ],
        yAxis: [
          {
            min: "0",
            max: "100",
            interval: 20, // 指定刻度间隔
            name: "亿元", //y轴名字
            type: "value",
            axisLine: {
              lineStyle: {
                color: "#adadad",
              },
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: "#e0e0e0",
              },
            },
          },
        ],
        series: [
          {
            name: "目标税收",
            type: "line",
            itemStyle: {
              color: "#2CD9C5",
              barBorderRadius: [10, 10, 0, 0],
            },
            barWidth: 15,
            data: [70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70],
            // data: this.targetData
          },
          {
            name: "纳税金额",
            type: "bar",
            itemStyle: {
              color: "#0090FF",
              barBorderRadius: [10, 10, 0, 0],
            },
            barWidth: 15,
            data: this.chartData,
          },
        ],
      };
      // 绘制图表
      myChart.setOption(option);
      //多图表自适应
      window.addEventListener("resize", function() {
        myChart.resize();
      });
    },
  },
};
</script>

<style scoped lang="less">
.contain {
  // width: 800px;
  height: 200px;
}
</style>

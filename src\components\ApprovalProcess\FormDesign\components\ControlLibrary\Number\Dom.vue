<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 数字控件
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '数字'"
      style="margin-bottom:unset"
      prop="number"
      :rules="[
        {
          required: data.notNull,
          message: '请输入',
          trigger: 'blur'
        }
      ]"
      ><a-input
        :suffix="data.optionsData.unit"
        v-model="form.number"
        :placeholder="data.placeholder.placeholderText || '请输入'"
      ></a-input>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          placeholder: ""
        };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        number: null
      }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

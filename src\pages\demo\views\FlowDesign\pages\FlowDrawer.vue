<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 流程设计-设置人抽屉
-->
<template>
  <div>
    <!-- <p>流程设计-抽屉</p> -->
    <flow-drawer></flow-drawer>
  </div>
</template>
<script>
import FlowDrawer from "@/components/ApprovalProcess/ProcessDesign/components/FlowDrawer";
// import ControlForm from "@/components/ApprovalProcess/FormDesign/components/ControlForm";
export default {
  components: {
    FlowDrawer
  },
  data() {
    return {};
  },
  methods: {}
};
</script>

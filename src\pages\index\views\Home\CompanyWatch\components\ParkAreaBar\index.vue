<template>
  <div class="park-area-bar-container">
    <div id="barDom" class="bar-chart"></div>
  </div>
</template>

<script>
import { getIndustrialParkAreaApi } from "APIs/ComponyWatch";
import * as echarts from "echarts";
export default {
  data() {
    return {
      option: {
        legend: {},
        tooltip: {},
        dataset: {
          source: [
            ["product", "2015", "2016", "2017"],
            ["Matcha Latte", 43.3, 85.8, 93.7],
            ["Milk Tea", 83.1, 73.4, 55.1],
            ["Cheese Cocoa", 86.4, 65.2, 82.5],
            ["Walnut Brownie", 72.4, 53.9, 39.1],
            ["Matcha Latte1", 43.3, 85.8, 93.7],
            ["Milk Tea1", 83.1, 73.4, 55.1],
            ["Cheese Cocoa1", 86.4, 65.2, 82.5],
            ["Walnut Brownie1", 72.4, 53.9, 39.1],
            ["Matcha Latte2", 43.3, 85.8, 93.7],
            ["Milk Tea2", 83.1, 73.4, 55.1],
            ["Cheese Cocoa2", 86.4, 65.2, 82.5],
            ["Walnut Brownie2", 72.4, 53.9, 39.1],
            ["<PERSON>a <PERSON><PERSON>3", 43.3, 85.8, 93.7],
            ["<PERSON> <PERSON>3", 83.1, 73.4, 55.1],
            ["<PERSON><PERSON> <PERSON><PERSON>3", 86.4, 65.2, 82.5],
            ["Walnut <PERSON><PERSON>3", 72.4, 53.9, 39.1],
            ["<PERSON>a <PERSON><PERSON>4", 43.3, 85.8, 93.7],
            ["<PERSON> <PERSON>4", 83.1, 73.4, 55.1],
            ["Cheese Cocoa4", 86.4, 65.2, 82.5],
            ["Walnut Brownie4", 72.4, 53.9, 39.1],
          ],
        },
        xAxis: {},
        yAxis: { type: "category" },
        visualMap: {
          orient: "horizontal",
          left: "center",
          min: 10,
          max: 100,
          //   text: ["High Score", "Low Score"],
          // Map the score column to color
          //   dimension: 0,
          //   inRange: {
          //     color: ["#65B581", "#FFCE34", "#FD665F"],
          //   },
        },
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{ type: "bar" }, { type: "bar" }, { type: "bar" }],
      },
    };
  },
  mounted() {
    const barDom = document.getElementById("barDom");
    const barChart = echarts.init(barDom);
    barChart.setOption(this.option);
    this.initData();
  },
  methods: {
    initData() {
      getIndustrialParkAreaApi("").then((res) => {
        console.log(res, "~~~~");
      });
    },
  },
};
</script>

<style lang="less" scoped>
.park-area-bar-container {
  width: 50%;
}
.bar-chart {
  width: 100%;
  height: 400px;
}
</style>

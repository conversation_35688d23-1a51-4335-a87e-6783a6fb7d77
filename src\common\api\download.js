import axios from "axios";
import <PERSON><PERSON> from "js-cookie";

const TOKEN = "UTOKEN";

const instance = axios.create();
let config = {
  method: "get",
  responseType: "arraybuffer",
};

export default function(requestData) {
  const { url, params } = requestData;
  config.url = url;
  config.params = params;
  config.headers = {};
  config.headers["responseType"] = "blob";

  const token = Cookie.get(TOKEN);
  config.headers[TOKEN] = token;
  return new Promise((resolve, reject) => {
    instance(config)
      .then((res) => {
        console.log(res, "111");
        resolve(res.data);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

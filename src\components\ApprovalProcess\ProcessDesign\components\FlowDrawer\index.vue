<!--
* <AUTHOR>
* @time 2020-9-1
* @dec 流程设计-抽屉组件
-->
<template>
  <div class="control-tabs-page">
    <div v-if="openWay !== 'CC'">
      <a-tabs
        default-active-key="1"
        @change="callback"
        :tabBarStyle="{
          margin: 'unset'
        }"
      >
        <a-tab-pane
          key="1"
          :tab="
            openWay === 'launch'
              ? '设置发起人'
              : openWay === 'approval'
              ? '设置审批人'
              : openWay === 'transferTo'
              ? '设置流转人'
              : '设置抄送人'
          "
          :forceRender="true"
        >
          <set-people
            ref="setPeople"
            :openWay="openWay"
            :data="data"
          ></set-people>
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="审批设置"
          v-if="openWay == 'approval' && openWay !== 'transferTo'"
          :forceRender="true"
        >
          <approval-settings
            ref="approvalSettings"
            :data="data"
          ></approval-settings>
        </a-tab-pane>
        <a-tab-pane
          key="3"
          tab="表单操作权限"
          :forceRender="true"
          v-if="openWay !== 'transferTo'"
        >
          <operation-authority
            ref="operationAuthority"
            :data="data"
          ></operation-authority>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div v-else>
      <set-people ref="setPeople" :openWay="openWay" :data="data"></set-people>
    </div>
  </div>
</template>
<script>
import SetPeople from "@/components/ApprovalProcess/ProcessDesign/components/FlowDrawer/SetPeople";
import ApprovalSettings from "@/components/ApprovalProcess/ProcessDesign/components/FlowDrawer/ApprovalSettings";
import OperationAuthority from "@/components/ApprovalProcess/ProcessDesign/components/FlowDrawer/OperationAuthority";
import { deepClone } from "@/common/utils";
export default {
  components: {
    SetPeople, //设置人组件
    ApprovalSettings, //审批设置
    OperationAuthority //表单操作权限
  },
  props: {
    //类型 launch发起,approval审批,CC抄送
    openWay: {
      type: String,
      default: () => null
    },
    data: {}
  },
  data() {
    return {
      loading: false,
      node: deepClone(this.data)
    };
  },
  methods: {
    //切换tab
    callback(key) {
      console.log(key);
    },
    // 获取数据
    getData() {
      this.node.data = {
        // 人员设置
        roleAndUserAuth:
          (this.$refs.setPeople && this.$refs.setPeople.getData()) || [],
        // 权限设置
        formModuleAuths:
          this.$refs.operationAuthority &&
          this.$refs.operationAuthority.getData(),
        // 审批设置
        approvalAuths:
          this.$refs.approvalSettings && this.$refs.approvalSettings.getData()
      };
      return deepClone(this.node);
    }
  },
  mounted() {}
};
</script>
<style lang="less" scoped>
.control-tabs-page {
  margin: -24px;
}
</style>

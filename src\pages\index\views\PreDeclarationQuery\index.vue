<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <div class="enterprise" v-else>
    <div class="enterpriseFrom">
      <div class="carrierFrom">
        <a-form
          :form="form"
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
          @submit="handleSubmit"
        >
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="企业名称">
                <a-input
                  allowClear
                  v-model="queryParam.companyName"
                  placeholder="请输入企业名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="联系人">
                <a-input
                  allowClear
                  v-model="queryParam.contactPerson"
                  placeholder="请输入联系人"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="联系方式">
                <a-input
                  allowClear
                  v-model="queryParam.contactWay"
                  placeholder="请输入联系方式"
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="40" align="center">
            <a-col :span="8">
              <a-form-item label="审批状态">
                <a-select
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  allowClear
                  v-model="queryParam.status"
                  placeholder="全部"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in approveArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="申报时间">
                <a-range-picker v-model="queryParam.applyTime" />
              </a-form-item>
            </a-col>
            <a-col :span="8" align="right">
              <a-form-item :wrapper-col="{ span: 24 }">
                <a-button
                  type="primary"
                  @click="search"
                  style="margin-right: 20px"
                  >查询</a-button
                >
                <a-button type="default" @click="reset">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>

    <!-- :rowKey="(record) => record.data.id" -->
    <a-card style="width: 100%; margin-top: 20px">
      <div class="list-tit">
        <p>预申报表查询列表</p>
        <a-button
          type="primary"
          @click="toAdd(1)"
          style="font-size: 16px; height: 40px; border-radius: 6px"
          >新增预申报表</a-button
        >
      </div>
      <s-table
        ref="table"
        size="default"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        rowKey="id"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </span>
        <template slot="companyName" slot-scope="text">
          <a-tooltip>
            <template slot="title">
              {{ text }}
            </template>
            <div
              style="
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <span slot="action" slot-scope="text, record">
          <a
            v-if="record.status == 1 || record.status == 2"
            href="javascript:;"
            @click="toDetail(record, 3)"
            >查看</a
          >
          <a href="javascript:;" v-else @click="toEdit(2, record)">编辑</a>
          <!-- <a-popconfirm
            title="确认删除?"
            @confirm="() => toDelete(record)"
            v-auth="'mapi:security:role:delete'"
          >
            <a href="javascript:;">删除</a>
          </a-popconfirm> -->
        </span>
      </s-table>
    </a-card>
  </div>
</template>

<script>
import moment from "moment";
import {
  getListApi,
  // deleteContractInfoApi,
} from "@/pages/index/data/api/PreDeclarationQuery";
import { ApiExportEnterSearch } from "@/pages/index/data/api/InfomationQuery/index";
import STable from "@/components/Table";
export default {
  components: {
    STable,
  },
  data() {
    return {
      approveArr: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "0",
          label: "待提交",
        },
        {
          value: "1",
          label: "已提交",
        },
        {
          value: "2",
          label: "已审批",
        },
        {
          value: "3",
          label: "已驳回",
        },
      ],
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: "60px",
          fixed: "left",
          align: "center",
        },
        {
          title: "企业名称",
          dataIndex: "companyName",
          scopedSlots: { customRender: "companyName" },
          width: 180,
          align: "center",
        },
        {
          title: "联系人",
          dataIndex: "contactPerson",
          width: 120,
          align: "center",
        },
        {
          title: "职务",
          dataIndex: "duty",
          width: 120,
          align: "center",
        },

        {
          title: "联系方式",
          dataIndex: "contactWay",
          width: 180,
          align: "center",
        },
        {
          title: "租赁需求",
          dataIndex: "leasingDemand",
          width: 180,
          align: "center",
        },
        {
          title: "审批状态",
          dataIndex: "status",
          width: 120,
          align: "center",
          scopedSlots: { customRender: "status" },
          customRender: (text) => {
            return text == 0
              ? "待提交"
              : text == 1
              ? "已提交"
              : text == 2
              ? "已审批"
              : "已驳回";
          },
        },
        {
          title: "申报时间",
          dataIndex: "createTime",
          width: 140,
          align: "center",
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          scopedSlots: { customRender: "action" },
          width: 160,
        },
      ],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      queryParam: {
        companyName: undefined, //企业名称
        applyTime: [], //申请时间
        contactPerson: undefined, //联系人
        createTimeStart: undefined, //申请时间开始
        createTimeEnd: undefined, //申请时间结束
        contactWay: undefined, //租赁企业
        status: undefined, //审核方式
        queryEntity: 0, //查询实体：0 实业公司, 1 经发公司
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (values) => {
        this.pageNo = values.pageNo;
        this.pageSize = values.pageSize;
        if (this.queryParam.applyTime?.length > 0) {
          this.queryParam.createTimeStart = moment(
            this.queryParam.applyTime[0]
          ).format("YYYY-MM-DD 00:00:00");
          this.queryParam.createTimeEnd = moment(
            this.queryParam.applyTime[1]
          ).format("YYYY-MM-DD 23:59:59");
        } else {
          this.queryParam.applyTime = [];
          this.queryParam.createTimeStart = undefined;
          this.queryParam.createTimeEnd = undefined;
        }

        const requestParameters = Object.assign(
          {
            currentPage: values.pageNo,
            pageSize: values.pageSize,
            queryEntity: 0,
          },
          this.queryParam
        );
        return getListApi(requestParameters).then((res) => {
          let dataObj = res.data;
          dataObj.data = res.data.records || [];
          return dataObj;
        });
      },
    };
  },
  mounted() {},
  methods: {
    moment,
    deduced() {
      ApiExportEnterSearch(this.queryParam);
    },

    reset() {
      (this.queryParam = {
        companyName: undefined, //企业名称
        contactPerson: undefined, //联系人
        applyTime: [], //申请时间
        createTimeStart: undefined, //申请时间开始
        createTimeEnd: undefined, //申请时间结束
        contactWay: undefined, //联系方式
        status: undefined, //审核方式
      }),
        this.$refs.table.refresh(true);
    },
    search() {
      console.log(this.queryParam);
      this.$refs.table.refresh(true);
    },
    toDetail(record, n) {
      this.$router.push(
        `/pre-declaration-query/query/add?statu=` + n + `&id=` + record.id
      );
    },
    toAdd(n) {
      this.$router.push(`/pre-declaration-query/query/add?statu=` + n);
    },
    toEdit(n, record) {
      this.$router.push(
        `/pre-declaration-query/query/add?statu=` + n + `&id=` + record.id
      );
    },
    // toDelete(record) {
    //   let params = {
    //     id: record.id,
    //   };
    //   deleteContractInfoApi(params).then((res) => {
    //     let dataObj = res.data;
    //     dataObj.data = res.data.records || [];
    //   });
    // },
  },
};
</script>

<style lang="less" scoped>
.enterprise {
  display: flex;
  flex-wrap: wrap;

  .enterpriseFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }

  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;

    .sort {
      margin-left: auto;

      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }

  .table {
    width: 100%;
    margin-top: 10px;
  }

  .list-tit {
    display: flex;
    justify-content: space-between;

    p {
      font-size: 20px;
    }
  }
}

.ellipse {
  width: 100%;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 这里是超出几行省略 */
}
</style>

import TabsView from "@/components/Layouts/Tabs/TabsView";
import PageView from "@/components/Layouts/PageView";
import ApprovalProcessDemo from "../views/ApprovalProcessDemo";

// 路由配置
const routes = [
  {
    path: "/approval-process",
    name: "",
    component: ApprovalProcessDemo,
  },
  {
    path: "/",
    name: "",
    component: TabsView,
    redirect: "/form/basic-settings",
    children: [
      {
        path: "/form",
        name: "表单引擎",
        component: PageView,
        redirect: "/form/basic-settings",
        meta: {
          icon: "home",
        },
        children: [
          {
            path: "approval",
            name: "流程审批封装主页",
            redirect: "/approval-process",
          },
          {
            path: "basic-settings",
            name: "基础配置",
            component: () => import("../views/BasicSettings"),
          },
          {
            path: "form-design",
            name: "表单引擎",
            redirect: "/form/form-design/single-choice",
            component: () => import("../views/FormDesign"),
            children: [
              {
                path: "card-id",
                name: "身份证控件",
                component: () => import("../views/FormDesign/pages/CardId"),
              },
              {
                path: "phone-no",
                name: "手机控件",
                component: () => import("../views/FormDesign/pages/PhoneNo"),
              },
              {
                path: "land-line",
                name: "座机控件",
                component: () => import("../views/FormDesign/pages/LandLine"),
              },
              {
                path: "picture",
                name: "图片控件",
                component: () => import("../views/FormDesign/pages/Picture"),
              },
              {
                path: "single-choice",
                name: "单选控件",
                component: () =>
                  import("../views/FormDesign/pages/SingleChoice"),
              },
              {
                path: "switch",
                name: "开关",
                component: () => import("../views/FormDesign/pages/Switch"),
              },
              {
                path: "date-selection",
                name: "日期选择",
                component: () =>
                  import("../views/FormDesign/pages/DateSelection"),
              },
              {
                path: "score",
                name: "评分",
                component: () => import("../views/FormDesign/pages/Score"),
              },
              {
                path: "time-selection ",
                name: "时间选择",
                component: () =>
                  import("../views/FormDesign/pages/TimeSelection"),
              },
              {
                path: "time-frame",
                name: "时间范围",
                component: () => import("../views/FormDesign/pages/TimeFrame"),
              },
              {
                path: "upload",
                name: "上传",
                component: () => import("../views/FormDesign/pages/Upload"),
              },
              {
                path: "multiple-choice",
                name: "多选控件",
                component: () =>
                  import("../views/FormDesign/pages/MultipleChoice"),
              },
              {
                path: "singlelinetext",
                name: "单行文本",
                component: () =>
                  import("../views/FormDesign/pages/Singlelinetext"),
              },
              {
                path: "multilinetext",
                name: "多行文本",
                component: () =>
                  import("../views/FormDesign/pages/Multilinetext"),
              },
              {
                path: "counter",
                name: "计数器",
                component: () => import("../views/FormDesign/pages/Counter"),
              },
              {
                path: "amountcounter",
                name: "金额计数器",
                component: () =>
                  import("../views/FormDesign/pages/AmountCounter"),
              },
              {
                path: "number",
                name: "数字",
                component: () => import("../views/FormDesign/pages/Number"),
              },
              {
                path: "button",
                name: "按钮",
                component: () => import("../views/FormDesign/pages/Button"),
              },
              {
                path: "modulename",
                name: "模块名称",
                component: () => import("../views/FormDesign/pages/ModuleName"),
              },
              {
                path: "explain-text",
                name: "说明文字",
                component: () =>
                  import("../views/FormDesign/pages/ExplainText"),
              },
            ],
          },
          {
            path: "form",
            name: "表单页面模板规范",
            component: () => import("../views/Form"),
          },
          {
            path: "tabs-route",
            name: "Tabs页面子路由",
            component: () => import("../views/TabsRoute"),
            redirect: "/form/tabs-route/list-a",
            children: [
              {
                path: "list-a",
                name: "列表a",
                component: () => import("../views/TabsRoute/views/ListA"),
              },
              {
                path: "list-b",
                name: "列表b",
                component: () => import("../views/TabsRoute/views/ListB"),
              },
            ],
          },
          {
            path: "tabs-component",
            name: "Tabs页面切换子组件",
            component: () => import("../views/TabsComponent"),
          },
          {
            path: "design-page",
            name: "设计主页",
            component: () => import("../views/DesignPage"),
          },
          {
            path: "suite-library",
            name: "套件库",
            component: () => import("../views/SuiteLibrary"),
            children: [
              {
                path: "non-aviation",
                name: "非航资源套件",
                component: () =>
                  import("../views/SuiteLibrary/pages/NonAviation"),
              },
              {
                path: "machine-room",
                name: "机房资源套件",
                component: () =>
                  import("../views/SuiteLibrary/pages/MachineRoom"),
              },
              {
                path: "personnel-record",
                name: "机房人员备案套件",
                component: () =>
                  import("../views/SuiteLibrary/pages/PersonnelRecord"),
              },
              {
                path: "operator",
                name: "作业人员套件",
                component: () => import("../views/SuiteLibrary/pages/Operator"),
              },
            ],
          },
          {
            path: "flow-design",
            name: "流程设计",
            redirect: "/form/flow-design/flow-drawer",
            component: () => import("../views/FlowDesign"),
            children: [
              {
                path: "flow-drawer",
                name: "设置人抽屉",
                component: () => import("../views/FlowDesign/pages/FlowDrawer"),
              },
              {
                path: "set-rule-branch",
                name: "规则分支配置抽屉",
                component: () =>
                  import("../views/FlowDesign/pages/SetRuleBranch"),
              },
            ],
          },
        ],
      },
    ],
  },
];

export default routes;

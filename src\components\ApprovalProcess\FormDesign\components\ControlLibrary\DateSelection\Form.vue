<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 日期选择控件表单
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input
        v-model="form.inputTitle"
        placeholder="申请主题"
        maxLength="20"
      />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        v-model="form.placeholderText"
        placeholder="请输入"
        maxLength="50"
      />
    </a-form-model-item>
    <a-form-model-item label="时间类型">
      <a-radio-group
        v-model="form.optionsData.timeType"
        @change="handTimeType"
        defaultValue="0"
      >
        <a-radio value="0">
          年.月.日
        </a-radio>
        <a-radio value="1">
          年.月.日 时:分:秒
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        optionsData: {}
      }
    };
  },

  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  },
  methods: {
    // 日期选择
    handTimeType(e) {
      this.form.timeType = e.target.value;
    }
  }
};
</script>

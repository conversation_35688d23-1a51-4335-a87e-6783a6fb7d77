/**
 * <AUTHOR>
 * @time 2020-9-10
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 案例
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
//分页查询轮播列表
export function ApiBusinessPageByConditionn(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/business/banner/pageByCondition",
    method: "post",
    middle,
    params,
  });
}
// 是否启用
export function ApiBusinessUpdateDisabled(params) {
  return api({
    url: BASE_URL + "/business/banner/updateDisabled",
    method: "post",
    params,
  });
}
// 新增
export function ApiBusinessCreate(params) {
  return api({
    url: BASE_URL + "/business/banner/create",
    method: "post",
    params,
  });
}
// 修改
export function ApiBusinessUpdate(params) {
  return api({
    url: BASE_URL + "/business/banner/update",
    method: "post",
    params,
  });
}
// 删除
export function ApiBusinessDeleteById(params) {
  return api({
    url: BASE_URL + "/business/banner/deleteById",
    method: "post",
    params,
  });
}

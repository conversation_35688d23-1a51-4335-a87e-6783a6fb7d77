<!-- 出租信息查看 -->
<template>
  <div>
    <!-- 变更合同信息 -->
    <div class="lessorInfo" v-if="contractTypeShow == 2">
      <div class="lessorInfo-tit">
        <div class="tit">变更合同信息</div>
        <div class="upload_file_item">
          <div class="Item">
            <FileAttachmentList
              title="变更补充协议"
              marked="true"
              :ifNeedPreviewOnline="true"
              @deleteFile="(file, fileList) => deleteConFile(file, fileList)"
              :fileList="contractFileList"
            >
            </FileAttachmentList>
          </div>
          <div v-if="!showInfo">
            <my-upload
              businessId="change_supplementary_agreement"
              @handleFileCallback="handleConFileCallback"
            />
          </div>
        </div>
      </div>
    </div>
    <a-form-model
      class="ant-advanced-search-form"
      ref="ruleForm"
      :model="rentalParmas"
      :rules="rules"
      :label-col="formItemLayout.labelCol"
      :wrapper-col="formItemLayout.wrapperCol"
    >
      <!-- 租户信息 -->
      <div class="lessorInfo" v-if="rentalTShow">
        <div class="lessorInfo-tit">
          <div class="tit" v-if="lessorInfoTit">变更租户信息</div>
          <div class="tit" v-else>租户信息</div>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-model-item prop="tenantry" label="承租方">
                <a-input
                  :title="rentalParmas.tenantry"
                  v-if="contractTypeShow == 1"
                  disabled
                  v-model="rentalParmas.tenantry"
                />
                <a-auto-complete
                  v-else
                  placeholder="请输入承租方"
                  :title="rentalParmas.tenantry"
                  :data-source="dataSource"
                  @search="onSearch"
                  @change="tenantryChange"
                  v-model="rentalParmas.tenantry"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-model-item prop="territorialSituation" label="属地情况">
                <a-select
                  @change="onChangeMain"
                  placeholder="请选择属地情况"
                  v-model="rentalParmas.territorialSituation"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in territorialArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col
              :span="8"
              v-if="rentalParmas.territorialSituation == '关联属地'"
            >
              <a-form-model-item prop="relationCompany">
                <a-auto-complete
                  v-model="rentalParmas.relationCompany"
                  :disabled="showInfo"
                  :data-source="relationSource"
                  placeholder="请输入关联企业的名称"
                  style="width: 100%; height: 40px"
                  @search="onSearchRelation"
                />
              </a-form-model-item>
            </a-col>
            <a-col
              :span="8"
              v-if="rentalParmas.territorialSituation == '未属地'"
            >
              <a-form-model-item prop="territorialRemark">
                <a-input
                  v-model="rentalParmas.territorialRemark"
                  :disabled="showInfo"
                  placeholder="请填写未属地原因及属地计划"
                  style="width: 100%; height: 40px"
                  maxLength="50"
                />
              </a-form-model-item>
            </a-col>
          </a-row>

          <div class="upload_file_item">
            <div class="Item">
              <FileAttachmentList
                title="承租方营业执照复印件（盖章版）"
                marked="true"
                :ifNeedPreviewOnline="true"
                :fileList="fileList"
                @deleteFile="(file, fileList) => deleteFile(file, fileList)"
              ></FileAttachmentList>
            </div>
            <div v-if="!showInfo">
              <my-upload
                businessId="tenant_business_license"
                @handleFileCallback="handleFileCallback"
              />
            </div>
          </div>
          <div class="upload_file_item">
            <div class="Item">
              <FileAttachmentList
                title="承租方法人身份证（盖章版）"
                marked="true"
                :ifNeedPreviewOnline="true"
                :fileList="fileList1"
                @deleteFile="(file, fileList) => deleteFile1(file, fileList)"
              ></FileAttachmentList>
            </div>
            <div v-if="!showInfo">
              <my-upload
                businessId="tenantry_id"
                @handleFileCallback="handleFileCallback1"
              />
            </div>
          </div>
          <div class="upload_file_item">
            <div class="Item">
              <FileAttachmentList
                title="信用报告"
                marked="true"
                :ifNeedPreviewOnline="true"
                @deleteFile="(file, fileList) => deleteFile2(file, fileList)"
                :fileList="fileList2"
              >
              </FileAttachmentList>
            </div>
            <div v-if="!showInfo">
              <my-upload
                businessId="credit_report"
                @handleFileCallback="handleFileCallback2"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 出租信息 -->
      <div class="lessorInfo" v-if="clauseTShow">
        <div class="lessorInfo-tit">
          <div class="tit">出租信息</div>
          <div v-if="adressShow">
            <div
              class="titItem"
              style="padding-left: 14px"
              v-for="(item, index) in rentalParmas.contractBuildingInfo"
              :key="item.contractInfoId"
            >
              <p class="mR20" style="margin-right: 16px">
                <span style="color: red">*</span> 出租地址
              </p>
              <a-select
                v-model="item.parkName"
                :disabled="contractTypeShow == 1 ? true : false"
                style="width: 14%; height: 40px; margin-right: 16px"
                @focus="getParkName"
                @change="ParkNameChange(item)"
                placeholder="园区"
              >
                <a-select-option
                  :value="item2.parkName"
                  v-for="item2 in parkNameArr"
                  :key="item2.item"
                >
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span> {{ item2.parkName }}</span>
                    </template>
                    {{ item2.parkName }}
                  </a-tooltip>
                </a-select-option>
              </a-select>
              <a-select
                v-model="item.buildingName"
                :disabled="contractTypeShow == 1 ? true : false"
                style="width: 10%; height: 40px; margin-right: 16px"
                @focus="buildingNameFocus(item)"
                @change="buildingNameChange(item)"
                placeholder="楼号"
              >
                <a-select-option
                  :value="item2"
                  v-for="item2 in buildingNameArr"
                  :key="item2.item"
                >
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span> {{ item2 }}</span>
                    </template>
                    {{ item2 }}
                  </a-tooltip>
                </a-select-option>
              </a-select>
              <a-select
                v-model="item.floorName"
                :disabled="contractTypeShow == 1 ? true : false"
                style="width: 10%; height: 40px; margin-right: 16px"
                @change="floorNameChange(item)"
                @focus="floorNameFocus(item)"
                placeholder="楼层"
              >
                <a-select-option
                  :value="item2"
                  v-for="item2 in floorNameArr"
                  :key="item2.item"
                >
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span> {{ item2 }}</span>
                    </template>
                    {{ item2 }}
                  </a-tooltip>
                </a-select-option>
              </a-select>
              <a-select
                v-model="item.roomName"
                style="width: 10%; height: 40px; margin-right: 16px"
                @focus="roomNameChange(item)"
                @change="onBlur(item)"
                placeholder="房间号"
                :disabled="contractTypeShow == 1 ? true : false"
              >
                <a-select-option
                  :value="item1.roomNumber"
                  v-for="item1 in roomNameArr"
                  :key="item1.item"
                  >{{ item1.roomNumber }}</a-select-option
                >
              </a-select>
              <a-button
                v-if="item.delId == 0 && contractTypeShow != 1"
                @click="addItem"
                style="font-size: 30px; color: #1777ff; border: 0"
                icon="plus-circle"
              />
              <a-button
                v-if="item.delId > 0 && contractTypeShow != 1"
                @click="delItem(index)"
                style="font-size: 30px; color: #fe6a6a; border: 0"
                icon="minus-circle"
              />
            </div>
            <div class="upload_file_item" v-if="clauseTShow">
              <div class="Item">
                <FileAttachmentList
                  title="出租物业的产权证明复印件（盖章版，如果部分没有或者全无，需上传情况说明）"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="(file, fileList) => deleteFile3(file, fileList)"
                  :fileList="fileList3"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="property_right"
                  @handleFileCallback="handleFileCallback3"
                />
              </div>
            </div>
          </div>
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-model-item prop="leaseArea" label="租赁面积">
                <a-input
                  v-model="rentalParmas.leaseArea"
                  disabled
                  style="width: 100%; height: 40px"
                  addon-after="㎡"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item
                prop="accumulateLeaseArea"
                label="累计租赁面积"
              >
                <a-input
                  v-model="rentalParmas.accumulateLeaseArea"
                  style="width: 100%; height: 40px"
                  @input="
                    handleAccumulateLeaseAreaInput(
                      rentalParmas.accumulateLeaseArea
                    )
                  "
                  addon-after="㎡"
                />
              </a-form-model-item>
            </a-col>
            <a-col
              :span="8"
              v-if="
                rentalParmas.leasePurpose == '办公' ||
                !rentalParmas.leasePurpose
              "
            >
              <a-form-model-item prop="leasePurpose" label="租赁用途">
                <a-select
                  v-model="rentalParmas.leasePurpose"
                  :disabled="
                    cluShowInfo && isChangeShow == 1 && contractTypeShow == 2
                  "
                  placeholder="请选择租赁用途"
                  style="width: 100%; height: 40px"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in rentalPurposeArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row
            :gutter="24"
            v-if="
              rentalParmas.leasePurpose == '其他' ||
              rentalParmas.leasePurpose == '商业'
            "
          >
            <a-col :span="8">
              <a-form-model-item prop="leasePurpose" label="租赁用途">
                <a-select
                  v-model="rentalParmas.leasePurpose"
                  :disabled="
                    cluShowInfo && isChangeShow == 1 && contractTypeShow == 2
                  "
                  placeholder="请选择租赁用途"
                  style="width: 100%; height: 40px"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in rentalPurposeArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item prop="leasePurposeInfo" label="">
                <a-input
                  v-model="rentalParmas.leasePurposeInfo"
                  placeholder="请输入内容"
                  :disabled="
                    cluShowInfo && isChangeShow == 1 && contractTypeShow == 2
                  "
                  style="width: 100%; height: 40px"
                  maxLength="20"
                />
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-model-item prop="rentalUnitPrice" label="租赁单价">
                <a-input
                  type="number"
                  step="0.01"
                  @input="handleInput1(rentalParmas.rentalUnitPrice)"
                  v-model="rentalParmas.rentalUnitPrice"
                  :disabled="showInfo"
                  placeholder="请输入租赁单价"
                  style="width: 100%; height: 40px; line-height: 40px"
                  addon-after="元/㎡/天"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="16">
              <div
                style="
                  width: 100%;
                  display: flex;
                  align-items: center;
                  padding-left: 12px;
                  margin-bottom: 0;
                "
              >
                <div class="titItem" style="align-items: center">
                  <p class="mR20"><span style="color: red">*</span>定价依据</p>
                  <div class="basisItem">
                    <FileAttachmentList
                      @deleteFile="
                        (file, fileList) => deleteFile4(file, fileList)
                      "
                      :ifNeedPreviewOnline="true"
                      :fileList="fileList4"
                    >
                    </FileAttachmentList>
                  </div>
                  <div v-if="!showInfo">
                    <my-upload
                      businessId="pricing_basis"
                      @handleFileCallback="handleFileCallback4"
                    />
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-model-item prop="annualRent" label="年租金">
                <a-input
                  type="number"
                  step="0.01"
                  v-model="rentalParmas.annualRent"
                  @input="handleInput2(rentalParmas.annualRent)"
                  placeholder="请输入年租金"
                  style="width: 100%; height: 40px; margin-right: 0"
                  addon-after="元"
                  maxLength="20"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item prop="leaseDeposit" label="租赁保证金">
                <a-input
                  v-model="rentalParmas.leaseDeposit"
                  type="number"
                  addon-after="元"
                  @input="handleInput5(rentalParmas.leaseDeposit)"
                  placeholder="请输入租赁保证金"
                  style="width: 100%; height: 40px; margin-right: 0"
                  maxLength="20"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item prop="priceIncrease" label="价格递增机制">
                <a-select
                  v-model="rentalParmas.priceIncrease"
                  placeholder="请选择价格递增机制"
                  style="width: 100%; height: 40px"
                  @change="priceIncreaseChange"
                >
                  <a-select-option
                    :value="item.value"
                    v-for="item in priceIncreaseArr"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <div v-if="leaseDepositStatu">
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="租赁保证金低于3个月租金情况说明(盖章版)"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="(file, fileList) => deleteFile6(file, fileList)"
                  :fileList="fileList6"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="lease_deposit_below_3_months_explanation"
                  @handleFileCallback="handleFileCallback6"
                />
              </div>
            </div>
          </div>
          <div v-if="priceIncreaseStatu">
            <a-row
              v-for="(item, index) in rentalParmas.multTenancyInfoList"
              :key="item"
              :gutter="24"
              style="position: relative"
            >
              <a-col :span="8">
                <a-form-model-item :label="`租期` + (index + 1)" required>
                  <a-range-picker
                    format="YYYY-MM-DD"
                    :value="[item.startTime, item.endTime]"
                    :disabled="showInfo"
                    :placeholder="['请选择', '请选择']"
                    style="width: 100%"
                    @change="
                      (date, dateString) =>
                        onChangezuqi(date, dateString, index)
                    "
                  />
                </a-form-model-item>
              </a-col>

              <a-col :span="8">
                <a-form-model-item label="租赁单价" required>
                  <!-- step="0.01"取消浏览器对 type="number"的默认提示 -->
                  <a-input
                    type="number"
                    step="0.01"
                    @input="handleInput3(index, item.rentalUnitPrice)"
                    v-model="item.rentalUnitPrice"
                    :disabled="showInfo"
                    placeholder="请输入租赁单价"
                    style="width: 100%; height: 40px; line-height: 40px"
                    addon-after="元/㎡/天"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item label="年租金" required>
                  <a-input
                    type="number"
                    step="0.01"
                    v-model="item.annualRent"
                    @input="handleInput4(index, item.annualRent)"
                    placeholder="请输入年租金"
                    style="width: 100%; height: 40px; margin-right: 0"
                    addon-after="元"
                    maxLength="20"
                  />
                </a-form-model-item>
              </a-col>
              <a-button
                v-if="item.delId == 0"
                @click="addItem2"
                style="
                  font-size: 30px;
                  color: #1777ff;
                  border: 0;
                  position: absolute;
                  top: -4px;
                  right: -15px;
                "
                icon="plus-circle"
              />
              <a-button
                v-if="item.delId > 0"
                @click="delItem2(index)"
                style="
                  font-size: 30px;
                  color: #fe6a6a;
                  border: 0;
                  position: absolute;
                  top: -4px;
                  right: -15px;
                "
                icon="minus-circle"
              />
            </a-row>
          </div>
          <div v-if="priceIncreaseStatu1">
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="无价格递增情况说明（盖章版）"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="(file, fileList) => deleteFile5(file, fileList)"
                  :fileList="fileList5"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="non_incremental_pricing"
                  @handleFileCallback="handleFileCallback5"
                />
              </div>
            </div>
          </div>
          <template>
            <a-row :gutter="24">
              <a-col :span="8">
                <!-- :disabled="cluShowInfo && isChangeShow == 1" -->
                <a-form-model-item prop="leaseTermStart" label="租期起">
                  <a-date-picker
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :disabled="statu == 1"
                    v-model="rentalParmas.leaseTermStart"
                    :disabled-date="disabledStartDate"
                    style="width: 100%"
                    @change="leaseTimeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseTermEnd" label="租期止">
                  <a-date-picker
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    v-model="rentalParmas.leaseTermEnd"
                    :disabled="!rentalParmas.leaseTermStart || statu == 1"
                    :disabled-date="disabledEndDate"
                    style="width: 100%"
                    @change="leaseTimeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item prop="leaseTerm" label="总租期">
                  <a-input
                    v-model="rentalParmas.leaseTerm"
                    disabled
                    addon-after="年"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </template>

          <!-- <div v-if="processFlow == 1">
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="特殊通道情况说明(pdf盖章版)"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="(file, fileList) => deleteFile8(file, fileList)"
                  :fileList="fileList8"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="special_channel_description"
                  @handleFileCallback="handleFileCallback8"
                />
              </div>
            </div>
          </div> -->
          <div style="position: relative">
            <a-row
              :gutter="24"
              v-for="(item, index) in rentalParmas.contractFreePeriod"
              :key="item.id"
            >
              <a-col :span="8">
                <a-form-model-item label="免租期起">
                  <a-date-picker
                    format="YYYY-MM-DD"
                    :disabled="showInfo"
                    v-model="item.startTime"
                    style="width: 100%"
                    @change="rentFreeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item label="免租期止">
                  <a-date-picker
                    format="YYYY-MM-DD"
                    :disabled="showInfo"
                    v-model="item.endTime"
                    style="width: 100%"
                    @change="rentFreeChange"
                  />
                </a-form-model-item>
              </a-col>
              <a-button
                v-if="item.delId == 0"
                @click="addItem1"
                style="font-size: 30px; color: #1777ff; border: 0"
                icon="plus-circle"
              />
              <a-button
                v-if="item.delId > 0"
                @click="delItem1(index)"
                style="font-size: 30px; color: #fe6a6a; border: 0"
                icon="minus-circle"
              />
            </a-row>
            <div
              class="titItem"
              style="
                width: 30.5%;
                padding-left: 0;
                position: absolute;
                right: 13px;
                top: 0;
              "
            >
              <p class="mR20">总免租期</p>
              <a-input
                v-model="rentalParmas.rentFreeDays"
                addon-after="天"
                disabled
              />
            </div>
          </div>

          <div class="rentFree">
            <a-icon type="info-circle" />
            <span style="margin-left: 25px"
              >如分段给予免租期，请分多段录入。</span
            >
          </div>
          <div v-if="contractFreePeriodStatu">
            <div class="upload_file_item">
              <div class="Item">
                <FileAttachmentList
                  title="免租期超过租期的10%或超过90天情况说明(盖章版)"
                  marked="true"
                  :ifNeedPreviewOnline="true"
                  @deleteFile="(file, fileList) => deleteFile7(file, fileList)"
                  :fileList="fileList7"
                >
                </FileAttachmentList>
              </div>
              <div v-if="!showInfo">
                <my-upload
                  businessId="rent_free_period_exceed_explanation"
                  @handleFileCallback="handleFileCallback7"
                />
              </div>
            </div>
          </div>
          <div v-if="clauseTShow && isChangeShow == 1 && contractTypeShow == 2">
            <div class="titItem1">
              <p style="width: 108px; margin-right: 13px">
                <span style="color: red">*</span> 变更具体条款及原因
              </p>
              <a-textarea
                compact="true"
                style="width: 50%; line-height: 20px"
                v-model="rentalParmas.alterReason"
                placeholder="请输入变更具体条款及原因"
                :disabled="showInfo"
                :auto-size="{ minRows: 4, maxRows: 6 }"
              />
            </div>
          </div>
          <div
            style="
              width: 94%;
              margin: 0 auto;
              padding: 12px;
              margin-bottom: 30px;
              background: #f9f9f9;
            "
          >
            <div class="titItem" style="margin-left: 10px; padding-left: 0">
              <p style="margin-right: 25px">合同中是否对以下必备条款进行约束</p>
            </div>
            <div style="margin-left: 15px; font-size: 16px; color: #1d2129">
              <p>
                1.合同终止情形及免责条款：如因政府市政搬迁、土地收储、无证建筑拆除等情形，公司有权终止合同并予以免责；
              </p>
              <p>
                2.违约责任条款：对于未按期缴纳租金的情况，应约定滞纳金；对于逾期三个月及以上未按时付清租金的情况，应明确公司有权解除租赁合同，收回物业，且不承担赔偿责任；
              </p>
              <p>
                3.租约束条款：公司应严格控制转租行为，承租人与次承租人的租赁合同必须报公司备案。严禁层层转租行为。
              </p>
            </div>
            <div class="titItem" style="padding-left: 15px">
              <a-radio-group
                :disabled="showInfo"
                v-model="rentalParmas.essentialSituation"
              >
                <a-radio :value="'1'"> 是 </a-radio>
                <a-radio :value="'0'"> 否 </a-radio>
              </a-radio-group>
            </div>
          </div>
          <div>
            <div class="titItem1">
              <p style="width: 90px; margin-right: -8px; margin-left: 48px">
                特殊条款
              </p>
              <a-textarea
                style="width: 50%; line-height: 20px"
                v-model="rentalParmas.specialTerms"
                placeholder="请输入特殊条款"
                :disabled="showInfo"
                :auto-size="{ minRows: 4, maxRows: 6 }"
                maxLength="40"
              />
            </div>
          </div>
        </div>
      </div>
    </a-form-model>
  </div>
</template>

<script>
import moment from "moment";
import FileAttachmentList from "@/components/FileAttachmentList";
import myUpload from "@/components/Importer";
import debounce from "lodash/debounce";
import {
  getAreaTotalApi,
  findFloorsByBuildingInfoApi,
  queryAllParkApi1,
  queryAllBuildingApi,
  findRoomsByBuildingInfoApi,
  queryTenantryApi,
} from "@/pages/index/data/api/RegistrationRecordInquery";
export default {
  props: {
    processFlow: {
      type: Number,
      default: 0,
    },
    contractType: {
      type: String,
      required: false,
    },
    isChange: {
      type: String,
      required: false,
    },
    oldRentalParmas: {
      type: Object,
      required: false,
    },
  },
  components: {
    myUpload,
    FileAttachmentList,
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 16 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 17 },
        },
      },
      rules: {
        tenantry: [
          { required: true, message: "请输入承租方", trigger: "blur" },
        ],
        territorialSituation: [
          { required: true, message: "请选择属地情况", trigger: "change" },
        ],
        relationCompany: [
          {
            required: true,
            message: "必填项请输入",
            trigger: "change",
          },
        ],
        territorialRemark: [
          {
            required: true,
            message: "必填项请输入",
            trigger: "change",
          },
        ],
        leasePurpose: [
          { required: true, message: "请选择租赁用途", trigger: "blur" },
        ],
        leasePurposeInfo: [
          { required: true, message: "请输入内容", trigger: "blur" },
        ],
        rentalUnitPrice: [
          { required: true, message: "请输入租赁单价", trigger: "blur" },
        ],
        accumulateLeaseArea: [
          { required: true, message: "请输入累计租赁面积", trigger: "blur" },
        ],
        annualRent: [
          { required: true, message: "请输入年租金", trigger: "blur" },
        ],
        leaseDeposit: [
          { required: true, message: "请输入租赁保证金", trigger: "blur" },
        ],
        priceIncrease: [
          { required: true, message: "请选择价格递增机制", trigger: "change" },
        ],
        leaseTermStart: [
          { required: true, message: "请选择租期开始时间", trigger: "change" },
        ],
        leaseTermEnd: [
          { required: true, message: "请选择租期结束时间", trigger: "change" },
        ],
      },
      rentalPurposeArr: [
        { label: "办公", value: "办公" },
        { label: "商业", value: "商业" },
        { label: "其他", value: "其他" },
      ],
      rentalInfoRules: false,
      statu: "",
      rentalTShow: false,
      clauseTShow: false,
      adressShow: false,
      lessorInfoTit: false,
      priceIncreaseStatu: false,
      priceIncreaseStatu1: false,
      //合同文件上传状态
      tenantBusinessLicenseFile: false,
      tenantryIdFile: false,
      creditReportFile: false,
      propertyRightFile: false,
      pricingBasisFile: false,
      nonIncrementalPricing: false,
      leaseDepositBelowFile: false,
      contractFreePeriodFile: false,

      changeSupplementaryAgreementFile: false,
      rentalFileRules: false,
      contractTypeShow: "",
      isChangeShow: "",
      oldleaseTermEnd: "",
      showInfo: false,
      parkNameArr: [],
      buildingNameArr: [],
      floorNameArr: [],
      roomNameArr: [],
      fileList: [],
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
      fileList6: [],
      fileList7: [],
      // fileList8: [],
      contractFileList: [],
      rentalFlie: [],
      dataSource: [],
      relationSource: [],
      territorialArr: [
        { label: "镇属", value: "镇属" },
        { label: "区属", value: "区属" },
        { label: "关联属地", value: "关联属地" },
        { label: "未属地", value: "未属地" },
      ],
      priceIncreaseArr: [
        { label: "是", value: "1" },
        { label: "未满3年(含三年)无价格递增", value: "0" },
        { label: "租期超3年无价格递增", value: "2" },
      ],
      rentalParmas: {
        tenantry: "",
        territorialSituation: undefined,
        territorialRemark: "",
        relationCompany: "",
        leaseArea: 0,
        annualRent: "", //年租金
        leaseDeposit: "", //租赁保证金
        leasePurpose: undefined,
        leasePurposeInfo: "",
        rentalUnitPrice: "",
        rentFreeDays: "",
        leaseTermStart: "",
        leaseTermEnd: "",
        leaseTerm: "",
        priceIncrease: undefined,
        accumulateLeaseArea: "",
        specialTerms: "",
        essentialSituation: "1",
        alterReason: "",
        contractFreePeriod: [
          {
            delId: 0,
            startTime: "",
            endTime: "",
          },
        ], //租赁合同免租期数据
        multTenancyInfoList: [], //分段租金递增
        contractBuildingInfo: [
          {
            delId: 0,
            parkName: undefined,
            contractInfoId: undefined,
            buildingName: undefined,
            floorName: undefined,
            roomName: undefined,
          },
        ],
      },
    };
  },
  created() {
    // this.getStatu();
    this.statu = this.$route.query.statu;
    console.log(
      this.statu,
      this.oldRentalParmas.leasePurpose,
      "this.oldRentalParmas.leasePurpose"
    );
  },
  computed: {
    leaseDepositStatu() {
      // 租赁保证金 年租金
      // console.log('租赁保证金:',this.rentalParmas.leaseDeposit ,'年租金:',this.rentalParmas.annualRent , this.rentalParmas.leaseDeposit &&this.rentalParmas.annualRent && (this.rentalParmas.leaseDeposit < this.rentalParmas.annualRent/4))
      if (
        this.rentalParmas.leaseDeposit &&
        this.rentalParmas.annualRent &&
        parseInt(this.rentalParmas.leaseDeposit) <
          parseInt(this.rentalParmas.annualRent / 4)
      ) {
        return true;
      } else {
        return false;
      }
    },
    contractFreePeriodStatu() {
      let leaseTermDays; //总租期天数
      if (this.rentalParmas.leaseTermStart && this.rentalParmas.leaseTermEnd) {
        leaseTermDays =
          moment(this.rentalParmas.leaseTermEnd).diff(
            moment(this.rentalParmas.leaseTermStart),
            "days",
            false
          ) + 1;
      } else {
        leaseTermDays = 0;
      }
      console.log(
        "总租期天数:",
        leaseTermDays,
        "总免租期:",
        this.rentalParmas.rentFreeDays
      );
      // 免租期超过租期的10%或超过90天情况说明
      if (
        (leaseTermDays &&
          this.rentalParmas.rentFreeDays &&
          this.rentalParmas.rentFreeDays / leaseTermDays > 0.1) ||
        this.rentalParmas.rentFreeDays > 90
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    // processFlow: {
    //   handler(newVal) {
    //     if (newVal == 0) {
    //       this.fileList8 = [];
    //     }
    //   },
    //   immediate: true,
    //   deep: true
    // },
    contractType: {
      handler() {
        console.log("contractType changed:", this.contractType);
        this.$nextTick(() => {
          this.fileList = [];
          this.fileList1 = [];
          this.fileList2 = [];
          this.fileList3 = [];
          this.fileList4 = [];
          this.fileList5 = [];
          this.fileList6 = [];
          this.fileList7 = [];
          // this.fileList8 = [];
          this.contractFileList = [];
          this.rentalFlie = [];
          this.contractTypeShow = this.contractType;
          if (this.contractTypeShow == 0) {
            this.rentalTShow = true;
            this.clauseTShow = true;
            this.adressShow = true;
            this.lessorInfoTit = false;
          } else if (this.contractTypeShow == 1) {
            this.adressShow = true;
            this.rentalTShow = true;
            this.clauseTShow = true;
            this.lessorInfoTit = false;
          } else if (this.contractTypeShow == 3) {
            this.lessorInfoTit = false;
          }
        });
      },
      immediate: true,
      deep: true,
    },
    "rentalParmas.leasePurpose": {
      handler() {
        this.$nextTick(() => {
          this.rentalParmas.leasePurposeInfo = "";
        });
      },
      immediate: true,
      deep: true,
    },
    isChange: {
      handler() {
        this.$nextTick(() => {
          this.fileList = [];
          this.fileList1 = [];
          this.fileList2 = [];
          this.fileList3 = [];
          this.fileList4 = [];
          this.fileList5 = [];
          this.fileList6 = [];
          this.fileList7 = [];
          // this.fileList8 = [];
          this.contractFileList = [];
          this.isChangeShow = this.isChange;
          this.rentalFlie = [];
          if (this.contractTypeShow == 2 && this.isChangeShow == 0) {
            this.rentalTShow = true;
            this.clauseTShow = false;
            this.adressShow = false;
            this.lessorInfoTit = true;
          } else if (this.contractTypeShow == 2 && this.isChangeShow === 1) {
            this.rentalTShow = false;
            this.clauseTShow = true;
            this.adressShow = false;
            this.cluShowInfo = true;
          }
        });
      },
      immediate: true,
      deep: true,
    },
    oldRentalParmas: {
      handler() {
        this.$nextTick(() => {
          this.oldleaseTermEnd = this.oldRentalParmas.leaseTermEnd;
          if (this.contractType == 1 || this.contractType == 2) {
            this.$set(
              this.rentalParmas,
              "tenantry",
              this.oldRentalParmas.tenantry
            );
            this.$set(
              this.rentalParmas,
              "leaseArea",
              this.oldRentalParmas.leaseArea
            );
            this.$set(
              this.rentalParmas,
              "accumulateLeaseArea",
              this.oldRentalParmas.accumulateLeaseArea
            );

            this.$set(
              this.rentalParmas,
              "leasePurpose",
              this.oldRentalParmas.leasePurpose
                ? this.oldRentalParmas.leasePurpose
                : undefined
            );

            this.$set(
              this.rentalParmas,
              "leaseDeposit",
              this.oldRentalParmas.leaseDeposit
            );
            this.$set(
              this.rentalParmas,
              "rentalUnitPrice",
              this.oldRentalParmas.rentalUnitPrice
            );
            this.$set(
              this.rentalParmas,
              "contractBuildingInfo",
              this.oldRentalParmas.contractBuildingInfo
            );
            this.$set(
              this.rentalParmas,
              "annualRent",
              this.oldRentalParmas.annualRent
            );
          }
          if (this.isChangeShow == 0) {
            this.$set(this.rentalParmas, "tenantry", "");
            this.$set(
              this.rentalParmas,
              "leaseTermStart",
              this.oldRentalParmas.leaseTermStart
            );
            this.$set(
              this.rentalParmas,
              "leaseTermEnd",
              this.oldRentalParmas.leaseTermEnd
            );
          }
          if (this.isChangeShow == 1) {
            this.$set(
              this.rentalParmas,
              "territorialSituation",
              this.oldRentalParmas.territorialSituation
            );
            this.$set(
              this.rentalParmas,
              "relationCompany",
              this.oldRentalParmas.relationCompany
            );
            this.$set(
              this.rentalParmas,
              "territorialRemark",
              this.oldRentalParmas.territorialRemark
            );
            this.$set(
              this.rentalParmas,
              "leaseTermStart",
              this.oldRentalParmas.leaseTermStart
            );
            this.$set(
              this.rentalParmas,
              "leaseTermEnd",
              this.oldRentalParmas.leaseTermEnd
            );
            this.$set(
              this.rentalParmas,
              "leaseTerm",
              this.oldRentalParmas.leaseTerm
            );
          }
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleAccumulateLeaseAreaInput(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.rentalParmas.accumulateLeaseArea = val;
    },
    onSearch: debounce(function (searchText) {
      let parmas = {
        contractType: 0,
        tenantry: searchText,
      };
      queryTenantryApi(parmas).then((res) => {
        if (res.data.length > 50) {
          this.dataSource = res.data.splice(0, 50);
        } else {
          this.dataSource = res.data;
          setTimeout(() => {
            if (!res.data.length) {
              this.$message.error("没有相关数据,请重新输入");
              this.rentalParmas.tenantry = "";
            }
          }, 500);
        }
      });
    }, 300), // 300ms防抖
    tenantryChange() {
      if (this.rentalParmas.leaseArea > 0) {
        this.areaCount();
      }
    },
    onSearchRelation(searchText) {
      let parmas = {
        contractType: 0,
        tenantry: searchText,
      };
      queryTenantryApi(parmas).then((res) => {
        if (res.data.length > 50) {
          this.relationSource = res.data.splice(0, 50);
        } else {
          this.relationSource = res.data;
        }
      });
    },
    getStatu() {
      this.statu = this.$route.query.statu;
      this.rentalParmas.tenantry = this.$route.query.tenantry;
      if (this.statu == 2) {
        this.showInfo = false;
        this.cluShowInfo = false;
      } else {
        this.showInfo = false;
        this.cluShowInfo = false;
      }
    },
    getParkName() {
      queryAllParkApi1("").then((res) => {
        this.parkNameArr = res.data.parkList;
      });
    },
    buildingNameFocus(v) {
      if (v.parkName) {
        let parmas = {
          parkName: v.parkName,
        };
        queryAllBuildingApi(parmas).then((res) => {
          this.buildingNameArr = res.data.buildingNumbers;
        });
      } else {
        this.buildingNameArr = [];
      }
    },
    floorNameFocus(v) {
      let parmas = {
        buildingNumber: v.buildingName, //楼栋号
        floorNumber: v.floorName, // 楼层
        parkName: v.parkName, // 园区名称
        tenantry: this.rentalParmas.tenantry, // 承租方
      };
      findFloorsByBuildingInfoApi(parmas).then((res) => {
        this.floorNameArr = res.data;
      });
    },
    ParkNameChange(item) {
      this.$set(item, "buildingName", undefined);
      this.$set(item, "floorName", undefined);
      this.$set(item, "roomName", undefined);
      this.$set(item, "roomArea", 0);
      this.areaCount();
    },
    buildingNameChange(item) {
      this.$set(item, "floorName", undefined);
      this.$set(item, "roomName", undefined);
      this.$set(item, "roomArea", 0);
      this.areaCount();
    },
    floorNameChange(item) {
      this.$set(item, "roomName", undefined);
      this.$set(item, "roomArea", 0);
      this.areaCount();
    },
    //主体变更上传文件
    onChangeMain(e) {
      if (e != "未属地") {
        this.rentalParmas.territorialRemark = "";
      }
      if (e != "关联属地") {
        this.rentalParmas.relationCompany = "";
      }
    },
    handleInput1(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.rentalParmas.rentalUnitPrice = val;
    },
    handleInput2(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.rentalParmas.annualRent = val;
    },
    handleInput3(i, value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.rentalParmas.multTenancyInfoList[i].rentalUnitPrice = val;
    },
    handleInput4(i, value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.rentalParmas.multTenancyInfoList[i].annualRent = val;
    },
    handleInput5(value) {
      //大于等于0，且只能输入2位小数
      let val = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, "$1");
      if (val == null || val == undefined || val == "") {
        val = "";
      }
      this.rentalParmas.leaseDeposit = val;
    },

    addItem() {
      console.log(this.rentalParmas.contractBuildingInfo);
      if (this.rentalParmas.contractBuildingInfo[0].roomName) {
        let len = this.rentalParmas.contractBuildingInfo.length - 1;
        this.rentalParmas.contractBuildingInfo.push({
          delId: Number(this.rentalParmas.contractBuildingInfo[len].delId) + 1,
          parkName: undefined,
          contractInfoId: undefined,
          buildingName: undefined,
          floorName: undefined,
          roomName: undefined,
        });
      }
    },
    delItem(v) {
      this.rentalParmas.contractBuildingInfo.splice(v, 1);
      this.areaCount();
    },
    roomNameChange(v) {
      let parmas = {
        buildingNumber: v.buildingName, //楼栋号
        floorNumber: v.floorName, // 楼层
        parkName: v.parkName, // 园区名称
        tenantry: this.rentalParmas.tenantry, // 承租方
      };
      findRoomsByBuildingInfoApi(parmas).then((res) => {
        this.roomNameArr = res.data;
      });
    },
    areaCount() {
      let list = this.rentalParmas.contractBuildingInfo;
      console.log(list);
      if (list.length == 1) {
        this.rentalParmas.leaseArea = Number(list[0].roomArea).toFixed(2);
      } else {
        let num = 0;
        list.forEach((e) => {
          if (e.roomArea) {
            num += Number(e.roomArea);
          }
        });
        this.rentalParmas.leaseArea = num.toFixed(2);
      }
      let parmas1 = {
        tenantry: this.rentalParmas.tenantry, // 承租方
      };
      getAreaTotalApi(parmas1).then((res) => {
        if (res.data) {
          this.rentalParmas.accumulateLeaseArea = Number(
            Number(this.rentalParmas.leaseArea) + Number(res.data)
          ).toFixed(2);
        } else {
          this.rentalParmas.accumulateLeaseArea = Number(
            this.rentalParmas.leaseArea
          ).toFixed(2);
        }
      });
    },
    priceIncreaseChange(v) {
      if (v == 1) {
        this.priceIncreaseStatu = true;
        this.priceIncreaseStatu1 = false;
        this.rentalParmas.multTenancyInfoList = [
          {
            delId: 0,
            startTime: "",
            endTime: "",
            rentalUnitPrice: "",
            annualRent: "",
          },
        ];
      } else if (v == 2) {
        this.priceIncreaseStatu = false;
        this.priceIncreaseStatu1 = true;
      } else {
        this.priceIncreaseStatu = false;
        this.priceIncreaseStatu1 = false;
        this.rentalParmas.multTenancyInfoList = [];
      }
    },
    leaseTimeChange() {
      console.log(
        this.rentalParmas.leaseTermStart,
        this.rentalParmas.leaseTermEnd,
        "租期开始和结束时间"
      );

      // this.$set(this.rentalParmas, "leaseTermEnd", "");
      if (this.rentalParmas.leaseTermStart && this.rentalParmas.leaseTermEnd) {
        let num = moment(this.rentalParmas.leaseTermEnd).diff(
          moment(this.rentalParmas.leaseTermStart),
          "days",
          false
        );
        this.rentalParmas.leaseTerm = (Number(num) / 365).toFixed(2);
      } else {
        this.rentalParmas.leaseTerm = "";
      }
    },
    disabledStartDate(current) {
      if (this.processFlow == 0) {
        if (
          this.contractType == 1 &&
          this.oldleaseTermEnd &&
          moment().valueOf() < moment(this.oldleaseTermEnd).valueOf()
        ) {
          return (
            current && current < moment(this.oldleaseTermEnd).endOf("days")
          );
        } else {
          return current && current < moment().subtract(1, "days");
        }
      }
    },
    disabledEndDate(current) {
      return (
        current &&
        current < moment(this.rentalParmas.leaseTermStart).subtract(1, "days")
      );
    },
    rentFreeChange() {
      let num = 0;
      this.rentalParmas.contractFreePeriod.forEach((e) => {
        if (e.startTime && e.endTime) {
          num += moment(e.endTime).diff(moment(e.startTime), "days", false) + 1;
        }
      });
      this.rentalParmas.rentFreeDays = num;
    },
    onChangezuqi(date, dateString, i) {
      this.rentalParmas.multTenancyInfoList[i].startTime = dateString[0];
      this.rentalParmas.multTenancyInfoList[i].endTime = dateString[1];
    },
    addItem1() {
      let len = this.rentalParmas.contractFreePeriod.length - 1;
      this.rentalParmas.contractFreePeriod.push({
        delId: this.rentalParmas.contractFreePeriod[len].delId + 1,
        startTime: "",
        endTime: "",
      });
    },
    delItem1(v) {
      this.rentalParmas.contractFreePeriod.splice(v, 1);
      this.rentFreeChange();
    },

    addItem2() {
      let len = this.rentalParmas.multTenancyInfoList.length - 1;
      this.rentalParmas.multTenancyInfoList.push({
        delId: this.rentalParmas.multTenancyInfoList[len].delId + 1,
        startTime: "",
        endTime: "",
        rentalUnitPrice: "",
        annualRent: "",
      });
    },
    delItem2(v) {
      this.rentalParmas.multTenancyInfoList.splice(v, 1);
    },
    handleFileCallback: function (file) {
      let tmpList = this.fileList;
      tmpList.push(file);
      this.tenantBusinessLicenseFile = true;
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
      this.$set(this, "fileList", tmpList);
    },
    deleteFile: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.tenantBusinessLicenseFile = false;
      }
      this.fileList = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback1: function (file) {
      let tmpList = this.fileList1;
      tmpList.push(file);
      this.tenantryIdFile = true;
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile1: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.tenantryIdFile = false;
      }
      this.fileList1 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback2: function (file) {
      let tmpList = this.fileList2;
      tmpList.push(file);
      this.creditReportFile = true;
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile2: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.creditReportFile = false;
      }
      this.fileList2 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback3: function (file) {
      this.propertyRightFile = true;
      let tmpList = this.fileList3;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile3: function (file, fileList) {
      if (fileList.length == 0) {
        this.propertyRightFile = false;
      }
      this.fileList3 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback4: function (file) {
      this.pricingBasisFile = true;
      let tmpList = this.fileList4;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile4: function (file, fileList) {
      if (fileList.length == 0) {
        this.pricingBasisFile = false;
      }
      this.fileList4 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback5: function (file) {
      this.nonIncrementalPricing = true;
      let tmpList = this.fileList5;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile5: function (file, fileList) {
      if (fileList.length == 0) {
        this.nonIncrementalPricing = false;
      }
      this.fileList5 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback6: function (file) {
      this.leaseDepositBelowFile = true;
      let tmpList = this.fileList6;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteFile6: function (file, fileList) {
      if (fileList.length == 0) {
        this.leaseDepositBelowFile = false;
      }
      this.fileList6 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    handleFileCallback7: function (file) {
      this.contractFreePeriodFile = true;
      let tmpList = this.fileList7;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },

    deleteFile7: function (file, fileList) {
      if (fileList.length == 0) {
        this.contractFreePeriodFile = false;
      }
      this.fileList7 = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },

    handleConFileCallback: function (file) {
      this.changeSupplementaryAgreementFile = true;
      let tmpList = this.contractFileList;
      tmpList.push(file);
      let list = this.rentalFlie;
      this.rentalFlie = list.concat(tmpList);
    },
    deleteConFile: function (file, fileList) {
      if (fileList.length == 0) {
        this.changeSupplementaryAgreementFile = false;
      }
      this.contractFileList = fileList;
      this.rentalFlie = this.rentalFlie.filter(
        (item) => item.fileId !== file.fileId
      );
    },
    onClear() {
      this.$refs.ruleForm.resetFields();
      Object.keys(this.rentalParmas).forEach(
        (k) => (this.rentalParmas[k] = "")
      );
      this.rentalParmas.contractFreePeriod = [
        {
          delId: 0,
          startTime: "",
          endTime: "",
        },
      ];
      this.rentalParmas.contractBuildingInfo = [
        {
          delId: 0,
          parkName: undefined,
          contractInfoId: undefined,
          buildingName: undefined,
          floorName: undefined,
          roomName: undefined,
        },
      ];
      this.rentalParmas.leaseArea = 0;
      this.rentalParmas.territorialSituation = undefined;
      this.rentalParmas.priceIncrease = undefined;
      this.rentalParmas.leasePurpose = undefined;
      this.rentalParmas.leasePurposeInfo = "";
      this.rentalParmas.accumulateLeaseArea = "";
      this.rentalParmas.essentialSituation = "1";
      //文件上传状态
      this.tenantBusinessLicenseFile = false;
      this.tenantryIdFile = false;
      this.creditReportFile = false;
      this.propertyRightFile = false;
      this.pricingBasisFile = false;
      this.nonIncrementalPricing = false;
      this.leaseDepositBelowFile = false;
      this.contractFreePeriodFile = false;
      this.changeSupplementaryAgreementFile = false;
      this.priceIncreaseStatu = false;
      this.priceIncreaseStatu1 = false;
      this.rentalParmas.multTenancyInfoList = [];
    },
    onBlur(v) {
      this.roomNameArr.forEach((e) => {
        if (v.roomName == e.roomNumber) {
          this.$set(v, "roomArea", e.roomArea);
        }
      });
      this.areaCount();
    },
    onChange() {
      this.$emit("rentalInfo", this.rentalParmas);
      this.$emit("call", this.rentalFlie);
    },
    onFileUp() {
      if (this.contractTypeShow == 0 || this.contractTypeShow == 1) {
        if (
          this.tenantBusinessLicenseFile &&
          this.tenantryIdFile &&
          this.creditReportFile &&
          this.propertyRightFile &&
          this.pricingBasisFile &&
          (!this.priceIncreaseStatu1 ||
            (this.priceIncreaseStatu1 && this.nonIncrementalPricing)) &&
          (!this.leaseDepositStatu ||
            (this.leaseDepositStatu && this.leaseDepositBelowFile)) &&
          (!this.contractFreePeriodStatu ||
            (this.contractFreePeriodStatu && this.contractFreePeriodFile))
        ) {
          this.rentalFileRules = true;
        } else {
          this.rentalFileRules = false;
        }
      }
      if (this.contractTypeShow == 2 && this.isChangeShow == 0) {
        if (
          this.changeSupplementaryAgreementFile &&
          this.tenantBusinessLicenseFile &&
          this.tenantryIdFile &&
          this.creditReportFile
        ) {
          this.rentalFileRules = true;
        } else {
          this.rentalFileRules = false;
        }
      }
      if (this.contractTypeShow == 2 && this.isChangeShow == 1) {
        if (
          this.changeSupplementaryAgreementFile &&
          this.pricingBasisFile &&
          (!this.priceIncreaseStatu1 ||
            (this.priceIncreaseStatu1 && this.nonIncrementalPricing)) &&
          (!this.leaseDepositStatu ||
            (this.leaseDepositStatu && this.leaseDepositBelowFile)) &&
          (!this.contractFreePeriodStatu ||
            (this.contractFreePeriodStatu && this.contractFreePeriodFile))
        ) {
          this.rentalFileRules = true;
        } else {
          this.rentalFileRules = false;
        }
      }
      this.$emit("rentalFileRules", this.rentalFileRules);
    },
    onFileRequireStatus() {
      console.log(
        "==租赁保证金低于3个月租金==",
        this.leaseDepositStatu,
        "==免租期超过租期的10%或超过90天==",
        this.contractFreePeriodStatu
      );
      this.$emit(
        "rentFileRequireStatus",
        this.leaseDepositStatu,
        this.contractFreePeriodStatu
      );
    },
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        console.log(this.rentalParmas, "999999");
        if (valid) {
          this.rentalInfoRules = true;
          this.$emit("rentalInfoRules", this.rentalInfoRules);
        } else {
          this.rentalInfoRules = false;
          this.$emit("rentalInfoRules", this.rentalInfoRules);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "../common.less";
// .ant-advanced-search-form {
//   width: 100%;
//   padding: 24px;
//   background: #fbfbfb;
//   border: 1px solid #d9d9d9;
//   border-radius: 6px;
// }
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}
.ant-advanced-search-form /deep/.ant-input {
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
}
// .ant-advanced-search-form .ant-form-item-control-wrapper {
//   width: 100% !important;
//   height: 40px !important;
//   line-height: 40px !important;
//   flex: 1;
// }
</style>

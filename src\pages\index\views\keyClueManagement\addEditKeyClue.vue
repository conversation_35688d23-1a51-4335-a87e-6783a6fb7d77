<template>
  <div class="keyClueForm">
    <a-form-model
      ref="formData"
      :model="formData"
      layout="horizontal"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :rules="rules"
    >
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="项目名称" prop="projectName">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入项目名称"
              v-model.trim="formData.projectName"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="营商分部" prop="businessDivision">
            <a-input
              disabled="true"
              v-model="formData.businessDivision"
              placeholder="请选择营商分部"
            >
            </a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="项目简介"
            :labelCol="{ span: 4 }"
            prop="projectDescription"
          >
            <a-input
              type="textarea"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder=" 请输入至少20个字符，限制200个字符"
              v-model="formData.projectDescription"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="企业名称">
            <a-input
              placeholder="请输入企业名称"
              v-model="formData.companyName"
              :desabled="pageType === 'view'"
              maxLength="30"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="统一社会信用代码">
            <a-input
              placeholder="请输入"
              v-model="formData.socialCreditCode"
              allowClear
              :desabled="pageType === 'view'"
              maxLength="30"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="内/外资" prop="investmentType">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.investmentType"
              placeholder="请选择内/外资"
            >
              <a-select-option value="1"> 内资 </a-select-option>
              <a-select-option value="2"> 外资 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="预计税收规模">
            <a-select
              disabled="true"
              placeholder="请输入预计税收规模"
              v-model="formData.estimatedTaxRevenue"
              allowClear
            >
              <a-select-option value="1"> 百万级 </a-select-option>
              <a-select-option value="2"> 千万级 </a-select-option>
              <a-select-option value="3"> 亿万级 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12" style="display: flex">
          <a-form-model-item
            prop="estimatedInvestment"
            style="width: 65%"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
          >
            <span
              slot="label"
              class="label-box"
              style="max-width: 80%; line-height: revert"
              >预计投资/注册资金</span
            >
            <a-input-number
              placeholder="请输入预计投资/注册资金"
              v-model="formData.estimatedInvestment"
              allowClear
              style="width: 100%"
            ></a-input-number>
          </a-form-model-item>
          <a-form-model-item
            label=""
            prop="currency"
            :style="{ width: formData.currency == 3 ? '17%' : '35%' }"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
            style="margin: 0 5px"
          >
            <a-select
              v-model="formData.currency"
              placeholder="请选择币种"
              style="width: 100%"
            >
              <!-- @change="changeCoins()" -->
              <a-select-option value="1">人民币</a-select-option>
              <a-select-option value="2">美元</a-select-option>
              <a-select-option value="3">欧元</a-select-option>
              <a-select-option value="4"> 日元</a-select-option>
              <a-select-option value="5">港币</a-select-option>
              <a-select-option value="6">英镑 </a-select-option>
            </a-select>
          </a-form-model-item>
          <!-- <a-form-model-item
            label=""
            v-if="formData.currency == 3"
            prop="otherCurrencyName"
            style="width: 17%"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              placeholder="请输入"
              v-model="formData.otherCurrencyName"
            />
          </a-form-model-item> -->
        </a-col>
        <a-col :span="12" style="display: flex">
          <a-form-model-item
            :style="{ width: formData.taxProofSubmitted == 1 ? '50%' : '100%' }"
            prop="taxProofSubmitted"
            :label-col="{ span: formData.taxProofSubmitted == 1 ? 16 : 8 }"
            :wrapper-col="{ span: formData.taxProofSubmitted == 1 ? 8 : 16 }"
          >
            <span
              slot="label"
              class="label-box"
              style="max-width: 80%; line-height: normal"
            >
              落地企业是否提交税收依据</span
            >
            <a-select
              v-model="formData.taxProofSubmitted"
              placeholder="请选择落地企业是否提交税收依据"
              allowClear
              @change="changeIsTaxBasis()"
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            style="width: 50%; padding-left: 5px"
            v-if="formData.taxProofSubmitted == 1"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
            prop="taxFileList"
          >
            <FileAttachmentList
              v-if="formData.taxFileList.length > 0"
              title=""
              :ifNeedPreviewOnline="true"
              marked="true"
              @deleteFile="(file, fileList) => deleteConFile(file, fileList)"
              :fileList="formData.taxFileList"
            >
            </FileAttachmentList>
            <my-upload
              v-else
              :accept="accept"
              businessId="tax_basis"
              @handleFileCallback="handleConFileCallback"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="招商类型" prop="investmentTypeCategory">
            <a-select
              v-model="formData.investmentTypeCategory"
              placeholder="请选择招商类型"
              allowClear
            >
              <a-select-option value="1"> 新设</a-select-option>
              <a-select-option value="2"> 迁入</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" style="display: flex">
          <a-form-model-item
            label="是否梅开二度"
            :style="{
              width: formData.isSecondRound == 1 ? '50%' : '100%',
            }"
            :label-col="{
              span: formData.isSecondRound == 1 ? 16 : 8,
            }"
            :wrapper-col="{
              span: formData.isSecondRound == 1 ? 8 : 16,
            }"
            prop="isSecondRound"
          >
            <!-- <span slot="label" class="label-box">是否梅开二度</span> -->
            <a-select
              v-model="formData.isSecondRound"
              placeholder="请选择是否梅开二度"
              allowClear
              @change="changeIsSecondTime()"
            >
              <a-select-option value="1">是 </a-select-option>
              <a-select-option value="0">否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            style="width: 50%; padding-left: 10px"
            v-if="formData.isSecondRound == 1"
            prop="originalCompany"
          >
            <a-input
              v-model="formData.originalCompany"
              placeholder="请填写梅开二度原公司"
              style="width: 100%"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否总部经济" prop="isHeadquartersEconomy">
            <a-select
              v-model="formData.isHeadquartersEconomy"
              placeholder="请选择是否总部经济"
              allowClear
            >
              <a-select-option value="1"> 跨国公司</a-select-option>
              <a-select-option value="2"> 民营</a-select-option>
              <a-select-option value="3"> 研发中心</a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否500强企业" prop="isTop500">
            <a-select
              v-model="formData.isTop500"
              placeholder="请选择是否500强企业"
              allowClear
            >
              <a-select-option value="1"> 世界五百强 </a-select-option>
              <a-select-option value="2"> 中国五百强 </a-select-option>
              <a-select-option value="3"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否4大产业集群" prop="isFourClusters">
            <a-select
              v-model="formData.isFourClusters"
              placeholder="请选择是否4大产业集群"
              allowClear
            >
              <a-select-option value="1">人工智能</a-select-option>
              <a-select-option value="2">生命健康</a-select-option>
              <a-select-option value="3">科技金融</a-select-option>
              <a-select-option value="4">艺术传媒</a-select-option>
              <a-select-option value="5"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否行业标杆" prop="isIndustryBenchmark">
            <a-select
              v-model="formData.isIndustryBenchmark"
              placeholder="请选择是否行业标杆"
              allowClear
            >
              <a-select-option value="1"> 央企投资</a-select-option>
              <a-select-option value="2"> 上市公司 </a-select-option>
              <a-select-option value="3"> 优质外资 </a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否科创引领" prop="isTechLeader">
            <a-select
              v-model="formData.isTechLeader"
              placeholder="请选择是否科创引领"
              allowClear
            >
              <a-select-option value="1"> 高新技术企业</a-select-option>
              <a-select-option value="2"> 专精特新</a-select-option>
              <a-select-option value="3">科技小巨人</a-select-option>
              <a-select-option value="4">专精特新小巨人</a-select-option>
              <a-select-option value="5"> 独角兽 </a-select-option>
              <a-select-option value="6"> 瞪羚企业 </a-select-option>
              <a-select-option value="7"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="营业执照">
            <FileAttachmentList
              v-if="formData.businessLicenseFileList.length > 0"
              title=""
              :ifNeedPreviewOnline="true"
              marked="true"
              @deleteFile="(file, fileList) => deleteConFile1(file, fileList)"
              :fileList="formData.businessLicenseFileList"
            >
            </FileAttachmentList>
            <my-upload
              v-else
              :accept="accept"
              businessId="business_license"
              @handleFileCallback="handleConFileCallback1"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="项目阶段" prop="projectStage">
            <a-select
              v-model="formData.projectStage"
              placeholder="请选择项目阶段"
              allowClear
            >
              <a-select-option value="1">准备期</a-select-option>
              <a-select-option value="2">洽谈中 </a-select-option>
              <a-select-option value="3">注册中 </a-select-option>
              <a-select-option value="4">已落户 </a-select-option>
              <a-select-option value="5">暂缓库 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="初冾时间" prop="initialContactDate">
            <a-date-picker
              v-model="formData.initialContactDate"
              placeholder="请选择初冾时间"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="具体企业诉求及处理"
            :labelCol="{ span: 4 }"
            prop="enterpriseDemands"
          >
            <a-textarea
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入限制200个字符"
              v-model="formData.enterpriseDemands"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="备注" :labelCol="{ span: 4 }">
            <a-textarea
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入备注"
              v-model="formData.remarks"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item
            label="具体跟踪和进展情况"
            :labelCol="{ span: 4 }"
            prop="trackingProgress"
          >
            <a-textarea
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入具体跟踪和进展情况"
              v-model="formData.trackingProgress"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12" style="display: flex">
          <a-form-model-item
            label="落地渠道"
            :label-col="{ span: formData.landingChannel == 6 ? 16 : 8 }"
            :wrapper-col="{ span: formData.landingChannel == 6 ? 8 : 16 }"
            :style="{ width: formData.landingChannel == 6 ? '50%' : '100%' }"
            prop="landingChannel"
          >
            <a-select
              v-model="formData.landingChannel"
              placeholder="请选择落地渠道"
              allowClear
              style="width: 100%"
              @change="changeLandingChannels()"
            >
              <a-select-option value="1"> 关键人物对接</a-select-option>
              <a-select-option value="2"> 业务支持 </a-select-option>
              <a-select-option value="3"> 租赁客户 </a-select-option>
              <a-select-option value="4"> 企业承诺 </a-select-option>
              <a-select-option value="5"> 上级部门推荐 </a-select-option>
              <a-select-option value="6"> 其他 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            v-if="formData.landingChannel == 6"
            style="width: 50%; padding-left: 10px"
            prop="otherLandingChannel"
          >
            <a-input
              v-model="formData.otherLandingChannel"
              placeholder="请填写"
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" style="display: flex">
          <a-form-model-item
            label="是否需要经发支持"
            :style="{ width: formData.needsSupport == 1 ? '50%' : '100%' }"
            :label-col="{ span: formData.needsSupport == 1 ? 16 : 8 }"
            :wrapper-col="{ span: formData.needsSupport == 1 ? 8 : 16 }"
            prop="needsSupport"
          >
            <a-select
              v-model="formData.needsSupport"
              placeholder="请选择是否需要经发支持"
              allowClear
              @change="changeIsNeedSupports()"
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            style="width: 50%; padding-left: 10px"
            prop="supportType"
            v-if="formData.needsSupport == 1"
          >
            <a-select
              v-model="formData.supportType"
              placeholder="请选择"
              allowClear
            >
              <a-select-option value="1"> 政策支持 </a-select-option>
              <a-select-option value="2"> 载体支持 </a-select-option>
              <a-select-option value="3"> 工商注册 </a-select-option>
              <a-select-option value="4"> 其他 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="跟进人" prop="followerName">
            <a-input
              v-model="formData.followerName"
              placeholder="请填写跟进人"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="手机号码" prop="followerPhone">
            <a-input
              v-model="formData.followerPhone"
              placeholder="请填写手机号码"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- //审批详情 -->
    <template v-for="(item, index) in approvalHistory">
      <div class="summarizeBox" :key="index">
        <div class="comTitle">
          {{ item.reviewNodes == "初审节点" ? "初审小结" : "复审小结" }}
        </div>
        <div style="margin-bottom: 20px; padding-left: 20px">
          <span style="margin-right: 50px"
            >{{ item.approver }}：{{ item.remark }}
          </span>
          <span style="margin-right: 50px">审批意见：{{ item.comments }} </span>
          <span>审批时间：{{ item.approvalTime }} </span>
        </div>
        <!-- <div style="margin-bottom: 20px">
          <span>审批意见：同意 </span>
        </div> -->
      </div>
    </template>
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      "
    >
      <a-button @click="toBack">返回</a-button>
      <a-button type="primary" style="margin: 0 10px" @click="onSaveFn"
        >保存</a-button
      >
      <a-button type="primary" @click="onSubmit">提交申请</a-button>
    </div>
  </div>
</template>
<script>
import FileAttachmentList from "@/components/fileView";
import myUpload from "@/components/Importer";
import {
  saveBusinessClue,
  getCompanyAbbr,
  updatesaveBusinessClue,
  getDetailById,
  getApprovalHistory,
} from "@/pages/index/data/api/keyAndHouse/index";
export default {
  components: {
    FileAttachmentList,
    myUpload,
  },
  data() {
    return {
      approvalHistory: [],
      accept: ".pdf,.png,.jpeg,.jpg",
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
      id: "", //路由参数id
      pageType: "", //路由参数状态:1:编辑，2:查看，3审批
      openType: "", //0:保存，1:提交
      companyAbbrList: "", //企业简称
      taxInfoFile: false,
      businessLicenseInfoFile: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      formData: {
        projectName: "", //项目名称
        businessDivision: undefined, //营商分布
        projectDescription: "", //项目描述
        companyName: "", //企业名称
        socialCreditCode: "", //统一社会信用代码
        investmentType: undefined, //内/外资
        estimatedTaxRevenue: "", //预计税收规模
        estimatedInvestment: "", //预计投资/注册资金
        currency: undefined, //币种
        otherCurrencyName: "", //其他币种
        taxProofSubmitted: undefined, //落地企业是否提交税收依据
        taxFileList: [], // 落地企业是否提交税收依据文件
        investmentTypeCategory: undefined, //招商类型
        isSecondRound: undefined, //是否梅开二度
        originalCompany: "", //梅开二度值
        isHeadquartersEconomy: undefined, //是否总部经济
        isTop500: undefined, //是否500强企业
        isFourClusters: undefined, //是否4大产业集群
        isIndustryBenchmark: undefined, //是否行业标杆
        isTechLeader: undefined, //是否科创引领
        projectStage: undefined, //项目阶段
        initialContactDate: "", //初冾时间
        enterpriseDemands: "", //具体企业诉求及处理
        remarks: "", //备注
        trackingProgress: "", //具体跟踪和进展情况
        landingChannel: undefined, //落地渠道：
        otherLandingChannel: "", //落地渠道值
        needsSupport: undefined, //是否需要经发支持
        supportType: undefined, //是否需要经发支持值
        followerName: "", //跟进人
        followerPhone: "", //手机号码
        businessLicenseFileList: [], // 营业执照
      },
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "不能少于1字符，且不超过20个字符",
            trigger: "blur",
          },
        ],
        businessDivision: [
          { required: true, message: "营商分部不能为空", trigger: "change" },
        ],
        projectDescription: [
          { required: true, message: " 项目简介不能为空", trigger: "blur" },
          {
            min: 20,
            max: 200,
            message: "不能少于20字符，且不超过200个字符",
            trigger: "blur",
          },
        ],
        investmentType: [
          { required: true, message: " 内/外资不能为空", trigger: "blur" },
        ],
        estimatedInvestment: [
          {
            required: true,
            message: "预计投资/注册资金不能为空",
            trigger: "blur",
          },
        ],
        currency: [
          { required: true, message: "币种不能为空", trigger: "change" },
        ],
        otherCurrencyName: [
          { required: false, message: "其他币种不能为空", trigger: "blur" },
        ],
        taxProofSubmitted: [
          {
            required: true,
            message: "是否落地企业是否提交税收依据不能为空",
            trigger: "change",
          },
        ],
        taxFileList: [
          {
            required: false,
            message: " 落地企业是否提交税收依据文件不能为空",
            trigger: "change",
          },
        ],
        investmentTypeCategory: [
          { required: true, message: "招商类型不能为空", trigger: "change" },
        ],
        isSecondRound: [
          {
            required: true,
            message: "是否梅开二度不能为空",
            trigger: "change",
          },
        ],
        originalCompany: [
          {
            required: false,
            message: "梅开二度原公司不能为空",
            trigger: "blur",
          },
        ],
        isHeadquartersEconomy: [
          {
            required: true,
            message: "是否总部经济不能为空",
            trigger: "change",
          },
        ],
        isTop500: [
          {
            required: true,
            message: "是否500强企业不能为空",
            trigger: "change",
          },
        ],
        isFourClusters: [
          {
            required: true,
            message: "是否4大产业集群不能为空",
            trigger: "change",
          },
        ],
        isIndustryBenchmark: [
          {
            required: true,
            message: "是否行业标杆不能为空",
            trigger: "change",
          },
        ],
        isTechLeader: [
          {
            required: true,
            message: "是否科创引领不能为空",
            trigger: "change",
          },
        ],
        projectStage: [
          { required: true, message: "项目阶段不能为空", trigger: "change" },
        ],
        initialContactDate: [
          { required: true, message: "初冾时间不能为空", trigger: "change" },
        ],
        enterpriseDemands: [
          {
            required: true,
            message: "具体企业诉求及处理不能为空",
            trigger: "blur",
          },
          {
            min: 1,
            max: 200,
            message: "字符不能超过200个",
            trigger: "blur",
          },
        ],
        trackingProgress: [
          {
            required: true,
            message: "具体跟踪和进展情况不能为空",
            trigger: "blur",
          },
          {
            min: 1,
            max: 200,
            message: "字符不能超过200个",
            trigger: "blur",
          },
        ],
        landingChannel: [
          { required: true, message: "落地渠道不能为空", trigger: "change" },
        ],
        otherLandingChannel: [
          { required: true, message: "渠道值不能为空", trigger: "blur" },
        ],
        needsSupport: [
          {
            required: true,
            message: "是否需要经发支持不能为空",
            trigger: "change",
          },
        ],
        supportType: [
          {
            required: true,
            message: "支持值不能为空",
            trigger: "change",
          },
        ],
        followerName: [
          { required: true, message: "跟进人不能为空", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "字符不能超过20个",
            trigger: "blur",
          },
        ],
        followerPhone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },

  created() {
    if (this.$route.query.taxScale) {
      this.formData.estimatedTaxRevenue =
        this.$route.query.taxScale == 1
          ? "百万级"
          : this.$route.query.taxScale == 2
          ? "千万级"
          : this.$route.query.taxScale == 3
          ? "亿万级"
          : ""; //获取路由参数
    }
    if (this.$route.query.id && this.$route.query.statu) {
      this.id = this.$route.query.id; //获取路由参数
      this.pageType = this.$route.query.statu; //获取路由参数
    }

    this.getCompanyAbbr(); //获取营商分布
    if (this.pageType == 1) {
      //编辑
      this.getBusinessClueInfo(); //获取线索详情
      this.getApprovalHistory();
    }
  },
  methods: {
    //获取审批历史
    async getApprovalHistory() {
      await getApprovalHistory({ contractInfoId: this.id }).then((res) => {
        if (res.code == 0) {
          if (res.data.length > 0) {
            this.approvalHistory = res.data.reverse();
          }
        }
      });
    },

    //获取营商分布
    getCompanyAbbr() {
      getCompanyAbbr().then((res) => {
        console.log(res, "formData");
        if (res.code == 0) {
          // this.formData.businessDivision = res.data;
          this.$set(this.formData, "businessDivision", res.data); //设置营商分布
          // this.companyAbbrList = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // //改变币种
    // changeCoins() {
    //   this.formData.otherCurrencyName = ""; //清空其他币种值
    //   if (this.formData.currency == 3) {
    //     this.$set(this.rules, "otherCurrencyName", {
    //       required: true,
    //       message: "其他币种不能为空",
    //       trigger: "blur",
    //     });
    //   } else {
    //     this.$set(this.rules, "otherCurrencyName", {
    //       required: false,
    //       message: "其他币种不能为空",
    //       trigger: "blur",
    //     });
    //   }
    // },
    changeIsTaxBasis() {
      this.formData.taxFileList = []; //清空落地企业是否提交税收依据文件值
      if (this.formData.taxProofSubmitted == 1) {
        this.$set(this.rules, "taxFileList", {
          required: true,
          message: " 落地企业是否提交税收依据文件不能为空",
          trigger: "change",
        });
      } else {
        this.$set(this.rules, "taxFileList", {
          required: false,
          message: " 落地企业是否提交税收依据文件不能为空",
          trigger: "change",
        });
      }
    },
    //改变是否梅开二度
    changeIsSecondTime() {
      this.formData.originalCompany = ""; //清空梅开二度值
      if (this.formData.isSecondRound == 1) {
        this.$set(this.rules, "originalCompany", {
          required: true,
          message: "梅开二度原公司不能为空",
          trigger: "blur",
        });
      } else {
        this.$set(this.rules, "originalCompany", {
          required: false,
          message: "梅开二度原公司不能为空",
          trigger: "blur",
        });
      }
    },
    //改变落地渠道
    changeLandingChannels() {
      this.formData.otherLandingChannel = ""; //清空落地渠道值
      if (this.formData.landingChannel == 6) {
        this.$set(this.rules, "otherLandingChannel", {
          required: true,
          message: "落地渠道值不能为空",
          trigger: "blur",
        });
      } else {
        this.$set(this.rules, "otherLandingChannel", {
          required: false,
          message: "落地渠道值不能为空",
          trigger: "blur",
        });
      }
    },
    //改变是否需要经发支持
    changeIsNeedSupports() {
      this.formData.supportType = undefined; //清空是否需要经发支持值
      if (this.formData.needsSupport == 1) {
        this.$set(this.rules, "supportType", {
          required: true,
          message: "经发支持类型不能为空",
          trigger: "change",
        });
      } else {
        this.$set(this.rules, "supportType", {
          required: false,
          message: "经发支持类型不能为空",
          trigger: "change",
        });
      }
    },
    async getBusinessClueInfo() {
      await getDetailById({ id: this.id }).then((res) => {
        if (res.code == 0) {
          this.formData = res.data;
          this.id = res.data.id;
          let taxFileList = []; //税收依据文件列表
          let businessLicenseFileList = []; //收据文件列表
          console.log(this.formData, "详情");
          this.formData.attachmentList.forEach((item) => {
            if (item.businessType == "tax_basis") {
              taxFileList.push(item);
            } else if (item.businessType == "business_license") {
              businessLicenseFileList.push(item);
            }
          });
          this.$set(this.formData, "taxFileList", taxFileList);
          this.$set(
            this.formData,
            "businessLicenseFileList",
            businessLicenseFileList
          );
        }
      });
    },
    //保存
    onSaveFn() {
      this.openType = "0"; //保存
      if (this.pageType == 1) {
        //编辑
        this.updatesaveBusinessClue(); //更新线索
      } else {
        this.saveInfo(); //保存线索
      }
    },
    //提交申请
    onSubmit() {
      this.openType = "1"; //提交申请
      if (this.pageType == 1) {
        //编辑
        this.updatesaveBusinessClue(); //更新线索
      } else {
        this.saveInfo();
      }
    },
    //信息保存、提交
    saveInfo() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formData));
          params.attachmentList = [
            ...this.formData.businessLicenseFileList,
            ...this.formData.taxFileList,
          ];
          params.businessLicenseFileList = "";
          params.taxFileList = "";
          params.openType = this.openType; //0:保存，1:提交
          saveBusinessClue(params).then((res) => {
            if (res.code == 0) {
              this.$message.success(
                `${this.openType == 0 ? "保存" : "提交"}成功`
              );
              setTimeout(() => {
                this.$router.push(`/key-clue-management`);
              }, 1000);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
      console.log("submit!", this.form);
    },

    //updatesaveBusinessClue 更新线索
    updatesaveBusinessClue() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.formData));
          params.attachmentList = [
            ...this.formData.businessLicenseFileList,
            ...this.formData.taxFileList,
          ];
          params.businessLicenseFileList = "";
          params.taxFileList = "";
          params.id = this.id; //获取路由参数id
          params.openType = this.openType; //0:保存，1:提交
          updatesaveBusinessClue(params).then((res) => {
            if (res.code == 0) {
              this.$message.success(
                `${this.openType == 0 ? "保存" : "提交"}成功`
              );
              setTimeout(() => {
                this.$router.push(`/key-clue-management`);
              }, 1000);
              // this.$router.push(`/key-clue-management`);
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    handleConFileCallback: function (file) {
      this.$refs["formData"].clearValidate(["taxFileList"]);
      this.taxInfoFile = true;
      let tmpList = this.formData.taxFileList;
      tmpList.push(file);
      this.$set(this.formData, "taxFileList", tmpList);
    },

    deleteConFile: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.taxInfoFile = false;
      }
      // this.formData.taxFileList = fileList;
      this.$set(this.formData, "taxFileList", fileList);
    },
    //上传营业执照
    handleConFileCallback1: function (file) {
      this.businessLicenseInfoFile = true;
      let tmpList = this.formData.businessLicenseFileList;
      tmpList.push(file);

      this.$set(this.formData, "businessLicenseFileList", tmpList);
    },

    //删除营业执照
    deleteConFile1: function (file, fileList) {
      console.log(file, fileList);
      if (fileList.length == 0) {
        this.businessLicenseInfoFile = false;
      }
      // this.formData.taxFileList = fileList;
      this.$set(this.formData, "businessLicenseFileList", fileList);
    },

    //返回
    toBack() {
      let that = this;
      this.$confirm({
        title: "当前内容尚未保存，请确认是否返回？",
        content: "",
        onOk() {
          that.$router.push(`/key-clue-management`);
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.keyClueForm {
  width: 100%;
  padding: 20px;
  background: #fff;
  .boxWidth {
    /deep/.ant-form-item-control-wrapper {
      width: 100%;
    }
  }
  .comTitle {
    position: relative;
    width: 100%;
    margin: 10px 0;
    padding: 0 10px;
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    &::before {
      content: "";
      width: 4px;
      height: 20px;
      background: #1777ff;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-10px);
    }
  }
  .label-box {
    width: 80%; // 建议80%，太长就会超出内容
    display: inline-block;
    height: auto !important;
    white-space: break-spaces;
    line-height: 1;
    text-align: right;
    vertical-align: bottom; // 这是为了让整体的字往下移动一点
  }
  .ant-form-item {
    margin-bottom: 16px;
  }
  .ant-form-item-label {
    text-align: left;
  }
  .ant-form-item-control {
    margin-left: 0px;
  }
}
</style>

<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 数字控件表单配置 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input
        v-model="form.inputTitle"
        placeholder="申请主题"
        maxLength="20"
        allowClear
      ></a-input>
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        v-model="form.placeholder.placeholderText"
        type="textarea"
        placeholder="请输入"
        maxLength="50"
        allowClear
      ></a-input>
    </a-form-model-item>
    <a-form-model-item label="精度">
      <a-input-number
        min="0"
        max="10"
        v-model="form.optionsData.precision"
      ></a-input-number>
    </a-form-model-item>
    <a-form-model-item label="单位">
      <a-input v-model="form.optionsData.unit" placeholder="请输入"></a-input>
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
    <a-form-model-item label="验证" help="勾选后可作为流程条件">
      <a-switch v-model="form.isRegex" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        placeholder: {},
        optionsData: {}
      }
    };
  },
  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  }
};
</script>

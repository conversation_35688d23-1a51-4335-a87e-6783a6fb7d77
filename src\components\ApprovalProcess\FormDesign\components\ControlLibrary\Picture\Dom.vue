<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 图片控件 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :help="
        data.optionsData.fileNum || data.optionsData.fileSize
          ? uploadTip
          : false
      "
      :label="data.inputTitle || '图片'"
      style="margin-bottom:unset"
      prop="fileList"
      :rules="[
        {
          required: data.notNull,
          message: '请上传图片！',
          trigger: 'change'
        }
      ]"
    >
      <a-upload
        action=""
        list-type="picture-card"
        :file-list="fileList"
        @preview="handlePreview"
        @change="handleChange"
      >
        <div>
          <a-icon type="plus" />
          <div class="ant-upload-text">
            选择上传
          </div>
        </div>
      </a-upload>
      <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </a-modal>
      <div>
        {{
          (data.placeholder && data.placeholder.placeholderText) ||
            "请输入提示文字"
        }}
      </div>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      previewVisible: false,
      previewImage: "",
      fileList: [],
      uploadTip: [] //文件上传提示
    };
  },
  watch: {
    "data.optionsData": {
      handler: function(flag) {
        if (flag) {
          this.uploadTip = [];
          if (this.data.optionsData.fileNum) {
            let tip = "最多可上传" + this.data.optionsData.fileNum + "个";
            this.uploadTip.push(tip);
          }
          if (this.data.optionsData.fileSize) {
            let tip =
              "每个文件大小不超过" + this.data.optionsData.fileSize + "MB";
            this.uploadTip.push(tip);
          }
          this.uploadTip = this.uploadTip.join(",");
          console.log("上传控件属性", this.data.optionsData, this.uploadTip);
        }
      },
      deep: true
    }
  },
  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    handleChange({ fileList }) {
      this.fileList = fileList;
    }
  }
};
</script>
<style lang="less" scoped>
@import "../index.less";
.form-design-controlLib-field-label {
  width: unset !important;
}
</style>

<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 身份证控件 
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '身份证'"
      style="margin-bottom:unset"
      prop="cardId"
      :rules="[
        {
          required: data.notNull,
          message: '请输入身份证！',
          trigger: 'blur'
        }
      ]"
    >
      <a-input
        v-model="form.cardId"
        :placeholder="data.placeholder.tipsTitleText || '请输入'"
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          placeholder: ""
        };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        cardId: null
      }
    };
  }
};
</script>
<style lang="less" scoped>
@import "../index.less";
</style>

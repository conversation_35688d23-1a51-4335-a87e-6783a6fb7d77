<template>
  <div class="headerContent">
    <div class="header">
      <a-row>
        <a-col :span="8">
          <div class="card">
            <div class="info">
              <span class="title">企业信息</span>
              <span class="little_title"
                >总企业数：<span class="specialColor">{{
                  companyMessage.totalEnterprise
                }}</span
                >家</span
              >
            </div>
            <div class="box">
              <div
                class="box_item"
                v-for="(item, key) in areaData"
                :key="item.id"
              >
                <div class="number">
                  {{
                    key == 0
                      ? companyMessage.territorial
                      : key == 1
                      ? companyMessage.quTerritorial
                      : key == 2
                      ? companyMessage.xuhui
                      : key == 3
                      ? companyMessage.noXuhui
                      : companyMessage.nonTerritorial
                  }}<span class="specialColor">家</span>
                </div>
                <div class="name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="card">
            <div class="info">
              <span class="title">税收信息</span>
              <span class="little_title"
                >总税收：<span class="specialColor">{{
                  taxMessage.totalTax
                }}</span
                >亿元</span
              >
            </div>
            <div class="box" style="justify-content: left;">
              <div
                style="width: 29%;margin-right: 5px;"
                class="box_item"
                v-for="(item, key) in areaData1"
                :key="item.id"
              >
                <div
                  class="number"
                  v-html="
                    formatMoney(
                      key == 0
                        ? taxMessage.territorial
                        : key == 1
                        ? taxMessage.quTerritorial
                        : key == 2
                        ? taxMessage.noXuhui
                        : key == 3
                        ? taxMessage.xuhui
                        : taxMessage.nonTerritorial
                    )
                  "
                ></div>
                <div class="name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="card" id="classification">
            <div
              class="statistics"
              v-for="(item, key) in statisticsData"
              :key="item.id"
            >
              <div style="display: flex;width: 100px;">
                <div class="icon" :id="item.boxColor"></div>
                <div class="typeName" @click="handleTo(item.value)">
                  {{ item.typeName }}
                </div>
              </div>
              <div class="middle">
                <a-progress
                  strokeWidth="5"
                  :percent="getPercent(key)"
                  status="active"
                  :showInfo="false"
                  :strokeColor="item.color"
                />
              </div>
              <div class="statisticsNumber">
                <span :id="item.wordColor"
                  >{{
                    key == 0
                      ? labelCompany["1"]
                        ? labelCompany["1"]
                        : 0
                      : key == 1
                      ? labelCompany["2"]
                        ? labelCompany["2"]
                        : 0
                      : key == 2
                      ? labelCompany["3"]
                        ? labelCompany["3"]
                        : 0
                      : labelCompany["4"]
                      ? labelCompany["4"]
                      : 0
                  }}家</span
                >
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- <div class="prompt">
      <div class="showInfo">
        <a-icon
          type="bell"
          theme="filled"
          style="color: rgb(100, 175, 254); margin-top: 10px; margin-left: 20px;"
        />
        <span>园区大事、热门事件、优惠信息、宣传推广信息...</span>
      </div>
      <div class="positioning">
        <a-icon
          type="environment"
          theme="filled"
          style="color: rgb(209, 167, 247); margin-top: 10px; margin-left: 20px;"
        />
        <span>华泾镇</span>
        <a-icon
          type="caret-down"
          theme="filled"
          style="color: rgb(202, 202, 202); margin-top: 10px; margin-left: 20px;"
        />
      </div>
    </div> -->
  </div>
</template>
<script>
// import { ApiGetDictByParentCode } from "../../../../../../data/api/common";
import {
  ApiGetCompanyMessage,
  ApiGetTaxMessage,
  ApiGetLabelCompanyMessage,
} from "../../../../data/api/EnterpriseMonitoring";
export default {
  components: {},
  data() {
    return {
      areaData: [
        {
          id: 1,
          name: "镇属",
        },
        {
          id: 2,
          name: "区属",
        },
        {
          id: 3,
          name: "2头在外(本区)",
        },
        {
          id: 4,
          name: "2头在外(外区)",
        },
        {
          id: 5,
          name: "非属地",
        },
      ], //定义存放4种类型的名字
      areaData1: [
        {
          id: 1,
          name: "镇属",
        },
        {
          id: 2,
          name: "区属",
        },
        {
          id: 4,
          name: "2头在外(外区)",
        },
      ], //定义存放4种类型的名字
      companyMessage: {}, //用来存储后端传过来的企业信息的数据
      taxMessage: {}, //用来存储后端传过来的税收信息的数据
      labelCompany: {}, //用来存储后端传过来的不同标签的总企业数和总税收
      unit: "", //定义一个变量来存储税收的单位
      maxTax: 0, //定义一个变量来存放最大的税收值
      statisticsData: [
        {
          id: 1,
          typeName: "高新技术",
          boxColor: "blue",
          wordColor: "blue1",
          color: "rgb(0, 144, 255)",
          value: "1",
        },
        {
          id: 2,
          typeName: "专精特新",
          boxColor: "orange",
          wordColor: "orange1",
          color: "rgb(255, 162, 44)",
          value: "2",
        },
        {
          id: 3,
          typeName: "外资总部",
          boxColor: "purple",
          wordColor: "purple1",
          color: "rgb(91, 118, 249)",
          value: "3",
        },
        {
          id: 4,
          typeName: "上市公司",
          boxColor: "green",
          wordColor: "green1",
          color: "rgb(1, 208, 222)",
          value: "4",
        },
      ],
    };
  },
  mounted() {
    this.getCompanyMessage();
    this.getTaxMessage();
    this.getLabelCompanyMessage();
    //从字典表获取企业标签
    // ApiGetDictByParentCode({ dictCode: "company_label" }).then(res => {
    //   this.labelArr = res.data;
    // });
  },
  methods: {
    handleTo(name) {
      this.$router.push({
        path: "/information-query/enterprise",
        query: {
          name: name,
        },
      });
    },

    getPercent(key) {
      return (
        (parseInt(
          key == 0
            ? this.labelCompany["1"]
            : key == 1
            ? this.labelCompany["2"]
            : key == 2
            ? this.labelCompany["3"]
            : this.labelCompany["4"]
        ) /
          this.maxTax) *
        100
      );
    },
    //处理单位的函数
    formatMoney(data) {
      // debugger
      let unit;
      // if (data > 10000) {
      //   unit = "亿元";
      //   data = (data / 10000).toFixed(2);
      // } else {
      //   unit = "万元";
      //   data = parseFloat(data).toFixed(2);
      // }
      unit = "亿";
      if (!data)
        return `<div class="number">0.00<span class="specialColor">${unit}</span></div>`;
      data = (data / 10000).toFixed(2);
      return `<div class="number">${data}<span class="specialColor">${unit}</span></div>`;
    },
    //获取企业信息数据的方法
    getCompanyMessage: function() {
      ApiGetCompanyMessage().then((res) => {
        console.log(res.data, "9999");
        this.companyMessage = res.data;
      });
    },
    //获取税收信息数据的方法
    getTaxMessage: function() {
      ApiGetTaxMessage().then((res) => {
        this.taxMessage = res.data;
      });
    },
    //获取标签企业的企业数量的方法
    getLabelCompanyMessage: function() {
      ApiGetLabelCompanyMessage().then((res) => {
        this.labelCompany = res.data;
        console.log(this.labelCompany, this.labelCompany["1"], "00000");
        const temp = this.labelCompany;
        const tmpArr = [];
        for (let key in temp) {
          tmpArr.push(temp[key]);
        }
        console.log(tmpArr, 88888);
        this.maxTax = Math.max(...tmpArr);
        console.log(this.maxTax, "+++++++");
      });
    },
  },
  computed: {
    //后端返回的是万元所以用这个，后面返回的是亿就不用计算了
    // totalTax() {
    //   return this.taxMessage.totalTax
    //     ? parseFloat(this.taxMessage.totalTax / 10000).toFixed(2)
    //     : 0;
    // }
  },
};
</script>
<style scoped lang="less">
.headerContent {
  .header {
    .card {
      background-color: white;
      margin-right: 20px;
      border-radius: 10px;

      .info {
        padding-top: 20px;

        .title {
          color: black;
          font-size: 16px;
          font-weight: 600;
          margin-left: 25px;
        }

        .little_title {
          float: right;
          margin-right: 25px;
          font-size: 12px;

          .specialColor {
            color: rgb(5, 143, 250);
          }
        }
      }

      .box {
        display: flex;
        justify-content: center;
        flex-wrap: nowrap;

        .box_item {
          margin-bottom: 25px;
          width: 100px;
          margin-left: 5px;
          margin-top: 30px;
          height: 80px;
          background-color: #e3eefe;
          border-radius: 5px;

          .number {
            color: rgb(0, 144, 254);
            font-size: 20px;
            font-weight: 800;
            margin-top: 15px;
            margin-left: 10px;

            .specialColor {
              color: #058ffa;
              font-size: 12px;
              margin-left: 3px;
              font-weight: 400;
            }
          }

          /deep/ .number {
            color: rgb(0, 144, 254);
            font-size: 20px;
            font-weight: 800;
            margin-top: 15px;
            // margin-left: 10px;
          }

          /deep/ .specialColor {
            color: #058ffa;
            font-size: 12px;
            margin-left: 3px;
            font-weight: 400;
          }

          .name {
            color: black;
            margin-left: 10px;
            font-size: 12px;
            margin-top: 8px;
          }
        }

        .box_item:first-child {
          margin-left: 20px;
        }

        .box_item:last-child {
          margin-right: 20px;
        }
      }

      .statistics {
        display: flex;
        justify-content: center;

        .icon {
          width: 10px;
          height: 10px;
          margin-top: 20px;
          margin-right: 10px;
        }

        .typeName {
          color: rgba(0, 0, 0, 0.8);
          font-size: 14px;
          font-weight: 600;
          margin-right: 20px;
          margin-top: 15px;
          cursor: pointer;
        }

        .middle {
          margin-top: 13px;
          width: 55px;

          .statisticsMoney {
            color: #bfbfbf;
            font-size: 12px;
          }
        }

        .statisticsNumber {
          width: 60px;
          font-size: 16px;
          font-weight: 600;
          margin-top: 15px;
          margin-left: 160px;
          #blue1 {
            color: rgb(0, 144, 255);
          }

          #orange1 {
            color: rgb(255, 162, 44);
          }

          #purple1 {
            color: rgb(91, 118, 249);
          }

          #green1 {
            color: rgb(1, 208, 222);
          }
        }

        #blue {
          background-color: rgb(0, 144, 255);
        }

        #orange {
          background-color: rgb(255, 162, 44);
        }

        #purple {
          background-color: rgb(91, 118, 249);
        }

        #green {
          background-color: rgb(1, 208, 222);
        }
      }
    }

    #classification {
      height: 178px;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }

  .prompt {
    margin-top: 10px;
    display: flex;

    .showInfo {
      width: 84%;
      height: 40px;
      margin-right: 20px;
      border-radius: 5px;
      background-color: white;

      span {
        margin-left: 20px;
      }
    }

    .positioning {
      width: 14%;
      height: 40px;
      border-radius: 5px;
      background-color: white;

      span {
        margin-left: 20px;
      }
    }
  }

  /deep/.ant-progress-outer {
    width: 350%;
  }

  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 5px;
    /* 滚动条宽度 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background-color: #ffffff;
    /* 轨道背景颜色 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #e6e6e6;
    /* 滑块背景颜色 */
  }

  /* 滚动条滑块悬停状态 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #e6e6e6;
    /* 悬停状态下滑块背景颜色 */
  }
}
</style>

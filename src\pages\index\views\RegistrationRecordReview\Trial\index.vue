<template>
  <a-form
    :form="form"
    :formLayout="formLayout"
    :label-col="formItemLayout.labelCol"
    :wrapper-col="formItemLayout.wrapperCol"
  >
    <!-- 基本信息部分 -->
    <!-- 引入封装好的组件 -->
    <grayCard :data="trialDataObj"></grayCard>
    <!-- 中间条款部分 -->
    <a-card style="margin-top: 12px">
      <commonTitle
        >审核信息<a-button @click="showApprovalRecords()"
          >审批记录</a-button
        ></commonTitle
      >
      <AgreeItemView
        :isProfessionalProp="isProfessional"
        v-model="trialDataObj.contractReviews[index]"
        style="margin-bottom: 24px"
        v-for="(item, index) in trialDataObj.contractReviews"
        :annex="item.attachments"
        :title="{ titleName: `${item.titleName}:`, value: item.titleValue }"
        :numType="item.numType"
        :index="item.index"
        :key="index"
        :lookType="'view'"
        :advice="item.advice"
        :agreement="item.agreement"
      ></AgreeItemView>

      <!-- 审批小结 -->
      <!-- 审批小结初审、复审都有都显示 -->
      <commonTitle>审批小结</commonTitle>
      <a-row :gutter="24">
        <a-col span="12">
          <a-form-item :label="trialDataObj.contractInfo.approver">
            <p>
              {{ trialDataObj.contractInfo.approvalSummary }}
            </p>
          </a-form-item>
          <a-form-item label="审核方式">
            <p>
              {{
                trialDataObj.contractInfo.auditType == 0
                  ? "备案"
                  : trialDataObj.contractInfo.auditType == 1
                  ? "审批"
                  : "会审"
              }}
            </p>
          </a-form-item>
          <a-form-item label="审核时间">
            <p>
              {{
                trialDataObj.contractInfo.approverTime
                  ? moment(trialDataObj.contractInfo.approverTime).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  : ""
              }}
            </p>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 初审意见的查看 -->
      <!-- 查看或者有初审意见都显示 -->
      <div
        v-if="
          (viewType === 'view' &&
            trialDataObj.contractInfo.preliminaryReviewOpinion) ||
          trialDataObj.contractInfo.preliminaryReviewOpinion
        "
      >
        <commonTitle>初审意见</commonTitle>
        <a-row>
          <a-col span="12">
            <a-form-item :label="trialDataObj.contractInfo.preReviewer">
              <p>
                {{ trialDataObj.contractInfo.preliminaryReviewOpinion }}
              </p>
            </a-form-item>
            <a-form-item label="审核时间">
              <p>
                {{
                  trialDataObj.contractInfo.preReviewerTime
                    ? moment(trialDataObj.contractInfo.preReviewerTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    : ""
                }}
              </p>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <!-- 查看页面并且复审完成有复审数据之后显示 -->
      <div
        v-if="
          (viewType === 'view' && trialDataObj.contractInfo.reviewOpinions) ||
          trialDataObj.contractInfo.reviewOpinions
        "
      >
        <commonTitle>经发公司意见</commonTitle>
        <a-row>
          <a-col span="12">
            <a-form-item :label="trialDataObj.contractInfo.reviewer">
              <p>
                {{ trialDataObj.contractInfo.reviewOpinions }}
              </p>
            </a-form-item>
            <a-form-item label="审核时间">
              <p>
                {{
                  trialDataObj.contractInfo.reviewerTime
                    ? moment(trialDataObj.contractInfo.reviewerTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    : ""
                }}
              </p>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        v-if="
          (viewType === 'view' &&
            trialDataObj.contractApprovalHistorys &&
            trialDataObj.contractApprovalHistorys.town_leader_approval_node &&
            trialDataObj.contractApprovalHistorys.town_leader_approval_node
              .comments) ||
          (trialDataObj.contractApprovalHistorys &&
            trialDataObj.contractApprovalHistorys.town_leader_approval_node &&
            trialDataObj.contractApprovalHistorys.town_leader_approval_node
              .comments)
        "
      >
        <commonTitle>经发办意见</commonTitle>
        <a-row>
          <a-col span="12">
            <a-form-item
              :label="
                trialDataObj.contractApprovalHistorys.town_leader_approval_node
                  .approver
              "
            >
              <p>
                {{
                  trialDataObj.contractApprovalHistorys
                    .town_leader_approval_node.comments
                }}
              </p>
            </a-form-item>
            <a-form-item label="审核时间">
              <p>
                {{
                  trialDataObj.contractApprovalHistorys
                    .town_leader_approval_node.approvalTime
                    ? moment(
                        trialDataObj.contractApprovalHistorys
                          .town_leader_approval_node.approvalTime
                      ).format("YYYY-MM-DD HH:mm:ss")
                    : ""
                }}
              </p>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <div
        v-if="
          (viewType === 'view' &&
            trialDataObj.contractApprovalHistorys &&
            trialDataObj.contractApprovalHistorys.town_leader_review_node &&
            trialDataObj.contractApprovalHistorys.town_leader_review_node
              .comments) ||
          (trialDataObj.contractApprovalHistorys &&
            trialDataObj.contractApprovalHistorys.town_leader_review_node &&
            trialDataObj.contractApprovalHistorys.town_leader_review_node
              .comments)
        "
      >
        <commonTitle>分管镇长意见</commonTitle>
        <a-row>
          <a-col span="12">
            <a-form-item
              :label="
                trialDataObj.contractApprovalHistorys.town_leader_review_node
                  .approver
              "
            >
              <p>
                {{
                  trialDataObj.contractApprovalHistorys.town_leader_review_node
                    .comments
                }}
              </p>
            </a-form-item>
            <a-form-item label="审核时间">
              <p>
                {{
                  trialDataObj.contractApprovalHistorys.town_leader_review_node
                    .approvalTime
                    ? moment(
                        trialDataObj.contractApprovalHistorys
                          .town_leader_review_node.approvalTime
                      ).format("YYYY-MM-DD HH:mm:ss")
                    : ""
                }}
              </p>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-card>
    <!-- 填写初审或经发公司意见 -->
    <a-card style="margin-top: 12px" v-if="viewType !== 'view'">
      <commonTitle v-if="roles.includes('经发公司复核员')"
        >经发公司意见</commonTitle
      >
      <commonTitle v-if="roles.includes('经发公司审核员')"
        >复审意见</commonTitle
      >
      <commonTitle v-if="roles.includes('镇领导')">经发办意见</commonTitle>
      <commonTitle v-if="roles.includes('镇领导复核')"
        >分管镇长意见</commonTitle
      >
      <a-row>
        <a-col span="12">
          <a-form-item :label="changeTitle">
            <!-- default-value设置默认值也就是默认选中哪一个 -->
            <a-radio-group
              default-value="agree"
              @change="handleFormLayoutChange"
              v-model="optionAdvice"
            >
              <a-radio :value="0">
                {{
                  roles.includes("镇领导复核") ||
                  (roles.includes("经发公司复核员") &&
                    trialDataObj.contractInfo.auditType == 0)
                    ? "同意"
                    : "拟同意"
                }}
              </a-radio>
              <a-radio :value="1"> 不同意 </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label=" " :required="false" :colon="false">
            <!-- 复审 -->
            <a-textarea
              v-if="status == 3"
              placeholder="请输入意见具体内容"
              maxLength="500"
              v-decorator="[
                'reviewOpinions',
                {
                  rules: [{ required: fasle, message: '请输入意见具体内容' }],
                },
              ]"
            />
            <!-- 初审 -->
            <a-textarea
              v-else-if="status == 2"
              placeholder="请输入意见具体内容"
              maxLength="500"
              v-decorator="[
                'preliminaryReviewOpinion',
                {
                  rules: [{ required: fasle, message: '请输入意见具体内容' }],
                },
              ]"
            />
            <a-textarea
              v-else-if="status == 5 || status == 12"
              placeholder="请输入意见具体内容"
              maxLength="500"
              v-decorator="[
                'townReviewOpinion',
                {
                  rules: [{ required: false, message: '请输入意见具体内容' }],
                },
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>
    <!-- 底下按钮组 -->
    <a-row type="flex" justify="space-around" align="middle">
      <a-col span="10" class="flex-col">
        <a-button
          type="danger "
          @click="handleSubmit(1)"
          v-if="viewType !== 'view'"
          >驳回</a-button
        >
        <a-button type="default" @click="handleSubmit(2)">返回</a-button>
        <a-button
          type="primary"
          @click="handleSubmit(3)"
          v-if="viewType !== 'view'"
          >保存</a-button
        >
        <a-button
          type="primary"
          @click="handleSubmit(4)"
          v-if="viewType !== 'view'"
          >提交审核</a-button
        >
      </a-col>
    </a-row>
    <!-- 审批记录的弹框 -->
    <a-modal
      v-model="visible"
      title="审批记录"
      @cancel="closeModal"
      @ok="closeModal"
      :footer="null"
    >
      <ApprovalRecords :visible="visible" :id="id"></ApprovalRecords>
    </a-modal>
  </a-form>
</template>

<script>
import grayCard from "../components/GrayCard";
import commonTitle from "../components/CommonTitle";
import AgreeItemView from "../components/AgreeItemView";
import ApprovalRecords from "../components/ApprovalRecords";
import {
  ApiEditRecordReview,
  ApiGetReviewItem,
} from "../../../data/api/RegistrationRecordReview/index";
import moment from "moment";
export default {
  components: {
    AgreeItemView,
    ApprovalRecords,
    grayCard,
    commonTitle,
  },
  data() {
    return {
      isProfessional: 0,
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
      data: [], //审核详细信息
      agreementData: [], //协议列表
      form: this.$form.createForm(this, { name: "reviewer_form" }),
      //水平
      formLayout: "horizontal",
      //设置表单位置
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 4 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
      },
      optionAdvice: 0, //存放审核意见的0为同意，1为不同意
      visible: false, //控制审批记录的弹框
      status: undefined, //定义变量用来区分初审和复审的，为2代表初审，3为复审
      viewType: undefined, //接收列表传过来的标识，是否是查看页面view是查看，其余都是操作页面
      id: undefined, //接收列表传过来的合同id
      filingNumber: undefined, //接收列表传过来的备案编号
      clickSave: false, //用来标识是否已经点击了保存按钮false没有，true点击过了
      clickSubmit: false, //用来标识是否已经点击了提交按钮false没有，true点击过了
      trialDataObj: {
        contractApprovalHistorys: {}, //各节点审核对象
        contractFreePeriods: [], //免租期的基本信息
        contractInfo: {}, //初审合同的基本信息
        contractReviews: [], //初审的审核条目，每个条目是一个对象
        originalContractInfo: {}, //原合同的基本信息
      },
      //之前的假数据
      agreementList: [
        [
          {
            annex: { filePath: "1.png", fileName: "1.png" },
            numType: 1,
            title: {
              titleName: "产权性质：",
              value: "私人店铺",
            },
            agreement: 2,
            advice: "测试",
            index: 1,
          },
          {
            annex: { filePath: "1.png", fileName: "1.png" },
            numType: 1,
            title: {
              titleName: "租赁单价（元/㎡/天）：",
              value: "以营业额扣率结算",
            },
            agreement: 1,
            advice: "测试",
            index: 2,
          },
        ],
        [
          {
            numType: 2,
            title: {
              titleName: "租赁面积（㎡）：",
              value: "84.93㎡",
            },
            agreement: 1,
            advice: "测试",
            index: 3,
          },
          {
            numType: 2,
            title: {
              titleName: "累计租赁面积（㎡）：",
              value: "84.93㎡",
            },
            agreement: 1,
            advice: "测试",
            index: 4,
          },
          {
            annex: { filePath: "1.png", fileName: "1.png" },
            numType: 2,
            title: {
              titleName: "租赁期限：",
              value: "4年：2023.11.1-2027.10.31",
            },
            agreement: 1,
            advice: "测试",
            index: 5,
          },
          {
            numType: 2,
            title: {
              titleName: "价格递增机制：",
              value: "第三年起：根据实际运营状况商定",
            },
            agreement: 1,
            advice: "测试",
            index: 6,
          },
          {
            numType: 2,
            title: {
              titleName: "免租期：",
              value: "无",
            },
            agreement: 1,
            advice: "测试",
            index: 7,
          },
          {
            numType: 2,
            title: {
              titleName: "租赁保证金：",
              value: "2000",
            },
            agreement: 1,
            advice: "测试",
            index: 8,
          },
          {
            numType: 2,
            title: {
              titleName: "必备条款：",
              value: "合同内具备必备条款",
            },
            agreement: 1,
            advice: "测试",
            index: 9,
          },
          {
            numType: 2,
            title: {
              titleName: "特殊条款：",
              value:
                "第一年租金：按乙方月营业额实际营收4%的扣率作为提取作为房屋月租金；第二年租金：按乙方月营业额实际营收5%的扣率作为提取作为房屋月租金；",
            },
            agreement: 1,
            advice: "测试",
            index: 10,
          },
          {
            type: 2, //1条款，2附件
            numType: 2,
            title: {},
            annex: [{ fileName: "3-10复印件", filePath: "1.png" }],
            agreement: 1,
            advice: "测试",
          },
        ],
        [
          {
            numType: 3,
            title: {
              titleName: "资质与信用等级资料：",
              value: "承租方营业执照、法人身份证和信用报告",
            },
            annex: [
              {
                fileName: "3-10复印件",
                filePath: "营业执照复印件.png",
              },
              {
                fileName: "3-10复印件",
                filePath: "法人身份证.png",
              },
              {
                fileName: "3-10复印件",
                filePath: "信用报告.png",
              },
            ],
            agreement: 1,
            advice: "测试",
            index: 11,
          },
          {
            numType: 3,
            title: {
              titleName: "注册属地情况：",
              value: "200区属0",
            },
            agreement: 1,
            advice: "测试",
            index: 12,
          },
          {
            numType: 3,
            title: {
              titleName: "决策程序：",
              value: "已经过法律顾问审核；已经过董事会决议；已经过三重一大会议",
            },
            annex: [
              {
                fileName: "3-10复印件",
                filePath: "董事会决议复印件.png",
              },
              {
                fileName: "3-10复印件",
                filePath: "董事会决议复印件.png",
              },
            ],
            agreement: 1,
            advice: "测试",
            index: 13,
          },
        ],
      ],
    };
  },
  created() {
    this.form.getFieldDecorator("preliminaryReviewOpinion", {});
    this.form.getFieldDecorator("reviewOpinions", {});
    //获取后台传过来的数据
    ApiGetReviewItem({ filingNumber: this.$route.query.filingNumber }).then(
      (res) => {
        this.trialDataObj = res.data;
        this.isProfessional = res.data.contractInfo.isProfessional || 0;
        console.log(res.data, "res.data~~~");
        this.trialDataObj.contractReviews = res.data.contractReviews;
        //调用条款排序方法，进行排序显示在页面上
        this.trialDataObj.contractReviews = this.sortViewItem(
          this.trialDataObj.contractReviews
        );
        this.trialDataObj.contractReviews = this.trialDataObj.contractReviews.filter((res)=>{
          return !!res.titleName
        })
        this.trialDataObj.contractReviews.forEach((item) => {
          console.log(item.titleName,'sssewe')
          if (item.titleName.includes("租赁单价")) {
            this.showIsProfessional = true;
            let arr1 = item.attachments.filter(
              (it) => it.type == "pricing_basis"
            );
            let arr2 = item.attachments.filter(
              (it) => it.type == "pricing_basis_review_by_jingfa"
            );
            arr2.forEach((e) => {
              e.businessType = e.type;
              e.fileId = e.id;
              e.fileName = e.oldName;
              e.fileNewName = e.newName;
            });
            item.attachments = arr1;
            item.attachmentsIsProfessional = arr2;
          }
        });
        if (
          this.status == 2 &&
          this.trialDataObj.contractInfo.preliminaryReviewOption !== null
        ) {
          this.optionAdvice =
            this.trialDataObj.contractInfo.preliminaryReviewOption;
          console.log(this.optionAdvice, this.status, "nnnn");
        } else if (
          this.status == 3 &&
          this.trialDataObj.contractInfo.reviewOption !== null
        ) {
          this.optionAdvice = this.trialDataObj.contractInfo.reviewOption;
          console.log(this.optionAdvice, this.status, "nnnn");
        }
        console.log(this.trialDataObj, "bbbbbb");
        //给初审意见和经发公司意见赋值
        if (this.status == 3) {
          this.form.setFieldsValue({
            reviewOpinions: this.trialDataObj.contractInfo.reviewOpinions,
          });
        } else {
          this.form.setFieldsValue({
            preliminaryReviewOpinion:
              this.trialDataObj.contractInfo.preliminaryReviewOpinion,
          });
        }
      }
    );

    let temp;
    //接收从列表传过来的区分初审和复审的参数，为2代表初审，3为复审
    temp = this.$route.query.status;
    this.status = temp;
    console.log(this.status, "00000");

    //接收列表传过来的标识，是否是查看页面view是查看，其余都是操作页面
    this.viewType = this.$route.query.viewType;

    //接收列表传过来的合同id
    this.id = this.$route.query.id;

    //接收列表传过来的备案编号
    this.filingNumber = this.$route.query.filingNumber;
  },
  computed: {
    changeTitle() {
      let tempTitle;
      if (this.status == 2 && this.roles.includes("经发公司审核员")) {
        tempTitle = "复审意见";
      } else if (this.status == 3 && this.roles.includes("经发公司复核员")) {
        tempTitle = "经发公司意见";
      } else if (this.status == 5 && this.roles.includes("镇领导")) {
        tempTitle = "经发办意见";
      } else if (this.status == 12 && this.roles.includes("镇领导复核")) {
        tempTitle = "分管镇长意见";
      }
      return tempTitle;
    },
  },
  methods: {
    moment,
    //页面初审条款的排序方法
    sortViewItem(array) {
      array = JSON.parse(JSON.stringify(array));
      array.sort((a, b) => {
        return a.index - b.index;
      });
      //复制出一个新数组，操作原来的数组因为操作会改变原数组
      let newArray = [...array];
      // array.shift()它会修改原始数组，将数组的第一个元素移除，并返回该元素的值。
      //index为0代表是附件需要放到合适的位置
      let first = array.shift();
      //如果不为0代表是条款因此不能删除第一条所以返回复制后的数组
      if (first.index !== 0) {
        return newArray;
      }
      let titleName = first.titleName;
      let attachIndex = parseInt(titleName.split("-")[1]);
      first.numType = array[attachIndex - 1].numType;
      array.splice(attachIndex, 0, first);
      console.log(array, "array");
      return array;
    },
    //调用后台接口传数据
    sendData(operateType, values) {
      const params = {
        ...values,
        filingNumber: this.filingNumber,
        operateType: operateType,
        reviewOption: this.optionAdvice,
        status: this.status,
        approvalComments:
          this.status == 2
            ? values["preliminaryReviewOpinion"]
            : this.status == 3
            ? values["reviewOpinions"]
            : values["townReviewOpinion"],
      };
      ApiEditRecordReview(params).then((res) => {
        if (res.code == 0) {
          switch (operateType) {
            case "reject":
              this.$message.success("驳回成功");
              break;
            case "save":
              this.$message.success("保存成功");
              console.log("已经点击了");
              //调用接口后将变量改为true点击保存不会再调用接口
              this.clickSave = true;
              break;
            case "submit":
              this.$message.success("提交审核成功");
              this.clickSubmit = true;

              break;
          }
        }
        setTimeout(() => {
          this.$router.back(-1);
        }, 1000);
        console.log(res.data, "zzzzz");
      });
      // });
    },
    /**
     *
     * @param {*} type 1 驳回 2返回 3 保存 4 提交审核
     */
    handleSubmit(type) {
      let self = this;
      switch (type) {
        case 1:
          console.log("驳回");
          this.$confirm({
            title: "确认驳回？",
            content: "此操作将驳回，请确认是否继续",
            onOk() {
              self.form.validateFields((err, values) => {
                //如果是提交审核检验一下是否填写意见，当没有填写的时候就给出提示并且不调接口
                // if (self.status == 2 && !values["preliminaryReviewOpinion"]) {
                //   self.$message.error("请填写初审意见具体内容");
                //   return;
                // } else if (self.status == 3 && !values["reviewOpinions"]) {
                //   self.$message.error("请填写经发公司意见具体内容");
                //   return;
                // } else if (
                //   (self.status == 5 || self.status == 12) &&
                //   !values["townReviewOpinion"]
                // ) {
                //   self.$message.error("请填写意见具体内容");
                //   return;
                // }
                // optionAdvice审核意见的0为同意时，不能驳回
                if (
                  !self.optionAdvice &&
                  (self.status == 2 ||
                    self.status == 3 ||
                    self.status == 5 ||
                    self.status == 12)
                ) {
                  self.$message.error("请检查" + self.changeTitle);
                  return;
                }
                if (
                  self.roles.includes("镇领导") ||
                  self.roles.includes("镇领导复核")
                ) {
                  values["superiorsApprovalOption"] = "1";
                }
                self.sendData("reject", values);
              });
            },
            onCancel() {},
          });
          break;
        case 2:
          //ToDO
          console.log("返回");
          this.$router.back(-1);
          break;
        case 3:
          console.log("保存");
          this.form.validateFields((err, values) => {
            //如果已经点击过保存之后又点击就不让调用接口了
            if (this.clickSave === false) {
              console.log(this.clickSave, "baocunanniu");
              this.sendData("save", values);
            }
          });
          break;
        case 4:
          console.log("提交审核");
          this.$confirm({
            title: "确认提交审核？",
            content: "此操作将提交审核，请确认是否继续",
            onOk() {
              self.form.validateFields((err, values) => {
                //如果是提交审核检验一下是否填写意见，当没有填写的时候就给出提示并且不调接口
                // if (self.status == 2) {
                //   self.$message.error("请填写初审意见具体内容");
                //   return;
                // } else if (self.status == 3 && !values["reviewOpinions"]) {
                //   self.$message.error("请填写经发公司意见具体内容");
                //   return;
                // } else if (
                //   (self.status == 5 || self.status == 12) &&
                //   !values["townReviewOpinion"]
                // ) {
                //   self.$message.error("请填写意见具体内容");
                //   return;
                // }
                if (
                  self.roles.includes("镇领导") ||
                  self.roles.includes("镇领导复核")
                ) {
                  values["superiorsApprovalOption"] = "1";
                }
                // optionAdvice审核意见的1为不同意时，不能提交审核
                if (
                  self.optionAdvice &&
                  (self.status == 2 ||
                    self.status == 3 ||
                    self.status == 5 ||
                    self.status == 12)
                ) {
                  self.$message.error("请检查" + self.changeTitle);
                  return;
                }

                if (self.clickSubmit == false) {
                  self.sendData("submit", values);
                }
              });
            },
            onCancel() {},
          });
          break;
      }
    },
    //点击审批记录调用的方法
    showApprovalRecords() {
      this.visible = true;
    },
    //关闭弹框调用的方法
    closeModal() {
      this.visible = false;
    },
  },
};
</script>

<style lang="less" scoped>
// 头部
.topColumn {
  display: flex;
  > .toptitle {
    height: 32px;
    background: #f7f8fa;
    border-radius: 80px;
    width: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
  }
  > .toptitleContent {
    font-size: 22px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    text-align: LEFT;
    color: #1d2129;
    line-height: 32px;
  }
}

// 底部按钮
.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 32px;
}
//底部按钮end
</style>

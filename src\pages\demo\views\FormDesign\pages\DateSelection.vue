<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 日期选择
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <date-option-dom :data="formData"></date-option-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <date-option-form v-bind:data.sync="formData"></date-option-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 单选控件 DOM/Form
import {
  DateOptionDom,
  DateOptionForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/DateSelection";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    DateOptionDom,
    DateOptionForm
  },
  data() {
    return {
      formData: {
        inputTitle: undefined, //标题
        placeholderText: undefined, //提示文字
        timeType: "yyyy-MM-dd", //时间选择单选按钮
        notNull: true
      },
      moduleVoList: []
    };
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: this.formData.placeholderText, //提示文字
        notNull: this.formData.notNull ? 1 : 0, //是否必须
        inputType: "dateSelection",
        inputName: this.formData.timeType
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      let dataOption = [];
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        this.switchData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "dateSelection") {
              dataOption.push(item);
            }
          });
        });
        this.formData = {
          inputTitle:
            dataOption && dataOption[dataOption.length - 1].inputTitle, //标题
          placeholderText:
            dataOption && dataOption[dataOption.length - 1].placeholder, //提示文字
          timeType: dataOption && dataOption[dataOption.length - 1].inputName, //时间选择单选按钮
          notNull: dataOption && dataOption[dataOption.length - 1].notNull
        };
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

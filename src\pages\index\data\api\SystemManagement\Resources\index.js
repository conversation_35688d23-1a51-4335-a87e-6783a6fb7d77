/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 获取列表接口
 * @dec 接口： /security/resource/pageByCondition
 * @dec 命名：ApiSecurityPageByCondition
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

export function ApiSecurityPageByCondition(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/pageByCondition",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 新增接口
 * @dec 接口： /security/resource/create
 * @dec 命名：ApiSecurityCreate
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSecurityCreate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/create",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 修改接口
 * @dec 接口： /security/resource/update
 * @dec 命名：ApiSecurityUpdate
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSecurityUpdate(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/update",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 删除接口
 * @dec 接口： /security/resource/delete
 * @dec 命名：ApiSecurityDelete
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSecurityDelete(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/delete",
    method: "post",
    middle,
    params,
  });
}
/**
 * <AUTHOR>
 * @time 2020-8-14
 * @function 是否启用接口
 * @dec 接口： /security/resource/updateDisabled
 * @dec 命名：ApiSecurityUpdateDisabled
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSecurityUpdateDisabled(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/resource/updateDisabled",
    method: "post",
    middle,
    params,
  });
}

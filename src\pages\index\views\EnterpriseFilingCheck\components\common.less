.detail {
    width: 100%;
    padding: 15px 0;
    background-color: #fff;
  }
  
  .back {
    width: 112px;
    margin: 15px auto;
    height: 40px;
    border-radius: 6px;
    margin-right: 32px;
  }
  .name {
    width: 200px;
    min-height: 66px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f9ff;
    border: 1px solid #deeafb;
    font-size: 16px;
    color: #262626;
  }
  .employeesInfoOut {
    display: flex;
    width: 20%;
    margin-bottom: 8px;
    line-height: normal !important;
    .item {
      padding: 2px 6px;
      margin-right: 4px;
      color: #4c96ff;
      background-color: #e8f3ff;
      border: 1px solid #e8f3ff;
      border-radius: 4px;
      box-sizing: border-box;
      color: #262626;
    }
  }
  .partnersInfoOut {
    display: flex;
    width: 50%;
    margin-bottom: 8px;
    line-height: normal !important;
    .item {
      width: 100%;
      padding: 2px 6px;
      margin-right: 4px;
      box-sizing: border-box;
      color: #262626;
    }
    .item1 {
      padding-top: 2px;
      background-color: #fff7e8;
      color: #ffaf60;
      border: 1px solid #fff7e8;
      border-radius: 4px;
    }
    .item::before {
      content: "•";
      color: #4c96ff;
      display: inline-block;
      width: 2em;
      margin-left: -1em;
      transform: scale(3);
      text-align: center;
    }
  }
  .componyType {
    width: 160px;
    display: flex;
    align-items: center;
    text-indent: 1em;
    border: 1px solid #deeafb;
  }
  
  .aptitudeStyle {
    height: 34px;
    line-height: 34px;
    padding: 0px 12px;
    background: #e8ffea;
    border-radius: 4px;
    font-size: 16px;
    color: #00b42a;
    margin: 15px 20px;
  }
  .ant-advanced-search-form /deep/ .ant-checkbox-group {
    .ant-checkbox-group-item {
      font-size: 17px !important;
      margin-left: 68px;
      margin-bottom: 30px;
    }
  }
  .ant-advanced-search-form /deep/ .ant-col-8 {
    .ant-col-sm-8 {
      width: 31% !important;
    }
    .ant-col-sm-16 {
      width: 69% !important;
    }
  }
  .ant-advanced-search-form /deep/ .ant-col-16 {
    .ant-col-sm-8 {
      width: 15% !important;
    }
    .ant-col-sm-16 {
      width: 85% !important;
    }
  }
  .ant-advanced-search-form .ant-form-item {
    display: flex;
  }
  .ant-advanced-search-form /deep/.ant-form-item-label > label {
    font-size: 16px;
  }
  
  .ant-advanced-search-form /deep/.ant-input {
    height: 40px;
    border-radius: 4px;
    box-sizing: border-box;
  }
  .ant-advanced-search-form /deep/ .ant-select-selection {
    height: 40px !important;
    line-height: 40px !important;
  }
  .ant-advanced-search-form /deep/ .ant-select-selection__rendered {
    height: 40px !important;
    line-height: 40px !important;
  }
  .ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
    content: "";
  }
  .ant-advanced-search-form /deep/ .ant-form-item-label {
    margin-right: 8px;
    // width: 166px !important;
  }
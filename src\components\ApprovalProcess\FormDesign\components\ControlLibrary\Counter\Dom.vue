<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 计数器控件
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '计数器'"
      style="margin-bottom:unset"
      prop="counter"
      :rules="[
        {
          required: data.notNull,
          message: '请输入',
          trigger: 'blur'
        }
      ]"
      ><a-input-number
        v-model="form.counter"
        :max="data.optionsData.max"
        :min="data.optionsData.min"
        :step="data.optionsData.step"
        :precision="data.optionsData.precision"
        :placeholder="data.placeholder.placeholderText || '请输入'"
      ></a-input-number>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return { placeholder: "" };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        counter: null
      }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

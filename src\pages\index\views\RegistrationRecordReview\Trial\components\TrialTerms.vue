<template>
  <!-- 动态绑定类属性当后面的值为true时就使用它前面的这个类名 -->
  <!-- 这个渲染的是条款的背景颜色 -->
  <div
    class="agree-item-view-left"
    :class="{
      'agree-item-view-left-bg1': numType == 1,
      'agree-item-view-left-bg2': numType == 2,
      'agree-item-view-left-bg3': numType == 3,
      'agree-item-view-left-bg4': numType == 4,
      'agree-item-view-left-bg5': numType == 5
    }"
  >
    <!-- 这个渲染的是条款的编号颜色 -->
    <!-- 如果index有值就渲染并显示，没有值就不显示 -->
    <div
      v-if="index"
      class="agree-item-left-num"
      :class="{
        'agree-item-left-num-bg1': numType == 1,
        'agree-item-left-num-bg2': numType == 2,
        'agree-item-left-num-bg3': numType == 3,
        'agree-item-left-num-bg4': numType == 4,
        'agree-item-left-num-bg5': numType == 5
      }"
    >
      {{ index }}
    </div>
    <div class="agree-item-left-content">
      <!-- 条款的标题有值就显示 -->
      <!-- Array.isArray() 静态方法用于确定传递的值是否是一个数组。 -->
      <!-- 如果是一个数组或者有上传的附件就使用类名agree-item-left-content-title-border -->
      <div
        v-if="title.titleName"
        class="agree-item-left-content-title"
        :class="{
          'agree-item-left-content-title-border':
            Array.isArray(annex) || annex.filePath
        }"
      >
        <span class="agree-item-left-content-title-name">
          {{ title.titleName }}</span
        >
        <span class="agree-item-left-content-title-value">
          {{ title.value }}
        </span>
      </div>
      <!-- 如果有附件就显示 -->
      <div class="agree-item-left-content-desc" v-if="annex">
        <!-- 选项类型 -->
        <!-- 如果index有值就显示 -->
        <template v-if="index">
          <!-- 传入数组 -->
          <!-- 判断传入的是否是一个数组，是数组就显示 -->
          <template v-if="Array.isArray(annex)">
            <!-- 循环显示附件数组中的数据 -->
            <a
              :href="annex.filePath"
              download="附件.png"
              :key="key"
              v-for="(item, key) in annex"
              >{{ item.filePath }}</a
            >
          </template>
          <!-- 传入的附件不是多个也就是不是数组显示下面这个 -->
          <a
            v-else-if="annex.filePath"
            :href="annex.filePath"
            download="附件.png"
            >{{ annex.filePath }}</a
          >
        </template>
        <!-- 附件类型 -->
        <!-- 没有index显示下面这个 -->
        <template v-else>
          <template>
            {{ annex.fileName }}:
            <a
              :href="item"
              download="附件.png"
              :key="key"
              class="annexList"
              v-for="(item, key) in annex.filePath"
              >{{ item }}</a
            >
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  //父组件传过来的值
  props: {
    //条款的序号
    numType: {
      type: Number,
      default: 1
    },
    value: {
      type: Object,
      default: () => ({})
    },
    // 上传的附件
    annex: {
      type: Object,
      default: () => {
        return null;
      }
    },
    index: {
      type: [Number, String],
      default: ""
    },
    //条款的标题和值
    title: {
      type: Object,
      default: () => ({ titleName: "titleName", value: "title" })
    }
  }
};
</script>

<style lang="less" scoped>
.agree-item-view-left {
  display: flex;
  width: 100%;
  display: flex;
  background: #f0fcf8;
  border-radius: 6px;
  //green
  &-bg1 {
    background: #f0fcf8;
  }
  //blue
  &-bg2 {
    background: #eef7ff;
  }
  // yellow
  &-bg3 {
    background: #fff7ec;
  }
  //setup-youself
  &-bg4 {
    background: #f0fcf8;
  }
  > .agree-item-left-num {
    width: 34px;

    border-radius: 6px 0px 0px 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: #fff;
  }
  > .agree-item-left-num-bg1 {
    background: #26c06d;
  }
  > .agree-item-left-num-bg2 {
    background: #1777ff;
  }
  > .agree-item-left-num-bg3 {
    background: #ec9a50;
  }
  > .agree-item-left-num-bg4 {
    background: #26c06d;
  }
  > .agree-item-left-content {
    padding: 0 3%;
    width: 100%;

    .agree-item-left-content-title {
      display: flex;
      width: 50%;
      width: 100%;
      align-items: center;
      font-size: 16px;
      padding: 19px 0px 19px 0px;
      > .agree-item-left-content-title-name {
        color: #86909c;
        white-space: nowrap;
      }
      > .agree-item-left-content-title-value {
        color: #000;
      }
    }
    > .agree-item-left-content-title-border {
      border-bottom: 1px solid #999;
    }
    > .agree-item-left-content-desc {
      display: flex;
      align-items: center;
      font-size: 16px;
      > a {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
      }
    }
  }
}
</style>

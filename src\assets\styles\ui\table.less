// 表格强制短行
.ant-table {
  word-break: break-all;
}
// 数据列表 样式
.table-alert {
    margin-bottom: 16px;
  }
  // 数据列表 操作
  .table-operator {
    margin-bottom: 18px;
  
    button {
      margin-right: 8px;
    }
  }
  // 数据列表 搜索条件
  .table-page-search-wrapper {
  
    .ant-form-inline {
      .ant-form-item {
        display: flex;
        margin-bottom: 24px;
        margin-right: 0;
  
        .ant-form-item-control-wrapper {
          flex: 1 1;
          display: inline-block;
          vertical-align: middle;
        }
  
        > .ant-form-item-label {
          line-height: 32px;
          padding-right: 8px;
          width: auto;
        }
        .ant-form-item-control {
          height: 32px;
          line-height: 32px;
        }
      }
    }
  
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
  }
  
  @media (max-width: @screen-xs) {
    .ant-table {
      width: 100%;
      overflow-x: auto;
      &-thead > tr,
      &-tbody > tr {
        > th,
        > td {
          white-space: pre;
          > span {
            display: block;
          }
        }
      }
    }
  }
  
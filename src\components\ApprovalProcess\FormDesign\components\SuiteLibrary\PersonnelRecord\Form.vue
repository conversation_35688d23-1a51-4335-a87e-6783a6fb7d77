<!--
* <AUTHOR>
* @time 2020-9-21
* @dec 机房人员备案套件表单配置
-->
<template>
  <div>
    <!-- <h3>机房人员备案套件:</h3> -->
    <!-- <div class="person-record-form">
      <div class="person-record-form-div">
        <p class="person-record-form-p">航站楼</p>
        <div class="person-record-form-cpuroom">T1机房</div>
        <div class="person-record-form-cpuroom">T2机房</div>
      </div>
      <div class="person-record-form-div">
        <p class="person-record-form-p">支持单选。</p>
      </div>
      <div class="person-record-form-div">
        <p class="person-record-form-p">
          点击添加则在下方增加一条填写行。可支持删除及修改即可
        </p>
      </div>
    </div> -->
    <a-table
      :columns="columns"
      :data-source="form.optionsData.regex"
    >
      <span slot="isRegex" slot-scope="text, record">
        <template>
          <a-switch v-model="record.isRegex" />
        </template>
      </span>
    </a-table>
  </div>
</template>
<script>
const columns = [
  {
    title: "字段",
    dataIndex: "name",
    key: "name",
    // width: 200,
    scopedSlots: { customRender: "name" }
  },
  {
    title: "是否验证",
    dataIndex: "isRegex",
    key: "isRegex",
    width: 100,
    scopedSlots: { customRender: "isRegex" }
  }
];

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      columns,
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        inputTitle: "机房人员备案套件",
        optionsData: {
          businessType: undefined,
          // 参与校验逻辑
          regex: [
            {
              name: "航站楼",
              isRegex: true
            }
          ]
        }
      }
    };
  },

  watch: {
    data: {
      handler: function(data) {
        if (data.optionsData && data.optionsData.regex) this.form = data;
      }
    },
    form: {
      handler: function() {
        setTimeout(() => {
          this.$emit("update:data", this.form);
        }, 50);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {}
};
</script>
<style lang="less" scoped>
// .person-record-form {
//   padding: 20px;
// }
// .person-record-form-div {
//   margin: 10px;
// }
// .person-record-form-p {
//   font-weight: bold;
// }
// .person-record-form-cpuroom {
//   margin-left: 20px;
// }
</style>

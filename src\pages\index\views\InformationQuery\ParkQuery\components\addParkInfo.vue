<template>
  <div class="form">
    <a-modal
      title="园区基础信息"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model :model="modalForm" ref="ruleForm" :rules="rules">
        <a-form-model-item
          prop="park"
          label="园区名称"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            style="width: 20.5em"
            :disabled="disabled"
            v-model="park"
          ></a-input>
          <div class="button_left" >
            <a-button type="primary" @click="changeParkName">
              编辑
            </a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="园区负责人"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          prop="parkManager"
        >
          <a-select
            style="width: 20.5em"
            v-model="modalForm.parkManager"
            placeholder="全部"
            @change="handleChange"
          >
            <a-select-option
              style="width: 20.5em"
              :value="item.id"
              v-for="item in leaderObj"
              :key="item.id"
              >{{ item.name }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          label="园区租金"
          prop="rent"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input-number
            :disabled="noEdit"
            v-model="modalForm.rent"
            placeholder="请输入租金"
            style="width: 20.5em"
            :min="0"
            :step="0.01"
            :precision="2"
            @change="onChange"
          />
        </a-form-model-item>
        <a-form-model-item
          label="园区物业费"
          prop="propertyFee"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input-number
            :disabled="noEdit"
            v-model="modalForm.propertyFee"
            placeholder="请输入物业费"
            style="width: 20.5em"
            :min="0"
            :step="0.01"
            :precision="2"
            @change="onChange"
          />
        </a-form-model-item>
        <a-form-model-item
          prop="parkDescription"
          label="园区介绍"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-textarea
            style="width: 20.5em"
            v-model="modalForm.parkDescription"
            placeholder="请输入园区介绍"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-form-model-item>
        <a-form-model-item
          prop="addressPhoto"
          label="园区区位图"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <Importer
            :useCustom="true"
            businessId="location_map"
            accept=".png,.jpg,.jpeg"
            @handleFileCallback="file => handleAreaFileCallback(file, record)"
          >
            <!-- 这里运用了具名插槽，将这个插入到子组件slot的地方 -->
            <template #import>
              <a-button type="primary" :disabled="areaButton"
                >上传区位图</a-button
              >
            </template>
          </Importer>
          <div
            class="pic"
            style="position:relative;width:128px;height:auto;"
            v-if="areaButton"
          >
            <img
              style="width:128px;height:auto;"
              :src="
                areaFileList[0] != null
                  ? `${domain_url}/common/attachment/show?id=${areaFileList[0]}`
                  : null
              "
            />
            <div
              class="icon-container"
              style="position:absolute;top:0px;right:8px;width:10px;height:10px;"
              v-if="!noEdit"
            >
              <a-popconfirm
                title="您确认要删除区位图吗"
                ok-text="确认"
                cancel-text="取消"
                @confirm="confirmArea(areaFileList[0])"
              >
                <!-- <a href="#" v-if="!noEdit" style="width: 50px;">删除</a> -->

                <a-icon
                  type="delete"
                  style="font-size:18px;color:red;"
                  class="delete-icon"
                />
              </a-popconfirm>
            </div>
          </div>
        </a-form-model-item>
        <a-form-model-item
          prop="photo"
          label="园区相册"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <!-- 引入封装的上传组件，useCustom自定义按钮，accept上传的类型 -->
          <Importer
            useCustom="true"
            businessId="park_photo_album"
            accept=".png,.jpg,.jpeg"
            @handleFileCallback="
              file => handlePictureFileCallback(file, record)
            "
          >
            <!-- 这里运用了具名插槽，将这个插入到子组件Importer的slot的地方 -->
            <template #import>
              <a-button type="primary" :disabled="picButton"
                >上传园区图片</a-button
              >
            </template>
          </Importer>
          <template v-if="picShow">
            <div
              class="pic"
              v-for="(item, index) in pictureFileList"
              :key="index"
              style="position:relative;width:128px;height:auto;margin-bottom:4px;"
            >
              <img
                style="width:128px;height:auto;"
                :src="
                  item != null
                    ? `${domain_url}/common/attachment/show?id=${item}`
                    : null
                "
              />
              <div
                class="icon-container"
                style="position:absolute;top:0px;right:8px;width:10px;height:10px;"
                v-if="!noEdit"
              >
                <a-popconfirm
                  title="您确认要删除图片吗"
                  ok-text="确认"
                  cancel-text="取消"
                  @confirm="confirmPic(item)"
                >
                  <!-- <a href="#" v-if="!noEdit" style="width: 50px;">删除</a> -->

                  <a-icon
                    type="delete"
                    style="font-size:18px;color:red;"
                    class="delete-icon"
                  />
                </a-popconfirm>
              </div>
            </div>
          </template>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="修改园区名称"
      :visible="visibleParkName"
      @ok="handleParkNameOk"
      @cancel="handleParkNameCancel"
    >
      <a-form-model :model="modalFormEdit" ref="ruleFormParkName" :rules="rulesParkName">
        <a-form-model-item
          prop="parkName"
          label="园区名称"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            style="width: 20.5em"
            v-model="modalFormEdit.parkName"
          ></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import Importer from "@/components/Importer";
import {
  ApiEditParkInfo,
  ApiGetParkManger,
  ApiDeletePicture,
  ApiParkModify
} from "@/pages/index/data/api/InfomationQuery/index.js";
import { BASE_URL } from "Config";
export default {
  components: {
    Importer
  },
  props: {
    parkName: String,
    id: String,
    visible: Boolean,
    formData: Object
  },
  data() {
    return {
      isFlag:true,
      visibleParkName:false,
      rulesParkName: {
        parkName: [
          { required: true, message: "请输入园区名称", trigger: "blur" },
        ]
      },
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      disabled: true, //标识园区名称不可填写
      modalForm: {
        parkManager: "",
        rent: null,
        propertyFee: null,
        parkDescription: ""
      },
      modalFormEdit:{
        parkName:"",
      },
      leaderObj: [],
      pictureFileList: [], //定义变量接收园区相册上传的数据
      areaFileList: [], //定义变量接收区位图上传的数据
      picButton: false, //默认上传图片相册按钮可点击
      areaButton: false, //默认上传区位图按钮可点击
      picShow: false, //默认是没有图片要展示的
      domain_url: ""
    };
  },
  // computed: {
  //   park() {
  //     return this.parkName;
  //   }
  // },
  mounted() {
    this.$set(this, "domain_url", BASE_URL);
    this.getParkManger();
  },
  watch: {
    visible: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.park = this.parkName
        }
      }
    },
    visibleParkName: {
      immediate: true,
      handler: function (val) {
        if (val) {
          this.modalFormEdit.parkName = this.park
        }
      }
    },
    formData(newVal, oldVal) {
      console.log("formData发生变化了：", newVal, oldVal);
      this.modalForm = newVal;
      const tempPicArr = this.modalForm.parkPhotos;
      this.pictureFileList = tempPicArr.map(item => {
        return item.id;
      });
      if (this.pictureFileList.length != 0) {
        this.picShow = true;
        if (this.pictureFileList.length == 3) {
          this.picButton = true;
        }
      }
      const tempAreaArr = this.modalForm.locationMaps;
      this.areaFileList = tempAreaArr.map(item => {
        return item.id;
      });
      if (this.areaFileList.length != 0) {
        this.areaButton = true;
      }
      console.log(this.pictureFileList, this.areaFileList, "ssssss");
    }
  },
  methods: {
    //获取负责人列表
    getParkManger() {
      ApiGetParkManger({ roleName: "实业公司" }).then(res => {
        console.log(res.data, "fffff");
        this.leaderObj = res.data;
      });
    },
    //点击区位图的删除按钮调用的方法
    confirmArea(id) {
      //将要删除的图片id传入调用删除接口
      console.log(id, "hhhhh");
      ApiDeletePicture({ id: id }).then(res => {
        if (res.code == 0) {
          //将删除的图片从图片数组中移除不再显示，将上传按钮启用
          const tempArr = this.areaFileList.filter(item => item !== id);
          //将新的数组重新赋值给图片数组，重新渲染页面
          this.areaFileList = tempArr;
          if (tempArr.length == 0) {
            this.areaButton = false;
          }
        }
      });
    },
    //点击相册图片删除调用的方法
    confirmPic(id) {
      ApiDeletePicture({ id: id }).then(res => {
        if (res.code == 0) {
          const tempArr = this.pictureFileList.filter(item => item !== id);
          this.pictureFileList = tempArr;
          if (tempArr.length < 3) {
            this.picButton = false;
          }
        }
      });
    },
    //园区相册上传之后的回调函数
    handlePictureFileCallback(file) {
      console.log(file);
      //将图片的id都添加在数组中
      this.pictureFileList.push(file.fileId);
      //如果图片没有超过3张可以一直回显图片
      if (this.pictureFileList.length < 4) {
        this.picShow = true;
      }
      //如果图片已经上传了3张就将上传按钮禁用
      if (this.pictureFileList.length >= 3) {
        this.picButton = true;
      }
      console.log(this.pictureFileList, "999999");
    },
    //区位图上传之后的回调函数
    handleAreaFileCallback(file) {
      console.log(file, "ppppp");
      //将区位图的id都添加在数组中
      this.areaFileList.push(file.fileId);
      console.log(this.areaFileList, "?????");
      //区位图只能上传一张，一张之后就将按钮禁用
      if (this.areaFileList.length >= 1) {
        this.areaButton = true;
      }
    },
    //修改园区名称弹窗
    changeParkName(){
      this.visibleParkName = true
    },
    handleParkNameCancel(){
      this.modalFormEdit.parkName = ''
      this.visibleParkName = false
      this.$refs.ruleFormParkName.resetFields()
    },
    //修改园区名称弹窗确认调用的方法
    handleParkNameOk(){
      if(!this.isFlag) return
      this.isFlag = false
      this.$refs.ruleFormParkName.validate((valid) => {
        if (valid) {
          ApiParkModify({parkName:this.modalFormEdit.parkName,parkId: this.id}).then(res => {
            if (res.code == 0) {
              this.$message.info(res.data);
              this.park = this.modalFormEdit.parkName
              this.handleParkNameCancel()
              this.$emit("refresh", true);
            }else{
              this.$message.error(res.data||res.msg);
            }
          }).catch((error) => {
            console.log('error',error);
            this.$message.error(error.data.msg);
          })
        }
      });
      setTimeout(() => {
        this.isFlag = true;
      }, 2000);
    },
    //弹框点击确认调用的方法
    handleOk: function() {
      console.log(this.parkName, this.id, "kkkkk");
      //将数组变成数组对象的形式
      const objects = [...this.pictureFileList, ...this.areaFileList].map(
        item => ({
          id: item
        })
      );
      ApiEditParkInfo({
        ...this.modalForm,
        parkName: this.park,
        parkId: this.id,
        attachments: objects
      }).then(res => {
        console.log(res.data);
      });
      //将弹框隐藏
      this.$emit("close", false);
      // this.refresh = true;
      // this.$emit("refresh", this.refresh);
      //将上传图片区位图的数组都清空
      this.pictureFileList = [];
      this.areaFileList = [];
      //将上传按钮都启用
      this.picButton = false;
      //因为区位图就一张所以图片展示位置和按钮可以公用一个变量
      this.areaButton = false;
      //将展示图片的位置隐藏
      this.picShow = false;
      this.modalForm = {
        parkManager: "",
        rent: null,
        propertyFee: null,
        parkDescription: ""
      };
    },
    //关闭弹框调用的方法
    handleCancel: function() {
      //将弹框隐藏
      this.$emit("close", false);
      this.pictureFileList = [];
      this.areaFileList = [];
      this.picButton = false;
      this.areaButton = false;
      this.picShow = false;
      this.modalForm = {
        parkManager: "",
        rent: null,
        propertyFee: null,
        parkDescription: ""
      };
    }
  }
};
</script>
<style lang="less" scoped>
.form {
}
/deep/.screen-avatar > img {
  object-fit: contain;
}
.pic > .icon-container {
  display: none;
}
.pic:hover > .icon-container {
  display: block;
}
.delete-icon {
  cursor: pointer;
}
.button_left{
  position: absolute;
  top: -11px;
  right: -70px;
}
</style>

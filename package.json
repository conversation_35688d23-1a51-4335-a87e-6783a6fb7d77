{"name": "hongqiao-airport-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "mock": "cross-env BUILD_ENV=mock vue-cli-service serve --mode development", "test": "vue-cli-service serve --mode testing", "pro": "vue-cli-service serve --mode production", "build": "vue-cli-service build --mode production", "dll": "cross-env NODE_ENV=production node webpack/bin/dll", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@babel/polyfill": "^7.4.4", "@vue-office/docx": "^1.6.0", "@vue-office/pdf": "^1.6.0", "@vue/composition-api": "^1.7.2", "clipboard": "^2.0.6", "crypto-js": "^4.0.0", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "html2pdf-2img": "^1.0.8", "lodash": "^4.17.21", "moment": "^2.29.4", "particles.js": "^2.0.0", "sortablejs": "^1.10.2", "vue-demi": "^0.14.6", "vue2-editor": "^2.10.2"}, "devDependencies": {"@ant-design/colors": "^4.0.1", "@babel/core": "^7.4.4", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-proposal-object-rest-spread": "^7.4.4", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/runtime": "^7.4.4", "@babel/runtime-corejs2": "^7.4.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-unit-jest": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^1.0.3", "ant-design-vue": "^1.6.4", "axios": "^0.18.0", "babel-eslint": "^10.1.0", "chalk": "^2.4.1", "compression-webpack-plugin": "6.0.4", "copy-webpack-plugin": "^5.0.3", "core-js": "^3.6.5", "cross-env": "^5.2.0", "deepmerge": "^4.2.2", "enquire.js": "^2.1.6", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "fast-deep-equal": "^3.1.3", "html-webpack-include-assets-plugin": "^1.0.10", "js-cookie": "^2.2.1", "less": "^3.0.4", "less-loader": "^5.0.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "mockjs": "^1.0.1-beta3", "nprogress": "", "prettier": "^1.19.1", "style-resources-loader": "^1.3.2", "svg-sprite-loader": "^4.1.3", "throttle-debounce": "^2.3.0", "uglifyjs-webpack-plugin": "^1.3.0", "vue": "2.6.12", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-i18n": "^8.18.2", "vue-template-compiler": "2.6.12", "webpack": "^4.32.0", "webpack-cli": "^3.3.2", "webpack-theme-color-replacer": "^1.3.12", "webpackbar": "^2.6.4"}}
import api, { formDownLoad } from "@/common/api";
import { BASE_URL } from "Config";

// import _ from "lodash"
/**
 * 查询关联企业列表
 */
export function ApiGetParkReport(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryParkAnalysReportV2",
    method: "post",
    params,
  });
}

/**
 * 企业分析报表的查询列表
 */
export function ApiGetEnterpriseReportData(params) {
  return api({
    url: BASE_URL + "/company/port/pageByCondition",
    method: "post",
    params,
  });
}
/**
 * 企业纳税分析查询
 */
export function ApiGetTaxAnalysis(params) {
  return api({
    url: process.env.VUE_APP_MOCK_HOST_DEV
      ? "/Mock/reportManager/getTaxAnalysisReport"
      : BASE_URL + "/reportManager/getTaxAnalysisReport",
    method: "post",
    params,
  });
}
/**
 * 园区导出接口
 * @param {*} params
 * @returns
 */
export function ApiExportReportPark(params) {
  //1
  // return api({
  //   url: BASE_URL + "/manage/admin/exportParkAnalysReport",
  //   method: "post",
  //   params,
  //   headers: {
  //     "Content-Type": "application/json"
  //   },
  //   responseType: "blob"
  // })
  //2
  // var xhr = new XMLHttpRequest()
  // xhr.open(
  //   "post",
  //   `${BASE_URL}/manage/admin/exportParkAnalysReport`,
  //   true
  // )
  // xhr.responseType = "blob"
  // // 设置请求头
  // xhr.setRequestHeader("Content-Type", "application/json")
  //3
  // xhr.onload = function() {
  //   if (xhr.status === 200) {
  //     var blob = xhr.response
  //     console.log("blob", blob)
  //     var url = window.URL.createObjectURL(new Blob([blob]))
  //     var a = document.createElement("a")
  //     a.style.display = "none"
  //     a.href = url
  //     a.download = "园区导出.xlsx"
  //     document.body.appendChild(a)
  //     a.click()
  //     window.URL.revokeObjectURL(url)
  //   } else {
  //     console.error("Error:", xhr.status, xhr.statusText)
  //   }
  // }
  // let values = ""
  // let formData = new FormData()

  // _.forIn(params, (value, key) => {
  //   formData.append(key, value)
  // })
  // console.log("values", values)
  // xhr.send(JSON.stringify({}))
  // return api({
  //   url: BASE_URL + "/manage/admin/exportParkAnalysReport",
  //   method: "post",
  //   params,
  //   isPost: true,
  //   headers: {
  //     "Content-Type": "application/json"
  //   },
  //   responseType: "blob"
  // }).then((data) => {
  //   let url = window.URL.createObjectURL(new Blob([data]))
  //   let link = document.createElement("a")
  //   link.style.display = "none" //设置隐藏创建的标签
  //   link.href = url
  //   link.setAttribute("download", `下载文件.xlsx`) //下载文件名称
  //   document.body.appendChild(link)
  //   link.click() //点击下载
  //   link.remove() //下载后移除
  //   window.URL.revokeObjectURL(link.href) //用完之后使用URL.revokeObjectURL()释放；
  // })
  //4
  return formDownLoad("/manage/admin/exportParkAnalysReport", params);
}

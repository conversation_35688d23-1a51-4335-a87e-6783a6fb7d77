// 此配置为系统默认设置，需修改的设置项，在src/config/config.js中添加修改项即可。也可直接在此文件中修改。
module.exports = {
  lang: "CN", //语言，可选 CN(简体)、HK(繁体)、US(英语)，也可扩展其它语言
  theme: {
    //主题
    color: "#3eaf7c", //主题色
    mode: "dark", //主题模式 可选 dark、 light 和 night
    success: "#52c41a", //成功色
    warning: "#faad14", //警告色
    error: "#f5222d", //错误色
  },
  layout: "side", //导航布局，可选 side 和 head，分别为侧边导航和顶部导航
  fixedHeader: false, //固定头部状态栏，true:固定，false:不固定
  fixedSideBar: true, //固定侧边栏，true:固定，false:不固定
  weekMode: false, //色弱模式，true:开启，false:不开启
  multiPage: false, //多页签模式，true:开启，false:不开启
  hideSetting: true, //隐藏设置抽屉，true:隐藏，false:不隐藏
  systemName: process.env.VUE_APP_NAME, //系统名称
  copyright: "", //copyright
  animate: {
    //动画设置
    disabled: true, //禁用动画，true:禁用，false:启用
    name: "bounce", //动画效果，支持的动画效果可参考 ./animate.config.js
    direction: "left", //动画方向，切换页面时动画的方向，参考 ./animate.config.js
  },
  footerLinks: [],
};

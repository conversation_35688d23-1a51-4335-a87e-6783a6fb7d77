import api from "@/common/api"
import { BASE_URL } from "Config"
export function ApiGetRecordReviewpageByCondition(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/pageByCondition",
    method: "POST",
    params
  })
}
/**
 *查询审核详情
 * @param {*} params
 * @returns
 */
export function ApiGetReviewItem(params) {
  return api({
    url: BASE_URL + "/leaseReviewAudit/economicCompanyAuditQuery",
    method: "POST",
    params
  })
}
/**
 * 审核详情编辑
 * @param {*} params
 * @returns
 */
export function ApiEditRecordReview(params) {
  return api({
    url: BASE_URL + "/leaseReviewAudit/economicCompanyAuditSave",
    method: "POST",
    params
  })
}
/**
 * 上传附件更改状态
 */
export function ApiUpdateStatusByAttachment(params) {
  return api({
    url: BASE_URL + "/manager/ContractInfo/updateContractStatus",
    method: "POST",
    params
  })
}
/**
 * 资料归档岗驳回
 * @param {*} params 
 * filingNumber 出租备案编号 
 * status 状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核 7:上传合同 8:完成 9:经发驳回 11:上传初审表 12：镇领导复核 13：上传会议纪要
 * @returns 
 */
export function ApiEconomicCompanyAuditReject(params) {
  return api({
    url: BASE_URL + "/leaseReviewAudit/economicCompanyAuditReject",
    method: "POST",
    params
  })
}
<!--
* <AUTHOR>
* @time 2020-9-9
* @dec 门户管理 - 公告管理
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="8">
            <a-form-item label="类型">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                placeholder="请选择类型"
                v-model="queryParam.groupId"
              >
                <a-select-option
                  v-for="(item, index) in typeData"
                  :key="index"
                  :value="item.dictValue"
                >
                  {{ item.nameCn }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="8">
            <a-form-item label="标题">
              <a-input
                placeholder="请输入标题名称"
                v-model="queryParam.title"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button style="margin-left: 8px" @click="handResetForm"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd"
        >添加文章</a-button
      >
    </div>
    <s-table
      ref="table"
      size="default"
      rowKey="id"
      :columns="columns"
      :data="loadData"
    >
      <span slot="groupId" slot-scope="text">
        {{ formatText(text) }}
      </span>
      <span slot="isEnable" slot-scope="text, record">
        <a-switch
          :checked="!record.isEnable"
          @change="handleChangeDisabled(record.isEnable, record)"
        />
      </span>
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record)">修改</a>
          <a-divider type="vertical" />
          <a-popconfirm
            placement="bottomLeft"
            ok-text="确认"
            cancel-text="取消"
            @confirm="handleDeleteOk(record)"
          >
            <template slot="title">
              <p>确认删除？</p>
            </template>
            <a>删除</a>
          </a-popconfirm>
        </template>
      </span>
    </s-table>
    <create-form
      ref="createModal"
      :visible="visible"
      :data="mdl"
      :typeData="typeData"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>
<script>
// 表格组件
import STable from "@/components/Table";
// API接口
import {
  ApiActivitiAnnouncementCondition,
  ApiActivitiUpdate,
  ApiActivitiDelete,
} from "@/pages/index/data/api/PortalManagement/Notice";
import CreateForm from "./compontents/CreateForm";
const columns = [
  {
    title: "类型",
    dataIndex: "groupId",
    scopedSlots: { customRender: "groupId" },
  },
  {
    title: "标题",
    dataIndex: "title",
  },
  {
    title: "排序",
    dataIndex: "orderBy",
  },
  {
    title: "是否启用",
    width: "150px",
    dataIndex: " isEnable",
    scopedSlots: { customRender: "isEnable" },
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "150px",
    scopedSlots: { customRender: "action" },
  },
];

export default {
  name: "Notice",
  components: {
    STable,
    CreateForm,
  },
  data() {
    this.columns = columns;
    return {
      visible: false, //是否显示新增修改框
      mdl: null, //传入数据到新增
      typeData: [], //获取的分组类型
      // 查询参数
      queryParam: {
        currentPage: 1,
        title: undefined, //标题
        groupId: undefined, //分组id
        // pageSize: 10,
        resourceType: 2,
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        this.queryParam.currentPage = parameter.pageNo;
        this.queryParam.current = this.queryParam.currentPage;
        const requestParameters = Object.assign({}, parameter, this.queryParam);
        return ApiActivitiAnnouncementCondition(requestParameters).then(
          (res) => {
            return res.data;
          }
        );
      },
    };
  },
  mounted() {
    this.typeData = this.$store.getters["dictionaries/getType"](
      "announcement_type"
    );
  },
  methods: {
    formatText(text) {
      if (this.typeData) {
        return (
          this.typeData.filter((i) => i.dictValue === text)[0] &&
          this.typeData.filter((i) => i.dictValue === text)[0].nameCn
        );
      }
    },
    //确认删除当前一列
    handleDeleteOk(record) {
      ApiActivitiDelete({ id: record.id })
        .then(() => {
          this.$refs.table.refresh(true);
          this.$message.info("删除成功");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 新增表单
    handleAdd() {
      this.mdl = null;
      this.visible = true;
    },
    // 重置表单
    handResetForm() {
      this.queryParam = {
        currentPage: 1,
        title: undefined, //标题
        groupId: undefined, //分组id
        // pageSize: "10",
        resourceType: 2,
      };
    },
    // 修改表单
    handleEdit(record) {
      this.visible = true;
      this.mdl = { ...record };
    },
    // 是否启用表格调接口
    handleChangeDisabled(isEnable, record) {
      record.isEnable = !isEnable;
      let params = {
        orderBy: record.orderBy,
        isEnable: !isEnable,
        title: record.title,
        id: record.id,
      };
      ApiActivitiUpdate(params)
        .then(() => {
          if (isEnable) {
            this.$message.info("启用成功");
          } else {
            this.$message.info("禁用成功");
          }
        })
        .catch(() => {
          record.isEnable = !record.isEnable;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消表单弹框
    handleCancel() {
      this.visible = false;
      const form = this.$refs.createModal.form;
      form.resetFields(); // 清理表单数据（可不做）
    },
  },
};
</script>

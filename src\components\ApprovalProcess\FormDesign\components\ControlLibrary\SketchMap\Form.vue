<!--
* <AUTHOR>
* @time 2021-1-4
* @dec 示意图控件 
-->
<template>
  <a-form-model
    :rules="rules"
    :model="form"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
  >
    <a-form-model-item label="示意图标题" help="标题最多20字" props="fileSize">
      <a-input
        v-model="form.inputTitle"
        placeholder="请输入示意图标题"
        maxLength="20"
      />
    </a-form-model-item>
    <a-form-model-item class="sketchMap">
      <a-upload
        action=""
        list-type="picture-card"
        :file-list="fileList"
        :before-upload="beforeUpload"
        @preview="handlePreview"
        @change="handleChange"
      >
        <div v-if="fileList.length < 1">
          <a-icon type="plus" />
          <div class="ant-upload-text">
            选择上传
          </div>
        </div>
      </a-upload>
      <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
        <img alt="" style="width: 100%" :src="previewImage" />
      </a-modal>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { Apicommonupload } from "@/data/api/components/ApprovalProcess";

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      fileList: [],
      previewVisible: false,
      previewImage: "",
      form: {
        inputTitle: null,
        optionsData: {}
      }
    };
  },
  watch: {
    data(data) {
      if (data.optionsData.thumbnailUrl) {
        this.fileList = [
          { uid: Math.random(), url: data.optionsData.thumbnailUrl }
        ];
      }
      this.form = data;
    },
    form: {
      handler: function(form) {
        // console.log("表单", form);
        this.$emit("update:data", form);
      },
      deep: true
    }
  },
  methods: {
    // 上传图片调用接口
    handleChange({ file, fileList }) {
      if (file.status === "removed") {
        this.fileList = [];
        this.$set(this.form.optionsData, "thumbnailUrl", "");
        return;
      }
      // console.log("file", file);
      // console.log("fileList", fileList);
      if (file.name) {
        let fileType = file.name.substr(file.name.lastIndexOf(".") + 1);
        let checkFileType = this.isAssetTypeAnImage(fileType);
        if (!checkFileType) {
          this.$message.error("请上传指定的图片类型！");
          return false;
        }
      }

      this.fileList = fileList;
      if (fileList.length > 1 || fileList.length < 1) {
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("图片大小不能超过10MB!");
        return false;
      }
      let params = {
        objectId: "5", //待处理
        name: "1",
        type: "jpg",
        file: file
      };
      Apicommonupload(params).then(res => {
        this.$set(this.form.optionsData, "thumbnailUrl", res.data.thumbnailUrl);
      });
    },

    //上传文件之前的钩子
    beforeUpload() {
      return false;
    },

    // 取消查看图片
    handleCancel() {
      this.previewVisible = false;
    },

    //点击文件链接或预览图标时的回调
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
      console.log("previewImage", this.previewImage);
    },

    //判断图片后缀名
    isAssetTypeAnImage(ext) {
      return ["png", "jpg", "jpeg"].indexOf(ext.toLowerCase()) !== -1;
    }
  }
};
</script>
<style lang="less">
// .sketchMap {
//   .ant-form-item-control {
//     margin: 0 38%;
//   }
// }
</style>

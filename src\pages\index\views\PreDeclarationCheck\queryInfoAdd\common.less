.ant-radio-wrapper {
    font-size: 16px !important;
}

.titItem /deep/ .ant-calendar-picker-input {
    width: 100%;
    height: 40px !important;
    margin-right: 16px;
}

.titItem /deep/ .ant-calendar-picker-icon {
    top: 36% !important;
}

.titItem /deep/ .ant-input {
    width: 100%;
    height: 40px !important;
    margin-right: 16px;
    border-radius: 4px;
    box-sizing: border-box;
}

.titItem /deep/ .ant-select-selection {
    height: 40px !important;
}

.titItem /deep/ .ant-select-selection__rendered{
    height: 40px !important;
}
/deep/ .ant-input-disabled{
    color: #666 !important;
}
/deep/ .ant-select-disabled{
    color: #666;
}
.mR20 {
    min-width: 100px;
    margin-right: 20px;
    text-align: right;
    height: 40px;
    line-height: 40px;
}
.rentFree{
    width: 60%;
    margin-left: 105px;
    margin-bottom: 30px;
    height: 32px;
    line-height: 32px;
    background: #fffbeb;
    border-radius: 4px;
    color: #F59E0B;
}
.lessorInfo {
    width: 100%;
    background-color: #fff;
    margin-bottom: 38px;

    .lessorInfo-tit {
        width: 96%;
        margin: 0 auto;

        .tit {
            padding-left: 12px;
            margin-bottom: 32px;
            height: 20px;
            font-size: 20px;
            font-weight: 500;
            color: #1d2129;
            line-height: 20px;
            border-left: 4px solid #1777ff;
        }

        .titItem {
            width: 100%;
            margin-bottom: 16px;
            display: flex;
            font-size: 16px;
            color: #262626;
        }
        .titItem1 {
            width: 100%;
            margin-bottom: 16px;
            display: flex;
            font-size: 16px;
            color: #262626;
          }
        .upload_file_item{
            width: 80%;
            display: flex;
            align-items: center;
            margin-left: 24px;
            margin-bottom: 24px;
        }
        .Item {
            width: 100%;
            min-height: 70px;
            background: #f3f9ff;
            border-radius: 6px;
            font-size: 15px;
            margin-right: 15px;
            .itemFont {
                font-weight: bold;

                span {
                    color: red;
                }
            }
        }
        .basisItem {
            width: 70%;
            min-height: 66px;
            padding-bottom: 12px;
            margin-top: -10px;
            margin-right: 30px;
            background: #f3f9ff;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 15px;
        }
    }

}
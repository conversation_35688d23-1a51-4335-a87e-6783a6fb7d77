// 字体居中
.text-center {
  text-align: center;
}

// 字体居右
.text-right {
  text-align: right;
}

// 字体图标垂直居中
.display-inline-flex {
  display: inline-flex;
  align-items: center;
}

// 字体溢出省略号
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 自动换行
.break-word {
  word-wrap: break-word;
  word-break: normal;
}

// 强制断行
.break-all {
  word-break:break-all;
}

// 相对布局
.position-relative {
  position: relative;
}

// 隐藏
.hide {
  display: none!important;
}

// 鼠标滑过
.cursor-pointer {
  cursor: pointer;
}
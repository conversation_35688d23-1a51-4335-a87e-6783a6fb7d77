<template>
	<div>
		<div>
			<a-table
				ref="table"
				:columns="columns || curColumns"
				:data-source="data || curData"
				:scroll="{ x: 1500 }"
			>
				<template #action="{ record }">
					<a type="primary" @click="toDetail(record)">查看</a>
				</template>
			</a-table>
		</div>
	</div>
</template>
<script>
export default {
	data() {

		return {
			
		};
	},
  props: {
    columns: {
      type: Array,
      default() {
        return [];
      }
    },
    data: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  created() {
    console.log(11111, this.columns);
    // this.columns = this.columns
  },
	methods: {
		onRefresh() {
			this.$refs.table.refresh();
		},
		toDetail(record) {
			console.log('record==', record);
			this.$router.push(`/information-query/park-detail`);
		},
	},
};
</script>
<style lang="less" scoped></style>

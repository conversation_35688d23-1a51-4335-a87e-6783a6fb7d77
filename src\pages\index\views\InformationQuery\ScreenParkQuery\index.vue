<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <div v-else>
    <a-card>
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
        @submit="handleSubmit"
      >
        <a-row :gutter="20" align="center">
          <a-col :span="8">
            <a-form-item label="园区名称">
              <a-input
                placeholder="请输入园区名称"
                v-model="queryParam.parkName"
                allowClear
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="16" align="right" justify="center">
            <a-form-item :wrapper-col="{ span: 24 }">
              <a-button type="primary" @click="search" style="margin-right:20px"
                >查询</a-button
              >
              <a-button type="default" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
    <a-card style="width:100%;margin-top:15px">
      <s-table
        ref="table"
        size="default"
        :columns="columns"
        :data="loadData"
        rowKey="id"
        :showPagination="false"
      >
        <span slot="action" slot-scope="text, record">
          <template>
            <a
              href="javascript:;"
              style="margin-right: 10px"
              @click="toEdit(record)"
              >编辑</a
            >
          </template>
          <template>
            <a href="javascript:;" @click="toDetail(record)">查看</a>
          </template>
        </span>
      </s-table>
    </a-card>
    <addScreenParkInfo
      :visible="visible"
      :screenParkName="screenParkName"
      :id="id"
      @refresh="refresh"
      :formData="formData"
      :noEdit="noEdit"
      @close="close"
    ></addScreenParkInfo>
  </div>
</template>

<script>
import {
  ApiGetScreenParkList,
  ApiGetScreenParkInfo
} from "@/pages/index/data/api/InfomationQuery/index.js";
import STable from "@/components/Table";
import addScreenParkInfo from "./components/addScreenParkInfo";
export default {
  components: {
    STable,
    addScreenParkInfo
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        }
      },
      visible: false, //控制编辑弹框的变量
      columns: [
        {
          title: "园区名称",
          // width: 400,
          dataIndex: "screenParkName",
          align: "center"
        },
        // {
        //   title: "园区税收",
        //   dataIndex: "mergedParkName",
        //   align: "center"
        // },
        // {
        //   title: "园区入驻企业数",
        //   dataIndex: "lessor",
        //   align: "center"
        // },
        // {
        //   title: "园区楼栋数",
        //   dataIndex: "parkTotalArea",
        //   align: "center"
        // },
        // {
        //   title: "园区属地率",
        //   dataIndex: "buildingNumbers",
        //   align: "center"
        // },
        {
          title: "园区租金",
          dataIndex: "rent",
          align: "center"
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          width: 150,
          scopedSlots: { customRender: "action" }
        }
      ],
      queryParam: {
        parkName: undefined
      },
      screenParkName: "", //定义存放园区名字，方便将该变量传到子组件中
      id: "",
      formData: {}, //用来存放获取的表单数据，将数据传给子组件
      noEdit: false //用来存放子组件表单是否能够编辑，false能编辑
    };
  },
  methods: {
    loadData: function() {
      return ApiGetScreenParkList(this.queryParam).then(res => {
        let dataObj = res.data;
        dataObj.data = res.data;
        return dataObj;
      });
    },
    //调用获取弹框信息的接口
    getInfo(id) {
      ApiGetScreenParkInfo({ id: id }).then(res => {
        this.formData = res.data;
      });
    },
    //点击查询调用的方法
    search: function() {
      this.$refs.table.loadData({}, this.queryParam);
    },
    //点击重置调用的方法
    reset: function() {
      const param = {
        parkName: undefined
      };
      this.queryParam = param;
      this.$refs.table.loadData({}, this.queryParam);
    },
    //点击查看调用的方法
    toDetail: function(record) {
      //给弹框中的园区进行赋值
      this.screenParkName = record.screenParkName;
      this.visible = true;
      console.log(record, "ooooooo");
      this.getInfo(record.id);
      //标识表单不能编辑
      this.noEdit = true;
    },
    //点击编辑按钮调用的方法
    toEdit: function(record) {
      this.getInfo(record.id);
      //显示弹框
      this.visible = true;
      //标识表单能编辑
      this.noEdit = false;
      //给弹框中的园区进行赋值
      this.screenParkName = record.screenParkName;
      this.id = record.id;
      console.log(this.screenParkName, "mmmmmm");
    },
    //刷新方法
    refresh(data) {
      //如果子组件传来刷新标识为true就重新调用列表接口
      if (data) {
        this.reset();
        console.log("nihao");
      }
    },
    //关闭弹窗
    close(data) {
      this.visible = data;
    }
  }
};
</script>

<style lang="less" scoped>
.sort {
  text-align: right;
}
/deep/.ant-select {
  width: 10rem;
}
</style>

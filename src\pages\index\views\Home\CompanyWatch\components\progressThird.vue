<template>
  <div
    class="ant-progress ant-progress-line ant-progress-status-normal ant-progress-show-info ant-progress-default"
  >
    <div
      style="display: flex; justify-content: flex-end; margin-top: 25px"
      v-for="item in areaData"
      :key="item.id"
    >
      <div>
        <a-tooltip>
          <template #title>{{ item.parkName }}</template>
          <div class="title">{{ item.parkName }}</div>
        </a-tooltip>
      </div>
      <div class="ant-progress-outer">
        <div class="ant-progress-inner">
          <div
            class="ant-progress-bg"
            style="width: 100%; height: 27px; border-radius: 100px"
          ></div>
          <div
            class="ant-progress-vacantArea-bg"
            :style="{ width: item.vacantAreaWidth + '%' }"
          >
            <a-tooltip>
              <template #title>{{ item.vacantAreaWidth }}%</template>
              <div class="title">{{ item.vacantAreaWidth }}%</div>
            </a-tooltip>
          </div>
          <div
            class="ant-progress-zlmj-bg"
            :style="{
              width: item.leasedAreaWidth + '%',
              left: item.vacantAreaWidth + '%',
            }"
          >
            <a-tooltip>
              <template #title>{{ item.leasedAreaWidth }}%</template>
              <div>{{ item.leasedAreaWidth }}%</div>
            </a-tooltip>
          </div>

          <div
            class="ant-progress-lhydq-bg"
            v-if="Number(item.due) !== 0"
            :style="{
              width: item.dueWidth + '%',
              right: 0 + '%',
            }"
          >
            <a-tooltip>
              <template #title>{{ item.dueWidth }}%</template>
              <div class="title">{{ item.dueWidth }}%</div>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getIndustrialParkAreaApi } from "@/pages/index/data/api/ComponyWatch";
export default {
  data() {
    return {
      areaData: [],
    };
  },
  mounted() {
    getIndustrialParkAreaApi("").then((res) => {
      let vacantAreaWidthNum = 0;
      let leasedAreaWidthNum = 0;
      let dueWidthNum = 0;
      // let leftNum = 0
      this.areaData = res.data;
      this.areaData.forEach((e) => {
        if (e.vacantArea != 0 && e.leasedArea != 0) {
          vacantAreaWidthNum =
            (e.vacantArea / (e.vacantArea + e.leasedArea)) * 100;
          e["vacantAreaWidth"] = vacantAreaWidthNum.toFixed(2);
          leasedAreaWidthNum =
            (e.leasedArea / (e.vacantArea + e.leasedArea)) * 100;
          e["leasedAreaWidth"] = leasedAreaWidthNum.toFixed(2);
          if (e.due == 0) {
            e["dueWidth"] = 0;
          } else {
            dueWidthNum =
              (leasedAreaWidthNum / 100) * (e.due / e.leasedArea) * 100;
            e["dueWidth"] = dueWidthNum.toFixed(2);
          }
        } else {
          e["vacantAreaWidth"] = 0;
          e["leasedAreaWidth"] = 0;
          e["dueWidth"] = 0;
        }
      });
    });
  },
  created() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.ant-progress-line {
  position: relative;
  width: 100%;
  font-size: 14px;
  margin-top: 12px;
}

.ant-progress-outer {
  display: inline-block;
  width: 90%;
  margin-right: calc(-2em - 8px);
  padding-right: calc(2em + 8px);
}

.ant-progress {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  display: inline-block;
}

.ant-progress-bg {
  position: relative;
  background-color: #fff;
  border-radius: 100px;
  transition: all 0.4s cubic-bezier(0.08, 0.82, 0.17, 1) 0s;
}

.title {
  width: 80px;
  margin-right: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ant-progress-vacantArea-bg {
  border-radius: 100px, 0, 0, 100px;
  height: 27px;
  line-height: 27px;
  position: absolute;
  top: 0;
  left: 0;
  color: #fff;
  text-align: center;
  background-color: #9079fd;
}

.ant-progress-zlmj-bg {
  height: 27px;
  line-height: 27px;
  position: absolute;
  top: 0;
  color: #fff;
  text-align: center;
  background-color: #2777e4;
}

.ant-progress-lhydq-bg {
  border-radius: 100px;
  margin-top: 3px;
  height: 20px;
  line-height: 20px;
  position: absolute;
  top: 0;
  color: #fff;
  text-align: center;
  background-color: rgba(47, 203, 190, 0.8);
}
</style>

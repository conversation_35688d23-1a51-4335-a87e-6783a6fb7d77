<template>
  <router-view v-if="$route.meta.level == 3" />
  <div v-else>
    <div v-if="notificationList.length > 0">
      <template v-for="(item, index) in notificationList">
        <div :key="index">
          <div class="item" @click="jumpToDetail(item)">
            <div class="item-left">
              <div class="item-status-read" v-if="item.deletedFlag">已读</div>
              <div class="item-status-unread" v-else>未读</div>
              <div class="item-title">{{ item.theme }}</div>
            </div>
            <div class="item-right">{{ item.createTime }}</div>
          </div>
          <div class="line-container">
            <div class="line"></div>
          </div>
        </div>
      </template>
      <div v-if="notificationList.length > 0" class="page">
        <a-pagination
          :current="current"
          show-size-changer
          :total="total"
          @showSizeChange="onShowSizeChange"
          @change="onChange"
        />
      </div>
    </div>
    <div v-else>
      <div class="no-data">
        <img
          src="@/assets/image/common/no_notification.jpg"
          style="width:300px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  ApiNoticeList,
  // ApiNoticeUpdate,
  ApiNoticeReadCount,
} from "APIs/PortalManagement/NotificationManagement/index.js";
export default {
  data() {
    return {
      notificationList: [],
      current: 1,
      pageSize: 10,
      total: 0,
    };
  },
  mounted() {
    const params = {
      currentPage: this.current,
      pageSize: this.pageSize,
    };
    this.queryNoticeList(params);
    this.checkIfHasNewNotice();
  },
  watch: {
    $route: {
      handler() {
        this.current = 1;
        this.pageSize = 10;
        const params = {
          currentPage: 1,
          pageSize: 10,
        };
        this.queryNoticeList(params);
        this.checkIfHasNewNotice();
      },
    },
  },
  methods: {
    checkIfHasNewNotice: function() {
      ApiNoticeReadCount().then((response) => {
        if (response.code == 0) {
          this.$bus.$emit("notify", response.data);
        }
      });
    },
    queryNoticeList: function(params) {
      ApiNoticeList(params).then((res) => {
        if (res.code == 0) {
          const records = res.data?.records || [];
          this.$set(this, "notificationList", records);
          this.$set(this, "total", res.data?.total || 0);
        }
      });
    },
    jumpToDetail: function(info) {
      console.log(info);
      const id = info.id;
      // ApiNoticeUpdate({ id }).then((response) => {
      //   if (response.code == 0) {
      //     const params = {
      //       currentPage: this.current,
      //       pageSize: this.pageSize,
      //     };
      //     ApiNoticeList(params).then((res) => {
      //       if (res.code == 0) {
      //         const records = res.data?.records || [];
      //         this.$set(this, "notificationList", records);
      //         this.$set(this, "total", res.data?.total || 0);
      //       }
      //     });
      //   }
      // });
      // this.current = 1;
      this.$router.push({
        path: "/notification-management/detail",
        query: {
          id,
          theme:info.theme.split("[")[1].split("]")[0]
        },
      });
    },
    onShowSizeChange: function(current, pageSize) {
      this.$set(this, "pageSize", pageSize);
      this.$set(this, "current", current);
      const params = {
        currentPage: current,
        pageSize: pageSize,
      };
      this.queryNoticeList(params);
    },
    onChange: function(pageNo) {
      console.log(pageNo, "~!@#$");
      this.$set(this, "current", pageNo);
      const params = {
        currentPage: pageNo,
        pageSize: this.pageSize,
      };
      this.queryNoticeList(params);
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  padding-top: 24px;
  padding-bottom: 24px;
  padding-left: 32px;
  padding-right: 32px;
  &-left {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-status-unread {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 32px;
    border-radius: 80px;
    background-color: #fff1f3;
    color: #e11d48;
    font-size: 16px;
    font-weight: 400;
  }
  &-status-read {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 32px;
    border-radius: 80px;
    background-color: #f7f8fa;
    color: #cccccc;
    font-size: 16px;
    font-weight: 400;
  }
  &-title {
    margin-left: 18px;
    color: #595959;
    font-size: 18px;
    font-weight: 400;
  }
  &-right {
  }
}
.line-container {
  background-color: #ffffff;
  display: flex;

  .line {
    width: 100%;
    height: 0px;
    opacity: 0.08;
    border-bottom: 1px solid #000000;
    margin-left: 32px;
    margin-right: 32px;
    background-color: #ffffff;
  }
}
.page {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.no-data {
  width: 100%;
  height: 400px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

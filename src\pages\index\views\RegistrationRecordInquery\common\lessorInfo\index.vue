<!-- 出租方加编号信息 -->
<template>
  <div class="lessorInfo">
    <div class="lessorInfo-tit">
      <div class="titItem">
        <p class="mR20" style="height: 40px; line-height: 40px">出租方</p>
        <a-select
          style="width: 426px; height: 40px"
          :disabled="showInfo"
          @change="onChange"
          placeholder="请选择出租方"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body;
            }
          "
          allowClear
          v-model="lessorName"
        >
          <a-select-option
            :value="item.companyName"
            v-for="item in lessorArr"
            :key="item.id"
            >{{ item.companyName }}</a-select-option
          >
        </a-select>
      </div>
      <div
        class="titItem"
        style="color: #86909c; justify-content: flex-end"
        v-if="lessorParmas.filingNumber"
      >
        <p class="mR20">编号:</p>
        <p>{{ lessorParmas.filingNumber }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getContractNumberApi,
  getIndustrialCompaniesApi,
} from "@/pages/index/data/api/RegistrationRecordInquery";
export default {
  props: {
    totalParmas: {
      type: Object,
      required: false,
    },
  },
  data() {
    return {
      statu: "",
      showInfo: false,
      lessorName: undefined,
      lessorParmas: {
        lessor: undefined,
        filingNumber: "",
      },

      lessorArr: [],
    };
  },
  watch: {
    totalParmas: {
      handler() {
        this.$nextTick(() => {
          if (this.statu != 3) {
            this.lessorParmas.lessor = this.totalParmas.lessor;
            this.lessorParmas.filingNumber = this.totalParmas.filingNumber;
            console.log("编辑时回显~~~~出租方编号", this.lessorParmas);
          }
        });
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.getStatu();
  },
  methods: {
    getStatu() {
      this.statu = this.$route.query.statu;
      if (this.statu == 1) {
        this.showInfo = true;
        this.lessorName = this.$route.query.lessor;
      } else if (this.statu == 2) {
        this.showInfo = false;
        this.lessorName = this.$route.query.lessor;
        getIndustrialCompaniesApi("").then((res) => {
          this.lessorArr = res.data;
        });
      } else {
        this.showInfo = false;
        this.getContractNumber();
        getIndustrialCompaniesApi("").then((res) => {
          this.lessorArr = res.data;
        });
      }
    },
    getContractNumber() {
      let params = {};
      getContractNumberApi(params).then((res) => {
        this.lessorParmas.filingNumber = res.data;
      });
    },
    onChange(value) {
      console.log(this.lessorParmas,"出租方组件输出");
      if (value) {
        const list = this.lessorArr.find((item) => item.companyName == value);
        this.lessorParmas.lessor = list.id;
        sessionStorage.setItem("lessorId", this.lessorParmas.lessor);
      }else{
        this.lessorParmas.lessor = ''
      }
      this.$emit("lessorInfo", this.lessorParmas);
    },
  },
};
</script>

<style lang="less" scoped>
.titItem /deep/ .ant-input {
  width: 100%;
  height: 40px !important;
  line-height: 40px !important;
  margin-right: 16px;
}

.titItem /deep/ .ant-select-selection {
  height: 40px !important;
  line-height: 40px !important;
}

.titItem /deep/ .ant-select-selection__rendered {
  height: 40px !important;
  line-height: 40px !important;
}

.titItem /deep/ .ant-select-selection-selected-value {
  color: #666;
}

.mR20 {
  margin-right: 20px;
  margin-left: 55px;
}

.lessorInfo {
  width: 100%;
  background-color: #fff;

  .lessorInfo-tit {
    width: 96%;
    margin: 0 auto;
    height: 96px;
    display: flex;
    align-items: center;

    .titItem {
      width: 100%;
      display: flex;
      font-size: 18px;
    }
  }
}
</style>

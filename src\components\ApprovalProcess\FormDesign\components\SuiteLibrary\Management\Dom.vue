<!--
* <AUTHOR>
* @time 2021-4-7
* @dec 办理人员信息套件
-->
<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>办理人员信息套件</h3>
      <a-form-model-item label="单位">
        <a-input placeholder="请输入单位" />
      </a-form-model-item>
      <a-form-model-item label="业务类型">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="1">
            开通
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="航站楼">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="1">
            T1 机房
          </a-select-option>
          <a-select-option value="2">
            T2 东交机房
          </a-select-option>
          <a-select-option value="3">
            T2 楼内机房
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="申请理由">
        <a-input placeholder="请输入申请理由" />
      </a-form-model-item>
      <div class="personnel-record-title-button">
        <div>办理人员信息</div>
        <div>
          <a-button type="primary" size="small">
            添加办理人员信息
          </a-button>
        </div>
      </div>
      <a-table
        :scroll="{ x: 1000 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * 10 + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
    </a-form-model>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    dataIndex: "serial",
    key: "serial",
    scopedSlots: { customRender: "serial" },
    width: "80px"
  },
  {
    title: "部门",
    dataIndex: "manageDepart",
    key: "manageDepart",
    width: "100px"
  },
  {
    title: "姓名",
    dataIndex: "manageName",
    key: "manageName",
    width: "80px"
  },
  {
    title: "身份证号",
    dataIndex: "manageIdCard",
    key: "manageIdCard",
    width: "180px"
  },
  {
    title: "通行证卡号",
    dataIndex: "manageSourceId",
    key: "manageSourceId",
    width: "180px"
  },
  {
    title: "职务",
    dataIndex: "applicantPost",
    key: "applicantPost",
    width: "80px"
  },
  {
    title: "负责人",
    dataIndex: "personInCharge",
    key: "personInCharge",
    width: "80px"
  },
  {
    title: "联系电话",
    dataIndex: "contactNumber",
    key: "contactNumber",
    width: "80px"
  },
  {
    title: "工作地点",
    dataIndex: "workPlace",
    key: "workPlace",
    width: "80px"
  },
  {
    title: "密码",
    dataIndex: "passWord",
    key: "passWord",
    width: "80px"
  },
  {
    title: "申请人通行证许可区域",
    dataIndex: "manageType",
    key: "manageType",
    width: "150px"
  },
  {
    title: "门禁申请区域",
    dataIndex: "manageAccess",
    key: "manageAccess",
    width: "150px"
  },
  {
    title: "备注",
    dataIndex: "manageRemarks",
    key: "manageRemarks",
    width: "80px"
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "120px",
    scopedSlots: { customRender: "action" }
  }
]
const values = [
  {
    manageDepart: "信息中心", //部门
    manageName: "王二", //姓名
    manageIdCard: "320902198408085030", //身份证号
    manageSourceId: "1113209085030", //通行证卡号
    manageType: "T1航站楼出发", //申请人通行证许可区域
    manageAccess: "国际出发 国际到达 国内出发 国内到达", //门禁申请区域
    manageRemarks: "*********" //备注
  }
]
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columns,
      values
    }
  },
  watch: {
    value(val) {
      console.log(`selected:`, val)
    }
  }
}
</script>

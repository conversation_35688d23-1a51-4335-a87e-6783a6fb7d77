<template>
  <div>
    <div class="search-container">
      <a-form :label-col="labelCol" :wrapper-col="wrapperCol" :form="form">
        <a-row gutter="24">
          <a-col :span="8">
            <a-form-item label="园区名称">
              <a-select
                allowClear
                style="width: 100%"
                placeholder="请选择"
                :value="form.parkNameNo"
                @change="handleParkNameChange"
              >
                <a-select-option
                  v-for="(item, index) in parkList"
                  :key="(index + 9).toString(36) + index"
                  :value="item.serialNo"
                >
                  {{ item.parkName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="企业名称">
              <a-input
                allowClear
                :value="form.companyName"
                placeholder="请输入企业名称"
                @change="handleEnterpriseChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="预警类型">
              <a-select
                allowClear
                style="width: 100%"
                placeholder="请选择"
                @change="handleTypeChange"
                :value="form.type"
              >
                <a-select-option
                  v-for="(item, index) in prewarningTypeArr"
                  :key="(index + 9).toString(36) + index"
                  :value="item.id"
                >
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row gutter="24">
          <a-col :span="8">
            <a-form-item label="预警时间">
              <a-range-picker
                allowClear
                @change="handleTime"
                :value="[form.startTime, form.endTime]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="预警状态">
              <a-select
                allowClear
                style="width: 100%"
                placeholder="请选择"
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                @change="handleStatusChange"
                :value="form.status"
              >
                <a-select-option
                  v-for="(item, index) in prewarningStatusArr"
                  :key="index"
                  :value="item.id"
                  >{{ item.value }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label=" " :colon="false">
              <div class="search-btn-group">
                <a-space>
                  <a-button type="primary" @click="search">查询</a-button>
                  <a-button type="primary" @click="reset">重置</a-button>
                </a-space>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="list-container">
      <div class="table-title-block">
        <div class="table-title">预警查询列表</div>
        <div class="table-action">
          <a-button type="primary" @click="addOutMigrationWarningBtn"
            >外迁预警申报</a-button
          >
        </div>
      </div>
      <common-table
        style="margin:20px;margin-top:unset;"
        ref="prewarningInfoTable"
        :columns="columns"
        :scroll="{ x: 1000 }"
        :loadData="loadData"
      >
        <div slot="type" slot-scope="text, record">
          <template>
            <div v-if="record.type == -1">全部</div>
            <div v-else-if="record.type == 1">税收预警</div>
            <div v-else-if="record.type == 2">租赁预警</div>
            <div v-else-if="record.type == 3">外迁预警</div>
          </template>
        </div>
        <div slot="createTime" slot-scope="text, record">
          <template>
            <div>{{ timeFormat(record.createTime) }}</div>
          </template>
        </div>
        <div slot="operateTime" slot-scope="text, record">
          <template>
            <div>{{ timeFormat(record.operateTime) }}</div>
          </template>
        </div>
        <div slot="status" slot-scope="text, record">
          <template>
            <div v-if="record.status == -1">全部</div>
            <div v-else-if="record.status == 0">待处理</div>
            <div v-else-if="record.status == 1">已分派</div>
            <div v-else-if="record.status == 2">已接收</div>
            <div v-else-if="record.status == 3">已处理</div>
          </template>
        </div>
        <div slot="operation" slot-scope="text, record">
          <template>
            <a @click="look(record, 1)">查看</a>
            <a-divider v-if="record.status == 0" type="vertical" />
            <a @click="look(record, 2)" v-if="record.status == 0">处理</a>
            <a-divider
              v-if="record.status == 1 && isIndustry == true"
              type="vertical"
            />
            <a
              @click="sign(record)"
              v-if="record.status == 1 && isIndustry == true"
              >签收</a
            >
            <a-divider
              v-if="record.status == 2 && isIndustry == true"
              type="vertical"
            />
            <a
              @click="handleEvent(record)"
              v-if="record.status == 2 && isIndustry == true"
              >处理</a
            >
          </template>
        </div>
      </common-table>
    </div>
    <div v-if="canShow">
      <prewarning-processing-modal
        :visible="canShow"
        :detailInfo="detailInfo"
        @handleOkAction="handleOKCallback"
        @handleCancelAction="handleCancelCallback"
      />
    </div>
    <div v-if="outMigrationVisible">
      <out-migration-warning-modal
        :outMigrationVisible="outMigrationVisible"
        @updateModal="outMigrationWarningCallback"
      />
    </div>
  </div>
</template>

<script>
import {
  ApiGetParksName,
  ApiQueryWarningPage,
  ApiSignWarningByType,
  // ApiIndustryUpdateWarningByType,
} from "APIs/PrewarningManagement";
// import STable from "@/components/Table";
import moment from "moment";
// import * as utils from "@/common/utils/index.js";
import PrewarningProcessingModal from "../components/PrewarningProcessingModal";
import OutMigrationWarningModal from "../components/OutMigrationWarningModal";
import CommonTable from "@/components/CommonTable/index.js";
export default {
  components: {
    // STable,
    PrewarningProcessingModal,
    OutMigrationWarningModal,
    CommonTable,
  },
  data() {
    return {
      labelCol: {
        span: 8,
      },
      wrapperCol: {
        span: 16,
      },
      form: {
        parkNameNo: undefined,
        companyName: "",
        type: undefined,
        status: -1,
        startTime: null,
        endTime: null,
      },
      canShow: false,
      detailInfo: {},
      parkList: [{ serialNo: -1, parkName: "全部" }], //园区名称列表
      prewarningTypeArr: [
        {
          id: -1,
          value: "全部",
        },
        {
          id: 1,
          value: "税收预警",
        },
        {
          id: 2,
          value: "租赁预警",
        },
        {
          id: 3,
          value: "外迁预警",
        },
      ],
      prewarningStatusArr: [
        {
          id: -1,
          value: "全部",
        },
        {
          id: 0,
          value: "待处理",
        },
        {
          id: 1,
          value: "已分派",
        },
        {
          id: 2,
          value: "已接收",
        },
        {
          id: 3,
          value: "已处理",
        },
      ],
      columns: [
        {
          title: "序号",
          dataIndex: "serialNo",
          align: "center",
          width: 80,
        },
        // {
        //   title: "园区名称",
        //   dataIndex: "parkName",
        //   align: "center",
        // },
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center",
        },
        {
          title: "预警类型",
          // dataIndex: "type",
          align: "center",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "预警时间",
          // dataIndex: "createTime",
          align: "center",
          scopedSlots: { customRender: "createTime" },
        },
        {
          title: "预警状态",
          // dataIndex: "status",
          align: "center",
          scopedSlots: { customRender: "status" },
        },
        {
          title: "处理人",
          dataIndex: "operateBy",
          align: "center",
        },
        {
          title: "处理时间",
          // dataIndex: "operateTime",
          align: "center",
          scopedSlots: { customRender: "operateTime" },
        },
        {
          title: "备注",
          dataIndex: "remark",
          align: "center",
          // scopedSlots: { customRender: "operateTime" },
        },
        {
          title: "操作",
          align: "center",
          scopedSlots: { customRender: "operation" },
        },
      ],
      isIndustry: false, //是否为实业公司
      outMigrationVisible: false, //外迁预警modal 是否可见
    };
  },
  created() {
    // let initStatusArr = [{ id: "-1", nameCn: "全部" }];
    // let initTypeArr = [{ id: "-1", nameCn: "全部" }];
    // const tmpStatusArr = this.$store.getters["dictionaries/getType"](
    //   "warningStatus"
    // );
    // const tmpTypeArr = this.$store.getters["dictionaries/getType"](
    //   "warningType"
    // );
    // const targetStatusArr = initStatusArr.concat(tmpStatusArr);
    // const targetTypeArr = initTypeArr.concat(tmpTypeArr);
    // this.$set(this, "prewarningStatusArr", targetStatusArr);
    // this.$set(this, "prewarningTypeArr", targetTypeArr);
    const companyName = this.$route.query.companyName || "";
    const warningType = this.$route.query.type;
    if (companyName) {
      this.$set(this.form, "companyName", companyName);
    }
    this.$set(this.form, "type", warningType);
  },
  mounted() {
    const userInfo = localStorage.getItem("USER_KEY");
    const roles = JSON.parse(userInfo).roles;
    if (roles && roles.includes("实业公司")) {
      this.$set(this, "isIndustry", true);
    } else {
      this.$set(this, "isIndustry", false);
    }
    this.init();
    console.log(this.$route.query.companyName, "~~~~");
  },
  methods: {
    init: function() {
      ApiGetParksName().then((res) => {
        let tmpList = [];
        tmpList.push({ serialNo: -1, parkName: "全部" });
        let parkBuildingBos = []
        res.data&&res.data.forEach((item,index) => {
          parkBuildingBos.push({ serialNo: index, parkName: item });
        });
        let mergeList = tmpList.concat(parkBuildingBos || []);
        console.log(mergeList, "~~~");
        this.$set(this, "parkList", mergeList);
      });
    },
    loadData: function(queryParams) {
      const {
        parkNameNo = -1,
        companyName = "",
        type = -1,
        status = -1,
        startTime = null,
        endTime = null,
      } = this.form;
      const parkInfo = this.parkList.find((o) => o.serialNo == parkNameNo);
      const tmpParkName = parkInfo.parkName == "全部" ? "" : parkInfo.parkName;
      const tmpType = type == -1 ? undefined : type;
      const tmpStatus = status == -1 ? undefined : status;
      const tmpStartTime = startTime ? startTime : null;
      const tmpEndTime = endTime ? endTime : null;
      const queryParam = {
        parkName: tmpParkName,
        companyName,
        type: tmpType,
        status: tmpStatus,
        startTime: tmpStartTime,
        endTime: tmpEndTime,
      };
      console.log(tmpParkName, "parkName");
      const params = Object.assign(
        { currentPage: queryParams.pageNum },
        queryParams,
        queryParam
      );
      console.log(params, "params");
      return ApiQueryWarningPage(params).then((res) => {
        return res.data;
      });
    },
    timeFormat: function(timeStr) {
      return timeStr ? moment(timeStr).format("YYYY-MM-DD") : "";
    },
    handleParkNameChange: function(e) {
      console.log(e, "园区查询");
      this.$set(this.form, "parkNameNo", e);
    },
    handleTypeChange: function(e) {
      this.$set(this.form, "type", e);
    },
    handleStatusChange: function(e) {
      this.$set(this.form, "status", e);
    },
    handleEnterpriseSearch: function(e) {
      console.log(e);
    },
    handleEnterpriseChange: function(e) {
      if (e && e.target.value) {
        console.log(e.target.value);
        this.$set(this.form, "companyName", e.target.value);
      } else {
        this.$set(this.form, "companyName", "");
        console.log(this.form.companyName, "~~~");
      }
    },
    handleTime: function(e) {
      console.log(e);
      let tmpStartTime = null;
      let tmpEndTime = null;
      if (e.length > 0) {
        tmpStartTime = moment(e[0]).format("YYYY-MM-DD HH:mm:ss");
        tmpEndTime = moment(e[1]).format("YYYY-MM-DD HH:mm:ss");
      }
      this.$set(this.form, "startTime", tmpStartTime);
      this.$set(this.form, "endTime", tmpEndTime);
      console.log(tmpStartTime, tmpEndTime, "~~~");
    },
    search: function() {
      const {
        parkNameNo,
        companyName,
        type,
        status,
        startTime,
        endTime,
      } = this.form;
      console.log(parkNameNo, companyName, type, status, startTime, endTime);
      this.$refs.prewarningInfoTable.search(this.form);
    },
    reset: function() {
      this.$set(this.form, "parkNameNo", undefined);
      this.$set(this.form, "companyName", "");
      this.$set(this.form, "type", undefined);
      this.$set(this.form, "status", undefined);
      this.$set(this.form, "startTime", null);
      this.$set(this.form, "endTime", null);
      this.$refs.prewarningInfoTable.search(this.form);
    },
    look: function(record, type) {
      if (type == 1) {
        this.$set(this, "detailInfo", {
          id: record.id,
          type: record.type,
          canEdit: 2,
        });
      } else if (type == 2) {
        this.$set(this, "detailInfo", {
          id: record.id,
          type: record.type,
          canEdit: 1,
        });
      }
      this.$set(this, "canShow", true);
    },
    sign: function(record) {
      console.log(record);
      const id = record.id;
      const type = record.type;
      ApiSignWarningByType({ id, type }).then((res) => {
        console.log(res);
        if (res.code == 0) {
          this.$refs.prewarningInfoTable.search(this.form);
        }
      });
    },
    handleEvent: function(record) {
      console.log(record);
      this.$set(this, "detailInfo", {
        id: record.id,
        type: record.type,
        canEdit: 1,
      });
      this.$set(this, "canShow", true);
    },
    handleOKCallback: function() {
      this.$set(this, "canShow", false);
      this.$set(this, "detailInfo", {});
      this.$refs.prewarningInfoTable.search(this.form);
    },
    handleCancelCallback: function() {
      this.$set(this, "canShow", false);
      this.$set(this, "detailInfo", {});
      this.$refs.prewarningInfoTable.search(this.form);
    },
    addOutMigrationWarningBtn: function() {
      this.outMigrationVisible = true;
    },
    outMigrationWarningCallback: function() {
      this.outMigrationVisible = false;
      //刷新预警管理列表
      this.$refs.prewarningInfoTable.search(this.form);
    },
  },
};
</script>

<style lang="less" scoped>
.search-container {
  background-color: #fff;
  padding-top: 20px;
  padding-right: 40px;
  margin-bottom: 20px;
}
.search-btn-group {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.list-container {
  background-color: #fff;
}
.table-title-block {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}
.table-title {
  font-size: 20px;
  font-weight: 600;
  color: #595959;
}
.table-action {
}
</style>

<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="类型">
          <a-select
            placeholder="请选择类型"
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body;
              }
            "
            v-decorator="[
              'groupId',
              {
                rules: [
                  {
                    required: true,
                    message: '请选择类型',
                  },
                ],
              },
            ]"
          >
            <a-select-option
              v-for="(item, index) in typeData"
              :key="index"
              :value="item.dictValue"
            >
              {{ item.nameCn }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="排序">
          <a-input-number
            style="width:100%"
            placeholder="请输入排序"
            v-decorator="[
              'orderBy',
              {
                initialValue: '',
                rules: [
                  { required: true, message: '请输入排序' },
                  {
                    validator: checkPositiveNum,
                  },
                ],
              },
            ]"
            :min="1"
            :max="999"
          />
        </a-form-item>
        <a-form-item label="标题">
          <a-input
            autoComplete="off"
            placeholder="请输入标题"
            maxLength="50"
            v-decorator="[
              'title',
              {
                initialValue: '',
                rules: [{ required: true, message: '请输入标题' }],
              },
            ]"
          />
        </a-form-item>
        <a-form-item label="是否启用">
          <a-radio-group
            v-decorator="[
              'isEnable',
              { initialValue: false, rules: [{ required: true, message: '' }] },
            ]"
          >
            <a-radio :value="false">
              是
            </a-radio>
            <a-radio :value="true">
              否
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-model-item label="文章内容" prop="editor2Content">
          <vue-editor
            id="editor2"
            v-model="content"
            :editorToolbar="defaultToolbar"
          ></vue-editor>
        </a-form-model-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { VueEditor } from "vue2-editor";
import pick from "lodash.pick";
// Api接口
// import {
//   ApiActivitiUpdate,
//   ApiActivitiCreate
// } from "@/pages/index/data/api/BusinessManagement/ApprovalIndex"
// 校验
import { checkPositiveNum } from "@/common/validate";
// 表单字段
const fields = ["id", "groupId", "orderBy", "title", "isEnable"];

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      default: () => null,
    },
    typeData: {
      type: Object,
      default: () => null,
    },
  },
  components: {
    VueEditor,
  },
  data() {
    this.formLayout = {
      labelCol: 24,
      wrapperCol: 24,
    };
    return {
      title: "添加文章",
      content: "",
      loading: false,
      form: this.$form.createForm(this),
      defaultToolbar: [
        [
          {
            header: [false, 1, 2, 3, 4, 5, 6],
          },
        ],
        ["bold", "italic", "underline", "strike"], // toggled buttons
        [
          {
            align: "",
          },
          {
            align: "center",
          },
          {
            align: "right",
          },
          {
            align: "justify",
          },
        ],
        [
          {
            list: "ordered",
          },
          {
            list: "bullet",
          },
          {
            list: "check",
          },
        ],
        [
          {
            indent: "-1",
          },
          {
            indent: "+1",
          },
        ], // outdent/indent
        [
          {
            color: [],
          },
          {
            background: [],
          },
        ], // dropdown with defaults from theme
        ["link", "image"],
        ["clean"], // remove formatting button
      ],
    };
  },
  created() {
    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v));
    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields));
      this.content = (this.data && this.data.content) || "";
      if (this.data && this.data.id) {
        this.title = "修改文章";
      } else {
        this.title = "添加文章";
      }
    });
  },
  methods: {
    // 正整数校验
    checkPositiveNum,
    // 新增和修改
    handleOk() {
      this.loading = true;
      this.form.validateFields(
        (
          errors
          // values
        ) => {
          if (!errors) {
            if (this.data && this.data.id) {
              // 修改
              // ApiActivitiUpdate(
              //   Object.assign(values, {
              //     id: this.data.id,
              //     content: this.content,
              //     resourceType: 2
              //   })
              // )
              //   .then(() => {
              //     this.$emit("cancel")
              //     // 重置表单数据
              //     this.form.resetFields()
              //     this.content = (this.data && this.data.content) || ""
              //     // 刷新表格
              //     this.$emit("ok")
              //     this.$message.info("修改成功")
              //   })
              //   .finally(() => {
              //     this.loading = false
              //   })
            } else {
              // 新增
              // ApiActivitiCreate(
              //   Object.assign(values, { content: this.content, resourceType: 2 })
              // )
              //   .then(() => {
              //     this.$emit("cancel")
              //     // 重置表单数据
              //     this.form.resetFields()
              //     this.content = (this.data && this.data.content) || ""
              //     // 刷新表格
              //     this.$emit("ok")
              //     this.$message.info("新增成功")
              //   })
              //   .finally(() => {
              //     this.loading = false
              //   })
            }
          } else {
            this.loading = false;
          }
        }
      );
    },
  },
};
</script>
<style lang="less" scoped></style>

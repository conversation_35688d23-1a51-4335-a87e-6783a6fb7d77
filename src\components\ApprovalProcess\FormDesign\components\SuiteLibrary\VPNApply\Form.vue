<!--
* <AUTHOR>
* @time 2021-4-7
* @dec VPN可访问主机套件套件表单配置
-->
<template>
  <div>
    <div>
      <p style="font-weight:bolder">应用业务:</p>
      <div style="padding:0 30px 0">
        <p style="font-weight:bolder">VPN申请、审批业务</p>
        <p>无验证项即不参与流程条件判断</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        inputTitle: "VPN可访问主机套件",
        optionsData: {}
      }
    };
  },
  watch: {
    data: {
      handler: function(data) {
        if (data.optionsData && data.optionsData.regex) this.form = data;
      }
    },
    form: {
      handler: function() {
        setTimeout(() => {
          this.$emit("update:data", this.form);
        }, 50);
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="less" scoped></style>

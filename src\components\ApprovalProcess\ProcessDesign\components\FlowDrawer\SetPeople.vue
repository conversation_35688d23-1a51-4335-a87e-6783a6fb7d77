<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 流程设计-抽屉-设置发起人/审批人/抄送人
-->
<template>
  <div class="set-people">
    <div class="mb15">
      <a-radio-group v-model="authType" @change="onChangeTab">
        <a-radio :value="1" v-if="openWay === 'launch'">
          所有人
        </a-radio>
        <a-radio :value="2">
          指定角色
        </a-radio>
        <a-radio :value="3">
          指定成员
        </a-radio>
        <a-radio :value="4" v-if="openWay === 'approval'">
          指定发起人
        </a-radio>
        <a-radio :value="5" v-if="openWay === 'approval'">
          指定组织结构
        </a-radio>
      </a-radio-group>
    </div>
    <div class="mb15" v-if="authType !== 1 && authType !== 4">
      <a-button type="primary" @click="goAddMember">
        <span>{{ tags.length == 0 ? "添加" : "修改" }}</span
        ><span v-if="authType !== 5">{{
          authType === 3 ? "成员" : "角色"
        }}</span>
        <span v-else>组织结构</span>
      </a-button>
      <!-- <span style="margin-left:10px" v-if="authType === 3">不能超过20个</span> -->
    </div>
    <div class="mb15">
      <a-tag
        closable
        v-for="tag in tags"
        :key="tag"
        @close="() => handleClose(tag)"
      >
        {{ tag.name || tag.groupName }}
      </a-tag>
      <!-- v-if="this.tags.length > 0 && authType === 3" -->
      <a-button
        type="link"
        v-if="this.tags.length > 0 && authType !== 1 && authType !== 4"
        @click="clearTags"
      >
        清空
      </a-button>
    </div>
    <a-divider />
    <div
      v-if="
        (this.tags.length > 1 &&
          openWay === 'approval' &&
          openWay !== 'transferTo' &&
          authType !== 4 &&
          authType !== 5) ||
          (this.tags.length > 0 &&
            (authType === 2 || authType === 5) &&
            openWay == 'approval' &&
            openWay !== 'transferTo')
      "
    >
      <p>多人审批时采用的审批方式</p>
      <a-radio-group v-model="approvalMethod" @change="onChangeApprovalMethod">
        <a-radio :style="radioStyle" value="1" v-if="authType === 3">
          依次审批 (按审批人顺序)
        </a-radio>
        <a-radio :style="radioStyle" value="2">
          会签 (须所有审批人同意,一人拒绝即拒绝)
        </a-radio>
        <a-radio :style="radioStyle" value="3">
          或签 (一名审批人同意或拒绝即可)
        </a-radio>
      </a-radio-group>
    </div>
    <add-member
      ref="addMember"
      :visible="addMemberVisible"
      @cancel="handleCancel"
      :dataList="memberOptions"
      :tags="tags"
    />
    <add-role
      ref="addRole"
      :visible="addRoleVisible"
      @cancel="handleCancel"
      :dataList="roleTreeData"
      :tags="tags"
    />
    <add-organization
      ref="addOrganization"
      :visible="addOrganizationVisible"
      @cancel="handleCancel"
      :dataList="OrganizationData"
      :tags="tags"
    ></add-organization>
  </div>
</template>
<script>
import AddMember from "./AddMember.vue";
import AddRole from "./AddRole.vue";
import AddOrganization from "./AddOrganization";
export default {
  components: {
    AddMember,
    AddRole,
    AddOrganization
  },
  props: {
    openWay: String,
    default: () => null, //launch发起,approval审批,CC抄送
    data: {}
  },
  data() {
    return {
      loading: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      roleTreeData: this.$store.getters["approvalProcess/roleData"], //角色列表
      tags: [], //标签列表
      memberOptions: this.$store.getters["approvalProcess/userData"], //成员列表
      OrganizationData: this.$store.getters["approvalProcess/organData"], //组织结构列表
      authType: this.openWay == "launch" ? 1 : 2, //根据进入方式动态设置tab初始值
      approvalMethod:
        this.authType === 1 ? "" : this.authType === 3 ? "1" : "2", //审批方式
      radioStyle: {
        display: "block",
        height: "30px",
        lineHeight: "30px"
      },

      addMemberVisible: false, //添加成员弹框
      addRoleVisible: false, //添加角色弹框
      addOrganizationVisible: false //添加组织结构弹窗
    };
  },
  mounted() {
    this.setData();
  },
  methods: {
    // 回显数据
    setData() {
      let roleAndUserAuth = this.data.data.roleAndUserAuth;
      console.log(roleAndUserAuth, "roleAndUserAuth");
      if (roleAndUserAuth) {
        this.approvalMethod = roleAndUserAuth.approvalMethod;
        this.authType = roleAndUserAuth.authType;
        this.tags = roleAndUserAuth.tags;
      }
    },
    // 获取数据
    getData() {
      return {
        // 选择人 1/所有人 2/指定成员 3/指定角色
        authType: this.authType,
        // 审批方式 1/依次审批 2/会签 3/或签
        approvalMethod: this.approvalMethod,
        // 人员列表
        tags: [...this.tags]
      };
    },
    //tab切换
    onChangeTab() {
      this.approvalMethod =
        this.authType === 1 ? "" : this.authType === 3 ? "1" : "2";
      this.tags = [];
    },
    //tag关闭回调
    handleClose(removedTag) {
      const tags = this.tags.filter(tag => tag !== removedTag);
      this.tags = tags;
    },
    //添加成员/角色/组织结构
    goAddMember() {
      if (this.authType === 3) {
        this.addMemberVisible = true;
      } else if (this.authType === 5) {
        this.addOrganizationVisible = true;
      } else {
        this.addRoleVisible = true;
      }
    },
    //弹框取消
    handleCancel(data) {
      this.addMemberVisible = false;
      this.addRoleVisible = false;
      this.addOrganizationVisible = false;
      this.tags = data;
    },
    //切换审批方式
    onChangeApprovalMethod(e) {
      this.approvalMethod = e.target.value;
    },
    //清空标签
    clearTags() {
      this.tags = [];
    }
  }
};
</script>
<style lang="less" scoped>
.set-people {
  padding: 24px;
  .mb15 {
    margin-bottom: 15px;
  }
}
</style>

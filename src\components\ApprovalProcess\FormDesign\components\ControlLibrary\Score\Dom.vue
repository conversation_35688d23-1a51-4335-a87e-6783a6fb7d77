<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 评分表单
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :help="
        (data.placeholder && data.placeholder.placeholderText) ||
          '请输入提示文字'
      "
      :label="data.inputTitle || '评分'"
      style="margin-bottom:unset"
      :rules="[
        {
          required: data.notNull,
          message: '请输入评分',
          trigger: 'blur'
        }
      ]"
    >
      <a-rate disabled allowHalf />
      <span v-if="data.optionsData.showScore" style="margin-left:10px">0</span>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

/**
 * <AUTHOR>
 * @time 2020-9-10
 * @dec 公共方法
 */
// https://github.com/reduxjs/redux/blob/master/src/compose.js
import { store } from "@/data/store";
export function compose(...funcs) {
  if (funcs.length === 0) {
    return arg => arg;
  }
  if (funcs.length === 1) {
    return funcs[0];
  }
  return funcs.reduce((a, b) => (...args) => a(b(...args)));
}

/**
 * 转化为el-tree树形结构数据
 */
export function transToTreeDat(arr) {
  let list = arr;
  let colNodes = list.filter(i => !i.isRow);

  let colNodesGroup = getColNode(colNodes);
  let allNodes = list.concat(colNodesGroup);
  let tree = transTree(allNodes);
  return tree;
}
export function getColNode(colNodeArrs) {
  let colNodes = colNodeArrs;
  let map = {};
  colNodes.forEach(i => {
    if (!map[i.groupId]) {
      map[i.groupId] = [];
    }
    map[i.groupId].push(i);
  });
  let colNodesArr = [];
  for (const groupId in map) {
    let obj = {
      id: groupId,
      groupId: map[groupId][0].groupPid,
      type: "route",
      isRow: true,
      isFlowTo: map[groupId][0].type === "6",
      conditionNodes: map[groupId]
    };
    colNodesArr.push(obj);
  }

  return colNodesArr;
}
/**
 *
 * @param {allNodes} arr 所有的整行元素
 * @param {*} list  所有的节点元素
 */
export function getPidArr(list) {
  let colNodes = list.filter(i => !i.isRow);
  let rowNodes = list.filter(i => i.isRow);
  let colNodesGroup = getColNode(colNodes, list);
  let arr = colNodesGroup.concat(rowNodes);

  let map = {}; //所有整行元素的字典对象
  for (let item of arr) {
    map[item.id] = item;
  }
  //获取节点所在行
  for (let lis of list) {
    lis.pids = [];
    if (lis.type === "1") {
      continue;
    }
    if (!lis.isRow) {
      let p = map[lis.groupPid];
      if (lis.groupPid === "root") {
        lis.pids.push(p.id);
        continue;
      }
      //当上一层为rowNode
      getColPid(p, lis);
    } else {
      let p = map[lis.groupId];
      getRowPid(p, lis);
    }
  }
}
//获取row的父节点id
function getRowPid(p, lis) {
  if (!p) {
    lis.pids.push(lis.groupId);
  } else {
    if (p.conditionNodes) {
      p.conditionNodes.forEach(i => {
        loopGetPid(i, lis);
      });
    } else {
      lis.pids.push(p.id);
    }
  }
}
/**
 * 获取col节点的父节点
 */
function getColPid(p, lis) {
  if (!p) {
    //当上一层为条件框元素
    lis.pids.push(lis.groupPid);
  } else {
    //当上一层为整行元素
    loopGetPidCol(p, lis);
  }
}
/**
 *
 * @param {*} parentRow
 * @param {*} lis
 * 单独处理一下col节点
 */
export function loopGetPidCol(parentRow, lis) {
  if (parentRow.conditionNodes) {
    parentRow.conditionNodes.forEach(i => {
      loopGetPid(i, lis);
    });
  } else {
    lis.pids.push(parentRow.id);
  }
}
/**
 *  轮询节点获取pid
 * @param {*} node
 * @param {*} lis
 */
export function loopGetPid(node, lis) {
  if (node.childNode) {
    loopGetPid(node.childNode, lis);
  } else if (node.conditionNodes) {
    node.conditionNodes.forEach(i => {
      loopGetPid(i, lis);
    });
  } else {
    lis.pids.push(node.id);
  }
}
/**
 * 转化为el-tree树形结构数据
 */
export function transTree(arr) {
  let list = arr;
  if (!list || !list.length) return [];
  let map = {};
  for (let item of list) {
    map[item.id] = item;
  }
  let nodes = [];
  for (let lis of list) {
    if (lis.isRoot) {
      nodes.push(lis);
      continue;
    }
    if (!lis.isRow) {
      continue;
    }
    let p = map[lis.groupId];
    if (!p) {
      continue;
    }
    p.isParent = true;
    p.childNode || (p.childNode = {});
    p.childNode = lis;
    if (
      p.childNode.conditionNodes &&
      p.childNode.conditionNodes[0].type === "6"
    ) {
      p.isFlowTo = true;
    }
  }
  return nodes;
}
/**
 * Hash 哈希值
 */
export function HashCode(hashLength) {
  // 默认长度 24
  return (
    "a" +
    Array.from(Array(Number(hashLength) || 15), () =>
      Math.floor(Math.random() * 36).toString(36)
    ).join("")
  );
}
/**
 * 树结构转化为扁平化结构
 */
export function deepTraversal(tree) {
  let list = [];
  tree.forEach(item => {
    const loop = data => {
      list.push(data);
      let children = data.children;
      children &&
        children.length &&
        children.forEach(child => {
          loop(child);
        });
    };
    loop(item);
  });
  return list;
}

/**
 * 回显流程树content
 */
// 判断节点类型更改展示数据 3 条件特殊处理 6 流转至特殊处理  //与业务代码耦合部分
export function judgeNodeTypeToChange(node, cNode) {
  if (node.type === "3") {
    if (!node.data.ruleInfo) return;
    const ruleLevel = { "1": "低", "2": "中", "3": "高" };
    node.data.ruleInfo.priority = ruleLevel[node.data.ruleInfo.ruleLevel];
    node.data.ruleInfo.ruleName = cNode.title;
    node.content = getContent(cNode);
    // 优先级 与 表达式
  } else {
    if (!cNode.data.roleAndUserAuth) return;
    node.data = cNode.data;
    if (cNode.data.roleAndUserAuth.authType === 1) {
      node.content = "所有人";
    } else if (cNode.data.roleAndUserAuth.authType === 4) {
      node.content = "发起人";
    } else {
      let names =
        cNode.data.roleAndUserAuth.tags &&
        cNode.data.roleAndUserAuth.tags.map(i => i.name || i.groupName);
      node.content = names && names.length ? names.join(",") : "请选择";
    }
  }
}
function getContent(cNode) {
  let {
    formData,
    conditionRule,
    value,
    formKeyName
  } = cNode.data.ruleInfo.conditions[0];
  formData = formData && JSON.parse(formData);
  let conditionRuleName = store.getters["dictionaries/getNameFromTypeCode"]({
    type: "ruleCondition",
    code: conditionRule
  });
  let title =
    formData && formData.inputTitle
      ? (formData.inputTitle || "") +
        (formKeyName ? "-" + formKeyName : "") +
        (conditionRuleName ? " (" + conditionRuleName + ") " : "") +
        (value || "") +
        ""
      : "请选择";
  return title;
}

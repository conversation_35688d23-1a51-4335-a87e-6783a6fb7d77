<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 手机控件 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '手机'"
      style="margin-bottom:unset"
      prop="phoneNo"
      :rules="[
        {
          required: data.notNull,
          message: '请输入手机号！',
          trigger: 'blur'
        }
      ]"
    >
      <a-input
        v-model="form.phoneNo"
        :placeholder="data.placeholder.tipsTitleText || '请输入'"
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          placeholder: ""
        };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        phoneNo: null
      }
    };
  }
};
</script>
<style lang="less" scoped>
@import "../index.less";
</style>

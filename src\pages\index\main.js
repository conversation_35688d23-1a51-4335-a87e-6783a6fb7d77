/**
 * <AUTHOR>
 * @time 2020-8-14
 * @dec 页面入口文件
 * @dec 请严格遵守书写规范，别让楼写歪了，写代码注释
 */
import Vue from "vue";
// vuex
import store from "./data/store";
// 跟组件
import App from "@/pages/common/views/App";
// 引入全站样式
import "@/assets/styles/index.less";
// SVG 图标
import "@/assets/icons";
// 插件
import Plugins from "@/plugins";
// 多语言实例
import { initI18n } from "@/common/utils/others/i18n";
// 初始化路由实例
import { initRouter } from "@/common/router";
// 路由配置
import routes from "./router/config";
import "@babel/polyfill";
import "./data/mock";
// 初始化路由配置，添加路由守卫，添加字典
import bootstrap from "@/common/bootstrap";
import Antd from "ant-design-vue";
import echarts from "echarts";
// import particles from "particles.js";
import { getDictByType, getDictValue } from "@/common/utils/dict";
const router = initRouter(routes);
const i18n = initI18n("CN", "US");
bootstrap({ router, store, i18n });

// 插件
Vue.use(Plugins);
Vue.use(Antd);
// Vue.use(particles);
Vue.prototype.$getDictByType = getDictByType;
//在vue的原型上挂载一个方法getDictValue，也就是定义全部方法，使得在所有的vue实例中都能使用
Vue.prototype.$getDictValue = getDictValue;
Vue.prototype.$echarts = echarts;
// 阻止启动生产消息，常用作指令。
Vue.config.productionTip = false;
new Vue({
  el: "#app",
  router,
  store,
  i18n,
  beforeCreate() {
    Vue.prototype.$bus = this;
  },
  render(createElement) {
    return createElement(App);
  },
}).$mount("#app");

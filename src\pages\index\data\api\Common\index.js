import api from "@/common/api";
import { BASE_URL } from "Config";
import downloadAPI from "@/common/api/download.js";

/**
 *
 * @param {*} params
 * @returns
 */
export function ApiGetDictByCode(params) {
  return api({
    url: BASE_URL + "/mapi/system/sysdict",
    method: "post",
    params,
  });
}
/**
 *
 */
export function ApiGetDictByParentCode(params) {
  return api({
    url: BASE_URL + "/system/sysdict/pageByParentCode",
    method: "post",
    params,
  });
}

/**
 * 附件上传
 * @param {*} params
 * @returns
 */
export function ApiAttachmentUpload(params) {
  return api({
    url: BASE_URL + "/common/attachment/upload",
    method: "post",
    params,
  });
}

/**
 * 附件删除
 * @param {*} params
 * @returns
 */
export function ApiAttachmentDeleteById(params) {
  return api({
    url: BASE_URL + "/common/attachment/deleteById",
    method: "post",
    params,
  });
}

/**
 * 附件下载
 */
export function ApiAttachmentDownById(params) {
  return downloadAPI({
    url: BASE_URL + "/common/attachment/down",
    method: "get",
    params,
  });
}

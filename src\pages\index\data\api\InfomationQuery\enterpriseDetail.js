import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 根据公司Id 获取信息
 */
export function ApiGetComponeyTagsById(params) {
  return api({
    url: BASE_URL + "/manager/companyBasic/getCompanyLabels",
    method: "post",
    params,
  });
}
/**
 * 保存企业标签
 */
export function ApiSaveComponeyLabelById(params) {
  return api({
    url: BASE_URL + "/manager/companyBasic/saveCompanyLabels",
    method: "post",
    params,
  });
}
/**
 * 查询所有企业标签
 */
export function ApiGetAllComponeyLabels(params = {}) {
  return api({
    url: BASE_URL + "/system/sysdict/pageByParentCode",
    method: "post",
    params: { ...params, dictCode: "company_label" },
  });
}
/**
 * 查询行业标签
 * @param {*} params
 * @returns
 */
export function ApiGetAllComponeyTypes(params = {}) {
  return api({
    url: BASE_URL + "/manager/companyBasic/getIndustryCategory",
    method: "post",
    params: params,
  });
}
/**
 * 保存企业行业标签
 */
export function ApiSaveEnterpriseTypes(params = {}) {
  return api({
    url: BASE_URL + "/manager/companyBasic/saveIndustryCategory",
    method: "post",
    params: params,
  });
}

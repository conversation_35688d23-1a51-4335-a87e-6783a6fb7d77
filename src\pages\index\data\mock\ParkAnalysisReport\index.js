import Mock from "mockjs";

//查询接口
const query = {
  code: 0,
  data: {
    current: 1,
    pages: 1,
    records: [
      {
        apanageRate: "80",
        buildingNum: "16",
        createBy: null,
        createDate: "2023-06-20",
        createTime: "2023-06-20 11:44:37.0",
        emigrationNum: "2",
        foreignOwnedNum: "20",
        isDeleted: "0",
        listedCompanyNum: "15",
        newaddNum: "10",
        output: "100",
        parkArea: "150000",
        parkDescription: null,
        parkName: "星联科技园",
        propertyCosts: "5",
        rent: "25",
        reportId: 1,
        residentNum: "50",
        settledNum: "75",
        settledRate: "70",
        specializedNum: "10",
        surrenderNum: "2",
        taxation: "20000",
        technologyNum: "30",
        updateBy: null,
        updateTime: "2023-09-20 11:44:37.0",
        vacancyArea: "800",
        vacancyRate: "25"
      }
    ],
    searchCount: true,
    size: 10,
    total: 4
  },
  msg: null
};
Mock.mock("/Mock/ParkAnalysisReport/query", query);

<template>
  <div>
    <a-modal
      :width="1400"
      :visible="visible"
      @cancel="cancelModal"
      :footer="null"
    >
      <div class="container" id="printPage">
        <div class="content">
          <div class="content-header">
            <div class="header-title">{{ title }}</div>
            <div class="header-no">
              <div>编号：{{ form.filingNumber }}-1</div>
            </div>
          </div>
          <div class="content-body">
            <div class="applicant-unit">
              <div class="label">申请单位</div>
              <div class="value">{{ form.lessor }}</div>
            </div>
            <div class="application-items">
              <div class="label" style="height:160px;">申请事项</div>
              <div class="value" style="height:160px;">
                {{ getApplicationItem }}
              </div>
            </div>
            <div class="application-type">
              <div class="label">申请类型</div>
              <div class="value" v-if="form.contractType == 0">新租</div>
              <div class="value" v-else-if="form.contractType == 1">续租</div>
              <div class="value" v-else-if="form.contractType == 2">
                提前终止
              </div>
              <div class="value" v-else-if="form.contractType == 3">
                变更-主体变更
              </div>
              <div class="value" v-else-if="form.contractType == 4">
                变更-内容变更
              </div>
            </div>
            <div class="approval-category">
              <div class="label">审批类别</div>
              <div class="value">
                <div
                  v-if="
                    form.leaseArea > 1000 ||
                      form.accumulateLeaseArea > 3000 ||
                      form.leaseTerm > 5 ||
                      form.priceDeviation == 1 ||
                      form.leaseArea > 5000
                  "
                >
                  <div
                    v-if="
                      form.leaseArea > 1000 || form.accumulateLeaseArea > 3000
                    "
                  >
                    面积1000㎡以上或累计超过3000㎡；
                  </div>
                  <div v-if="form.leaseTerm > 5">合同期限5年以上；</div>
                  <div v-if="form.priceDeviation == 1">价格偏离20%以上；</div>
                  <div v-if="form.leaseArea > 5000">
                    面积5000m²以上，或其他特殊情况。
                  </div>
                </div>
                <div v-else>
                  特殊情况
                </div>
              </div>
            </div>
            <div
              class="material-name"
              style="display: flex;align-items: unset;justify-content: unset;"
            >
              <div class="label" style="height:188px;">
                材料附件名称
              </div>
              <div class="value" style="height: 188px; line-height: 38px;">
                <!-- <template v-for="(item, index) in form.attachmentNameList">
                  <div style="height:48px" :key="index" v-if="index <= 3">
                    {{ index + 1 }}、{{ item }}
                  </div>
                </template> -->
                <!-- 附件名称显示方式：0：无决策无合同，1：有决策无合同，2：无决策有合同，3：决策+合同 -->
                <div v-if="form.attachmentType == 0">
                  <div>租户资质材料</div>
                  <div>产权证明材料</div>
                  <div>定价依据</div>
                </div>
                <div v-else-if="form.attachmentType == 1">
                  <div>租户资质材料</div>
                  <div>产权证明材料</div>
                  <div>定价依据</div>
                  <div>决策材料</div>
                </div>
                <div v-else-if="form.attachmentType == 2">
                  <div>租户资质材料</div>
                  <div>产权证明材料</div>
                  <div>定价依据</div>
                  <div>原合同</div>
                </div>
                <div v-else-if="form.attachmentType == 3">
                  <div>租户资质材料</div>
                  <div>产权证明材料</div>
                  <div>定价依据</div>
                  <div>决策材料</div>
                  <div>原合同</div>
                </div>
              </div>
            </div>
            <!-- <div class="receipt-date">
              <div class="label">收件日期</div>
              <div class="value">
                <div class="date-content">
                  <div style="margin-left: 120px;">年</div>
                  <div style="margin-left: 120px;">月</div>
                  <div style="margin-left: 120px;">日</div>
                </div>
              </div>
            </div> -->
            <div class="audit-method">
              <div class="label">审核方式</div>
              <div class="value" v-if="form.auditType == 0">备案</div>
              <div class="value" v-else-if="form.auditType == 1">审批</div>
              <div class="value" v-else-if="form.auditType == 2">会审</div>
              <div class="value" v-else>流转线上</div>
            </div>
            <div class="general-manager-comments">
              <div class="label">经发公司审核意见</div>
              <div class="value">
                <div style="width: 100%;">
                  <div
                    style="display: flex;align-items: center; line-height: 32px;height: 40px;"
                  >
                    <!-- {{ reviewOpinions ? reviewOpinions : "拟同意初审通过" }} -->
                    拟同意初审通过
                  </div>
                  <div class="comments-content" style="margin-top: 10px;">
                    <div style="margin-right: 80px; width: 220px;">{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.review_node && `审核人：${this.form.contractApprovalHistorys.review_node.approver}`}}</div>
                    <div style="margin-right: 80px;">{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.review_node && `审核日期：${formatDate(this.form.contractApprovalHistorys.review_node.approvalTime)}`}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="joint-trial">
              <div class="joint-trial-label">
                <div>会审意见</div>
              </div>
              <div>
                <div class="joint-unit-label" style="height:120px;">
                  经发办复核意见
                </div>
                <div class="joint-unit-label" style="height:180px;">
                  分管镇长审批意见
                </div>
                <div class="joint-unit-label" style="height:230px;">
                  镇营商服务联席会
                </div>
              </div>
              <div class="joint-trial-option">
                <div class="review-opton" style="height:120px;">
                  <div style="width: 100%;">
                    <div
                      style="display: flex;align-items: center; line-height: 32px;height: 80px;"
                    >
                    {{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.town_leader_approval_node && `${this.form.contractApprovalHistorys.town_leader_approval_node.comments}`}}
                  </div>
                    <div
                      style="margin-top: 10px; display: flex; align-items: center;justify-content: flex-end;"
                    >
                      <div style="margin-right: 80px; width: 220px;">{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.town_leader_approval_node && `审核人：${this.form.contractApprovalHistorys.town_leader_approval_node.approver}`}}</div>
                      <div style="margin-right: 80px;">{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.town_leader_approval_node && `审核日期：${formatDate(this.form.contractApprovalHistorys.town_leader_approval_node.approvalTime)}`}}</div>
                    </div>
                  </div>
                </div>
                <div class="approval-option" style="height:180px;">
                  <div style="width: 100%;">
                    <div
                      style="display: flex;align-items: center; line-height: 32px;height: 140px;"
                    >
                    {{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.town_leader_review_node && `${this.form.contractApprovalHistorys.town_leader_review_node.comments}`}}
                  </div>
                    <div
                      style="margin-top: 10px; display: flex; align-items: center;justify-content: flex-end;"
                    >
                      <div style="margin-right: 80px; width: 220px;">{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.town_leader_review_node && `审核人：${this.form.contractApprovalHistorys.town_leader_review_node.approver}`}}</div>
                      <div style="margin-right: 80px;">{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.town_leader_review_node && `审核日期：${formatDate(this.form.contractApprovalHistorys.town_leader_review_node.approvalTime)}`}}</div>
                    </div>
                  </div>
                </div>
                <div class="committee-approval-option" style="height:230px;">
                  <div style="width: 100%;">
                    <div
                      v-if="this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.joint_meeting_approval_node"
                      style="display: flex;align-items: center; line-height: 32px;height: 190px;"
                    >
                      同意
                    </div>
                    <div v-else style="display: flex;align-items: center; line-height: 32px;height: 190px;"></div>
                    <div
                      style="margin-top: 10px; display: flex; align-items: center;justify-content: flex-end;"
                    >
                      <div style="margin-right: 80px; width: 220px;">审核人：{{ this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.joint_meeting_approval_node && `${this.form.contractApprovalHistorys.joint_meeting_approval_node.approver}`}}</div>
                      <div
                        v-if="this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.joint_meeting_approval_node"
                        style="margin-right: 80px;">审核日期：{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.joint_meeting_approval_node && `${formatDate(this.form.contractApprovalHistorys.joint_meeting_approval_node.approvalTime)}`}}</div>
                      <div v-else style="margin-right: 80px; width: 223px;">审核日期：{{this.form.contractApprovalHistorys && this.form.contractApprovalHistorys.joint_meeting_approval_node && `${formatDate(this.form.contractApprovalHistorys.joint_meeting_approval_node.approvalTime)}`}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="remark remark-box">
              <div class="label" style="height:100px;">备注</div>
              <div class="value" style="height:100px;">
                <div style="width: 100%;">
                  <!-- <div
                    style="display: flex;align-items: center; line-height: 32px;height: 40px;"
                  ></div> -->
                  <div
                    style="margin-top: 20px; display: flex; align-items: center;justify-content: flex-end;margin-bottom:20px;"
                  >
                    <!-- <div style="margin-right: 300px;">签字：</div>
                    <div style="margin-right: 300px;">日期：</div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-footer">
            <div>{{ checkedUnit }}</div>
          </div>
        </div>
      </div>
      <div class="footer" id="footer">
        <div>
          <a-button class="footer-item" type="primary" @click="handleCancel"
            >返回</a-button
          >
        </div>
        <div class="right">
          <a-button class="footer-item" type="primary" @click="handleExport"
            >导出</a-button
          >
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
/**
 * props:modalVisible
 * event:closeModal
 */
import JsPdfImg from "html2pdf-2img";
import { ApiLeaseReviewAudit } from "APIs/Template/index.js";
import moment from 'moment';
export default {
  props: {
    modalVisible: Boolean,
    businessInfo: Object,
  },
  data() {
    return {
      visible: false,
      title: "华泾镇集体经济组织物业出租审核流转表",
      checkedUnit: "上海市徐汇区华泾镇经济发展办公室",
      form: {
        filingNumber: "",
      },
      dataInfo: {},
    };
  },
  watch: {
    modalVisible: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "visible", this.modalVisible);
        });
      },
      immediate: true,
    },
    businessInfo: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "dataInfo", this.businessInfo);
          this.queryBusinessInfoByFilingNumber();
        });
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    getApplicationItem() {
      return (
        this.form?.tenantry +
        " 承租 " +
        this.form?.rentalAddress +
        " 面积：" +
        this.form?.leaseArea +
        "㎡，租赁用途：" +
        this.form?.leasePurpose
      );
    },
  },
  mounted() {
    // const filingNumber = this.$router.query("filingNumber")
  },
  methods: {
    formatDate: function(source) {
      let target = "";
      if (source) {
        // target = moment(source).format("YYYY-MM-DD HH:mm:ss");
        target = moment(source).format("YYYY-MM-DD");
      }
      return target;
    },
    realHeight() {
      const height =
        (this.form?.attachmentNameList &&
          this.form?.attachmentNameList.length > 0 &&
          this.form?.attachmentNameList.length) ||
        0;
      console.log(height * 48);
      let targetHeight = height * 48;
      return height > 0
        ? "height: " +
            targetHeight +
            ";min-height: 220px; line-height: 48px;display: flex;"
        : "height: auto;min-height: 220px; line-height: 48px;display: flex;";
    },
    queryBusinessInfoByFilingNumber: function() {
      const { filingNumber } = this.businessInfo;
      const params = {
        filingNumber
      };
      ApiLeaseReviewAudit(params).then(res => {
        if (res.code == 0) {
          this.$set(this, "form", res.data);
        }
      });
    },
    cancelModal: function() {
      this.$emit("closeModal");
    },
    handleCancel: function() {
      console.log("close modal action.");
      this.$emit("closeModal");
    },
    handleExport: function() {
      console.log("close modal and export current page content as pdf");
      let footer = document.getElementById("footer");
      footer.style.display = "none";
      new JsPdfImg("#printPage", "华泾镇集体经济组织物业出租审核流转表", {
        pageBreak: [".title", "#area", "li", "h3"], // 当导出pdf时候，这个参数必填
        pageStartOffset: 200, // 每个页头的留空距离
        watermarkOption: {
          watermark_txt: "上海市徐汇区华泾镇经济发展办公室",
          z_index: 97,
          watermark_x: 0,
          watermark_y: 0,
          watermark_x_space: 80,
          watermark_y_space: 80,
          watermark_width: 480,
          watermark_fontsize: "30px",
          watermark_alpha: 0.08,
        },
      }).outPdf(() => {
        console.log("结束");
        footer.style.display = "flex";
        this.$emit("closeModal");
      });
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  font-size: 22px;
  color: #1d2129;
  font-weight: 400;
  .content {
    padding-top: 180px;
    margin-left: 90px;
    margin-right: 90px;
    &-header {
    }
    &-body {
      margin-top: 16px;
      width: 1170px;
      border: 1px solid #1d2129;
      //申请单位
      .applicant-unit {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .label {
          width: 350px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #000000;
          margin-left: 4px;
          margin-top: 4px;
        }
        .value {
          width: 100%;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-left: 48px;
          border: 1px solid #000000;
          border-left: none;
          margin-top: 4px;
          margin-right: 4px;
        }
      }
      //申请事项
      .application-items {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .label {
          width: 350px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-left: 1px solid #000000;
          border-right: 1px solid #000000;
          margin-left: 4px;
        }
        .value {
          width: 100%;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-left: 48px;
          border-right: 1px solid #000000;
          border-left: none;
          margin-right: 4px;
        }
      }
      .application-type,
      .approval-category,
      .material-name,
      .receipt-date,
      .audit-method,
      .general-manager-comments,
      .remark {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .label {
          width: 350px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-left: 1px solid #000000;
          border-right: 1px solid #000000;
          border-top: 1px solid #000000;
          margin-left: 4px;
        }
        .value {
          width: 100%;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-left: 48px;
          border-top: 1px solid #000000;
          border-right: 1px solid #000000;
          border-left: none;
          margin-right: 4px;
        }
      }

      .date-content,
      .comments-content {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      .joint-trial {
        display: flex;
        align-items: center;
        &-label {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 530px;
          writing-mode: vertical-lr;
          letter-spacing: 8px;
          margin-left: 4px;
          border: 1px solid #000000;
          border-bottom: none;
        }
        .joint-unit-label {
          text-align: center;
          padding: 10px;
          padding-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 204px;
          // height: 80px;
          border-top: 1px solid #000000;
        }
      }
      .joint-trial-option {
        width: 897px;
        margin-right: 4px;
      }
      .review-opton,
      .approval-option,
      .committee-approval-option {
        padding-left: 48px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        // height: 80px;
        border: 1px solid #000000;
        border-bottom: none;
      }
      .remark-box {
        border-bottom: none;
        margin-bottom: 4px;
        .label,
        .value {
          border-bottom: 1px solid #000000;
        }
      }
    }
    &-footer {
      margin-top: 16px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 18px;
      font-weight: 400;
      color: #1d2129;
    }
  }
  .header-title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 600;
    color: #1d2129;
  }
  .header-no {
    margin-top: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 22px;
    font-weight: 500;
    color: #1d2129;
  }
}
.footer {
  margin-top: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  .right {
    margin-left: 36px;
  }
  .footer-item {
    width: 112px;
    height: 40px;
    border-radius: 6px;
  }
}
</style>

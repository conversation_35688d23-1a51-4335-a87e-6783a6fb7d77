<template>
  <div class="gray-card">
    <a-card
      style="margin-top: 21px; border-radius: 8px"
      v-if="data.contractInfo"
    >
      <a-row type="flex" justify="space-between" align="center">
        <a-col span="12" class="topColumn">
          <div class="toptitle">申请人</div>
          <div class="toptitleContent">
            {{ data.lessorName }}
          </div>
        </a-col>
        <a-col span="12" class="review-number flexCenter"
          >审核流转编号：{{ data.contractInfo.filingNumber }}-1</a-col
        >
      </a-row>
      <div class="bggray">
        <a-row gutter="20">
          <a-col span="6" class="top-item"
            ><span>承租人：</span
            ><span>{{ data.contractInfo.tenantry }}</span></a-col
          >
          <a-col span="6" class="top-item">
            <span>申请类型：</span><span>{{ getApplicationType }}</span>
          </a-col>
          <a-col span="6" class="top-item">
            <span>承租面积：</span
            ><span>{{ data.contractInfo.leaseArea }}㎡</span>
          </a-col>
          <a-col span="6" class="top-item">
            <span>租赁用途：</span
            ><span>{{ data.contractInfo.leasePurpose }}</span>
          </a-col>
        </a-row>
        <a-row gutter="20" style="margin-top: 20px">
          <a-col class="top-item">
            <span>承租地址：</span
            ><span>{{ data.contractInfo.rentalAddress }}</span>
          </a-col>
        </a-row>
      </div>
    </a-card>
    <a-card
      style="margin-top: 21px; border-radius: 8px"
      v-if="data.originalContractInfo"
    >
      <commonTitle>原合同信息</commonTitle>
      <div class="bggray flex-around-content">
        <a-row class="flex-around-two-row" gutter="20">
          <a-col span="6" class="top-item"
            ><span>属地情况：</span
            ><span>{{
              data.originalContractInfo.territorialSituation
            }}</span></a-col
          >
          <a-col span="6" class="top-item">
            <span>租赁单价：</span
            ><span
              >{{ data.originalContractInfo.rentalUnitPrice }}元/m²/天</span
            >
          </a-col>
          <a-col span="6" class="top-item">
            <span>租赁价格机制：</span
            ><span>{{
              data.originalContractInfo.priceIncreaseMechanism == ""
                ? ""
                : data.originalContractInfo.priceIncreaseMechanism
            }}</span>
          </a-col>
          <a-col span="6" class="top-item"
            ><span>租赁用途：</span
            ><span>{{ data.originalContractInfo.leasePurpose }}</span></a-col
          >
        </a-row>
        <a-row class="flex-around-two-row" gutter="20">
          <a-col span="6" class="top-item">
            <span>租期：</span
            ><span
              >{{ data.originalContractInfo.leaseTermStart }}~{{
                data.originalContractInfo.leaseTermEnd
              }}</span
            >
          </a-col>
          <a-col span="6" class="top-item"
            ><span>总租期：</span
            ><span>{{ data.originalContractInfo.leaseTerm }}年</span></a-col
          >
          <a-col span="6" class="top-item">
            <span>免租期：</span
            ><span
              ><template>
                <a-tooltip>
                  <template slot="title">
                    <div
                      v-for="(item, index) in data.contractFreePeriods"
                      :key="index"
                    >
                      {{ item.startTime }}~{{ item.endTime }}
                    </div>
                  </template>
                  {{
                    data.contractFreePeriods[0] &&
                    data.contractFreePeriods[0].startTime
                  }}~{{
                    data.contractFreePeriods[0] &&
                    data.contractFreePeriods[0].endTime
                  }}
                </a-tooltip>
              </template></span
            >
          </a-col>
          <a-col span="6" class="top-item"
            ><span>总免租期：</span
            ><span
              >{{
                data.originalContractInfo.rentFreeDays
                  ? data.originalContractInfo.rentFreeDays
                  : 0
              }}天</span
            ></a-col
          >
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script>
import commonTitle from "./CommonTitle";
export default {
  components: {
    commonTitle,
  },
  name: "RenderFunctionComponent",
  props: {
    data: {
      type: Object,
      default: () => ({ contractInfo: {}, originalContractInfo: {} }),
    },
  },
  computed: {
    // 展示申请类型的计算属性
    getApplicationType() {
      //定义一个变量存放需要展示的类型
      let typePrefix = "";
      //解构赋值拿到contractInfo这个对象
      const { contractInfo } = this.data;
      //解构赋值拿到各个变量的值
      const { contractType, contentChangeType } = this.data.contractInfo;
      // console.log(contractInfo, "//////");
      //如果类型有值就进入判断，避免出错
      if (contractInfo && contractType != null) {
        console.log(typePrefix, "1======");
        contractType == 0
          ? (typePrefix = "新租")
          : contractType == 1
          ? (typePrefix = "续租")
          : contractType == 2
          ? (typePrefix = "变更")
          : (typePrefix = "提前终止");
        if (typePrefix === "变更") {
          console.log(typePrefix, "2======");
          contentChangeType == 0
            ? (typePrefix = "变更-主体变更")
            : contentChangeType == 1
            ? (typePrefix = "变更-内容变更")
            : (typePrefix = "");
          console.log(typePrefix, "3======");
        }
      }

      return typePrefix;
    },
  },
};
</script>
<style lang="less" scoped>
.topColumn {
  display: flex;
  > .toptitle {
    height: 32px;
    background: #f7f8fa;
    border-radius: 80px;
    width: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
  }
  > .toptitleContent {
    font-size: 22px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    text-align: LEFT;
    color: #1d2129;
    line-height: 32px;
  }
}
.review-number {
  font-size: 18px;
  font-family: PingFang SC, PingFang SC-500;
  font-weight: 500;
  text-align: LEFT;
  color: #86909c;
  line-height: 22px;
}
.bggray {
  background: #f7f8fa;
  border-radius: 6px;
  background: #f7f8fa;
  border-radius: 6px;
  padding: 18px 32px 18px 32px;
  margin-top: 24px;
}
.flexCenter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-around {
  display: flex;
  justify-content: space-around;
  height: 62px;
  background: #f7f8fa;
  border-radius: 6px;
  align-items: center;
  margin-top: 24px;
}
.flex-around-content {
  background: #f7f8fa;
  border-radius: 6px;
  padding: 18px 32px 6px 32px;
  margin-top: 24px;
}
.flex-around-two-row {
  display: flex;
  justify-content: space-around;
  padding-bottom: 12px;
}
.top-item > span:last-child {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-400;
  font-weight: 400;
  text-align: LEFT;
  color: #1d2129;
}

//标题样式
</style>

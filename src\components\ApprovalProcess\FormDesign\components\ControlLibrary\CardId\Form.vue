<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 身份证控件 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input v-model="form.inputTitle" placeholder="请输入" maxLength="20" />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        v-model="form.placeholder.tipsTitleText"
        placeholder="请输入"
        maxLength="50"
      />
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        inputTitle: null, //标题
        placeholder: {
          tipsTitleText: null //标题提示
        },
        notNull: false //是否必填
      }
    };
  },
  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  }
};
</script>

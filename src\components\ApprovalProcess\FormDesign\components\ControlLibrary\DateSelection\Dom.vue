<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 日期选择控件DOM
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '日期选择'"
      style="margin-bottom:unset"
      prop="dateTime"
      :rules="[
        {
          required: data.notNull,
          message: '请选择日期',
          trigger: 'onChange'
        }
      ]"
    >
      <a-date-picker
        :placeholder="
          (data.placeholderText || '请输入') + (data.notNull ? '(必填）' : '')
        "
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

import api, { formDownLoad } from "@/common/api";
import { BASE_URL } from "Config";

/**
 *
 * @param {*} params
 * @returns
 */
export function ApiGetParksName(params) {
  return api({
    // url: BASE_URL + "/manage/admin/queryParkAnalysisList",
    url: BASE_URL + "/manage/admin/findAllParkName",//查询所有园区名称 新接口
    method: "post",
    params
  });
}
export function ApiGetComponeyInfo(params) {
  return api({
    url: BASE_URL + "/system/sysdict/pageByParentCode",
    method: "post",
    params
  });
}
/**
 * 根据园区获取楼号
 */
export function ApiGetBuildFloorNumberByPark(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/queryAllBuilding",
    method: "post",
    params
  });
}
/**
 * 获取园区名称
 */
export function ApiGetParkBuildingNames(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryParkBuildings",
    method: "post",
    params
  });
}
/**
 * 根据归并园区获取园区名称
 */
export function ApiGetMergeParkBuildingNames(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findParkNamesByLessor",
    method: "post",
    params
  });
}
/**
 * 查询关联企业列表
 */
export function ApiGetRelationComponeyInfo(params) {
  return api({
    url: BASE_URL + "/manager/companyRelatedRelation/pageList",
    method: "post",
    params
  });
}
/**
 * 新增关联企业
 */
export function ApiAddRelationComponeyInfo(params) {
  return api({
    url: BASE_URL + "/manager/companyRelatedRelation/addRelatedCompanies",
    method: "post",
    params
  });
}
/**
 * 分页查询非关联企业
 */
export function ApiGetNotRelationComponeyInfo(params) {
  return api({
    url:
      BASE_URL + "/manager/companyRelatedRelation/pageListNoRelatedCompanies",
    method: "post",
    params
  });
}
/**
 * 查询实业公司
 */
export function ApiGetIndustryCompany(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findLessors",
    method: "post",
    params
  });
}
/**
 * 根据实业公司查询下属园区
 */
export function ApiGetIndustryCompanyEnterPrise(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findParkNamesByLessor",
    method: "post",
    params
  });
}
/**
 * 删除企业关联关系
 */
export function ApiDeleteRelationComponeyInfo(params) {
  return api({
    url: BASE_URL + "/manager/companyRelatedRelation/delete",
    method: "post",
    params
  });
}
/**
 * 根据归并园区查询下属园区
 */
export function ApiFindParkNamesByMergedName(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findParkNamesByMergedName",
    method: "post",
    params
  });
}
/**
 * 查询所有归并园区
 */
export function ApiSearchAllMergePark(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findAllMergedName",
    method: "post",
    params
  });
}
/**
 * 企业查询导出
 */
export function ApiExportEnterSearch(params) {
  return formDownLoad("/manager/companyBasic/screen/exportCompanyList", params);
}
/**
 * 载体查询导出
 */
export function ApiExportCarryQuerry(params) {
  return formDownLoad("/manage/admin/exportCarrierInformation", params);
}
/**
 * 园区查询导出
 */
export function ApiExportParkinfoMation(params) {
  return formDownLoad("/manage/admin/exportParkBuildings", params);
}

/**
 * 载体查询中删除载体
 */
export function ApiDeleteHousingCarriers(params) {
  return api({
    url: BASE_URL + "/buildings/floorRoom/deleteFloorRoom",
    method: "post",
    params
  });
}

/**
 * 载体查询中添加载体信息
 */
export function ApiAddHousingCarriers(params) {
  return api({
    url: BASE_URL + "/buildings/floorRoom/insertFloorRoom",
    method: "post",
    params
  });
}

/**
 * 载体查询中编辑载体信息
 */
export function ApiEditHousingCarriers(params) {
  return api({
    url: BASE_URL + "/buildings/floorRoom/updateFloorRoom",
    method: "post",
    params
  });
}

/**
 * 载体查询中编辑获取载体信息
 */
export function ApiGetHousingCarriers(params) {
  return api({
    url: BASE_URL + "/buildings/floorRoom/queryFloorRoom",
    method: "post",
    params
  });
}

/**
 * 大屏园区信息查询中获取列表数据
 */
export function ApiGetScreenParkList(params) {
  return api({
    url: BASE_URL + "/buildings/parkScreenInfo/listParkScreenInfo",
    method: "get",
    params: params
  });
}

/**
 * 大屏园区信息查询中获取园区基本信息
 */
export function ApiGetScreenParkInfo(params) {
  return api({
    url: BASE_URL + "/buildings/parkScreenInfo/getParkScreenInfoById",
    method: "get",
    params: params
  });
}

/**
 * 大屏园区信息查询中编辑园区的信息接口
 */
export function ApiEditScreenParkInfo(params) {
  return api({
    url: BASE_URL + "/buildings/parkScreenInfo/updateParkScreenInfo",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中编辑园区的信息接口
 */
export function ApiEditParkInfo(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/updateParkInfo",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中编辑园区获取园区负责人
 */
export function ApiGetParkManger(params) {
  return api({
    url: BASE_URL + "/security/user/queryUsersByRole",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中编辑园区删除图片
 */
export function ApiDeletePicture(params) {
  return api({
    url: BASE_URL + "/common/attachment/deleteById",
    method: "post",
    params: params
  });
}

/**
 * 园区信息查询中获取弹框园区基本信息
 */
export function ApiGetParkInfo(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/getParkInfoById",
    method: "get",
    params: params
  });
}

/**
 * 园区查询中根据实业公司查询下属归并园区
 */
export function ApiFindMergedByLessor(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/findMergedByLessor",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中新增园区
 */
export function ApiParkSave(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/parkSave",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中修改园区
 */
export function ApiParkModify(params) {
  return api({
    url: BASE_URL + "/buildings/parkRelation/parkModify",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中园区新增楼层
 */
export function ApiParkBuildingSave(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/parkBuildingSave",
    method: "post",
    params: params
  });
}

/**
 * 园区查询中园区修改楼层
 */
export function ApiParkBuildingModify(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/parkBuildingModify",
    method: "post",
    params: params
  });
}

/**
 * 获取园区楼
 */
export function ApiGetParkBuildings(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/queryParkBuildings",
    method: "post",
    params
  });
}

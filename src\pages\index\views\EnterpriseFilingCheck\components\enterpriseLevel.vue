<template>
  <div>
    <div id="EnterpriseLevel" ref="EnterpriseLevel" class="contain"></div>
  </div>
</template>

<script>
// import { getAreaOccupancyApi } from "@/pages/index/data/api/ComponyWatch";
import * as echarts from "echarts";
export default {
  data() {
    return {
      chartData: {},
      yData: [],
      seriesData: [],
    };
  },
  mounted() {
    this.initData();
  },
  created() {},
  methods: {
    async initData() {
      // this.chartData = await getAreaOccupancyApi("");
      // this.chartData.data.forEach((e) => {
      //   this.seriesData.push(Number(e.occupancyRate).toFixed(2));
      //   this.yData.push(e.parkName);
      // });
      this.drawLine();
    },
    drawLine() {
      let myChart = echarts.init(this.$refs.EnterpriseLevel);
      let option = {
        series: [
          {
            type: "gauge",
            startAngle: 180,
            endAngle: 0,
            center: ["50%", "75%"],
            radius: "90%",
            min: 0,
            max: 1,
            splitNumber: 8,
            axisLine: {
              lineStyle: {
                width: 6,
                color: [
                  [0.3, "#1882FF"],
                  [0.7, "#36EBCA"],
                  [1, "#F56D0B"],
                ],
              },
            },
            pointer: {
              icon: "path://M12.8,0.7l12,40.1H0.7L12.8,0.7z",
              length: "50%",
              width: 12,
              offsetCenter: [0, "-20%"],
              itemStyle: {
                color: "auto",
              },
            },
            axisTick: {
              length: 12,
              lineStyle: {
                color: "auto",
                width: 2,
              },
            },
            splitLine: {
              length: 20,
              lineStyle: {
                color: "auto",
                width: 5,
              },
            },
            axisLabel: {
              // color: function (value) {
              //   if (value < 0.375) {
              //     return "#1A87FD";
              //   } else if (value < 0.5) {
              //     return "#55ECAD";
              //   } else if (value === 0.625) {
              //     return "#F9FA26";
              //   } else if (value > 0.625) {
              //     return "#F6700C";
              //   }
              // },
              fontSize: 18,
              distance: 10,
              formatter: function (value) {
                if (value === 0) {
                  return "C";
                } else if (value === 0.125) {
                  return "CC";
                } else if (value === 0.25) {
                  return "CCC";
                } else if (value === 0.375) {
                  return "B";
                } else if (value === 0.5) {
                  return "BB";
                } else if (value === 0.625) {
                  return "BBB";
                } else if (value === 0.75) {
                  return "A";
                } else if (value === 0.875) {
                  return "AA";
                } else if (value === 1) {
                  return "AAA";
                }
              },
            },
            title: {
              offsetCenter: [0, "-10%"],
              fontSize: 20,
            },
            detail: {
              fontSize: 30,
              offsetCenter: [0, "-35%"],
              valueAnimation: true,
              formatter: function (value) {
                return Math.round(value * 100) + "";
              },
              color: "inherit",
            },
            data: [
              {
                value: 0.75,
                name: "",
                detail: {
                  offsetCenter: ["0%", "0%"],
                  formatter: function (value) {
                    if (value === 0) {
                      return "C";
                    } else if (value === 0.125) {
                      return "CC";
                    } else if (value === 0.25) {
                      return "CCC";
                    } else if (value === 0.375) {
                      return "B";
                    } else if (value === 0.5) {
                      return "BB";
                    } else if (value === 0.625) {
                      return "BBB";
                    } else if (value === 0.75) {
                      return "A 很好";
                    } else if (value === 0.875) {
                      return "AA";
                    } else if (value === 1) {
                      return "AAA";
                    }
                  },
                },
              },
            ],
          },
        ],
      };
      // 绘制图表
      myChart.setOption(option);
      //多图表自适应
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contain {
  width: 100%;
  height: 400px;
}
</style>

<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 流程设计-抽屉组件-规则分支配置
-->
<template>
  <div class="set-rule-branch">
    <a-form-model :model="form">
      <!-- <div>
        <p class="set-rule-branch-title">基本信息</p>
        <a-row> -->
      <!-- <a-col :span="12">
            <a-form-model-item
              label="规则名称"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }"
            >
              <a-input v-model="form.ruleName" placeholder="请输入规则名称" />
            </a-form-model-item>
          </a-col> -->
      <!-- <a-col :span="12">
            <a-form-model-item
              label="优先级"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }"
            >
              <a-select v-model="form.ruleLevel" placeholder="请选择">
                <a-select-option
                  :value="level.key"
                  v-for="level in levelList"
                  :key="level"
                >
                  {{ level.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </div> -->
      <div class="entry-rules">
        <p class="set-rule-branch-title">进入规则</p>
        <a-table
          :columns="columns"
          :data-source="form.conditions"
        >
          <template slot="formData" slot-scope="text, record">
            <a-form-model-item>
              <a-select
                v-model="record.formData"
                placeholder="请选择"
                @change="
                  () => {
                    $set(record, 'formKey', undefined)
                  }
                "
              >
                <a-select-option
                  v-for="item in options.conditions"
                  :key="
                    JSON.stringify({
                      inputId: item.key,
                      inputTitle: item.data.inputTitle || item.dataname,
                      inputType: item.datatype
                    })
                  "
                >
                  {{ item.data.inputTitle || item.dataname }}
                </a-select-option>
              </a-select>
              <a-select
                v-show="
                  record.formData &&
                    (JSON.parse(record.formData).inputType ===
                      'SuiteLibrary_nonAviation' ||
                      JSON.parse(record.formData).inputType ===
                        'SuiteLibrary_machineRoom' ||
                      JSON.parse(record.formData).inputType ===
                        'SuiteLibrary_personnelRecord' ||
                      JSON.parse(record.formData).inputType ===
                        'SuiteLibrary_manageMent')
                "
                v-model="record.formKey"
                placeholder="请选择子条件"
                @change="
                  () => {
                    record.formKeyName = options.formKey[
                      JSON.parse(record.formData).inputType
                    ].find((a) => {
                      return a.key === record.formKey
                    }).name
                  }
                "
              >
                <a-select-option
                  v-for="item in (record.formData &&
                    options.formKey[JSON.parse(record.formData).inputType]) ||
                    []"
                  :key="item.key"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </template>
          <template slot="conditionRule" slot-scope="text, record">
            <a-form-model-item>
              <a-select
                v-model="record.conditionRule"
                placeholder="请选择"
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
              >
                <a-select-option
                  v-for="item in options.conditionRule"
                  :key="item.dictValue"
                >
                  {{ item.nameCn }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </template>
          <template slot="value" slot-scope="text, record">
            <a-form-model-item>
              <a-input v-model="record.value" placeholder="请输入" />
            </a-form-model-item>
          </template>
          <template slot="relation" slot-scope="text, record">
            <a-form-model-item>
              <a-select
                v-model="record.relation"
                placeholder="请选择"
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
              >
                <a-select-option
                  v-for="item in options.relation"
                  :key="item.dictValue"
                >
                  {{ item.nameCn }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </template>
          <template slot="action" slot-scope="text, record, index">
            <div class="btns-action">
              <a-button
                shape="circle"
                icon="plus"
                size="small"
                @click="addOption(index)"
              />
              <a-button
                shape="circle"
                icon="minus"
                size="small"
                @click="delOption(index)"
              />
              <a-button
                shape="circle"
                icon="arrow-up"
                size="small"
                :disabled="index == 0"
                @click="arrowUpOption(index)"
              />
              <a-button
                shape="circle"
                icon="arrow-down"
                size="small"
                :disabled="index == form.conditions.length - 1"
                @click="arrowDownOption(index)"
              />
            </div>
          </template>
        </a-table>
      </div>
    </a-form-model>
  </div>
</template>
<script>
import { deepClone } from "@/common/utils"
const columns = [
  {
    title: "条件",
    dataIndex: "formData",
    width: 200,
    key: "formData",
    scopedSlots: { customRender: "formData" }
  },
  {
    title: "运算符",
    dataIndex: "conditionRule",
    width: 170,
    key: "conditionRule",
    scopedSlots: { customRender: "conditionRule" }
  },
  {
    title: "值",
    dataIndex: "value",
    key: "value",
    scopedSlots: { customRender: "value" }
  },
  {
    title: "关系符",
    dataIndex: "relation",
    width: 120,
    key: "relation",
    scopedSlots: { customRender: "relation" }
  },
  {
    title: "操作",
    width: 140,
    dataIndex: "action",
    scopedSlots: { customRender: "action" }
  }
]

export default {
  props: {
    data: {}
  },
  data() {
    return {
      levelList: [
        {
          key: 1,
          value: "低"
        },
        {
          key: 2,
          value: "中"
        },
        {
          key: 3,
          value: "高"
        }
      ],
      labelCol: { span: 8 },
      wrapperCol: { span: 12 },
      form: {
        //规则名称
        ruleName: "",
        //优先级
        ruleLevel: undefined,
        //表达式
        conditions: [
          {
            formData: undefined, //条件
            formKey: undefined, //子条件
            formKeyName: undefined, //子条件名称
            conditionRule: undefined, //运算符
            value: null, //值
            relation: "&&" //关系符 默认'并且'
          }
        ]
      },
      // 下拉选项
      options: {
        // 条件
        conditions: [],
        // 运算符
        conditionRule: [],
        // 关系符
        relation: [],

        // 套件条件
        formKey: {
          // 非航资源
          SuiteLibrary_nonAviation: [
            {
              key: "selectResource",
              name: "申请资源"
            }
          ],
          // 机房套件
          SuiteLibrary_machineRoom: [
            {
              key: "computerRoom",
              name: "机房"
            },
            {
              key: "terminal",
              name: "航站楼"
            },
            {
              key: "roomType",
              name: "机房作业类别"
            }
          ],
          // 机房人员套件
          SuiteLibrary_personnelRecord: [
            {
              key: "terminal",
              name: "航站楼"
            }
          ],
          // 非航资源
          SuiteLibrary_manageMent: [
            {
              key: "terminal",
              name: "航站楼"
            }
          ]
        }
      },
      columns
    }
  },
  mounted() {
    this.setOptionsData()
    this.setData()
  },
  methods: {
    // 回显数据
    setData() {
      let ruleInfo = this.data.data.ruleInfo
      if (!ruleInfo) return
      let arrList = this.$store.getters["approvalProcess/formDesign"].filter(
        (item) => {
          // 非航套件
          if (item.datatype === "SuiteLibrary_nonAviation") {
            return true
          }
          // 机房套件
          else if (item.datatype === "SuiteLibrary_machineRoom") {
            return true
          }
          // 机房人员套件
          else if (item.datatype === "SuiteLibrary_personnelRecord") {
            return true
          }
          //办理人员信息套件
          else if (item.datatype === "SuiteLibrary_manageMent") {
            return true
          } else {
            return item.data.isRegex
          }
        }
      )
      ruleInfo.conditions.forEach((i) => {
        if (!i.formData) return
        let inputId = JSON.parse(i.formData).inputId
        i.formData = JSON.stringify(
          arrList
            .filter((item) => {
              return item.key === inputId
            })
            .map((item) => {
              return {
                inputId: item.key,
                inputTitle: item.data.inputTitle || item.dataname,
                inputType: item.datatype
              }
            })[0]
        )
      })
      this.form = ruleInfo
    },
    //删除项
    delOption(index) {
      if (this.form.conditions.length > 1) {
        this.form.conditions.splice(index, 1)
      } else {
        this.$message.error("至少保留一项")
        return false
      }
    },
    getData() {
      return {
        ...this.data,
        title: this.form.ruleName,
        data: {
          ruleInfo: deepClone(this.form)
        }
      }
    },
    /**
     * 设置数据
     */
    setOptionsData() {
      this.options.conditions = this.$store.getters[
        "approvalProcess/formDesign"
      ].filter((item) => {
        // 非航套件
        if (item.datatype === "SuiteLibrary_nonAviation") {
          return true
        }
        // 机房套件
        else if (item.datatype === "SuiteLibrary_machineRoom") {
          return true
        }
        // 机房人员套件
        else if (item.datatype === "SuiteLibrary_personnelRecord") {
          return true
        }
        //办理人员信息套件
        else if (item.datatype === "SuiteLibrary_manageMent") {
          return true
        } else {
          return item.data.isRegex
        }
      })
      this.options.conditionRule =
        this.$store.getters["dictionaries/getType"]("ruleCondition") || []
      this.options.relation =
        this.$store.getters["dictionaries/getType"]("relationSymbol") || []
    },
    //添加项
    addOption(index) {
      let item = {
        formData: undefined, //条件
        formKey: undefined, //子条件
        formKeyName: undefined, //子条件名称
        conditionRule: undefined, //运算符
        value: undefined, //值
        relation: "&&" //关系符 默认'并且'
      }
      this.form.conditions.splice(index + 1, 0, item)
    },
    //上移项
    arrowUpOption(index) {
      let item = this.form.conditions.splice(index, 1)
      this.form.conditions.splice(index - 1, 0, item[0])
    },
    //下移项
    arrowDownOption(index) {
      let item = this.form.conditions.splice(index, 1)
      this.form.conditions.splice(index + 1, 0, item[0])
    }
  }
}
</script>
<style lang="less" scoped>
.set-rule-branch {
  .btns-action {
    button + button {
      margin-left: 3px;
    }
  }
  /deep/ .ant-table .ant-form-item {
    margin-bottom: 0;
  }
}
</style>

import api from "@/common/api";
import { BASE_URL } from "Config";

// 园区查询
export function queryParkBuildings(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryParkBuildings",
    method: "post",
    params,
  });
}
// 园区  => 楼宇信息
export function queryBuildingMsg(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryBuildingMsg",
    method: "post",
    params,
  });
}
// 园区  => 企业信息
export function queryEnterpriseInformation(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryEnterpriseInformation",
    method: "post",
    params,
  });
}
// 园区  => 税收信息
export function queryCurrencyTaxation(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryCurrencyTaxation",
    method: "post",
    params,
  });
}
// 载体查询
export function queryCarrierInformation(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryCarrierInformation",
    method: "post",
    params,
  });
}
// 园区  => 基本信息
export function queryParkBasicInformation(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryBasicInformation",
    method: "post",
    params,
  });
}
// 查询条件
export function queryCondition(params) {
  return api({
    url: BASE_URL + "/manage/admin/queryCondition",
    method: "post",
    params,
  });
}

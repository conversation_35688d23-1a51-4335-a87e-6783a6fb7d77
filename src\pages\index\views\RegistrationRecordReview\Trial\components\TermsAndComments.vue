<template>
  <div class="agree-item">
    <div class="agree-item-left">
      <!-- 引入子组件，传值给子组件 -->
      <TrialTerms
        :numType="numType"
        :annex="annex"
        :index="index"
        :title="title"
        @click="handleClick"
      ></TrialTerms>
    </div>
    <!-- 编写条款的审批意见部分，index有值才会显示 -->
    <div class="agree-item-right" v-if="index">
      <div class="agree-item-right-input-group">
        <div class="advice">
          <span class="adviceTitle">拟同意:</span>
          <span class="adviceContent">此处为处理意见</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TrialTerms from "../components/TrialTerms.vue";
export default {
  components: {
    TrialTerms
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    numType: {
      type: Number,
      default: 1
    },
    // 附件
    annex: {
      type: Object,
      default: () => {
        return {
          fileName: "",
          filePath: ""
        };
      }
    },
    //序号
    index: {
      type: Number,
      default: 1
    },
    //标题
    title: {
      type: Object,
      default: () => ({ titleName: "titleName", value: "title" })
    }
  },
  methods: {},
  data() {
    return {};
  }
};
</script>

<style lang="less" scoped>
.agree-item {
  display: flex;
  > .agree-item-left {
    width: 50%;
  }
  .agree-item-right {
    display: flex;
    width: 50%;
    align-items: center;
    justify-content: flex-start;
    padding-left: 41px;
    // 输入框组
    > .agree-item-right-input-group {
      display: flex;
      width: 100%;
      /deep/ .ant-radio-wrapper {
        font-size: 16px;
      }
    }
  }
}
</style>

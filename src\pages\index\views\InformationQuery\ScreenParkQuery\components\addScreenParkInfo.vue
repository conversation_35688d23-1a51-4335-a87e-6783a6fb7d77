<template>
  <div class="form">
    <a-modal
      title="园区基础信息"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model :model="modalForm" ref="ruleForm" :rules="rules">
        <a-form-model-item
          prop="parkName"
          label="园区名称"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            style="width: 20.5em"
            :disabled="disabled"
            v-model="parkName"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item
          label="园区租金"
          prop="rent"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input-number
            :disabled="noEdit"
            v-model="modalForm.rent"
            placeholder="请输入租金"
            style="width: 20.5em"
            :min="0"
            :step="0.01"
            :precision="2"
            @change="onChange"
          />
        </a-form-model-item>
        <a-form-model-item
          prop="parkDescription"
          label="园区介绍"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-textarea
            :disabled="noEdit"
            style="width: 20.5em"
            v-model="modalForm.parkDescription"
            placeholder="请输入园区介绍"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-form-model-item>
        <a-form-model-item
          prop="addressPhoto"
          label="园区区位图"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <Importer
            :useCustom="true"
            businessId="location_map"
            accept=".png,.jpg,.jpeg"
            @handleFileCallback="file => handleAreaFileCallback(file, record)"
          >
            <!-- 这里运用了具名插槽，将这个插入到子组件slot的地方 -->
            <template #import>
              <a-button type="primary" :disabled="noEdit || areaButton"
                >上传区位图</a-button
              >
            </template>
          </Importer>
          <div
            class="pic"
            style="position:relative;width:128px;height:auto;"
            v-if="areaButton"
          >
            <img
              style="width:128px;height:auto;"
              :src="
                areaFileList[0] != null
                  ? `${domain_url}/common/attachment/show?id=${areaFileList[0]}`
                  : null
              "
            />
            <div
              class="icon-container"
              style="position:absolute;top:0px;right:8px;width:10px;height:10px;"
              v-if="!noEdit"
            >
              <a-popconfirm
                title="您确认要删除区位图吗"
                ok-text="确认"
                cancel-text="取消"
                @confirm="confirmArea(areaFileList[0])"
              >
                <!-- <a href="#" v-if="!noEdit" style="width: 50px;">删除</a> -->

                <a-icon
                  type="delete"
                  style="font-size:18px;color:red;"
                  class="delete-icon"
                />
              </a-popconfirm>
            </div>
          </div>
        </a-form-model-item>
        <a-form-model-item
          prop="photo"
          label="园区相册"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <!-- 引入封装的上传组件，useCustom自定义按钮，accept上传的类型 -->
          <Importer
            useCustom="true"
            businessId="park_photo_album"
            accept=".png,.jpg,.jpeg"
            @handleFileCallback="
              file => handlePictureFileCallback(file, record)
            "
          >
            <!-- 这里运用了具名插槽，将这个插入到子组件Importer的slot的地方 -->
            <template #import>
              <a-button type="primary" :disabled="noEdit || picButton"
                >上传园区图片</a-button
              >
            </template>
          </Importer>
          <template v-if="picShow">
            <div
              class="pic"
              v-for="(item, index) in pictureFileList"
              :key="index"
              style="position:relative;width:128px;height:auto;margin-bottom:4px;"
            >
              <img
                style="width:128px;height:auto;"
                :src="
                  item != null
                    ? `${domain_url}/common/attachment/show?id=${item}`
                    : null
                "
              />
              <div
                class="icon-container"
                style="position:absolute;top:0px;right:8px;width:10px;height:10px;"
                v-if="!noEdit"
              >
                <a-popconfirm
                  title="您确认要删除图片吗"
                  ok-text="确认"
                  cancel-text="取消"
                  @confirm="confirmPic(item)"
                >
                  <!-- <a href="#" v-if="!noEdit" style="width: 50px;">删除</a> -->

                  <a-icon
                    type="delete"
                    style="font-size:18px;color:red;"
                    class="delete-icon"
                  />
                </a-popconfirm>
              </div>
            </div>
          </template>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import Importer from "@/components/Importer";
import {
  ApiEditScreenParkInfo,
  ApiDeletePicture
} from "@/pages/index/data/api/InfomationQuery/index.js";
import { BASE_URL } from "Config";
export default {
  components: {
    Importer
  },
  //接收父组件传过来的数据
  props: {
    visible: Boolean,
    screenParkName: String,
    id: String,
    formData: Object,
    noEdit: Boolean
  },
  data() {
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      disabled: true, //标识园区名称不可填写
      modalForm: {
        parkDescription: "",
        rent: null
      },
      leaderObj: [],
      fileList: [], //定义变量接收上传的数据
      params: "", //定义用来存储园区名字的
      refresh: false, //默认不刷新，true的时候刷新列表页
      pictureFileList: [], //定义变量接收园区相册上传的数据
      areaFileList: [], //定义变量接收区位图上传的数据
      picButton: false, //默认上传图片相册按钮可点击
      areaButton: false, //默认上传区位图按钮可点击
      picShow: false, //默认是没有图片要展示的
      domain_url: ""
    };
  },
  computed: {
    parkName() {
      return this.screenParkName;
    }
  },
  mounted() {
    this.$set(this, "domain_url", BASE_URL);
  },
  watch: {
    formData(newVal, oldVal) {
      console.log("formData发生变化了：", newVal, oldVal);
      this.modalForm = newVal;
      const tempPicArr = this.modalForm.parkPhotos;
      this.pictureFileList = tempPicArr.map(item => {
        return item.id;
      });
      if (this.pictureFileList.length != 0) {
        this.picShow = true;
        if (this.pictureFileList.length == 3) {
          this.picButton = true;
        }
      }
      const tempAreaArr = this.modalForm.locationMaps;
      this.areaFileList = tempAreaArr.map(item => {
        return item.id;
      });
      if (this.areaFileList.length != 0) {
        this.areaButton = true;
      }
      console.log(this.pictureFileList, this.areaFileList, "ssssss");
    }
  },
  methods: {
    //点击区位图的删除按钮调用的方法
    confirmArea(id) {
      //将要删除的图片id传入调用删除接口
      console.log(id, "hhhhh");
      ApiDeletePicture({ id: id }).then(res => {
        if (res.code == 0) {
          //将删除的图片从图片数组中移除不再显示，将上传按钮启用
          const tempArr = this.areaFileList.filter(item => item !== id);
          //将新的数组重新赋值给图片数组，重新渲染页面
          this.areaFileList = tempArr;
          if (tempArr.length >= 0) {
            this.areaButton = false;
          }
        }
      });
    },
    //点击相册图片删除调用的方法
    confirmPic(id) {
      ApiDeletePicture({ id: id }).then(res => {
        if (res.code == 0) {
          const tempArr = this.pictureFileList.filter(item => item !== id);
          this.pictureFileList = tempArr;
          if (tempArr.length < 3) {
            this.picButton = false;
          }
        }
      });
    },
    //园区相册上传之后的回调函数
    handlePictureFileCallback(file) {
      console.log(file);
      //将图片的id都添加在数组中
      this.pictureFileList.push(file.fileId);
      //如果图片没有超过3张可以一直回显图片
      if (this.pictureFileList.length < 4) {
        this.picShow = true;
      }
      //如果图片已经上传了3张就将上传按钮禁用
      if (this.pictureFileList.length >= 3) {
        this.picButton = true;
      }
    },
    //区位图上传之后的回调函数
    handleAreaFileCallback(file) {
      console.log(file);
      //将区位图的id都添加在数组中
      this.areaFileList.push(file.fileId);
      console.log(this.areaFileList, "?????");
      //区位图只能上传一张，一张之后就将按钮禁用
      if (this.areaFileList.length == 1) {
        this.areaButton = true;
      }
    },
    //弹框点击确认调用的方法
    handleOk: function() {
      if (this.noEdit == true) {
        this.handleCancel();
      } else {
        //将数组变成数组对象的形式
        const objects = [...this.pictureFileList, ...this.areaFileList].map(
          item => ({
            id: item
          })
        );
        ApiEditScreenParkInfo({
          ...this.modalForm,
          screenParkName: this.screenParkName,
          id: this.id,
          attachments: objects
        }).then(res => {
          console.log(res.data);
        });
        //将弹框隐藏
        this.$emit("close", false);
        console.log(this.visible, "bbbbbb");
        this.refresh = true;
        setTimeout(() => {
          this.$emit("refresh", this.refresh);
        }, 200);
        this.pictureFileList = [];
        this.areaFileList = [];
        this.picButton = false;
        this.areaButton = false;
        this.picShow = false;
      }
    },
    //关闭弹框调用的方法
    handleCancel: function() {
      //将弹框隐藏
      this.$emit("close", false);
      this.pictureFileList = [];
      this.areaFileList = [];
      this.picButton = false;
      this.areaButton = false;
      this.picShow = false;
      this.modalForm = {
        parkDescription: "",
        rent: null
      };
    }
  }
};
</script>
<style lang="less" scoped>
.form {
  /deep/.ant-select-selection--single {
    position: relative;
    height: 35px;
    cursor: pointer;
    width: 20.5em;
  }
}
/deep/.screen-avatar > img {
  object-fit: contain;
}
.pic > .icon-container {
  display: none;
}
.pic:hover > .icon-container {
  display: block;
}
.delete-icon {
  cursor: pointer;
}
</style>

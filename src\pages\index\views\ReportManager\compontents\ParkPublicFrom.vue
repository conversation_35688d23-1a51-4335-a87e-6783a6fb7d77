<template>
  <div>
    <a-form class="all">
      <a-form-item label="统计周期" class="label">
        <a-space direction="vertical" :size="12">
          <a-date-picker v-model="value1" @change="date($event)" />
        </a-space>
      </a-form-item>
      <a-form-item label="园区名称" class="label">
        <a-select
          v-model="this.value2"
          @change="getParkName($event)"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option
            :value="item.name"
            v-for="item in parkArr"
            :key="item.value"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <div class="btnGroup">
        <a-button type="primary" class="query" @click="query()">
          <a-icon type="search"></a-icon>
          查询
        </a-button>
        <a-button type="primary" class="export" @click="deduced()"
          >导出</a-button
        >
      </div>
    </a-form>
  </div>
</template>
<script>
import moment from "moment"
export default {
  components: {},
  data() {
    return {
      parkArr: [
        {
          name: "华泾镇",
          value: "1"
        },
        {
          name: "星联科技智慧园区",
          value: "2"
        }
      ],
      time: "",
      value1: "",
      value2: "园区名称",
      // 定义一个数组去接收后端查询传过来的数据
      queryData: [1, 2, 3]
    }
  },
  created() {
    console.log(this.queryData)
    this.$store.commit("tableData/saveParkQueryData", this.queryData)
  },
  methods: {
    //获取用户选择的时间
    date(e) {
      this.time = moment(e).format("YYYY MM DD")
      //   console.log(this.time);
    },
    //获取用户选择的园区
    getParkName(e) {
      //   console.log(e);
      this.value2 = e
    },
    //查询按钮
    query() {
      // let value1 = this.value1;
      // let value2 = this.value2;
      // let params = {
      //   value1,
      //   value2
      // };
    },
    //导出按钮
    deduced() {}
  }
}
</script>
<style lang="less" scoped>
.all {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 0.8rem;
  margin-left: 0.5rem;
  .label {
    display: flex;
    flex-wrap: nowrap;
    margin-right: 30px;
  }
  .btnGroup {
    margin-left: auto;
    margin-right: 10px;
    .query {
      margin-right: 10px;
    }
  }
}
/deep/.ant-select-selection--single {
  width: 12rem;
}
</style>

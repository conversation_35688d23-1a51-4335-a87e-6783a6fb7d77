<!--
* <AUTHOR>
* @time 2020-10-19
* @dec 系统管理 - 选项字典配置 - 查看
-->

<template>
  <a-modal
    title="查看"
    :width="640"
    :visible="visible"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
    v-bind="{
      footer: ''
    }"
  >
    <a-spin :spinning="false">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="中文名称">
          <span>{{ form.getFieldValue("nameCn") }}</span>
        </a-form-item>
        <a-form-item label="编码">
          <span>{{ form.getFieldValue("code") }}</span>
        </a-form-item>
        <a-form-item label="选项取值">
          <span>{{ form.getFieldValue("dictValue") }}</span>
        </a-form-item>
        <a-form-item label="描述">
          <span>{{ form.getFieldValue("desc") }}</span>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
// 表单字段
const fields = ["nameCn", "code", "dictValue", "desc"];
import pick from "lodash.pick";
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      form: this.$form.createForm(this)
    };
  },
  mounted() {
    // 防止表单未注册
    fields.forEach(v => this.form.getFieldDecorator(v));
    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields));
    });
  }
};
</script>

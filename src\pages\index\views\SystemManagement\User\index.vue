<!--
* <AUTHOR>
* @time 2020-8-21
* @dec 系统管理 - 用户管理页面
-->
<template>
  <a-card :bordered="false">
    <!-- {{ this.$store.getters["dictionaries/getType"]("USERTYPE") }} -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="用户名">
              <a-input
                v-model="queryParam.username"
                placeholder="请输入用户名"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="姓名">
              <a-input
                v-model="queryParam.name"
                allowClear
                placeholder="请输入姓名"
              />
            </a-form-item>
          </a-col>
          <template v-if="advanced">
            <a-col :md="8" :sm="24">
              <a-form-item label=" 手机号">
                <a-input
                  v-model="queryParam.phoneNo"
                  placeholder="请输入手机号"
                  allowClear
                />
              </a-form-item>
            </a-col>
            <!-- <a-col :md="8" :sm="24">
              <a-form-item label="公司类型">
                <a-select
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body
                    }
                  "
                  v-model="queryParam.orgType"
                  placeholder="请选择公司类型"
                  @change="switchType"
                >
                  
                  <a-select-option value="inside">内部</a-select-option>
                  <a-select-option value="outside">外部</a-select-option>
                </a-select>
              </a-form-item>
            </a-col> -->
            <a-col :md="8" :sm="24">
              <a-form-item label="部门">
                <a-select
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  v-model="queryParam.deptId"
                  allow-clear
                  placeholder="请选择部门"
                  @focus="findDepartmentTree"
                >
                  <a-select-option
                    v-for="d in departmentList"
                    :key="String(d.id)"
                    :value="String(d.id)"
                  >
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="是否启用">
                <a-select
                  v-model="queryParam.locked"
                  placeholder="请选择是否启用"
                  allowClear
                  :getPopupContainer="
                    (triggerNode) => {
                      return triggerNode.parentNode || document.body;
                    }
                  "
                >
                  <!-- <a-select-option value="">请选择</a-select-option> -->
                  <a-select-option value="0">启用</a-select-option>
                  <a-select-option value="1">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="注册时间" has-feedback>
                <a-range-picker style="width: 100%" v-model="queryParam.time" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="
                  () =>
                    (this.queryParam = {
                      deptId: undefined,
                      createStartTime: undefined,
                      createEndTime: undefined,
                      time: undefined,
                    })
                "
                >重置</a-button
              >
              <a @click="toggleAdvanced" style="margin-left: 8px">
                {{ advanced ? "收起" : "展开" }}
                <a-icon :type="advanced ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 批量操作 -->
    <div
      class="table-operator"
      v-if="
        $auth('mapi:security:user:batchDeleteById') ||
        $auth('mapi:security:user:setExpireTime') ||
        $auth('mapi:security:user:cancelExpireTime') ||
        $auth('mapi:security:user:create')
      "
    >
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd"
        v-auth="'mapi:security:user:create'"
        >新建</a-button
      >
      <a-popconfirm
        placement="top"
        ok-text="确定"
        cancel-text="取消"
        @confirm="handleBatchDel"
        title="确认删除?"
        v-auth="'mapi:security:user:batchDeleteById'"
        :disabled="!batchBtn"
      >
        <a-button type="primary" icon="delete" :disabled="!batchBtn"
          >删除</a-button
        >
      </a-popconfirm>
      <!-- <a-button
        type="primary"
        @click="handleSetTime"
        :disabled="!batchBtn"
        v-auth="'mapi:security:user:setExpireTime'"
        >设置过期</a-button
      >
      <a-popconfirm
        placement="top"
        ok-text="确定"
        cancel-text="取消"
        @confirm="handleCancelTime"
        v-auth="'mapi:security:user:cancelExpireTime'"
        title="确认取消?"
        :disabled="!cancelBtn"
      >
        <a-button type="primary" :disabled="!cancelBtn">取消过期</a-button>
      </a-popconfirm> -->
    </div>

    <s-table
      ref="table"
      size="default"
      rowKey="id"
      :columns="columns"
      :data="loadData"
      :rowSelection="
        ($auth('mapi:security:user:batchDeleteById') ||
          $auth('mapi:security:user:setExpireTime') ||
          $auth('mapi:security:user:cancelExpireTime')) &&
        rowSelection
      "
      :scroll="{ x: 1500 }"
    >
      <span slot="orgType" slot-scope="text">
        <span>{{ text | orgTypeFilter }}</span>
      </span>
      <span slot="locked" slot-scope="text, record">
        <a-switch
          v-if="$auth('mapi:security:user:lock')"
          :checked="!record.locked"
          @change="handleChangeLocked(record.locked, record)"
        />
        <span v-else>
          {{ text ? "否" : "是" }}
        </span>
      </span>
      <span slot="expired" slot-scope="text">
        <span>{{ text | expiredFilter }}</span>
      </span>
      <!-- <span slot="description" slot-scope="text">
        <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
      </span> -->
      <!-- <span slot="email" slot-scope="text">
        <ellipsis :length="10" tooltip>{{ text }}</ellipsis>
      </span> -->
      <span slot="roleName" slot-scope="text">
        {{ text }}
      </span>

      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleLook(record)">查看</a>
          <a-divider type="vertical" v-auth="'mapi:security:user:update'" />
          <a @click="handleEdit(record)" v-auth="'mapi:security:user:update'"
            >修改</a
          >
          <a-divider type="vertical" v-auth="'mapi:security:user:deleteById'" />
          <a-popconfirm
            title="确认删除?"
            @confirm="() => handleDel(record)"
            v-auth="'mapi:security:user:deleteById'"
          >
            <a href="javascript:;">删除</a>
          </a-popconfirm>
          <a-divider
            type="vertical"
            v-auth="'mapi:security:user:saveUserWithRoles'"
          />
          <a
            @click="handleDistribution(record)"
            v-auth="'mapi:security:user:saveUserWithRoles'"
            >角色分配</a
          >
          <a-divider
            type="vertical"
            v-auth="'mapi:security:user:updatePassWord'"
          />
          <a
            @click="handleModifyPwd(record)"
            v-auth="'mapi:security:user:updatePassWord'"
            >修改密码</a
          >
        </template>
      </span>
    </s-table>

    <create-form
      ref="createModal"
      :visible="visible"
      :data="mdl"
      :openWay="openWay"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
    <distribution
      ref="distributionModal"
      :visible="distributionVisible"
      :dataItem="distributionData"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
    <set-time
      ref="setTimeModal"
      :visible="setTimeVisible"
      :dataItem="setTimeData"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
    <modify-pwd
      ref="modifyPwdModal"
      :visible="modifyPwdVisible"
      :dataItem="modifyPwdData"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
    <look-detail
      ref="lookDtModal"
      :visible="lookDtvisible"
      :data="lookDtData"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table";
// import Ellipsis from "@/components/Ellipsis";
// API接口
import {
  ApiSecurityPageByCondition,
  ApiSecurityDeleteById,
  ApiSecurityBatchDeleteById,
  ApiSecurityLock,
  ApiSecurityUnlock,
  ApiSecurityCancelExpireTime,
  ApiSecurityFindDepartmentTree,
} from "@/pages//index/data/api/SystemManagement/User";

import CreateForm from "./compontents/CreateForm";
import Distribution from "./compontents/Distribution";
import SetTime from "./compontents/SetTime";
import ModifyPwd from "./compontents/ModifyPwd";
import LookDetail from "./compontents/LookDetail";

const columns = [
  {
    title: "用户名",
    dataIndex: "username",
    align: "center",
    width: "140px",
  },
  {
    title: "姓名",
    dataIndex: "name",
    align: "center",
    width: "110px",
  },
  {
    title: "手机号",
    dataIndex: "phoneNo",
    align: "center",
    width: "135px",
  },
  {
    title: "邮箱",
    dataIndex: "email",
    // scopedSlots: { customRender: "email" },
    align: "center",
    width: "200px",
  },
  {
    title: "公司类型",
    dataIndex: "orgType",
    scopedSlots: { customRender: "orgType" },
    align: "center",
    width: "100px",
  },
  {
    title: "部门",
    dataIndex: "deptName",
    align: "center",
    width: "160px",
  },
  {
    title: "角色",
    dataIndex: "roleName",
    scopedSlots: { customRender: "roleName" },
    align: "center",
    width: "160px",
  },
  {
    title: "是否启用",
    dataIndex: "locked",
    scopedSlots: { customRender: "locked" },
    align: "center",
    width: "100px",
  },
  {
    title: "注册时间",
    dataIndex: "createTime",
    align: "center",
    width: "140px",
  },
  {
    title: "操作",
    dataIndex: "action",
    fixed: "right",
    align: "center",
    width: "180px",
    scopedSlots: { customRender: "action" },
  },
];

const statusMap = {
  0: {
    status: "default",
    text: "关闭",
  },
  1: {
    status: "processing",
    text: "运行中",
  },
  2: {
    status: "success",
    text: "已上线",
  },
  3: {
    status: "error",
    text: "异常",
  },
};

const lockedMap = {
  false: {
    text: "启用",
  },
  true: {
    text: "禁用",
  },
};

const expiredMap = {
  false: {
    text: "未过期",
  },
  true: {
    text: "已过期",
  },
};

const orgTypeMap = {
  inside: {
    text: "内部",
  },
  outside: {
    text: "外部",
  },
};

export default {
  name: "SystemManagementUser",
  components: {
    STable,
    // Ellipsis,
    CreateForm,
    Distribution,
    SetTime,
    ModifyPwd,
    LookDetail,
  },
  data() {
    this.columns = columns;
    return {
      // create model
      batchBtn: false, //控制批量按钮
      cancelBtn: false, //取消过期按钮
      visible: false, //新增/修改/查看弹框
      openWay: null, //进入弹框方式
      distributionVisible: false, //角色分配弹框
      distributionData: null, //被分配数据标记
      setTimeVisible: false, //设置过期弹框
      setTimeData: null, //需要设置过期的数据
      confirmLoading: false,
      modifyPwdVisible: false, //修改密码弹框
      modifyPwdData: null, //被修改数据
      lookDtvisible: false, //查看用户弹框
      lookDtData: null, //查看数据
      mdl: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        ascs: "",
        branchCode: "",
        cityCode: "",
        currentPage: 1,
        descs: "",
        name: "",
        orgType: undefined,
        // pageSize: 10,
        phoneNo: "",
        username: "",
        createStartTime: null,
        createEndTime: null,
        locked: undefined,
        deptId: undefined,
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        if (this.queryParam.createTime) {
          this.queryParam.createTime =
            this.queryParam["createTime"].format("YYYY-MM-DD");
        }
        if (this.queryParam.time && this.queryParam.time.length > 0) {
          console.log(this.queryParam.time);
          this.queryParam.createStartTime =
            this.queryParam.time[0].format("YYYY-MM-DD");
          this.queryParam.createEndTime =
            this.queryParam.time[1].format("YYYY-MM-DD");
        } else {
          this.queryParam.createStartTime = null;
          this.queryParam.createEndTime = null;
        }

        // console.log("parameter", this.queryParam);
        this.queryParam.currentPage = parameter.pageNo;
        this.queryParam.current = this.queryParam.currentPage;
        const requestParameters = Object.assign({}, parameter, this.queryParam);
        return ApiSecurityPageByCondition(requestParameters).then((res) => {
          return res.data;
        });
        // .finally(() => {
        //   this.loading = false;
        // });
      },
      selectedRowKeys: [],
      selectedRows: [],
      departmentList: [], //部门列表
      departmentListLoading: false, //部门列表loading
    };
  },
  filters: {
    statusFilter(type) {
      return statusMap[type].text;
    },
    statusTypeFilter(type) {
      return statusMap[type].status;
    },
    lockedFilter(type) {
      return lockedMap[type].text;
    },
    expiredFilter(type) {
      return expiredMap[type].text;
    },
    orgTypeFilter(type) {
      return orgTypeMap[type].text;
    },
  },
  created() {},
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      };
    },
  },
  methods: {
    //新增
    handleAdd() {
      this.mdl = {};
      this.openWay = "add";
      this.visible = true;
    },
    //修改
    handleEdit(record) {
      this.visible = true;
      this.openWay = "edit";
      if (record.email == " ") {
        record.email = "";
      }
      if (record.phoneNo == " ") {
        record.phoneNo = "";
      }
      this.mdl = { ...record };
    },
    //查看
    handleLook(record) {
      this.lookDtvisible = true;
      this.lookDtData = { ...record };
    },
    //角色分配
    handleDistribution(record) {
      this.distributionData = record.id;
      this.distributionVisible = true;
    },
    //设置过期时间
    handleSetTime() {
      // if (this.selectedRows.length > 1) {
      //   this.$message.error("只能选择一条");
      //   return false;
      // }
      this.setTimeData = this.selectedRows
        .map((item) => {
          return item.id;
        })
        .join(",");
      console.log("asdf", this.setTimeData);
      this.setTimeVisible = true;
    },
    //取消过期时间
    handleCancelTime() {
      // if (this.selectedRows.length > 1) {
      //   this.$message.error("只能选择一条");
      //   return false;
      // }
      let selectedRowsIds = this.selectedRows
        .map((item) => {
          return item.id;
        })
        .join(",");
      const params = {
        id: selectedRowsIds,
      };
      ApiSecurityCancelExpireTime(params)
        .then((res) => {
          if (res.code == 0) {
            this.$message.info("取消过期成功!");
            this.cancelBtn = false;
            this.$refs.table.refresh();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //删除
    handleDel(record) {
      console.log("删除事件", record.id);
      this.loading = true;
      const params = {
        id: record.id,
      };
      ApiSecurityDeleteById(params)
        .then((res) => {
          console.log("loadData", res);
          this.$message.info("删除成功");
          this.$refs.table.refresh();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //弹框关闭
    handleCancel() {
      this.visible = false;
      this.distributionVisible = false;
      this.setTimeVisible = false;
      this.modifyPwdVisible = false;
      this.lookDtvisible = false;
      const form = this.$refs.createModal.form;
      this.selectedRowKeys = [];
      this.selectedRows = [];
      this.batchBtn = false;
      this.cancelBtn = false;
      form.resetFields(); // 清理表单数据（可不做）
    },
    //table勾选
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
      if (this.selectedRows.length > 0) {
        this.batchBtn = true;
        this.cancelBtn = false;
        this.selectedRows.forEach((item) => {
          if (item.expireTime) {
            this.cancelBtn = true;
          } else if (this.cancelBtn && !item.expireTime) {
            this.cancelBtn = true;
          } else if (!this.cancelBtn && !item.expireTime) {
            this.cancelBtn = false;
          }
        });
      } else {
        this.cancelBtn = false;
        this.batchBtn = false;
      }
    },
    //批量删除
    handleBatchDel() {
      let arr = [];
      this.selectedRows.forEach((item) => {
        arr.push(item.id);
      });
      let params = {
        ids: arr,
      };
      console.log("批量删除", params);
      ApiSecurityBatchDeleteById(params)
        .then((res) => {
          console.log("loadData", res);
          this.$message.info("删除成功");
          this.$refs.table.refresh();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //查询条件收起/展开
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    //修改密码
    handleModifyPwd(record) {
      this.modifyPwdData = record;
      this.modifyPwdVisible = true;
    },
    //切换用户类型
    switchType() {
      let companyType = this.queryParam.orgType == "inside" ? "0" : "1";
      this.queryParam.deptId = undefined;
      this.findDepartmentTree(companyType);
    },
    //根据用户类型查询部门列表
    findDepartmentTree() {
      const params = {
        companyType: "0",
      };
      // this.departmentListLoading = true;
      ApiSecurityFindDepartmentTree(params).then((res) => {
        this.departmentList = res.data;
      });
      // .finally(() => {
      //   this.departmentListLoading = false;
      // });
    },
    // 是否启用表格调接口
    handleChangeLocked(enable, record) {
      record.locked = !enable;
      const params = {
        id: record.id,
      };
      if (!record.locked) {
        ApiSecurityUnlock(params)
          .then(() => {
            this.$message.info("启用成功");
          })
          .catch(() => {
            record.locked = !record.locked;
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        ApiSecurityLock(params)
          .then(() => {
            this.$message.info("禁用成功");
          })
          .catch(() => {
            record.locked = !record.locked;
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
  },
};
</script> 
<style lang="less" scoped>
.ant-form-item /deep/.ant-form-item-label {
  width: 80px !important;
}
</style>

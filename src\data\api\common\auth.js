/**
 * <AUTHOR>
 * @time 2020-8-14
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 *
 * @dec 权限接口
 */
import api from "@/common/api"
import { BASE_URL } from "Config"
import { encrypt } from "@/common/aes"

/**
 * 登录接口
 */
export function ApiMapiLogin(params) {
  const middle = {
    request(params) {
      params.captchakey = encrypt(params.captchakey)
      params.captcha = encrypt(params.captcha)
      params.username = encrypt(params.username)
      params.password = encrypt(params.password)
      params.updatePassword = encrypt(params.updatePassword)
      return params
    },
    response(data) {
      return data
    }
  }
  return api({
    // url: process.env.VUE_APP_HOST + "/test",
    url: BASE_URL + "/login",
    method: "post",
    params,
    middle
  })
}
/**
 * 退出接口
 */
export function ApiMapiLogout() {
  return api({
    // url: process.env.VUE_APP_HOST + "/test",
    url: BASE_URL + "/logout",
    method: "post"
  })
}
/**
 * 获取权限接口
 */
export function ApiMapiPermission(params) {
  return api({
    // url: process.env.VUE_APP_HOST + "/test",
    url: BASE_URL + "/common/permission",
    method: "post",
    params
  })
}

/**
 * 获取菜单接口
 */
export function ApiMapiCommonMenu(params) {
  return api({
    // url: process.env.VUE_APP_HOST + "/test",
    url: BASE_URL + "/common/menu",
    method: "post",
    params
  })
}

/**
 *  获取验证码接口
 */
export function ApiMapiCaptcha(params) {
  return api({
    url: BASE_URL + "/captcha",
    method: "post",
    params
  })
}

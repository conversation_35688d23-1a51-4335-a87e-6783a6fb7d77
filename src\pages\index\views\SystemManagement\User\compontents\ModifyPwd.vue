<!--
* <AUTHOR>
* @time 2020-8-25
* @dec 系统管理 - 用户管理 - 修改密码
-->
<template>
  <a-modal
    title="修改密码"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="新密码">
          <a-input
            v-model="password"
            v-decorator="[
              'password',
              {
                rules: [
                  { required: true, message: '请输入新密码!' },
                  { validator: validatePass },
                ],
              },
            ]"
            type="text"
            placeholder="请输入新密码"
          />
          <a-button @click="handleNewPassword()">生成随机密码</a-button>
        </a-form-item>
        <a-form-item label="确认密码">
          <a-input
            v-decorator="[
              'confirm_password',
              {
                rules: [
                  { required: true, message: '请输入确认密码!' },
                  { validator: handleConfirmPass },
                ],
              },
            ]"
            type="password"
            placeholder="请输入确认密码"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick";
import { ApiSecurityUpdatePassWord } from "@/pages//index/data/api/SystemManagement/User";

// 表单字段
const fields = ["password", "confirm_password"];

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    dataItem: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 },
      },
    };
    return {
      loading: false,
      form: this.$form.createForm(this),
      password: "",
    };
  },
  watch: {
    visible: {
      handler: function (flag) {
        if (flag) {
          this.form.setFieldsValue({
            password: "",
            confirm_password: "",
          });
        }
      },
      deep: true,
    },
  },
  created() {
    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v));

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields));
    });
  },
  methods: {
    //密码验证
    validatePass(rule, value, callback) {
      console.log(rule);
      const trimmedValue = value.trim();
      const pattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,16}$/;

      if (trimmedValue && !pattern.test(trimmedValue)) {
        callback(new Error("密码格式：必须包含数字和字母，密码位数8-16位。"));
        return;
      }
      callback();
    },
    // handlePass(rule, value, callback) {
    //   this.password = value;
    //   if (value && !this.validatePassword(value).status) {
    //     callback(new Error(rule.message || this.validatePassword(value).msg));
    //   } else {
    //     callback();
    //   }
    // },
    // validatePassword(str) {
    //   if (str.length === 0) {
    //     return { status: false, msg: "不能为空！" };
    //   }
    //   const reg1 = /[a-z]+/;
    //   const reg2 = /[A-Z]+/;
    //   const reg3 = /[￥!@#$%^&*\-_+=,/./?]+/;
    //   const reg4 = /[\d]+/;
    //   const reg5 = /^(.){10,16}$/;
    //   if (
    //     str &&
    //     !(
    //       reg1.test(str) &&
    //       reg2.test(str) &&
    //       reg3.test(str) &&
    //       reg4.test(str) &&
    //       reg5.test(str)
    //     )
    //   ) {
    //     return {
    //       status: false,
    //       msg: "密码格式：密码位数8-16位，必须包含：数字+字母 这2种组成。",
    //     };
    //   }
    //   return { status: true };
    // },

    //确认密码验证
    handleConfirmPass(rule, value, callback) {
      if (this.password && this.password !== value) {
        callback("两次密码输入不一致！");
      }
      callback();
    },
    handleNewPassword() {
      let text = [
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
        "1234567890",
      ];
      let rand = function (min, max) {
        return Math.floor(Math.max(min, Math.random() * (max + 1)));
      };
      let len = rand(8, 16);
      console.log(len);
      let pw = [];
      for (let i = 0; i < len; i++) {
        let strpos = rand(0, 1);
        pw += text[strpos].charAt(rand(0, text[strpos].length - 1));
      }
      this.form.setFieldsValue({
        password: pw,
        confirm_password: pw,
      });
    },
    //确定提交
    handleOk(e) {
      this.loading = true;
      e.preventDefault();
      this.form.validateFields((errors, values) => {
        if (!errors) {
          let params = {
            userId: this.dataItem.id,
            passWord: values.confirm_password,
          };
          ApiSecurityUpdatePassWord(params)
            .then((res) => {
              if (res.code == 0) {
                this.$emit("cancel");
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("修改成功！");
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.loading = false;
        }
      });
    },
  },
};
</script>

/**
 * <AUTHOR>
 * @time 2020-9-3
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 */
import api from "@/common/api";
import { BASE_URL } from "Config";
/**
 * 表单保存接口
 * @dec 接口： /form/formTable/saveFormTable
 * @dec ApiFormSaveFormTable
 */
export function ApiFormSaveFormTable(params) {
  const middle = {
    request(params) {
      return params;
    },
    response(data) {
      return data;
    },
  };
  return api({
    url: BASE_URL + "/form/formTable/saveFormTable",
    method: "post",
    middle,
    params,
  });
}
/**
 * 表单查询接口
 * @dec 接口： /form/formTable/queryFormTable
 * @dec ApiFormQueryFormTable
 */
export function ApiFormQueryFormTable(params) {
  const middle = {
    request(params) {
      return params;
    },
    response(data) {
      return data;
    },
  };
  return api({
    url: BASE_URL + "/form/formTable/queryFormTable ",
    method: "post",
    middle,
    params,
  });
}
/**
 * 控件查询接口
 * @dec 接口： /form/formComponent/findAllFormComponent
 * @dec ApiFormFindAllFormComponent
 */
export function ApiFormFindAllFormComponent(params) {
  const middle = {
    request(params) {
      return params;
    },
    response(data) {
      return data;
    },
  };
  return api({
    url: BASE_URL + "/form/formComponent/findAllFormComponent",
    method: "post",
    middle,
    params,
  });
}

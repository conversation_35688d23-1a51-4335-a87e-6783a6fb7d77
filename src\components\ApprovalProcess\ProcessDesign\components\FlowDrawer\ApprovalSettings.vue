<!--
 * <AUTHOR>
 * @time 2020-9-9
 * @dec 流程设计-抽屉-审批设置
-->
<template>
  <div>
    <a-table
      :columns="columns"
      :data-source="approvalAuths"
    >
      <span slot="isAdd" slot-scope="text, record">
        <template>
          <a-switch v-model="record.isAdd" />
        </template>
      </span>
      <span slot="isRequired" slot-scope="text, record">
        <template>
          <a-switch v-model="record.isRequired" />
        </template>
      </span>
    </a-table>
  </div>
</template>
<script>
const columns = [
  {
    title: "审批流程操作",
    dataIndex: "nameCn",
    key: "nameCn",
    scopedSlots: { customRender: "nameCn" }
  },
  {
    title: "是否添加",
    dataIndex: "isAdd",
    key: "isAdd",
    scopedSlots: { customRender: "isAdd" }
  }
  // {
  //   title: "是否必填",
  //   dataIndex: "isRequired",
  //   key: "isRequired",
  //   scopedSlots: { customRender: "isRequired" }
  // }
];
export default {
  props: {
    data: {}
  },
  data() {
    return {
      columns,
      approvalAuths: []
    };
  },
  mounted() {
    this.setData();
    this.getData();
  },
  methods: {
    /**
     * 获取数据
     */
    getData() {
      console.log(
        this.approvalAuths,
        "approvalAuthsapprovalAuthsapprovalAuthsapprovalAuths"
      );
      return [...this.approvalAuths];
    },
    /**
     * 设置数据
     */
    setData() {
      let approvalAuths = this.data.data.approvalAuths;
      let arrList = this.$store.getters["dictionaries/getType"](
        "appro_auth"
      ).map(item => {
        return {
          ...item,
          isAdd: false,
          isRequired: false
        };
      });
      if (approvalAuths && approvalAuths.length) {
        if (approvalAuths.length < arrList.length) {
          let list = arrList.slice(approvalAuths.length, arrList.length + 1);
          // approvalAuths.push(...list);
          console.log(approvalAuths);
          approvalAuths = approvalAuths.concat(list);
          this.approvalAuths = approvalAuths;
        } else {
          this.approvalAuths = approvalAuths;
        }
      } else {
        this.approvalAuths = arrList;
        console.log(this.approvalAuths, 1111);
        this.approvalAuths.forEach(item => {
          if (
            item.dictValue === "sucess" ||
            item.dictValue === "opinions" ||
            item.dictValue === "withdraw" ||
            item.dictValue === "beforeSign"
          ) {
            item.isAdd = true;
          } else {
            item.isAdd = false;
          }
        });
      }
    }
  }
};
</script>
<style lang="less" scoped></style>

<template>
  <div class="keyClueForm">
    <a-form-model
      :model="formData"
      layout="horizontal"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="项目名称" props="projectName">
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入项目名称"
              v-model="formData.projectName"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="营商分部">
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.businessDivision"
              placeholder="请选择营商分布"
            >
            </a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item
            label="项目简介"
            props="projectdesc"
            :labelCol="{ span: 4 }"
          >
            <a-input
              :disabled="pageType === 'view'"
              type="textarea"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder=" 请输入至少20个字符，限制200个字符"
              v-model="formData.projectDescription"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="企业名称" props="projectType">
            <a-input
              placeholder="请输入企业名称"
              v-model="formData.companyName"
              :disabled="pageType === 'view'"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="统一社会信用代码">
            <a-input
              placeholder="请输入"
              v-model="formData.socialCreditCode"
              allowClear
              :disabled="pageType === 'view'"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="内/外资">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.investmentType"
              placeholder="请选择内/外资"
            >
              <a-select-option value="1"> 内资 </a-select-option>
              <a-select-option value="2"> 外资 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="预计税收规模">
            <a-select
              :disabled="pageType === 'view'"
              placeholder="请输入预计税收规模"
              v-model="formData.estimatedTaxRevenue"
              allowClear
            >
              <a-select-option value="1"> 百万级 </a-select-option>
              <a-select-option value="2"> 千万级 </a-select-option>
              <a-select-option value="3"> 亿万级 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12" style="display: flex">
          <a-form-model-item
            style="width: 65%"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
          >
            <span
              slot="label"
              style="width: 80%; line-height: revert"
              class="label-box"
            >
              预计投资/注册资金</span
            >
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入预计投资/注册资金"
              v-model="formData.estimatedInvestment"
              allowClear
            ></a-input>
          </a-form-model-item>

          <a-form-model-item
            label=""
            :style="{ width: formData.currency == 3 ? '17%' : '35%' }"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
            style="margin: 0 5px"
          >
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.currency"
              placeholder="请选择币种"
            >
              <a-select-option value="1">人民币 </a-select-option>
              <a-select-option value="2"> 美元 </a-select-option>
              <a-select-option value="3"> 其他 </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item
            v-if="formData.currency == 3"
            label=""
            style="width: 17%"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              :disabled="pageType === 'view'"
              placeholder="请输入"
              v-model="formData.otherCurrencyName"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center">
          <a-form-model-item
            :style="{ width: formData.taxProofSubmitted == 1 ? '50%' : '100%' }"
            prop="taxProofSubmitted"
            :label-col="{ span: formData.taxProofSubmitted == 1 ? 16 : 8 }"
            :wrapper-col="{ span: formData.taxProofSubmitted == 1 ? 8 : 16 }"
          >
            <span slot="label" class="label-box" style="width: 80%">
              落地企业是否提交税收依据</span
            >
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.taxProofSubmitted"
              placeholder="请选择落地企业是否提交税收依据"
              @change="changeTax()"
              allowClear
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            style="width: 50%; padding-left: 10px"
            v-if="formData.taxProofSubmitted == 1"
            :label-col="{ span: 0 }"
            :wrapper-col="{ span: 24 }"
          >
            <FileAttachmentList
              v-if="formData.taxFileList.length > 0"
              title=""
              pagesType="view"
              :ifNeedPreviewOnline="true"
              marked="true"
              @deleteFile="(file, fileList) => deleteConFile(file, fileList)"
              :fileList="formData.taxFileList"
            >
            </FileAttachmentList>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="招商类型">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.investmentTypeCategory"
              placeholder="请选择招商类型"
              allowClear
            >
              <a-select-option value="1"> 新设</a-select-option>
              <a-select-option value="2"> 迁入</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center">
          <a-form-model-item
            label="是否梅开二度"
            :style="{
              width: formData.isSecondRound == 1 ? '50%' : '100%',
            }"
            :label-col="{
              span: formData.isSecondRound == 1 ? 16 : 8,
            }"
            :wrapper-col="{
              span: formData.isSecondRound == 1 ? 8 : 16,
            }"
          >
            <!-- <span slot="label" class="label-box">是否梅开二度</span> -->
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isSecondRound"
              placeholder="请选择是否梅开二度"
            >
              <a-select-option value="1">是 </a-select-option>
              <a-select-option value="0">否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            style="width: 50%; padding-left: 10px"
            v-if="formData.isSecondRound == 1"
          >
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.originalCompany"
              palceholder="请填写"
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否总部经济">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isHeadquartersEconomy"
              placeholder="请选择是否总部经济"
              allowClear
            >
              <a-select-option value="1"> 跨国公司</a-select-option>
              <a-select-option value="2"> 民营</a-select-option>
              <a-select-option value="3"> 研发中心</a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否500强企业">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isTop500"
              placeholder="请选择是否500强企业"
              allowClear
            >
              <a-select-option value="1"> 世界五百强 </a-select-option>
              <a-select-option value="2"> 中国五百强 </a-select-option>
              <a-select-option value="3"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否4大产业集群">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isFourClusters"
              placeholder="请选择是否4大产业集群"
              allowClear
            >
              <a-select-option value="1">人工智能</a-select-option>
              <a-select-option value="2">生命健康</a-select-option>
              <a-select-option value="3">科技金融</a-select-option>
              <a-select-option value="4">艺术传媒</a-select-option>
              <a-select-option value="5"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否行业标杆">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isIndustryBenchmark"
              placeholder="请选择是否行业标杆"
              allowClear
            >
              <a-select-option value="1"> 央企投资</a-select-option>
              <a-select-option value="2"> 上市公司 </a-select-option>
              <a-select-option value="3"> 优质外资 </a-select-option>
              <a-select-option value="4"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="是否科创引领">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.isTechLeader"
              placeholder="请选择是否科创引领"
              allowClear
            >
              <a-select-option value="1"> 高新技术企业</a-select-option>
              <a-select-option value="2"> 专精特新</a-select-option>
              <a-select-option value="3">科技小巨人</a-select-option>
              <a-select-option value="4">专精特新小巨人</a-select-option>
              <a-select-option value="5"> 独角兽 </a-select-option>
              <a-select-option value="6"> 瞪羚企业 </a-select-option>
              <a-select-option value="7"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="营业执照">
            <FileAttachmentList
              v-if="formData.businessLicenseFileList.length > 0"
              title=""
              pagesType="view"
              :ifNeedPreviewOnline="true"
              marked="true"
              @deleteFile="(file, fileList) => deleteConFile(file, fileList)"
              :fileList="formData.businessLicenseFileList"
            >
            </FileAttachmentList>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="项目阶段">
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.projectStage"
              placeholder="请选择项目阶段"
              allowClear
            >
              <a-select-option value="1">准备期</a-select-option>
              <a-select-option value="2">洽谈中 </a-select-option>
              <a-select-option value="3">注册中 </a-select-option>
              <a-select-option value="4">已落户 </a-select-option>
              <a-select-option value="5">暂缓库 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="初冾时间">
            <a-date-picker
              v-model="formData.initialContactDate"
              :disabled="pageType === 'view'"
              placeholder="请选择初冾时间"
              style="width: 100%"
              :format="'YYYY-MM-DD'"
              :show-time="true"
              :show-now="false"
            ></a-date-picker>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item label="具体企业诉求及处理" :labelCol="{ span: 4 }">
            <a-textarea
              :disabled="pageType === 'view'"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder=" 请输入限制200个字符"
              v-model="formData.enterpriseDemands"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="备注" :labelCol="{ span: 4 }">
            <a-textarea
              :disabled="pageType === 'view'"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入备注"
              v-model="formData.remarks"
              :maxlength="200"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="具体跟踪和进展情况" :labelCol="{ span: 4 }">
            <a-textarea
              :disabled="pageType === 'view'"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入具体跟踪和进展情况"
              v-model="formData.trackingProgress"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12" style="display: flex; align-items: center">
          <a-form-model-item
            label="落地渠道"
            :label-col="{ span: formData.landingChannel == 6 ? 16 : 8 }"
            :wrapper-col="{ span: formData.landingChannel == 6 ? 8 : 16 }"
            :style="{ width: formData.landingChannel == 6 ? '50%' : '100%' }"
          >
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.landingChannel"
              placeholder="请选择落地渠道"
              allowClear
              style="width: 100%"
            >
              <a-select-option value="1"> 关键人物对接</a-select-option>
              <a-select-option value="2"> 业务支持 </a-select-option>
              <a-select-option value="3"> 租赁客户 </a-select-option>
              <a-select-option value="4"> 企业承诺 </a-select-option>
              <a-select-option value="5"> 上级部门推荐 </a-select-option>
              <a-select-option value="6"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            v-if="formData.landingChannel == 6"
            style="width: 50%; padding-left: 10px"
          >
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.otherLandingChannel"
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center">
          <a-form-model-item
            label="是否需要经发支持"
            :style="{ width: formData.needsSupport == 1 ? '50%' : '100%' }"
            :label-col="{ span: formData.needsSupport == 1 ? 16 : 8 }"
            :wrapper-col="{ span: formData.needsSupport == 1 ? 8 : 16 }"
          >
            <a-select
              :disabled="pageType === 'view'"
              v-model="formData.needsSupport"
              placeholder="请选择是否需要经发支持"
              allowClear
              style="padding-left: 10px"
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            v-if="formData.needsSupport == 1"
            style="width: 50%; padding-left: 10px"
          >
            <a-select
              :disabled="pageType === 'view'"
              placeholder="请选择"
              v-model="formData.supportType"
            >
              <a-select-option value="1"> 政策支持 </a-select-option>
              <a-select-option value="2"> 载体支持 </a-select-option>
              <a-select-option value="3"> 工商注册 </a-select-option>
              <a-select-option value="4"> 其他 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item label="跟进人">
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.followerName"
              placeholder="请填写跟进人"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="手机号码">
            <a-input
              :disabled="pageType === 'view'"
              v-model="formData.followerPhone"
              placeholder="请填写手机号码"
              allowClear
            ></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- //审批详情 -->
    <template v-for="(item, index) in approvalHistory">
      <div class="summarizeBox" :key="index">
        <div class="comTitle">
          {{
            item.reviewNodes == "初审节点"
              ? "初审小结"
              : item.reviewNodes == "复审节点"
              ? "复审小结"
              : "终审小结"
          }}
        </div>
        <div style="margin-bottom: 20px; padding-left: 20px">
          <span style="margin-right: 50px"
            >{{ item.approver }}：{{ item.remark }}
          </span>
          <span style="margin-right: 50px">审批意见：{{ item.comments }} </span>
          <span>审批时间：{{ item.approvalTime }} </span>
        </div>
        <!-- <div style="margin-bottom: 20px">
          <span>审批意见：同意 </span>
        </div> -->
      </div>
    </template>
    <!-- 审批（初审、复审）意见填写 -->
    <div class="approveBox" v-if="pageState == 3">
      <div class="comTitle">
        {{
          roles.includes("重点线索初审人")
            ? "初审小结"
            : roles.includes("重点线索复审人")
            ? "复审小结"
            : "终审小结"
        }}
      </div>

      <a-form-model
        ref="approveForm"
        :model="examineApproveData"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="approveRules"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="审批处理" prop="approveStatue">
              <a-select
                v-model="examineApproveData.approveStatue"
                placeholder="请选择"
              >
                <a-select-option value="0"
                  >{{
                    roles.includes("重点线索初审人") ||
                    roles.includes("重点线索复审人")
                      ? "拟同意"
                      : "同意"
                  }}
                </a-select-option>
                <a-select-option value="1"> 驳回 </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="审批意见">
              <a-input
                type="textarea"
                :auto-size="{ minRows: 3, maxRows: 5 }"
                placeholder="请输入审批意见"
                v-model="examineApproveData.comments"
                :maxlength="200"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      "
    >
      <a-button @click="toBack" style="margin: 0 10px">返回</a-button>
      <a-button type="primary" v-if="pageState == 3" @click="onSubmit()"
        >审批</a-button
      >
    </div>
  </div>
</template>
<script>
import FileAttachmentList from "@/components/fileView";
import {
  getDetailById,
  approvalBusinessClue,
  getApprovalHistory,
} from "@/pages/index/data/api/keyAndHouse/index";

export default {
  components: {
    FileAttachmentList,
  },
  data() {
    return {
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
      approvalHistory: [], //审批历史
      examineApproveData: {
        approveStatue: undefined, //审批处理
        comments: "", //审批意见
      },
      approveRules: {
        approveStatue: [
          { required: true, message: "请选择审批处理", trigger: "change" },
        ],
      },
      pageType: "view", //页面类型：view-查看，edit-编辑S
      pageState: "2", //页面状态：2-查看，3-处理
      id: "", //项目id
      contractFileList: [], //上传的收据
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      formData: {
        followUpPerson: "", //跟进人
        phoneNumber: "", //手机号码
        projectName: "", //项目名称
        businessDis: undefined, //营商分布
        projectdesc: "", //项目描述
        companyName: "", //企业名称
        companyCode: "", //统一社会信用代码
        investment: undefined, //内/外资
        taxScale: "", //预计税收规模
        investmentAmount: "", //预计投资/注册资金
        coins: undefined, //币种
        otherCoins: "", //其他币种
        isTaxBasis: "", //落地企业是否提交税收依据
        taxFileList: [],
        investmentType: undefined, //招商类型
        isSecondTime: undefined, //是否梅开二度
        secondTimeValue: "", //梅开二度值
        isHeadquarters: undefined, //是否总部经济
        is500Strong: undefined, //是否500强企业
        isFourIndustry: undefined, //是否4大产业集群
        isIndustryBenchmark: undefined, //是否行业标杆
        isScienceAndTechnology: undefined, //是否科创引领
        projectStage: undefined, //项目阶段
        initialTime: "", //初冾时间
        appeal: "", //具体企业诉求及处理
        remark: "", //备注
        track: "", //具体跟踪和进展情况
        landingChannels: undefined, //落地渠道：
        landingChannelsValue: "", //落地渠道值
        isNeedSupport: undefined, //是否需要经发支持
        isNeedSupportValue: undefined, //是否需要经发支持值
        businessLicenseFileList: [],
      },
    };
  },
  created() {
    console.log(this.$route.query);
    if (this.$route.query) {
      this.pageState = this.$route.query.statu;
      this.id = this.$route.query.id;
    }
    this.getDateils();
    this.getApprovalHistory();
  },
  methods: {
    //获取详情
    async getDateils() {
      await getDetailById({ id: this.id }).then((res) => {
        if (res.code == 0) {
          this.formData = res.data;
          this.formData.attachmentList = this.formData.attachmentList
            ? this.formData.attachmentList
            : [];
          this.id = res.data.id;
          let taxFileList = []; //税收依据文件列表
          let businessLicenseFileList = []; //收据文件列表
          console.log(this.formData, "查看详情");
          this.formData.attachmentList.forEach((item) => {
            if (item.businessType == "tax_basis") {
              taxFileList.push(item);
            } else if (item.businessType == "business_license") {
              businessLicenseFileList.push(item);
            }
          });
          this.$set(this.formData, "taxFileList", taxFileList);
          console.log(this.formData.taxFileList, "taxFileList");
          this.$set(
            this.formData,
            "businessLicenseFileList",
            businessLicenseFileList
          );
        }
      });
    },
    //获取审批历史
    async getApprovalHistory() {
      await getApprovalHistory({ contractInfoId: this.id }).then((res) => {
        if (res.code == 0) {
          if (res.data.length > 0) {
            this.approvalHistory = res.data.reverse();
          }
        }
      });
    },

    onSubmit() {
      this.$refs.approveForm.validate((valid) => {
        if (valid) {
          this.approvalBusinessClue();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //审批函数
    async approvalBusinessClue() {
      let params = {
        id: this.id,
        approveStatue: this.examineApproveData.approveStatue,
        comments: this.examineApproveData.comments,
      };
      await approvalBusinessClue(params).then((res) => {
        if (res.code == 0) {
          this.$message.success(`审批成功`);
          this.$router.push(`/key-clue-management`);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    toBack() {
      this.$router.push(`/key-clue-management`);
    },
  },
};
</script>
<style lang="less" scoped>
.keyClueForm {
  width: 100%;
  padding: 20px;
  background: #fff;

  .comTitle {
    position: relative;
    width: 100%;
    margin: 10px 0;
    padding: 0 10px;
    font-size: 20px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    &::before {
      content: "";
      width: 4px;
      height: 20px;
      background: #1777ff;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-10px);
    }
  }
  .summarizeBox,
  .approveBox {
    padding: 0 20px;
  }
  .boxWidth {
    /deep/.ant-form-item-control-wrapper {
      width: 100%;
    }
  }
  .label-box {
    width: 80%; // 建议80%，太长就会超出内容
    display: inline-block;
    height: auto !important;
    white-space: break-spaces;
    line-height: 18px;
    text-align: right;
    vertical-align: bottom; // 这是为了让整体的字往下移动一点
  }
  .ant-form-item {
    margin-bottom: 16px;
  }
  .ant-form-item-label {
    text-align: left;
  }
  .ant-form-item-control {
    margin-left: 0px;
  }
}
</style>

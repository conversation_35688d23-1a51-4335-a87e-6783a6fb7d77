<template>
  <div>
    <a-card style="width:100%;margin-top:20px">
      <a-tabs default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="租赁预警">
          <s-table
            ref="table"
            size="default"
            :columns="columnsLease"
            :data="loadData"
            :rowKey="id"
          >
            <span slot="serial" slot-scope="text, record, index">
              {{ (pageNo - 1) * pageSize + index + 1 }}
            </span>
            <template slot="warningDate" slot-scope="text, record">
              {{ record.year }}-{{ record.month }}
            </template>
            <template slot="warningType">
              <span>租赁到期预警</span>
            </template>
            <template slot="operate" slot-scope="text, record">
              <a @click="confirmCloseLease(record)">处理</a>
            </template>
          </s-table>
        </a-tab-pane>
        <a-tab-pane key="2" tab="税收预警">
          <s-table
            ref="tableTax"
            size="default"
            :columns="columns"
            :data="loadTaxiWarningData"
            :scroll="{ x: 1000, y: 500 }"
            :rowKey="id"
          >
            <span slot="serial" slot-scope="text, record, index">
              {{ (pageNo - 1) * pageSize + index + 1 }}
            </span>
            <template slot="warningDate" slot-scope="text, record">
              {{ record.year }}-{{ record.month }}
            </template>
            <template slot="warningType" slot-scope="text, record">
              <template>
                <a-icon
                  type="info"
                  style="color:red"
                  v-if="record.isLatest == 1"
                />
                <span v-if="record.warningType == 1"
                  >税收同比达到{{ record.deltaRate.toFixed(2) }}%
                  <a-icon type="rise" v-if="record.deltaRate > 0" />
                  <a-icon type="fall" v-else />
                </span>
                <span v-else
                  >税收净值同比达到{{
                    record.deltaTaxation.toFixed(2)
                  }}万<a-icon
                    type="rise"
                    v-if="record.deltaTaxation > 0"/><a-icon type="fall" v-else
                /></span>
              </template>
            </template>
            <template slot="operate" slot-scope="text, record">
              <a-popconfirm
                @confirm="() => confirmCloseTax(record.id)"
                title="确认关闭？"
                ok-text="确认"
                cancel-text="取消"
                v-if="delStatus != 1"
              >
                <a>关闭 </a>
              </a-popconfirm>
            </template>
          </s-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>
    <InfoModel
      v-on:myself="myDefine"
      :visible="visible"
      :textList="textList"
      @cancel="handleCancel"
    />
  </div>
</template>

<script>
import STable from "@/components/Table";
import InfoModel from "@/pages/index/views/Home/components/infoModel.vue";
import {
  getTaxationWarningPage,
  ApiCloseWarningMessage,
  ApiGetleaseWarning
} from "@/pages/index/data/api/Detail";
export default {
  components: {
    STable,
    InfoModel
  },
  data() {
    return {
      visible: false,
      textList: [],
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 60,
          fixed: "left",
          align: "center"
        },
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center"
        },
        {
          title: "预警类型",
          dataIndex: "warningType",
          align: "center",
          scopedSlots: { customRender: "warningType" }
        },
        {
          title: "预警时间",
          dataIndex: "warningDate",
          width: 200,
          align: "center",
          scopedSlots: { customRender: "warningDate" }
        },
        {
          title: "操作",
          dataIndex: "registeredCapital",
          scopedSlots: { customRender: "operate" },
          align: "center",
          width: 120
        }
      ],
      columnsLease: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 60,
          fixed: "left",
          align: "center"
        },
        {
          title: "企业名称",
          dataIndex: "tenantry",
          align: "center"
        },
        {
          title: "预警类型",
          dataIndex: "warningType",
          align: "center",
          width: 200,
          scopedSlots: { customRender: "warningType" }
        },
        {
          title: "租赁地址",
          dataIndex: "leaseAddress",
          align: "center"
        },
        {
          title: "租赁开始时间",
          dataIndex: "leaseStartTime",
          width: 200,
          align: "center"
        },
        {
          title: "租赁结束时间",
          dataIndex: "leaseEndTime",
          width: 200,
          align: "center"
        },
        {
          title: "操作",
          dataIndex: "registeredCapital",
          scopedSlots: { customRender: "operate" },
          align: "center",
          width: 120
        }
      ]
    };
  },
  methods: {
    myDefine(v) {
      this.visible = v;
    },
    confirmCloseTax(id) {
      ApiCloseWarningMessage({ id: id }).then(res => {
        console.log(res);
        if (res.code === 0) {
          this.$refs.tableTax.refresh(true);
        }
      });
    },
    getEarlyWarning(e) {
      console.log("仅仅为了不报错", e);
      this.$refs.table.refresh();
    },
    confirmCloseLease(record) {
      // 1:关闭不再提醒2: 近一个月内不在提醒临最后 3:30天在提醒给出处理方案并关闭预警提醒

      this.textList = [record];
      this.visible = true;
      // ApiResolveWarning({ warningId: id, operateType: 1 }).then((res) => {
      //   console.log(res)
      //   if (res.code === 0) {
      //     this.$refs.table.refresh(true)
      //   }
      // })
    },
    loadData(values) {
      this.id = this.$route.query.id;
      if (!this.id) {
        return;
      }
      this.pageNo = values.pageNo;
      this.pageSize = values.pageSize;
      const requestParameters = Object.assign(
        {},
        {
          companyId: this.id,
          pageNum: values.pageNo,
          pageSize: values.pageSize
        },
        this.queryParam
      );
      console.log("---", this.queryParam, values);

      return ApiGetleaseWarning(requestParameters)
        .then(res => {
          let dataObj = res.data;
          dataObj.data = res.data.records;
          return dataObj;
        })
        .catch(err => {
          console.log(err);
        });
    },
    loadTaxiWarningData(values) {
      this.id = this.$route.query.id;
      if (!this.id) {
        return;
      }
      this.pageNo = values.pageNo;
      this.pageSize = values.pageSize;
      const requestParameters = Object.assign(
        {},
        {
          companyId: this.id,
          currentPage: values.pageNo,
          pageSize: values.pageSize
        },
        this.queryParam
      );
      console.log("---", this.queryParam, values);

      return getTaxationWarningPage(requestParameters)
        .then(res => {
          let dataObj = res.data;
          dataObj.data = res.data.records;
          return dataObj;
        })
        .catch(err => {
          console.log(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
// /deep/.ant-table-thead
//   > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
//   > td,
// /deep/.ant-table-tbody
//   > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
//   > td,
// /deep/.ant-table-thead
//   > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
//   > td,
// /deep/.ant-table-tbody
//   > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
//   > td {
//   background: #fff;
// }
</style>

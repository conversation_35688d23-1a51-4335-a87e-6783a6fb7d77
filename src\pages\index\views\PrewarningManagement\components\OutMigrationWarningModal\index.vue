<template>
  <div>
    <a-modal
      :width="800"
      title="外迁预警申报"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @cancel="handleCancel"
      :footer="null"
    >
      <div>
        <div>
          <a-form :form="form" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row gutter="24">
              <a-col :span="24">
                <a-form-item label="申报企业名称" :colon="false" required>
                  <a-select
                    allowClear
                    show-search
                    :value="form.companyId"
                    placeholder="请输入企业名称"
                    style="width: 100%"
                    :default-active-first-option="false"
                    :show-arrow="false"
                    :filter-option="false"
                    :not-found-content="null"
                    @search="handleSearch"
                    @change="handleChange"
                  >
                    <a-select-option
                      v-for="company in companyArr"
                      :key="company.id"
                    >
                      {{ company.companyName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row gutter="24">
              <a-col :span="24">
                <a-form-item label="备注" :colon="false" required>
                  <a-textarea
                    placeholder="请输入外迁预警申报原因"
                    v-model="form.remark"
                    maxLength="100"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div :class="outMigrationModalName + 'btn-group'">
          <a-space>
            <a-button type="primary" @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk">保存</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  ApiAddMoveOutWarning,
  ApiQueryCompanylike,
} from "APIs/PrewarningManagement";
export default {
  props: {
    outMigrationVisible: Boolean,
  },
  data() {
    return {
      visible: false,
      isFlag: true,
      confirmLoading: false,
      form: {
        companyId: undefined,
        remark: "",
      },
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      outMigrationModalName: "out-migration-modal-",
      companyArr: [],
    };
  },
  watch: {
    visible: {
      handler() {
        this.$nextTick(() => {
          this.$set(this, "visible", this.outMigrationVisible);
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleCancel: function () {
      this.$emit("updateModal");
    },
    //保存外迁预警申报
    handleOk: function () {
      if (this.isFlag) {
        this.isFlag = false;
        const { companyId, remark } = this.form;
        console.log(companyId, "");
        if (remark && companyId) {
          ApiAddMoveOutWarning({
            companyId,
            remark,
          }).then((res) => {
            this.confirmLoading = true;
            if (res.code == 0) {
              console.log(res);
              this.$emit("updateModal");
            }
            this.confirmLoading = false;
          });
        } else {
          this.$message.info("您有必填项未填！");
        }
        setTimeout(() => {
          this.isFlag = true;
        }, 2000);
      }
    },
    //企业模糊搜索
    handleSearch: function (value) {
      console.log(value, "handleSearch~~~");
      if (value) {
        ApiQueryCompanylike({ companyName: value }).then((res) => {
          console.log(res);
          if (res.code == 0) {
            this.$set(this, "companyArr", res.data ? res.data : []);
          }
        });
      }
    },
    handleChange: function (value) {
      console.log(value, "handleChange~~~");
      this.$set(this.form, "companyId", value);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.out-migration-modal-btn-group {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

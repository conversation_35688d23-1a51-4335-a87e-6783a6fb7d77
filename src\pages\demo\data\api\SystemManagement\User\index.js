/**
 * <AUTHOR>
 * @time 2020-8-21
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 案例
 * @param {Object} middle 该字段可以为空，数据中间处理，可对发送前报文和响应数据进行格式化后，再到业务层
 */
export function ApiSecurityPageByCondition(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/user/pageByCondition",
    method: "post",
    middle,
    params,
  });
}
//新增用户
export function ApiSecurityCreate(params) {
  return api({
    url: BASE_URL + "/security/user/create",
    method: "post",
    params,
  });
}
//修改用户
export function ApiSecurityUpdate(params) {
  return api({
    url: BASE_URL + "/security/user/update",
    method: "post",
    params,
  });
}
//删除用户
export function ApiSecurityDeleteById(params) {
  return api({
    url: BASE_URL + "/security/user/deleteById",
    method: "post",
    params,
  });
}
//批量删除用户
export function ApiSecurityBatchDeleteById(params) {
  return api({
    url: BASE_URL + "/security/user/batchDeleteById",
    method: "post",
    params,
  });
}
//角色分配查询
export function ApiSecurityFindUserWithRoles(params) {
  return api({
    url: BASE_URL + "/security/user/findUserWithRoles",
    method: "post",
    params,
  });
}
//设置过期时间
export function ApiSecuritySetExpireTime(params) {
  return api({
    url: BASE_URL + "/security/user/setExpireTime",
    method: "post",
    params,
  });
}
//取消过期时间
export function ApiSecurityCancelExpireTime(params) {
  return api({
    url: BASE_URL + "/security/user/cancelExpireTime",
    method: "post",
    params,
  });
}
//锁定
export function ApiSecurityLock(params) {
  return api({
    url: BASE_URL + "/security/user/lock",
    method: "post",
    params,
  });
}
//解锁
export function ApiSecurityUnlock(params) {
  return api({
    url: BASE_URL + "/security/user/unlock",
    method: "post",
    params,
  });
}
//保存用户与角色关联
export function ApiSecuritySaveUserWithRoles(params) {
  return api({
    url: BASE_URL + "/security/user/saveUserWithRoles",
    method: "post",
    params,
  });
}
//查询部门树
export function ApiSecurityFindDepartmentTree(params) {
  return api({
    url: BASE_URL + "/security/department/findDepartmentTree",
    method: "post",
    params,
  });
}
//修改用户密码
export function ApiSecurityUpdatePassWord(params) {
  return api({
    url: BASE_URL + "/security/user/updatePassWord",
    method: "post",
    params,
  });
}

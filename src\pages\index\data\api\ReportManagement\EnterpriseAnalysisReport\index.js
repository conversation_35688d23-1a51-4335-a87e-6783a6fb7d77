import api, { formDownLoad } from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 企业分析报表
 */
export function ApiGetEnterListReport(params) {
  return api({
    url: BASE_URL + "/company/port/pageReport",
    method: "post",
    params,
  });
}
/**
 * 企业分析报表导出
 */
export function ApiExportEnterpriceReport(params) {
  return api({
    url: BASE_URL + "/company/port/ExportAnalysisReport",
    method: "post",
    params,
  });
}
/**
 * 分析报表导出，新接口
 */
export function ApiExportEnterReport(params) {
  //form 下载
  // var dlform = document.createElement("form")
  // dlform.style = "display:none;"
  // dlform.method = "post"
  // dlform.action = BASE_URL + "/company/port/download"
  // dlform.target = "_self"
  // Object.keys(params).forEach((key) => {
  //   if (params[key]) {
  //     var hdnFilePath = document.createElement("input")
  //     hdnFilePath.type = "hidden"
  //     hdnFilePath.name = key
  //     hdnFilePath.value = params[key]
  //     dlform.appendChild(hdnFilePath)
  //   }
  // })

  // document.body.appendChild(dlform)
  // dlform.submit()
  // document.body.removeChild(dlform)
  // return new Promise((resolve) => {
  //   setTimeout(() => {
  //     // 模拟请求
  //     resolve({ code: "0", msg: "success" })
  //   }, 500)
  // })
  return formDownLoad("/company/port/download", params);

  //xhr
  // return new Promise((resolve, reject) => {
  //   var xhr = new XMLHttpRequest()
  //   //xhr.open('POST', url, true);//POST请求，请求地址，是否异步
  //   xhr.open(
  //     "POST",
  //     BASE_URL +
  //       "/company/port/download2" +
  //       "?parkName=上海远中实业有限公司",
  //     true
  //   ) //POST请求，请求地址，是否异步
  //   xhr.responseType = "blob" // 返回类型blob
  //   xhr.onload = function() {
  //     // 请求完成处理函数
  //     if (this.status === 200) {
  //       var blob = this.response // 获取返回值
  //       console.log(blob)
  //       let link = document.createElement("a")
  //       link.href = window.URL.createObjectURL(blob)
  //       link.target = "_blank"
  //       link.download = "企业分析报表.xls"
  //       document.body.appendChild(link)
  //       link.click()
  //       document.body.removeChild(link)
  //       resolve(blob)
  //     } else {
  //       reject("错误")
  //     }
  //   }

  //   // 发送ajax请求
  //   xhr.send()
  // })
}

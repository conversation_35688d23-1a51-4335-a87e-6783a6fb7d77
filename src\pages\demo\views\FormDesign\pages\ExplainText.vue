<!--
* <AUTHOR>
* @time 2020-11-24
* @dec 说明文字
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <explain-text-dom :data="formData"></explain-text-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <explain-text-form v-bind:data.sync="formData"></explain-text-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 模块名称控件 DOM/Form
import {
  ExplainTextDom,
  ExplainTextForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/ExplainText";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    ExplainTextDom,
    ExplainTextForm
  },
  data() {
    return {
      formData: {
        inputTitle: "", //标题
        optionsData: {}
      },
      moduleVoList: []
    };
  },
  mounted() {
    this.moduleVoList = [];
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        // placeholder: JSON.stringify(this.formData.placeholder), //提示文字
        // optionsData: JSON.stringify(this.formData.optionsData), //文件信息
        notNull: this.formData.isNeed, //是否必填
        inputType: "explainText",
        inputName: "1"
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data).then(() => {
        this.$message.info("保存成功");
      });
    },
    handCheck() {
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        let allData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "ExplainText") {
              let itemData = {
                inputTitle: item.inputTitle
              };
              allData.push(itemData);
              this.formData = allData[allData.length - 1];
            }
          });
        });
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

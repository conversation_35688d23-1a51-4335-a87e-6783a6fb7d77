<template>
  <div class="tableContent">
    <s-table
      ref="homeTable"
      :data="loadData"
      :columns="columns"
      :scroll="{ y: 250, x: 800 }"
      rowKey="id"
    >
    </s-table>
  </div>
</template>
<script>
import sTable from "@/components/Table/index";
import { ApiGetTableData } from "../../../../data/api/EnterpriseMonitoring";
import * as utils from "@/common/utils/index.js";
export default {
  components: {
    sTable,
  },
  props: {
    typeLogo: Number,
  },
  data() {
    return {
      columns: [
        {
          title: "企业名称",
          dataIndex: "companyName",
          align: "center",
        },
        {
          title: "纳税金额(万元)",
          dataIndex: "taxAmount",
          align: "center",
          customRender: (text, row) => {
            return row && row.taxAmount && row.taxAmount.toFixed(2);
          },
        },
        // {
        //   title: "税源地",
        //   dataIndex: "taxArea",
        //   align: "center"
        // },
        {
          title: "入驻地",
          dataIndex: "settle",
          align: "center",
        },
        {
          title: "所属行业",
          dataIndex: "industry",
          align: "center",
        },
        {
          title: "企业类型",
          dataIndex: "econKind",
          align: "center",
        },
        {
          title: "注册资金",
          dataIndex: "registeredCapital",
          align: "center",
        },
        {
          title: "企业标签",
          dataIndex: "label",
          align: "center",
          customRender: (text, row) => {
            if (row.label) {
              let str = [];
              let label = JSON.stringify(row.label);
              if (label.includes("1")) {
                str.push("高新技术");
              }
              if (label.includes("2")) {
                str.push("专精特新");
              }
              if (label.includes("3")) {
                str.push("外资总部");
              }
              if (label.includes("4")) {
                str.push("上市公司");
              }
              return str.toString();
            } else {
              return "";
            }
          },
        },
      ],
    };
  },
  methods: {
    //默认要promise，如果没有数据就不显示
    loadData: function() {
      return ApiGetTableData().then((res) => {
        //优化原接收数据逻辑
        if (res.code == 0) {
          const tmpData = res.data;
          const key = this.typeLogo;
          let data = {};
          const records = tmpData[key];
          records.forEach((element) => {
            element["id"] = utils.getRandomNum("t", 8);
          });
          data["records"] = records;
          return data;
        }
        // let data1 = {
        //   records: []
        // };
        // let data2 = {
        //   records: []
        // };
        // let data3 = {
        //   records: []
        // };
        // let data4 = {
        //   records: []
        // };
        // for (let key in tmpData) {
        //   if (key == 1) {
        //     data1.records = tmpData[key];
        //     console.log(data1, "[[[[]]]]");
        //   } else if (key == 2) {
        //     data2.records = tmpData[key];
        //   } else if (key == 3) {
        //     data3.records = tmpData[key];
        //   } else {
        //     data4.records = tmpData[key];
        //   }
        // }
        // if (this.typeLogo == 1) {
        //   return data1;
        // } else if (this.typeLogo == 2) {
        //   return data2;
        // } else if (this.typeLogo == 3) {
        //   return data3;
        // } else if (this.typeLogo == 4) {
        //   return data4;
        // }
      });
    },
  },
};
</script>

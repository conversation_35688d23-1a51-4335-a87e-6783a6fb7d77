<!--
* <AUTHOR>
* @time 2020-10-19
* @dec 系统管理 - 选项字典配置
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button icon="plus" type="primary" @click="handleAdd(0, 1, '')"
                >新增</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <s-table
      ref="table"
      size="default"
      rowKey="key"
      :columns="columns"
      :data="loadData"
      :showPagination="false"
    >
      <span slot="description" slot-scope="text">
        <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="nameCn" slot-scope="text">
        <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="dictValue" slot-scope="text">
        <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="code" slot-scope="text">
        <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="desc" slot-scope="text">
        <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="action" slot-scope="text, record" style="white-space: nowrap">
        <template>
          <a @click="handleCheck(1, record)">查看</a>
          <a-divider type="vertical" />
          <a @click="handleAdd(0, record.level, record.id)">新增</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(2, record)">修改</a>
          <!-- <a-divider type="vertical" />
          <a-popconfirm
            placement="bottomLeft"
            ok-text="确认"
            cancel-text="取消"
            @confirm="handleDeleteOk(record)"
          >
            <template slot="title">
              <p>确认删除？</p>
            </template>
            <a>删除</a>
          </a-popconfirm> -->
        </template>
      </span>
    </s-table>
    <create-form
      ref="createModal"
      :visible="visible"
      :data="mdl"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
    <detail
      ref="systemDetail"
      :visible="visibleDetail"
      :data="mdlDetail"
      @cancel="handleCancelDetail"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table";
import Ellipsis from "@/components/Ellipsis";
// API接口
import {
  ApiSystemDelete,
  ApiSystemFindOptionByTree
} from "@/pages/index/data/api/SystemManagement/Option";
import CreateForm from "./compontents/CreateForm";
import Detail from "./compontents/Detail";
import { translateDataToTree } from "@/common/utils";
const columns = [
  {
    title: "中文名称",
    dataIndex: "nameCn",
    scopedSlots: { customRender: "nameCn" }
  },
  {
    title: "code值",
    dataIndex: "dictValue",
    scopedSlots: { customRender: "dictValue" }
  },
  {
    title: "编码",
    dataIndex: "code",
    scopedSlots: { customRender: "code" }
  },
  {
    title: "描述",
    dataIndex: "desc",
    scopedSlots: { customRender: "desc" }
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "150px",
    scopedSlots: { customRender: "action" }
  }
];

export default {
  name: "SystemManagementSystem",
  components: {
    STable,
    Ellipsis,
    CreateForm,
    Detail
  },
  data() {
    this.columns = columns;
    return {
      visible: false,
      visibleDetail: false,
      mdl: null,
      mdlDetail: null,
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        appType: "pc",
        currentPage: 1,
        dictNameCn: "",
        systemFlag: "",
        // pageSize: "10",
        dictCode: "",
        descs: ""
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam);
        return ApiSystemFindOptionByTree(requestParameters).then(res => {
          res.data = res.data.map(item => {
            item.key = item.id;
            return item;
          });
          res.data = translateDataToTree(res.data);
          return res;
        });
      },
      selectedRowKeys: [],
      selectedRows: [],
   
    };
  },
  methods: {
    // 查看
    handleCheck(type, record) {
      this.visibleDetail = true;
      this.mdlDetail = { ...record, type };
    },
    // 新增
    handleAdd(type, level, parentId) {
      this.mdl = { type, level, parentId };
      this.visible = true;
    },
    // 修改
    handleEdit(type, record) {
      this.visible = true;
      this.mdl = { ...record, type };
    },
    //确认删除
    handleDeleteOk(record) {
      ApiSystemDelete({ appType: record.appType, id: record.id })
        .then(() => {
          this.$refs.table.refresh(true);
          this.$message.info("删除成功");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单取消操作
    handleCancel() {
      this.visible = false;
      const form = this.$refs.createModal.form;
      form.resetFields(); // 清理表单数据（可不做）
    },
    // 表单取消操作
    handleCancelDetail() {
      this.visibleDetail = false;
      const form = this.$refs.createModal.form;
      form.resetFields(); // 清理表单数据（可不做）
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    }
  }
};
</script>

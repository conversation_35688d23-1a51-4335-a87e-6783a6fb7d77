import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 获取园区下拉列表
 * @param {*} params
 * @returns
 */
export function ApiGetParksName(params) {
  return api({
    // url: BASE_URL + "/manage/admin/queryParkAnalysisList",
    url: BASE_URL + "/manage/admin/findAllParkName",//查询所有园区名称 新接口
    method: "post",
    params,
  });
}

/**
 * 分页预警
 * @param {*} params
 * @returns
 */
export function ApiQueryWarningPage(params) {
  return api({
    url: BASE_URL + "/manage/warning/queryWarningPage",
    method: "post",
    params,
  });
}

/**
 * 预警统计
 * @param {*} params
 * @returns
 */
export function ApiQueryWarningStatistics(params) {
  return api({
    url: BASE_URL + "/manage/warning/queryWarningStatistics",
    method: "post",
    params,
  });
}

/**
 * 根据Id,type获取预警信息
 * @param {*} params
 * @returns
 */
export function ApiGetWarningByIdAndType(params) {
  return api({
    url: BASE_URL + "/manage/warning/getWarningByIdAndType",
    method: "post",
    params,
  });
}

/**
 * 处理预警信息
 * @param {*} params
 * @returns
 */
export function ApiUpdateWarningByType(params) {
  return api({
    url: BASE_URL + "/manage/warning/updateWarningByType",
    method: "post",
    params,
  });
}

/**
 * 预警派发-实业公司查询
 * @param {*} params
 * @returns
 */
export function ApiQueryDepartmentShiye(params) {
  return api({
    url: BASE_URL + "/manage/warning/queryDepartmentShiye",
    method: "post",
    params,
  });
}

/**
 * 预警派发-根据实业公司查询负责人
 * @param {*} params
 * @returns
 */
export function ApiQueryDepartmentOperatePerson(params) {
  return api({
    url: BASE_URL + "/manage/warning/queryDepartmentOperatePerson",
    method: "post",
    params,
  });
}

/**
 * 签收预警
 * @param {*} params
 * @returns
 */
export function ApiSignWarningByType(params) {
  return api({
    url: BASE_URL + "/manage/warning/signWarningByType",
    method: "post",
    params,
  });
}

/**
 * 实业负责人处理预警
 * @param {*} params
 * @returns
 */
export function ApiIndustryUpdateWarningByType(params) {
  return api({
    url: BASE_URL + "/manage/warning/industryUpdateWarningByType",
    method: "post",
    params,
  });
}

/**
 * 外迁预警-新增外迁预警
 * @param {*} params
 * @returns
 */
export function ApiAddMoveOutWarning(params) {
  return api({
    url: BASE_URL + "/manage/warning/addMoveOutWarning",
    method: "post",
    params,
  });
}

/**
 * 外迁预警-企业模糊查询
 * @param {*} params
 * @returns
 */
export function ApiQueryCompanylike(params) {
  return api({
    url: BASE_URL + "/manage/warning/queryCompanylike",
    method: "post",
    params,
  });
}

<template>
  <div class="tabs-router">
    <div class="tabs-head">
      <div>
        <a-tabs
          :tabBarStyle="{ margin: 0 }"
          @change="navigate"
          :activeKey="activeKey"
        >
          <a-tab-pane tab="列表a" key="ListA"></a-tab-pane>
          <a-tab-pane tab="列表b" key="ListB"></a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div class="tabs-content">
      <list-a v-show="activeKey === 'ListA'"></list-a>
      <list-b v-show="activeKey === 'ListB'"></list-b>
    </div>
  </div>
</template>

<script>
//列表a
import ListA from "./views/ListA";
//列表b
import ListB from "./views/ListB";
export default {
  name: "TabsComponent",
  components: { ListA, ListB },
  data() {
    return {
      activeKey: "ListA"
    };
  },
  methods: {
    navigate(key) {
      this.activeKey = key;
    }
  }
};
</script>

<template>
  <a-modal
    :title="title"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel')
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <!-- 检查是否有 id 并且大于0，大于0是修改。其他是新增，新增不显示主键ID -->
        <a-form-item label="应用端">
          <a-select
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
            style="width: 200px"
            placeholder="请选择应用端"
            v-decorator="[
              'appType',
              {
                rules: [
                  {
                    required: true,
                    message: '请选择应用端'
                  }
                ]
              }
            ]"
          >
            <a-select-option
              v-for="item in $store.getters['dictionaries/getType']('APPTYPE')"
              :key="item.dictValue"
            >
              {{ item.nameCn }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="类型">
          <a-select
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
            style="width: 200px"
            placeholder="请选择类型"
            v-decorator="[
              'type',
              {
                rules: [
                  {
                    required: true,
                    message: '请选择类型'
                  }
                ]
              }
            ]"
          >
            <a-select-option value="api">
              接口
            </a-select-option>
            <a-select-option value="element">
              元素
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="资源名称">
          <a-input
            placeholder="请输入资源名称"
            autoComplete="off"
            v-decorator="[
              'name',
              {
                initialValue: '',
                rules: [
                  {
                    required: true,
                    message: '请输入资源名称'
                  }
                ]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="链接地址">
          <a-input
            placeholder="请输入链接地址"
            autoComplete="off"
            @change="handleResourceStringChange"
            v-decorator="[
              'resourceString',
              {
                initialValue: '',
                rules: [{ required: true, message: '请输入链接地址' }]
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="授权码">
          <span>{{ form.getFieldValue("permissionString") || "-" }}</span>
          <a-input
            autoComplete="off"
            :disabled="data && data['id']"
            placeholder="请输入授权码"
            v-show="false"
            v-decorator="[
              'permissionString',
              {
                initialValue: ''
              }
            ]"
          />
        </a-form-item>
        <a-form-item label="是否启用" v-show="!(data && data.id > 0)">
          <a-radio-group
            v-decorator="[
              'disabled',
              { initialValue: false, rules: [{ required: true, message: '' }] }
            ]"
          >
            <a-radio :value="false">
              是
            </a-radio>
            <a-radio :value="true">
              否
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick"
// Api接口

import {
  ApiSecurityCreate,
  ApiSecurityUpdate
} from "@/pages/index/data/api/SystemManagement/Resources"
// 表单字段
const fields = [
  "id",
  "appType",
  "type",
  "name",
  "resourceString",
  "permissionString",
  "disabled"
]

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    }
    return {
      title: "新建资源",
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  // watch: {
  //   "data.resourceString": function(newVal) {
  //     console.log("newVal", newVal);
  //     // this.data.permissionString = newVal.replace("/", ":");
  //   }
  // },
  created() {
    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v))
    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields))
      if (this.data && this.data.id) {
        this.title = "修改资源"
      } else {
        this.title = "新建资源"
      }
    })
  },
  methods: {
    // 资源变化自动格式化授权码
    handleResourceStringChange(e) {
      let val = e.target.value.split("/").join(":")
      if (val) {
        if (val[0] === ":") {
          val = val.slice(1)
        }
        this.form.setFieldsValue({ permissionString: val })
      }
    },
    handleOk() {
      this.loading = true
      this.form.validateFields((errors, values) => {
        if (!errors) {
          if (this.data && this.data.id) {
            // 修改
            ApiSecurityUpdate(Object.assign(values, { id: this.data.id }))
              .then(() => {
                this.$emit("cancel")
                // 重置表单数据
                this.form.resetFields()
                // 刷新表格
                this.$emit("ok")
                this.$message.info("修改成功")
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            // 新增
            ApiSecurityCreate(values)
              .then(() => {
                this.$emit("cancel")
                // 重置表单数据
                this.form.resetFields()
                // 刷新表格
                this.$emit("ok")
                this.$message.info("新建成功")
              })
              .finally(() => {
                this.loading = false
              })
          }
        } else {
          this.loading = false
        }
      })
    }
  }
}
</script>

import api from "@/common/api";
import { BASE_URL } from "Config";
/**
 *
 * @param {*} params
 * @returns
 */
export function ApiGetDictByCode(params) {
  return api({
    url: BASE_URL + "/system/sysdict/pageByParentCode",
    method: "post",
    params
  });
}

/**
 *
 * @param {*} code 需要查找的code
 * @param {*} type 1 list 2 single 3 all
 * @param {*} parentCode 父级 need when  code search single
 * @returns
 */
export async function getDictByType(param) {
  let resp = await ApiGetDictByCode(param);
  return resp.data;
}

// 从本地缓存中获取初审意见字典表数据的方法
export function getDictValue() {
  let dict = localStorage.getItem("DICT_KEY");
  if (dict) {
    dict = JSON.parse(dict);
    let tempDict = dict.approval_list;
    console.log(tempDict, "dhguhuib ");
    dict = tempDict;
  }
  return dict;
}

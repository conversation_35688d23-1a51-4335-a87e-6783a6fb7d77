<template>
  <div class="wrap">
    <div>
      <span>常驻企业数：{{ enterpriseInformationCount }}家</span
      ><span class="mr-l"
        >本年新增企业数：{{ newlyAddedEnterpriseCount }}家</span
      >
    </div>
    <a-tabs default-active-key="1" @change="changeTab">
      <a-tab-pane key="1" tab="高新技术">
        <new-high-tech :pageNo="pageNo" :pageSize="pageSize" :loadData="initData"></new-high-tech>
      </a-tab-pane>
      <a-tab-pane key="2" tab="专精特新">
        <specialized :pageNo="pageNo" :pageSize="pageSize" :loadData="initData"></specialized>
      </a-tab-pane>
      <a-tab-pane key="3" tab="外资总部">
        <foreign-capital
          :pageNo="pageNo" :pageSize="pageSize"
          :loadData="initData"
        ></foreign-capital>
      </a-tab-pane>
      <a-tab-pane key="4" tab="上市公司">
        <listed-company :pageNo="pageNo" :pageSize="pageSize" :loadData="initData"></listed-company>
      </a-tab-pane>
      <a-tab-pane key="0" tab="其他">
        <other-type :pageNo="pageNo" :pageSize="pageSize" :loadData="initData"></other-type>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import NewHighTech from "./NewHighTech.vue"
import Specialized from "./Specialized.vue"
import ForeignCapital from "./ForeignCapital.vue"
import ListedCompany from "./ListedCompany.vue"
import OtherType from "./OtherType.vue"
import { queryEnterpriseInformation } from "@/pages/demo/data/api/api/park"
export default {
  components: {
    NewHighTech,
    Specialized,
    ForeignCapital,
    ListedCompany,
    OtherType
  },
  data() {
    return {
      parkName: "",
      // 查询条件参数
      queryParam: {
        pageNum: 1,
        pageSize: 10
      },
      pageNo: 1,
      pageSize: 10,
      NewHighTechList: () => [],
      SpecializedList: () => [],
      ForeignCapitalList: () => [],
      ListedCompanyList: () => [],
      OtherTypeList: [],
      enterpriseInformationCount: "",
      newlyAddedEnterpriseCount: "",
      tabName: "1",
      tabArray: ["高新技术", "专精特新", "外资总部", "上市公司", "其他"]
    }
  },
  created() {
    this.parkName = this.$route.query.parkName
  },
  mounted() {},
  methods: {
    changeTab(key) {
      this.tabName = key
    },
    initData({ pageNo, pageSize }) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      if (!this.$route.query.parkName) return

      let param = {
        parkName: this.$route.query.parkName,
        companyLabel: this.tabName,
        pageNum: pageNo,
        pageSize: pageSize
      }
      return queryEnterpriseInformation(param).then(async (res) => {
        let list = res.data.parkBuildingBos
        this.enterpriseInformationCount = res.data.residentEnterpriseCount
        this.newlyAddedEnterpriseCount = res.data.newlyAddedEnterpriseCount
        this.pageNo = pageNo
        console.log(this.pageNo, pageSize)
        switch (this.tabName) {
          case "1":
            this.NewHighTechList = list
            return this.NewHighTechList
          case "2":
            this.SpecializedList = list
            return this.SpecializedList
          case "3":
            this.ForeignCapitalList = list
            return this.ForeignCapitalList
          case "4":
            this.ListedCompanyList = list
            return this.ListedCompanyList
          case "0":
            this.OtherTypeList = list
            return this.OtherTypeList
          default:
            this.NewHighTechList = list
            return this.NewHighTechList
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.mr-l {
  margin-left: 30px;
}
.wrap {
}
/depp/ .ant-tabs-nav-scroll {
  background: #fff;
}
</style>

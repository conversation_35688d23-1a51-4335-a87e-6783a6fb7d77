/*
 * @Description:查询表格后获得的数据存储
 * @Author: cyw
 * @Date: 2023-06-19 15:39:34
 * @LastEditTime: 2023-06-19 15:53:04
 * @LastEditors: cyw
 */
import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    companyQueryData: [],
    parkQueryData: []
  },
  mutations: {
    // 保存企业分析报表查询后后端传过来的数据
    saveCompanyQueryData(state, companyQueryData) {
      state.companyQueryData = companyQueryData;
    },
    // 保存园区分析报表查询后后端传过来的数据
    saveParkQueryData(state, parkQueryData) {
      state.parkQueryData = parkQueryData;
    }
  }
});

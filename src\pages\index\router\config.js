import TabsView from "@/components/Layouts/Tabs/TabsView";
// import BlankView from "@/components/Layouts/BlankView";
import PageView from "@/components/Layouts/PageView";

const redirectPath = () => {
  console.log(localStorage.getItem("USER_KEY"),"localStorage called");
  if(!localStorage.getItem("USER_KEY")){
    return "/login";
  }
  let isIndustry = false;
  const userInfo = localStorage.getItem("USER_KEY");
  const roles = JSON.parse(userInfo).roles;
  if (roles && roles.includes("实业公司")) {
    isIndustry = true;
  } else {
    isIndustry = false;
  }
  return isIndustry ? "/registration-record-inquiry/query" : "/home/<USER>";
};

// 路由配置
const routes = [
  {
    path: "/",
    name: "",
    component: TabsView,
    redirect: redirectPath,
    children: [
      {
        path: "/home",
        name: "首页",
        meta: {
          authority: "admin:homeIndex",
          icon: "home"
        },
        component: PageView,
        redirect: "/home/<USER>",
        children: [
          {
            path: "index",
            name: "工作台",
            component: () => import("../views/Home")
          },
          {
            path: "/companyWatch",
            name: "载体监控",
            meta: {
              authority: "admin:companyWatch"
            },
            component: () => import("../views/Home/CompanyWatch")
          },
          {
            path: "/carrierMonitoring",
            name: "企业监控",
            meta: {
              authority: "admin:carrierMonitoring"
            },
            component: () => import("../views/CarrierMonitoring/index.vue")
          }
        ]
      },
      {
        path: "/information-query",
        name: "信息查询",
        meta: {
          authority: "admin:informationQuery",
          icon: "read"
        },
        redirect: "/information-query/carrier",
        component: PageView,
        children: [
          {
            path: "carrier",
            name: "载体查询",
            component: () => import("../views/InformationQuery/CarrierQuery"),
            meta: {
              authority: "admin:informationQuery:carrier"
            }
          },
          {
            path: "park",
            name: "园区查询",
            component: () => import("../views/InformationQuery/ParkQuery"),
            meta: {
              authority: "admin:informationQuery:park"
            },
            children: [
              {
                path: "/information-query/park-detail",
                name: "园区详情",
                component: () => import("../views/detail/parkDetail"),
                meta: {
                  hidden: true,
                  level: 3
                }
              }
            ]
          },
          {
            path: "screenPark",
            name: "载体查询",
            component: () =>
              import("../views/InformationQuery/ScreenParkQuery"),
            meta: {
              authority: "admin:informationQuery:screenPark"
            }
          },
          {
            path: "enterprise",
            name: "企业查询",
            component: () =>
              import("../views/InformationQuery/EnterpriseQuery"),
            meta: {
              authority: "admin:informationQuery:enterprise"
            },
            children: [
              {
                path: "/information-query/enterprise-detail",
                name: "企业详情",
                component: () => import("../views/detail/enterpriseDetail"),
                meta: {
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "report-manager",
        name: "报表管理",
        meta: {
          authority: "admin:reportManager",
          icon: "table"
        },
        redirect: "report-manager/enterprise",
        component: PageView,
        children: [
          {
            path: "enterprise",
            name: "企业分析报表",
            component: () =>
              import("../views/ReportManager/EnterpriseAnalysisReport"),
            meta: {
              authority: "admin:reportManager:enterprise"
            }
          },
          {
            path: "park",
            name: "园区分析报表",
            component: () =>
              import("../views/ReportManager/ParkAnalysisReport"),
            meta: {
              authority: "admin:reportManager:park"
            }
          }
          // {
          //   path: "tax",
          //   name: "税务分析报表",
          //   component: () => import("../views/ReportManager/TaxAnalysisReport"),
          //   meta: {
          //     authority: "admin:reportManager:tax"
          //   }
          // }
        ]
      },
      {
        path: "/system-management",
        name: "系统管理",
        meta: {
          authority: "admin:systemManagement",
          icon: "setting"
        },
        component: PageView,
        redirect: "/system-management/user",
        children: [
          {
            path: "user",
            name: "用户管理",
            component: () => import("../views/SystemManagement/User"),
            meta: {
              authority: "admin:systemManagement:user"
            }
          },
          {
            path: "role",
            name: "角色管理",
            component: () => import("../views/SystemManagement/Role"),
            meta: {
              authority: "admin:systemManagement:role"
            }
          },
          {
            path: "resources",
            name: "资源管理",
            component: () => import("../views/SystemManagement/Resources"),
            meta: {
              authority: "admin:systemManagement:resources"
            }
          },
          {
            path: "department",
            name: "部门管理",
            component: () => import("../views/SystemManagement/Department"),
            meta: {
              authority: "hadmin:systemManagement:department"
            }
          },
          {
            path: "menu",
            name: "菜单管理",
            component: () => import("../views/SystemManagement/Menu"),
            meta: {
              authority: "admin:systemManagement:menu"
            }
          },
          {
            path: "system",
            name: "系统配置",
            component: () => import("../views/SystemManagement/System"),
            meta: {
              authority: "admin:systemManagement:system"
            }
          },
          {
            path: "option",
            name: "选项配置",
            component: () => import("../views/SystemManagement/Option"),
            meta: {
              authority: "admin:systemManagement:option"
            }
          },
          {
            path: "operator",
            name: "操作日志",
            component: () => import("../views/SystemManagement/Operator"),
            meta: {
              authority: "admin:systemManagement:operator"
            }
          },
          {
            path: "message",
            name: "消息模板",
            component: () => import("../views/SystemManagement/Message"),
            meta: {
              authority: "admin:systemManagement:message"
            }
          },
          {
            path: "screenconfig",
            name: "大屏配置",
            component: () => import("../views/SystemManagement/ScreenConfig"),
            meta: {
              authority: "admin:systemManagement:screenConfig"
            }
          },
          {
            path: "import-data",
            name: "数据导入",
            component: () => import("../views/SystemManagement/ImportData"),
            meta: {
              authority: "admin:systemManagement:importData"
            }
          }
        ]
      },
      {
        path: "portal-management",
        name: "门户管理",
        meta: {
          icon: "tool",
          authority: "admin:portalManagement"
        },
        redirect: "/portal-management/notification-management",
        component: PageView,
        children: [
          // {
          //   path: "home-page",
          //   name: "首页管理",
          //   component: () => import("../views/PortalManagement/HomePage"),
          //   meta: {
          //     authority: "admin:portalManagement:homePage"
          //   }
          // },

          {
            path: "notice",
            name: "公告管理",
            component: () => import("../views/PortalManagement/Notice"),
            meta: {
              authority: "admin:portalManagement:notice"
            }
          },
          {
            path: "notification-management",
            name: "通知管理",
            component: () =>
              import("../views/PortalManagement/NotificationManagement"),
            meta: {
              authority: "admin:portalManagement:notificationManagement"
            },
            children: [
              {
                path: "/notification-management/detail",
                name: "通知详情",
                component: () =>
                  import(
                    "../views/PortalManagement/NotificationManagement/Detail"
                  ),
                meta: {
                  authority:
                    "admin:portalManagement:notificationManagement:detail",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "registration-record-inquiry",
        name: "租赁备案管理",
        meta: {
          authority: "admin:registrationRecordInquiry:management",
          icon: "file-search"
        },
        component: () => import("../views/RegistrationRecordInquery"),
        children: [
          {
            path: "query",
            name: "租赁备案查询",
            component: () => import("../views/RegistrationRecordInquery"),
            meta: {
              authority: "admin:registrationRecordInquiry:management:query"
            },
            children: [
              {
                path: "detail",
                name: "备案登记查看",
                component: () =>
                  import("../views/RegistrationRecordInquery/queryInfoDetail"),
                meta: {
                  authority:
                    "admin:registrationRecordInquiry:management:detail",
                  hidden: true,
                  level: 3
                }
              },
              {
                path: "edit",
                name: "备案登记编辑",
                component: () =>
                  import("../views/RegistrationRecordInquery/queryInfoEdit"),
                meta: {
                  authority: "admin:registrationRecordInquiry:management:edit",
                  hidden: true,
                  level: 3
                }
              },
              {
                path: "add",
                name: "新增备案登记",
                component: () =>
                  import("../views/RegistrationRecordInquery/queryInfoAdd"),
                meta: {
                  authority: "admin:registrationRecordInquiry:management:add",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "registration-record-review",
        name: "租赁备案审核管理",
        meta: {
          authority: "admin:registrationRecordReview:management",
          icon: "monitor"
        },
        component: () => import("../views/RegistrationRecordReview"),
        children: [
          {
            path: "record",
            name: "租赁备案审核查询",
            component: () => import("../views/RegistrationRecordReview"),
            meta: {
              authority: "admin:registrationRecordReview:management:record"
            },
            children: [
              {
                path: "reviewItem",
                name: "审批审核查看",
                component: () =>
                  import(
                    "../views/RegistrationRecordReview/RegistrationRecordReviewItem"
                  ),
                meta: {
                  authority:
                    "admin:registrationRecordReview:management:reviewItem",
                  hidden: true,
                  level: 3
                }
              },
              {
                path: "trial",
                name: "备案登记审核",
                component: () =>
                  import("../views/RegistrationRecordReview/Trial/index.vue"),
                meta: {
                  authority: "admin:registrationRecordReview:management:trial",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "prewarning-management",
        name: "预警管理",
        redirect: "/prewarning-management/prewarningInfo",
        meta: {
          authority: "admin:prewarning:management",
          icon: "warning"
        },
        component: PageView,
        children: [
          {
            path: "prewarningInfo",
            name: "预警管理列表",
            meta: {
              authority: "admin:prewarningManagement:prewarningInfo"
            },
            component: () =>
              import("../views/PrewarningManagement/PrewarningInfo")
          },
          {
            path: "prewarningStatistics",
            name: "预警统计",
            meta: {
              authority: "admin:prewarningManagement:prewarningStatistics"
            },
            component: () =>
              import("../views/PrewarningManagement/PrewarningStatistics")
          }
        ]
      },
      {
        path: "pre-declaration-query",
        name: "预申报表管理",
        meta: {
          authority: "admin:preDeclarationQuery:management",
          icon: "file-search"
        },
        component: () => import("../views/PreDeclarationQuery"),
        children: [
          {
            path: "query",
            name: "预申报表查询",
            component: () => import("../views/PreDeclarationQuery"),
            meta: {
              authority: "admin:preDeclarationQuery:management:query"
            },
            children: [
              {
                path: "add",
                name: "新增预申报表",
                component: () =>
                  import("../views/PreDeclarationQuery/queryInfoAdd"),
                meta: {
                  authority: "admin:preDeclarationQuery:management:add",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "pre-declaration-check",
        name: "预申报表审核管理",
        meta: {
          authority: "admin:preDeclarationCheck:management",
          icon: "file-search"
        },
        component: () => import("../views/PreDeclarationCheck"),
        children: [
          {
            path: "query",
            name: "预申报表查询",
            component: () => import("../views/PreDeclarationCheck"),
            meta: {
              authority: "admin:preDeclarationCheck:management:query"
            },
            children: [
              {
                path: "add",
                name: "预申报表审核",
                component: () =>
                  import("../views/PreDeclarationCheck/queryInfoAdd"),
                meta: {
                  authority: "admin:preDeclarationCheck:management:add",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "enterprise-filing",
        name: "企业建档表管理",
        meta: {
          authority: "admin:enterpriseFiling:management",
          icon: "file-search"
        },
        component: () => import("../views/EnterpriseFiling"),
        children: [
          {
            path: "query",
            name: "企业建档表查询",
            component: () => import("../views/EnterpriseFiling"),
            meta: {
              authority: "admin:enterpriseFiling:management:query"
            },
            children: [
              {
                path: "add",
                name: "企业建档表新增",
                component: () =>
                  import("../views/EnterpriseFiling/queryInfoAdd"),
                meta: {
                  authority: "admin:enterpriseFiling:management:add",
                  hidden: true,
                  level: 3
                }
              },
              {
                path: "detail",
                name: "企业建档表详情",
                component: () =>
                  import("../views/EnterpriseFiling/queryInfoDetail"),
                meta: {
                  authority: "admin:enterpriseFiling:management:detail",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },
      {
        path: "enterprise-filing-check",
        name: "企业建档表审批",
        meta: {
          authority: "admin:enterpriseFilingCheck:management",
          icon: "file-search"
        },
        component: () => import("../views/EnterpriseFilingCheck"),
        children: [
          {
            path: "query",
            name: "企业建档表查询",
            component: () => import("../views/EnterpriseFilingCheck"),
            meta: {
              authority: "admin:enterpriseFilingCheck:management:query"
            },
            children: [
              {
                path: "add",
                name: "企业建档表审批",
                component: () =>
                  import("../views/EnterpriseFilingCheck/queryInfoAdd"),
                meta: {
                  authority: "admin:enterpriseFilingCheck:management:add",
                  hidden: true,
                  level: 3
                }
              }
            ]
          }
        ]
      },

      {
        path: "key-clue-management",
        name: "重点线索管理",
        meta: {
          authority: "admin:keyClueManagement:management:query",
          icon: "file-search"
        },
        component: () => import("../views/keyClueManagement"),
        children: [
          {
            path: "addKeyClue",
            name: "新增重点线索",
            component: () =>
              import("../views/keyClueManagement/addEditKeyClue.vue"),
            meta: {
              authority: "admin:keyClueManagement:management:addKeyClue",
              hidden: true,
              level: 2
            }
          },
          {
            path: "editKeyClue",
            name: "编辑重点线索",
            component: () =>
              import("../views/keyClueManagement/addEditKeyClue.vue"),
            meta: {
              authority: "admin:keyClueManagement:management:editKeyClue",
              hidden: true,
              level: 2
            }
          },
          {
            path: "viewKeyClue",
            name: "查看重点线索",
            component: () =>
              import("../views/keyClueManagement/viewKeyClue.vue"),
            meta: {
              authority: "admin:keyClueManagement:management:viewKeyClue",
              hidden: true,
              level: 2
            }
          }
        ]
      },
      {
        path: "household-infor-managment",
        name: "户管信息管理",
        meta: {
          authority: "admin:householdInforManagment:management:query",
          icon: "file-search"
        },
        component: () => import("../views/householdInforManagment"),
        children: [
          {
            path: "addHosehold",
            name: "新建户管信息",
            component: () =>
              import("../views/householdInforManagment/addEditHosehold"),
            meta: {
              authority: "admin:householdInforManagment:management:addHosehold",
              hidden: true,
              level: 2
            }
          },
          {
            path: "editHosehold",
            name: "编辑户管信息",
            component: () =>
              import("../views/householdInforManagment/addEditHosehold"),
            meta: {
              authority:
                "admin:householdInforManagment:management:editHosehold",
              hidden: true,
              level: 2
            }
          },
          {
            path: "viewHosehold",
            name: "查看户管信息",
            component: () =>
              import("../views/householdInforManagment/viewHosehold"),
            meta: {
              authority:
                "admin:householdInforManagment:management:viewHosehold",
              hidden: true,
              level: 2
            }
          }
        ]
      }
    ]
  }
];

export default routes;

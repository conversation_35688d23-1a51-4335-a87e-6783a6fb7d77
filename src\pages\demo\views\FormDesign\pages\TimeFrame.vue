<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 日期选择控件DOM/Form
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <time-range-dom :data="formData"></time-range-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <time-range-form v-bind:data.sync="formData"></time-range-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 日期选择控件 DOM/Form
import {
  TimeRangeDom,
  TimeRangeForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/TimeFrame";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    TimeRangeDom,
    TimeRangeForm
  },
  data() {
    return {
      formData: {
        inputTitle: "租约期间", //标题1
        placeholderText: undefined, //提示文字
        notNull: true, //是否必填
        optionsData: {
          startTimeLimit: "", //开始时间限制 0--小于等于当前日期，1--当前日期，2--大于等于当前日期
          endTimeLimit: "", //结束时间限制 0--大于开始时间，1--大于等于开始时间
          startTime: "开始时间", //开始时间标题2
          endTime: "结束时间", //结束时间标题3,
          timeLength: "时长(天)", //时长标题4,
          autoComputerTime: true,
          timeType: "yyyy-MM-dd" //时间选择单选按钮
        }
      },
      moduleVoList: [],
      switchData: []
    };
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: this.formData.placeholderText, //提示文字
        notNull: this.formData.notNull ? 1 : 0, //是否必须
        inputType: "timeFrame", //组件名称
        inputName: "1",
        optionsData: JSON.stringify({
          startTime: this.formData.optionsData.startTime, //开始时间标题2
          endTime: this.formData.optionsData.endTime, //结束时间标题3,
          timeLength: this.formData.optionsData.timeLength, //时长标题4,
          autoComputerTime: this.formData.optionsData.autoComputerTime ? 1 : 0,
          timeType: this.formData.optionsData.timeType //时间选择单选按钮
        }) //是否必须
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      let dataOption = [];
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        this.switchData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "timeFrame") {
              dataOption.push(item);
            }
          });
        });
        let optionsData = JSON.parse(
          dataOption && dataOption[dataOption.length - 1].optionsData
        );
        console.log(optionsData);
        this.formData = {
          inputTitle:
            dataOption && dataOption[dataOption.length - 1].inputTitle, //标题1
          placeholderText:
            dataOption && dataOption[dataOption.length - 1].placeholder, //提示文字
          notNull: dataOption && dataOption[dataOption.length - 1].notNull, //是否必填
          optionsData: {
            startTime: optionsData.startTime, //开始时间标题2
            endTime: optionsData.endTime, //结束时间标题3,
            timeLength: optionsData.timeLength, //时长标题4,
            autoComputerTime: optionsData.autoComputerTime, //满分
            timeType: optionsData.timeType //时间选择单选按钮
          }
        };
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 基本配置，表单设计，流程设计，高级配置的整合
-->
<template>
  <div id="components-layout-demo-basic">
    <a-layout>
      <a-layout-header>
        <div class="card-container">
          <a-icon
            class="left-icon"
            type="left-circle"
            @click="
              () => {
                $router.back();
              }
            "
          />
          <a-tabs
            size="default"
            @change="handerComputer"
            :tabBarStyle="{ margin: 0 }"
          >
            <a-tab-pane :key="1" tab="基础设置"></a-tab-pane>
            <a-tab-pane :key="2" tab="表单设计"></a-tab-pane>
            <a-tab-pane :key="3" tab="流程设计"></a-tab-pane>
          </a-tabs>
          <div class="right-icon">
            <!-- <a-button class="text" type="text" @click="handleSubmit"
              >预览</a-button
            > -->
            <a-button
              v-if="type === 'add' || type === 'copy'"
              :loading="subLoading"
              class="text"
              type="primary"
              @click="handleSubmit"
              >发布</a-button
            >
            <a-button
              v-if="type === 'edit'"
              :loading="subLoading"
              class="text"
              type="primary"
              @click="handleEdit"
              >修改</a-button
            >
          </div>
        </div>
      </a-layout-header>
      <a-layout-content>
        <basic-settings
          ref="basicSettings"
          v-show="showFormComputer === 1"
        ></basic-settings>
        <form-design
          v-show="showFormComputer === 2"
          ref="formDesign"
        ></form-design>
        <process-design
          ref="process"
          @changeFormDesin="setFormDesign"
          v-show="showFormComputer === 3"
        ></process-design>
        <!-- <suite-library v-show="showFormComputer == 4"></suite-library> -->
      </a-layout-content>
    </a-layout>
  </div>
</template>
<script>
// 基础配置插件
import BasicSettings from "./BasicSettings";
import FormDesign from "./FormDesign";
import ProcessDesign from "./ProcessDesign";
import {
  ApiActivitiDeploy,
  ApiActivitiModifyDeploy
} from "@/data/api/components/ApprovalProcess";
export default {
  components: {
    BasicSettings,
    FormDesign,
    ProcessDesign
    // SuiteLibrary
  },
  data() {
    return {
      showFormComputer: 1,
      // 发布按钮loading
      subLoading: false,
      // 类型 add:新增 edit:修改 copy:复制
      type: this.$route.params.type,
      // 流程ID
      id: this.$route.params.id
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    // 获取页面数据
    getData() {
      //获取人员列表
      this.$store.dispatch("approvalProcess/getUserData").then(() => {
        //获取角色列表
        this.$store.dispatch("approvalProcess/getRoleData").then(() => {});
        //获取组织结构列表
        this.$store.dispatch("approvalProcess/getOrganData").then(() => {});
      });
    },
    // 切换tabs显示contain组件
    handerComputer(value) {
      this.showFormComputer = value;
    },
    // 发布
    handleSubmit() {
      this.subLoading = true;
      // 设置基础设置数据
      this.setBasicSettings();
      // 设置拖拽表单数据
      this.setFormDesign();
      // 设置发布流程数据
      this.setProcessDesign();
      ApiActivitiDeploy(this.$store.getters["approvalProcess/getAllData"])
        .then(() => {
          this.$message.info("发布成功");
          this.$router.back();
        })
        .catch()
        .finally(() => {
          this.subLoading = false;
        });
    },
    // 修改
    handleEdit() {
      this.subLoading = true;
      // 设置基础设置数据
      this.setBasicSettings();
      // 设置拖拽表单数据
      this.setFormDesign();
      // 设置发布流程数据
      this.setProcessDesign();
      ApiActivitiModifyDeploy({
        id: this.id,
        ...this.$store.getters["approvalProcess/getAllData"]
      })
        .then(() => {
          this.$message.info("修改成功");
          this.$router.back();
        })
        .catch()
        .finally(() => {
          this.subLoading = false;
        });
    },
    // 设置基础设置数据
    setBasicSettings() {
      let data = this.$refs.basicSettings.getData();
      this.$store.commit("approvalProcess/setBasicSettings", data);
    },
    // 设置拖拽表单数据
    setFormDesign() {
      let data = this.$refs.formDesign.getData();
      this.$store.commit("approvalProcess/setFormDesign", data);
    },
    // 设置发布流程数据
    setProcessDesign() {
      console.log(this.$refs.process, "this.$refs.process");
      let res = this.$refs.process.submit();
      let resArr = [];
      console.log(res, "ressssssssssssss");
      let endNode = this.setEndNode(res);
      resArr = [...res, endNode];

      this.$store.commit("approvalProcess/setProcessDesign", resArr);
      // this.subLoading = true;
      console.log(ApiActivitiDeploy, resArr);
    },
    /**
     * setEndNode
     */
    setEndNode(list) {
      let arrPids = list.map(i => i.pids);
      let arr = [];
      arrPids.forEach(i => {
        arr = arr.concat(i).filter(Boolean);
      });
      arr = Array.from(new Set(arr));
      let preNodeId = list.filter(i => arr.indexOf(i.id) === -1).map(i => i.id);
      let endNode = {
        type: "5",
        id: "theEnd",
        pids: preNodeId
      };
      return endNode;
    }
  }
};
</script>
<style scoped lang="less">
.left-icon {
  position: absolute;
  left: 36px;
  top: 12px;
  font-size: 20px;
}
.right-icon {
  text-align: right;
  position: absolute;
  right: 36px;
  top: 6px;
  line-height: 0;
  .text {
    margin-right: 10px;
  }
}
#components-layout-demo-basic {
  /deep/ .ant-layout {
    padding-bottom: 10px;
  }
  /deep/ .ant-layout-header {
    height: 45px;
    margin-bottom: 10px;
    text-align: center;
  }
}
// 头部背景颜色
#components-layout-demo-basic .ant-layout-header {
  background: @white;
}
//将tabs下面的那条线变短一点
/deep/ .card-container .ant-tabs-ink-bar {
  margin-left: 29px;
  width: 30px !important;
  bottom: 0;
}
// 删除tabs下面的那条线border
/deep/ .card-container .ant-tabs-nav-container {
  margin-bottom: 0;
}
/deep/ .card-container .ant-tabs-bar {
  border-bottom: 0;
}
/deep/ .card-container .ant-tabs-nav-wrap {
  margin-bottom: 0;
}
</style>

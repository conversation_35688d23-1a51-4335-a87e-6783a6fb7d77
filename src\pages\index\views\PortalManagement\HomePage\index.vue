<template>
  <div class="tabs-router">
    <div class="tabs-head">
      <div>
        <a-tabs
          :tabBarStyle="{ margin: 0 }"
          @change="navigate"
          :activeKey="activeKey"
        >
          <a-tab-pane tab="轮播图片管理" key="Rotation"></a-tab-pane>
          <a-tab-pane tab="快捷入口管理" key="QuickEntry"></a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div class="tabs-content">
      <rotation v-show="activeKey === 'Rotation'"></rotation>
      <quick-entry v-show="activeKey === 'QuickEntry'"></quick-entry>
    </div>
  </div>
</template>

<script>
//轮播
import Rotation from "./views/Rotation";
//快捷入口
import QuickEntry from "./views/QuickEntry";
export default {
  name: "TabsComponent",
  components: { Rotation, QuickEntry },
  data() {
    return {
      activeKey: "Rotation"
    };
  },
  methods: {
    navigate(key) {
      this.activeKey = key;
    }
  }
};
</script>

<template>
  <common-layout>
    <div
      style="display: flex; align-items: center; justify-content: space-between"
    >
      <div
        style="
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
        "
      >
        <div class="bg">
          <div>
            <div style="margin: 140px; margin-left: -100px">
              <img
                width="128px"
                src="~@/assets/image/common/big_logo_white.png"
              />
            </div>
            <div
              style="
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: center;
              "
            >
              <div>智慧营商</div>
              <div style="margin-left: 32px">数字营商</div>
            </div>
            <div>“全归集、全打通、全共享”</div>
          </div>
        </div>
      </div>
      <div
        style="
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
        "
      >
        <div
          style="
            background-color: #fff;
            padding: 50px 40px;
            border-radius: 20px;
          "
        >
          <div class="top">
            <!-- <div class="header">
              <img width="44" src="~@/assets/image/common/logo.png" />
              <span class="title">{{ systemName }}</span>
            </div>
            <div class="desc">华泾镇智慧营商平台</div> -->
            <div class="new-label">
              <div>
                <img width="44" src="~@/assets/image/common/big_logo.png" />
              </div>
              <div>华泾镇智慧营商平台</div>
            </div>
          </div>
          <div class="login">
            <a-form @submit="onSubmit" :form="form">
              <!-- <a-tabs
          size="large"
          :tabBarStyle="{ textAlign: 'center' }"
          style="padding: 0 2px;"
        >
          <a-tab-pane tab="账户密码登录" key="1"> -->
              <a-alert
                type="error"
                closable
                v-if="visible"
                :message="error"
                :after-close="handleClose"
                showIcon
                style="margin-bottom: 24px"
              />
              <a-form-item>
                <a-input
                  autocomplete="autocomplete"
                  size="large"
                  placeholder="请输入用户名"
                  v-decorator="[
                    'username',
                    {
                      rules: [
                        {
                          required: true,
                          message: '请输入用户名',
                          whitespace: true,
                        },
                      ],
                    },
                  ]"
                >
                  <a-icon slot="prefix" type="user" />
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-input
                  size="large"
                  placeholder="请输入密码"
                  autocomplete="autocomplete"
                  type="password"
                  v-decorator="[
                    'password',
                    {
                      rules: [
                        {
                          required: true,
                          message: '请输入密码',
                          whitespace: true,
                        },
                      ],
                    },
                  ]"
                >
                  <a-icon slot="prefix" type="lock" />
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-input
                      size="large"
                      placeholder="请输入验证码"
                      v-decorator="[
                        'captcha',
                        {
                          rules: [
                            {
                              required: true,
                              message: '请输入验证码',
                              whitespace: true,
                            },
                          ],
                        },
                      ]"
                    >
                      <a-icon slot="prefix" type="bulb" />
                    </a-input>
                  </a-col>
                  <a-col :span="12">
                    <img
                      alt="暂无图片或未正确显示"
                      title="点击切换"
                      :src="'data:image/jpg;base64,' + this.captchaImg"
                      id="captcha_img"
                      width="180px"
                      height="38px"
                      style="margin-bottom: 3px"
                      @click="changeImg()"
                    />
                  </a-col>
                </a-row>
              </a-form-item>
              <!-- </a-tab-pane> -->
              <!-- <a-tab-pane tab="手机号登录" key="2">
            <a-form-item>
              <a-input size="large" placeholder="mobile number">
                <a-icon slot="prefix" type="mobile" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-row :gutter="8" style="margin: 0 -4px">
                <a-col :span="16">
                  <a-input size="large" placeholder="captcha">
                    <a-icon slot="prefix" type="mail" />
                  </a-input>
                </a-col>
                <a-col :span="8" style="padding-left: 4px">
                  <a-button
                    style="width: 100%"
                    class="captcha-button"
                    size="large"
                    >获取验证码</a-button
                  >
                </a-col>
              </a-row>
            </a-form-item>
          </a-tab-pane> -->
              <!-- </a-tabs> -->
              <!-- <div>
          <a-checkbox :checked="true">自动登录</a-checkbox>
          <a style="float: right">忘记密码</a>
        </div> -->
              <a-form-item>
                <a-button
                  :loading="loading"
                  style="width: 100%; margin-top: 24px"
                  size="large"
                  htmlType="submit"
                  type="primary"
                  >登录</a-button
                >
              </a-form-item>
              <!-- <div>
          其他登录方式
          <a-icon class="icon" type="alipay-circle" />
          <a-icon class="icon" type="taobao-circle" />
          <a-icon class="icon" type="weibo-circle" />
          <router-link style="float: right" to="/"
            >注册账户</router-link
          >
        </div> -->
            </a-form>
          </div>
        </div>
      </div>
    </div>
  </common-layout>
</template>

<script>
import CommonLayout from "@/components/Layouts/CommonLayout";
// import { login, getRoutesConfig } from "@/services/user";
/**
 * @param ApiMapiLogin 登录接口
 * @param ApiMapiPermission 权限接口
 * @param ApiMapiCommonMenu 获取路由接口
 * @param ApiMapiCaptcha 获取验证码接口
 */
import {
  ApiMapiLogin,
  ApiMapiPermission,
  ApiMapiCommonMenu,
  ApiMapiCaptcha,
} from "@/data/api/common/auth";
import { setAuthorization, removeAuthorization } from "@/common/api";
// import { loadRoutes } from "@/common/router/routerUtil";
import { loadRoutes } from "@/common/router/routerUtil";
import { mapMutations } from "vuex";
export default {
  name: "Login",
  components: { CommonLayout },
  data() {
    return {
      loading: false,
      error: "",
      visible: false,
      form: this.$form.createForm(this),
      captchaImg: "",
      captchakey: "",
    };
  },
  created() {},
  mounted() {
    this.getMapiCaptcha();
    /* particlesJS.load(@dom-id, @path-json, @callback (optional)); */
  },
  computed: {
    systemName() {
      return this.$store.state.setting.systemName;
    },
  },
  methods: {
    changeImg() {
      this.getMapiCaptcha();
    },
    getMapiCaptcha() {
      ApiMapiCaptcha()
        .then((res) => {
          this.captchaImg = res.data.img;
          this.captchakey = res.data.key;
          // console.log("图片", this.captchaImg);
          // console.log("图片", this.captchakey);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    ...mapMutations("account", [
      "setUser",
      "setPermissions",
      "setRoles",
      "clearRouterMapInvisible",
    ]),
    onSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err) => {
        if (!err) {
          this.loading = true;
          const username = this.form.getFieldValue("username");
          const password = this.form.getFieldValue("password");
          const captcha = this.form.getFieldValue("captcha");
          removeAuthorization();
          ApiMapiLogin({
            username,
            password,
            captcha,
            captchakey: this.captchakey,
          })
            .then(this.afterLogin)
            .catch((error) => {
              if (error) {
                this.error = error.data.msg;
                this.visible = true;
                console.log(error, "sdfsdfsffsfdsfsdf");
                this.getMapiCaptcha();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    handleClose() {
      this.visible = false;
    },
    afterLogin(res) {
      console.log("res", res);
      if (res.code == 0) {
        this.setUser(res.data);
        setAuthorization({
          token: res.data.utoken,
          expireAt: new Date(
            res.data.expireAt || new Date().getTime() * 1000 * 60 * 60 * 2
          ),
        });
        // 获取全局字典
        this.$store.dispatch("dictionaries/getDictionaries");
        ApiMapiPermission()
          .then((res) => {
            console.log(res);
            this.setPermissions(res.data);
            ApiMapiCommonMenu()
              .then((res) => {
                this.clearRouterMapInvisible();
                loadRoutes(
                  {
                    router: this.$router,
                    store: this.$store,
                    i18n: this.$i18n,
                  },
                  res.data
                );
                this.$message.success("登录成功", 3);
                this.$router.push("/");
              })
              .finally(() => {
                this.loading = false;
              });
          })
          .catch(() => {
            this.loading = false;
          })
          .finally(() => {});
        // this.setRoles(roles);
        // 获取路由配置
        // getRoutesConfig().then(result => {
        //   const routesConfig = result.data.data;
        //   loadRoutes(
        //     { router: this.$router, store: this.$store, i18n: this.$i18n },
        //     routesConfig
        //   );
        // });
      } else if (res.code == 1003) {
        this.$router.push({
          path: `editPwd`,
        });
      } else {
        this.error = res.msg;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@baseFontSize: 1440; // 设计稿宽度1440px 以此计算vw自适应
.px2vw(@name, @px) {
  @{name}: 100 * @px / @baseFontSize * 1vw;
}
.common-layout {
  background-image: url(~@/assets/image/common/bg1.png);
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
  .top {
    text-align: center;
    .header {
      height: 44px;
      line-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        display: block;
        margin-right: 10px;
      }
      a {
        text-decoration: none;
      }
      .logo {
        height: 44px;
        vertical-align: top;
        margin-right: 16px;
      }
      .title {
        font-size: 33px;
        color: @title-color;
        font-family: "Myriad Pro", "Helvetica Neue", Arial, Helvetica,
          sans-serif;
        font-weight: 600;
        position: relative;
        top: 2px;
      }
    }
    .desc {
      font-size: 14px;
      color: @text-color-second;
      margin-top: 12px;
      margin-bottom: 40px;
    }
  }
  .login {
    width: 368px;
    // width: .px2vw(min-width, 268);
    margin: 0 auto;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
.bg {
  min-width: 500px;
  min-height: 165px;
  width: 50%;
  height: 165px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  font-size: 32px;
  color: #fff;
  font-weight: 600;
  padding-left: 85px;
  background-image: url("~@/assets/image/common/bg5.png");
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
}
.new-label {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  color: #1777ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
}
</style>

<template>
  <div class="agree-item">
    <div class="agree-item-left">
      <AgreeItemViewLeft
        :numType="numType"
        :annex="annex"
        :index="index"
        :title="title"
        @click="handleClick"
      >
      <!-- this.$route.query.type == '1' && -->
        <template v-if="showIsProfessional">
          <div class="show-isProfessional-radio">
            <span style="margin-right: 8px;color: rgba(0, 0, 0, 0.85);" >该定价依据是否为专业机构制定 :</span> 
            <a-radio-group
              v-model="isProfessional"
              :disabled="!isType"
            >
              <a-radio :value="0">
                是
              </a-radio>
              <a-radio :value="1">
                否
              </a-radio>
            </a-radio-group>
          </div>
          <div  class="isProfessionalInfo" v-if="isProfessionalStatus">
            <!-- 发起 -->
            <div v-if="isType">
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="经发公司对定价依据的复核(盖章版)"
                    shortStyle="true"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    @deleteFile="(file, fileList) => deleteFile(file, fileList)"
                    :fileList="fileList1"
                  >
                  </FileAttachmentList>
                </div>
                <div>
                  <my-upload
                    businessId="pricing_basis_review_by_jingfa"
                    @handleFileCallback="handleFileCallback"
                  />
                </div>
              </div>
            </div>
            <div v-else>
              <div class="upload_file_item">
                <div class="Item">
                  <FileAttachmentList
                    title="经发公司对定价依据的复核(盖章版)"
                    shortStyle="true"
                    marked="true"
                    :ifNeedPreviewOnline="true"
                    mode="preview"
                    :fileList="fileList1"
                  >
                  </FileAttachmentList>
                </div>
              </div>
            </div>
          </div>
        </template>
      </AgreeItemViewLeft>
    </div>
    <div class="agree-item-right" v-if="index">
      <!-- 创建初审表显示 -->
      <div class="agree-item-right-input-group" v-if="lookType === 'create'">
        <a-radio-group
          v-model="modelValue.agreement"
          @change="onChangeRadio"
          style="padding-top: 5px;width: 272px;"
        >
          <a-radio :value="1">
            拟同意
          </a-radio>
          <a-radio :value="2">
            不同意
          </a-radio>
        </a-radio-group>
        <AgreeItemInput
          v-model="modelValue.advice"
          @input="onChangeAdvise"
          :historyList="
            modelValue && modelValue.agreement == 2 ? rejectList : approvalList
          "
          :choice="
            modelValue && modelValue.agreement ? modelValue.agreement : 0
          "
        ></AgreeItemInput>
      </div>
      <!-- 初审和复审时显示 -->
      <div class="agree-item-right-input-group" v-else>
        <div class="advice">
          <span class="adviceTitle"
            >{{
              agreement === 1 ? "拟同意:" : agreement === 2 ? "不同意:" : ""
            }}&nbsp; &nbsp;</span
          >
          <span class="adviceContent">{{ advice }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AgreeItemViewLeft from "./AgreeItemViewLeft.vue";
import AgreeItemInput from "./AgreeItemInput";
import FileAttachmentList from "@/components/FileAttachmentList";
import myUpload from "@/components/Importer";

export default {
  components: {
    myUpload,
    FileAttachmentList,
    AgreeItemViewLeft,
    AgreeItemInput
  },
  props: {
    /**
     * agreement 1 同意 2 不同意
     * advice
     */
    value: {
      type: Object,
      default: () => ({
        advice: "",
        agreement: 1
      })
    },
    isProfessionalProp: {
      // type: String,
      default: null
    },
    // 颜色类型
    numType: {
      type: Number,
      default: 1
    },
    // 附件
    annex: {
      type: Object,
      default: () => {
        return {
          fileName: "",
          filePath: ""
        };
      }
    },
    //序号
    index: {
      type: Number,
      default: 0
    },
    /**
     * @param titleName 标题名称
     * @param value  标题值
     */
    title: {
      type: Object,
      default: () => ({ titleName: "titleName", value: "title" })
    },
    //父组件传过来区分初审复审还是创建的，create表示创建初审，view表示初审和复审
    lookType: {
      type: String,
      default: "create"
    },
    //每个条目的初审意见
    advice: {
      type: String
    },
    //每个条目的状态同意还是不同意
    agreement: {
      type: Number
    },
    approvalList: {
      type: Array,
      default: () => []
    },
    rejectList: { type: Array, default: () => [] }
  },
  emits: ["input"],
  methods: {
    handleClick() {
      this.click(this.index);
    },
    onChangeRadio(event) {
      console.log(event.target.value, "dhhdhh ");
      let modelValue = Object.assign({}, this.value, {
        agreement: event.target.value
      });
      console.log("modelValue", modelValue);
      this.$emit("input", modelValue);
    },
    onChangeAdvise(val) {
      console.log(val);
      let modelValue = Object.assign({}, this.value, { advice: val });
      this.$emit("input", modelValue);
    },
    getFileResult(){
      var isProfessionalValid = this.showIsProfessional&&(!this.isProfessionalStatus || this.isProfessionalStatus&&this.isProfessionalFile) ? true :false
      this.$emit('call',isProfessionalValid,this.fileList1)//(文件必传校验，文件 )
    },
    handleFileCallback: function(file) {
      this.isProfessionalFile = true;
      this.fileList1.push(file) 
      // console.log('handleFileCallback',file,this.fileList1);
    },
    deleteFile: function(file, fileList) {
      if (fileList.length == 0) {
        this.isProfessionalFile = false;
      }
      this.fileList1 = fileList;
      // console.log('deleteFile',file,fileList,this.fileList1);
    },
  },
  created() {
    if (this.title.titleName.includes('租赁单价')) {
      this.showIsProfessional = true;
      this.fileList1 = this.modelValue.attachmentsIsProfessional
      if (this.modelValue.attachmentsIsProfessional.length) {
        this.isProfessionalFile = true;
      }
      // console.log(this.modelValue.attachmentsIsProfessional);
    }
  },
  watch: {
    modelValue: {
      handler(newVal) {
        this.modelValue = newVal;
      }
    },
    isProfessional: {
      // deep: true,
      immediate: true,
      handler: function (val) {
        if (this.showIsProfessional) {
          this.$emit("changeIsProfessional", val);
          // console.log('是否为专业机构制定：0是1否 isProfessional',this.isProfessionalProp,this.isProfessional,this.title);
        }
      }
    },
  },
  computed: {
    isProfessionalStatus(){
      let status = this.isProfessional == '1' ? true : false;//
      return status;
    }
  },
  data() {
    return {
      showIsProfessional:false,
      isProfessional: this.isProfessionalProp || 0,
      fileList1: [],
      isProfessionalFile: false, 
      isType: this.$route.query.type == '1' ? true : false,// 发起
      modelValue: this.value,
      historyList: [] //提示列表
    };
  }
};
</script>

<style lang="less" scoped>
.agree-item {
  display: flex;
  > .agree-item-left {
    width: 50%;
  }
  .agree-item-right {
    display: flex;
    width: 50%;
    align-items: center;
    justify-content: flex-start;
    padding-left: 41px;
    // 输入框组
    > .agree-item-right-input-group {
      display: flex;
      width: 100%;
      /deep/ .ant-radio-wrapper {
        font-size: 16px;
      }
    }
  }
}
.show-isProfessional-radio{
  margin-bottom: 10px;
}
.isProfessionalInfo {
  width: 100%;
  margin-bottom: 20px;
  .upload_file_item{
    width: 100%;
    display: flex;
    align-items: center;
  }
  .Item {
    width: 100%;
    min-height: 70px;
    background: #f3f9ff;
    border-radius: 6px;
    font-size: 15px;
    margin-right: 15px;
  }
}
</style>

<!-- 原租户信息 -->
<template>
  <div class="lessorInfo">
    <a-form-model
      class="ant-advanced-search-form"
      ref="ruleForm"
      :model="rulesForm"
      :rules="rules"
      :label-col="formItemLayout.labelCol"
      :wrapper-col="formItemLayout.wrapperCol"
    >
      <div class="lessorInfo-tit">
        <div class="tit">原租户信息</div>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item prop="tenantry" label="承租方">
              <a-auto-complete
                v-model="rulesForm.tenantry"
                :title="rulesForm.tenantry"
                :disabled="showInfo"
                allowClear
                placeholder="请输入承租方"
                :data-source="dataSource"
                style="width: 100%; height: 40px"
                @select="onSelect"
                @search="onSearch"
                @change="onChangeTenantry"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item prop="contract" label="合同">
              <a-select
                style="width: 100%; height: 40px"
                :disabled="showInfo"
                @change="onChange"
                @focus="getContractId"
                v-model="rulesForm.contract"
                placeholder="请选择合同"
              >
                <a-select-option
                  v-for="item in contractArr"
                  :key="item.id"
                  :value="item.id"
                  >{{ item.contractIdentify }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item label="属地情况">
              <a-input
                v-model="parmasForm.territorialSituation"
                disabled
                style="width: 100%; height: 40px"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8" v-if="parmasForm.territorialSituation == '未属地'">
            <a-form-model-item>
              <a-tooltip>
                <template slot="title">
                  {{ parmasForm.territorialRemark }}
                </template>
                <a-input
                  v-model="parmasForm.territorialRemark"
                  disabled
                  style="width: 100%; height: 40px"
                />
              </a-tooltip>
            </a-form-model-item>
          </a-col>
          <a-col :span="8" v-if="parmasForm.territorialSituation == '关联属地'">
            <a-form-model-item>
              <a-input
                v-model="parmasForm.relationCompany"
                disabled
                style="width: 100%; height: 40px"
                maxLength="50"
              />
            </a-form-model-item>
          </a-col>
        </a-row>

        <div style="position: relative">
          <div
            class="titItem"
            v-for="item in parmasForm.contractBuildingInfo"
            :key="item.id"
            style="padding-left: 56px"
          >
            <p class="mR20" style="margin-right: 17px">出租地址</p>
            <a-input
              :title="item.parkName"
              v-model="item.parkName"
              disabled
              style="width: 22.5%; height: 40px"
            />
            <a-input
              :title="item.buildingName"
              v-model="item.buildingName"
              disabled
              style="
                width: 14.5%;
                height: 40px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
            />
            <a-input
              v-model="item.floorName"
              disabled
              style="width: 8.5%; height: 40px"
            />
            <a-input
              v-model="item.roomName"
              disabled
              style="width: 8.5%; height: 40px"
            />
          </div>
        </div>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item label="租赁用途">
              <a-input v-model="parmasForm.leasePurpose" disabled />
            </a-form-model-item>
          </a-col>
          <a-col
            :span="8"
            v-if="
              parmasForm.leasePurpose == '商业' ||
              parmasForm.leasePurpose == '其他'
            "
          >
            <a-form-model-item label=" ">
              <a-input v-model="parmasForm.leasePurposeInfo" disabled>
              </a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item label="租赁面积">
              <a-input
                v-model="parmasForm.leaseArea"
                disabled
                addon-after="㎡"
                style="width: 100%; height: 40px"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="累计租赁面积">
              <a-input
                v-model="parmasForm.accumulateLeaseArea"
                disabled
                addon-after="㎡"
                style="width: 100%; height: 40px"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="租赁单价">
              <a-input
                v-model="parmasForm.rentalUnitPrice"
                disabled
                addon-after="元/㎡/天"
                style="width: 100%; height: 40px"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item label="年租金">
              <a-input
                v-model="parmasForm.annualRent"
                disabled
                addon-after="元"
                style="width: 100%; height: 40px"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="租赁保证金">
              <a-input
                v-model="parmasForm.leaseDeposit"
                disabled
                addon-after="元"
                style="width: 100%; height: 40px; margin-right: 0"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="价格递增机制">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                disabled
                v-model="parmasForm.priceIncrease"
                style="width: 100%; height: 40px"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in priceIncreaseArr"
                  :key="item.value"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <template v-for="(item, index) in parmasForm.multTenancyInfoList">
          <a-row
            :key="item.id"
            :gutter="24"
            style="position: relative"
            v-if="parmasForm.priceIncrease == '1'"
          >
            <a-col :span="8">
              <a-form-model-item :label="`租期` + (index + 1)">
                <a-range-picker
                  format="YYYY-MM-DD"
                  :value="[item.startTime, item.endTime]"
                  disabled
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="租赁单价">
                <a-input
                  type="number"
                  v-model="item.rentalUnitPrice"
                  disabled
                  style="width: 100%; height: 40px; line-height: 40px"
                  addon-after="元/㎡/天"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="年租金">
                <a-input
                  type="number"
                  disabled
                  v-model="item.annualRent"
                  style="width: 100%; height: 40px; margin-right: 0"
                  addon-after="元"
                  maxLength="20"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item label="租期起">
              <a-input
                v-model="oldLeaseTermStart"
                disabled
                style="width: 100%; margin-right: 0"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="租期止">
              <a-input
                v-model="oldLeaseTermEnd"
                disabled
                style="width: 100%; margin-right: 0"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="总租期">
              <a-input
                v-model="oldLeaseTerm"
                disabled
                addon-after="年"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <div style="position: relative">
          <a-row
            :gutter="24"
            v-for="item in parmasForm.contractFreePeriod"
            :key="item.id"
          >
            <a-col :span="8">
              <a-form-model-item label="免租期起">
                <a-input
                  v-model="item.startTime"
                  disabled
                  style="width: 100%; margin-right: 0"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="免租期止">
                <a-input
                  v-model="item.endTime"
                  disabled
                  style="width: 100%; margin-right: 0"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="8" style="position: absolute; right: 0; top: -64px">
              <a-form-model-item label="总免租期">
                <a-input
                  v-model="parmasForm.rentFreeDays"
                  addon-after="天"
                  disabled
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <template v-if="isChange == 0">
          <div class="tit">出租信息</div>
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-model-item label="租期起" prop="leaseTermStart">
                <a-date-picker
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  v-model="rulesForm.leaseTermStart"
                  :disabled="!rulesForm.contract || statu == 1"
                  :disabled-date="disabledStartDate"
                  style="width: 100%"
                  @change="leaseTimeChange"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="租期止" prop="leaseTermEnd">
                <a-date-picker
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  v-model="rulesForm.leaseTermEnd"
                  :disabled="
                    !rulesForm.leaseTermStart ||
                    !rulesForm.contract ||
                    statu == 1
                  "
                  :disabled-date="disabledEndDate"
                  style="width: 100%"
                  @change="leaseTimeChange"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="总租期">
                <a-input
                  v-model="rulesForm.leaseTerm"
                  disabled
                  addon-after="年"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
      </div>
    </a-form-model>
  </div>
</template>

<script>
import debounce from "lodash/debounce";
import moment from "moment";
import {
  queryContractListApi,
  queryContractInfoByIdApi,
  queryTenantryApi,
} from "@/pages/index/data/api/RegistrationRecordInquery";
export default {
  props: {
    oldleaseTermData: {
      type: Object,
      required: false,
    },
    processFlow: {
      type: Number,
      default: 0,
    },
    isChange: {
      type: String,
      required: false,
    },
    relationContractId: {
      type: Object,
      required: false,
    },
    contractType: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
      },
      rules: {
        tenantry: [
          { required: true, message: "请输入承租方", trigger: "blur" },
        ],
        contract: [
          { required: true, message: "请选择合同", trigger: "change" },
        ],
        leaseTermStart: [
          { required: true, message: "请选择租期开始时间", trigger: "change" },
        ],
        leaseTermEnd: [
          { required: true, message: "请选择租期结束时间", trigger: "change" },
        ],
      },
      oldRentalInfoRules: false,
      statu: "",
      showInfo: false,
      dataSource: [],
      priceIncreaseArr: [
        { label: "是", value: "1" },
        { label: "未满3年无价格递增", value: "0" },
        { label: "租期超3年无价格递增", value: "2" },
      ],
      rulesForm: {
        tenantry: undefined,
        contract: undefined,
        leaseTermStart: null,
        leaseTermEnd: null,
        leaseTerm: "",
      },
      oldLeaseTermStart: null,
      oldLeaseTermEnd: null,
      oldLeaseTerm: "",
      parmasForm: {
        leaseArea: "",
        annualRent: "", //年租金
        leaseDeposit: "", //租赁保证金
        leasePurpose: undefined, //租赁用途
        leasePurposeInfo: "", //租赁用途
        territorialSituation: "", //属地情况
        rentalUnitPrice: "",
        rentFreeDays: "",
        leaseTermStart: null,
        leaseTermEnd: null,
        leaseTerm: "",
        priceIncrease: "",
        priceIncreaseMechanism: "",
        accumulateLeaseArea: "",
        multTenancyInfoList: [
          {
            delId: 0,
            startTime: "",
            endTime: "",
            rentalUnitPrice: "",
            annualRent: "",
          },
        ],
        contractFreePeriod: [
          {
            delId: 0,
            startTime: "",
            endTime: "",
          },
        ], //租赁合同免租期数据
        contractBuildingInfo: [
          {
            delId: 0,
            parkName: undefined,
            contractInfoId: undefined,
            buildingName: undefined,
            floorName: undefined,
            roomName: undefined,
          },
        ],
      },
      contractArr: [],
      plainOptions: ["是", "否"],
      oldContractType: "",
    };
  },
  created() {
    this.statu = this.$route.query.statu;
    if (this.statu == 1) {
      this.showInfo = true;
    }
  },
  mounted() {
    this.$nextTick(() => {
      console.log(this.oldleaseTermData, "oldleaseTermData");
      if (this.oldleaseTermData) {
        this.rulesForm.leaseTermStart = this.oldleaseTermData.leaseTermStart;
        this.rulesForm.leaseTermEnd = this.oldleaseTermData.leaseTermEnd;
        this.rulesForm.leaseTerm = this.oldleaseTermData.leaseTerm;
      }
    });
  },
  watch: {
    relationContractId: {
      handler() {
        this.$nextTick(() => {
          if (this.statu != 3 && this.relationContractId) {
            let parmas = { id: this.relationContractId };
            queryContractInfoByIdApi(parmas).then((res) => {
              this.parmasForm = JSON.parse(JSON.stringify(res.data));
              console.log(res.data, "ooooiii");
              this.oldLeaseTermStart = res.data.leaseTermStart;
              this.oldLeaseTermEnd = res.data.leaseTermEnd;
              this.oldLeaseTerm = res.data.leaseTerm;

              this.$emit("oldRental", this.parmasForm);
              this.rulesForm.tenantry = res.data.tenantry;
              this.rulesForm.contract = res.data.contractIdentify;
              // this.rulesForm.leaseTermStart = res.data.leaseTermStart;
              // this.rulesForm.leaseTermEnd = res.data.leaseTermEnd;
              // this.rulesForm.leaseTerm = res.data.leaseTerm;
              this.getStatu();
            });
          }
        });
      },
      immediate: true,
      deep: true,
    },
    contractType: {
      handler() {
        this.$nextTick(() => {
          this.oldContractType = this.contractType;
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    disabledStartDate(current) {
      if (this.processFlow == 0) {
        return current && current < moment().subtract(1, "days");
      }
    },
    disabledEndDate(current) {
      return (
        current &&
        current < moment(this.rulesForm.leaseTermStart).subtract(1, "days")
      );
    },
    leaseTimeChange() {
      console.log(
        this.parmasForm.leaseTermStart,
        this.parmasForm.leaseTermEnd,
        "租期开始和结束时间"
      );

      // this.$set(this.rentalParmas, "leaseTermEnd", "");
      if (this.rulesForm.leaseTermStart && this.rulesForm.leaseTermEnd) {
        let num = moment(this.rulesForm.leaseTermEnd).diff(
          moment(this.rulesForm.leaseTermStart),
          "days",
          false
        );
        this.rulesForm.leaseTerm = (Number(num) / 365).toFixed(2);
      } else {
        this.rulesForm.leaseTerm = "";
      }
    },

    onSearch: debounce(function (searchText) {
      let parmas = {
        contractType: 1,
        tenantry: searchText,
      };
      queryTenantryApi(parmas).then((res) => {
        this.dataSource = res.data;
        setTimeout(() => {
          if (!res.data.length) {
            this.$message.error("没有相关数据,请重新输入");
            this.rulesForm.tenantry = "";
          }
        }, 500);
      });
    }, 500),
    onSelect(value) {
      console.log("onSelect", value);
    },
    getStatu() {
      this.statu = this.$route.query.statu;
      if (this.statu == 1) {
        this.showInfo = true;
      } else if (this.statu == 2) {
        this.showInfo = false;
        this.getContractId();
      } else {
        this.showInfo = false;
      }
    },
    getContractId() {
      let parmas = {
        lessor: sessionStorage.getItem("lessorId"),
        tenantry: this.rulesForm.tenantry,
        contractType: this.oldContractType,
      };
      queryContractListApi(parmas).then((res) => {
        this.contractArr = res.data;
        if (res.data?.length != 0) {
          if (this.statu == 1) {
            this.rulesForm.contract = res.data?.[0].contractIdentify;
            this.onChange(res.data[0].id);
          }
        }
      });
    },
    isDeditChange() {
      this.parmasForm.dedit = "";
    },
    onChange(value) {
      if (value) {
        let parmas = {
          id: value,
        };
        queryContractInfoByIdApi(parmas).then((res) => {
          this.parmasForm = JSON.parse(JSON.stringify(res.data));
          console.log(this.parmasForm, "原合同内容");
          this.oldLeaseTermStart = res.data.leaseTermStart;
          this.oldLeaseTermEnd = res.data.leaseTermEnd;
          this.oldLeaseTerm = res.data.leaseTerm;
          this.$emit("oldRental", this.parmasForm);
        });
      }
    },
    getRentalParmas() {
      this.$emit("rentalInfo", this.rentalParmas);
    },
    onChangeTenantry() {
      Object.keys(this.parmasForm).forEach((k) => (this.parmasForm[k] = ""));
      this.rulesForm.contract = undefined;
      this.parmasForm.contractFreePeriod = [
        {
          delId: 0,
          startTime: null,
          endTime: null,
        },
      ];
      this.parmasForm.contractBuildingInfo = [
        {
          delId: 0,
          parkName: undefined,
          contractInfoId: undefined,
          buildingName: undefined,
          floorName: undefined,
          roomName: undefined,
        },
      ];
      this.oldLeaseTermStart = null;
      this.oldLeaseTermEnd = null;
      this.oldLeaseTerm = "";
      this.parmasForm.isDedit = 1;
      this.$emit("oldRental", this.parmasForm);
    },
    onClear() {
      this.$refs.ruleForm.resetFields();
      Object.keys(this.parmasForm).forEach((k) => (this.parmasForm[k] = ""));
      this.rulesForm.tenantry = undefined;
      this.rulesForm.contract = undefined;
      this.parmasForm.isDedit = 1;
      this.parmasForm.contractFreePeriod = [
        {
          delId: 0,
          startTime: null,
          endTime: null,
        },
      ];
      this.parmasForm.contractBuildingInfo = [
        {
          delId: 0,
          parkName: undefined,
          contractInfoId: undefined,
          buildingName: undefined,
          floorName: undefined,
          roomName: undefined,
        },
      ];
    },
    onSubmit() {
      this.$emit("oldRental", this.parmasForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.oldRentalInfoRules = true;
          this.$emit("oldRentalInfoRules", this.oldRentalInfoRules);
        } else {
          this.oldRentalInfoRules = false;
          this.$emit("oldRentalInfoRules", this.oldRentalInfoRules);
          return false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "../common.less";
.ant-advanced-search-form .ant-form-item {
  display: flex;
}
.ant-advanced-search-form /deep/.ant-form-item-label > label {
  font-size: 16px;
}

.ant-advanced-search-form /deep/.ant-input {
  height: 40px !important;
  border-radius: 4px;
  box-sizing: border-box;
}
.ant-advanced-search-form /deep/ .ant-select-selection {
  height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-select-selection__rendered {
  height: 40px !important;
}
.ant-advanced-search-form /deep/ .ant-form-item-label > label::after {
  content: "";
}
.ant-advanced-search-form /deep/ .ant-form-item-label {
  margin-right: 8px;
  width: 166px !important;
}
</style>

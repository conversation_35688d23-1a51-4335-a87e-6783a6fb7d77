<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 时间范围表单
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题1">
      <a-input v-model="form.inputTitle" placeholder="时间范围" />
    </a-form-model-item>
    <a-form-model-item label="标题2">
      <a-input v-model="form.optionsData.startTime" placeholder="开始时间" />
    </a-form-model-item>
    <a-form-model-item label="标题3">
      <a-input v-model="form.optionsData.endTime" placeholder="结束时间" />
    </a-form-model-item>
    <!-- <a-form-model-item label="提示文字">
      <a-input v-model="form.placeholderText" placeholder="请输入" />
    </a-form-model-item> -->
    <a-form-model-item label="时间类型">
      <a-radio-group
        v-model="form.optionsData.timeType"
        @change="handTimeType"
        defaultValue="0"
      >
        <a-radio value="0">
          年.月.日
        </a-radio>
        <a-radio value="1">
          年.月.日 时:分:秒
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <!-- <a-form-model-item label="开始时间约束">
      <a-radio-group
        v-model="form.optionsData.startTimeLimit"
        @change="handStartTimeLimit"
        defaultValue="1"
      >
        <a-radio value="0">
          小于等于当前日期
        </a-radio>
        <a-radio value="1">
          当前日期
        </a-radio>
        <a-radio value="2">
          大于等于当前日期
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item label="结束时间约束">
      <a-radio-group
        v-model="form.optionsData.endTimeLimit"
        @change="handEndTimeLimit"
        defaultValue="0"
      >
        <a-radio value="0">
          大于开始时间
        </a-radio>
        <a-radio value="1">
          大于等于开始时间
        </a-radio>
      </a-radio-group>
    </a-form-model-item> -->
    <a-form-model-item label="自动计算时长">
      <a-switch
        v-model="form.optionsData.autoComputerTime"
        @change="onChange"
      />
    </a-form-model-item>
    <a-form-model-item label="标题4" v-if="form.optionsData.autoComputerTime">
      <a-input v-model="form.optionsData.timeLength" placeholder="请输入" />
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        optionsData: {}
      }
    };
  },

  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  },
  methods: {
    // 时间格式选择
    handTimeType(e) {
      this.form.optionsData.timeType = e.target.value;
    },
    // // 开始时间范围选择
    // handStartTimeLimit(e) {
    //   this.form.optionsData.startTimeLimit = e.target.value;
    // },
    // // 结束时间范围选择
    // handEndTimeLimit(e) {
    //   this.form.optionsData.endTimeLimit = e.target.value;
    // }
    onChange(checked) {
      console.log(`a-switch to ${checked}`);
    }
  }
};
</script>

<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 单行文本
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="8">
        <a-row :gutter="[50, 50]">
          <a-col>
            <singlelinetext-dom :data="formData"></singlelinetext-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="16">
        <singlelinetext-form v-bind:data.sync="formData"></singlelinetext-form
      ></a-col>
    </a-row>
    <a-button @click="handSave">保存</a-button>
    <a-button @click="handCheck">查询</a-button>
  </a-card>
</template>
<script>
// 单行文本控件 DOM/Form
import {
  SinglelinetextDom,
  SinglelinetextForm
} from "@/components/ApprovalProcess/FormDesign/components/ControlLibrary/Singlelinetext";
import {
  ApiFormSaveFormTable,
  ApiFormQueryFormTable
} from "@/pages/demo/data/api/SystemManagement/Form";
export default {
  components: {
    SinglelinetextDom,
    SinglelinetextForm
  },
  data() {
    return {
      formData: {
        placeholder: {
          placeholderText: ""
        },
        isNeed: true, //是否必填
        inputTitle: "" //标题
      },
      moduleVoList: []
    };
  },
  mounted() {
    this.moduleVoList = [];
  },
  methods: {
    handSave() {
      let formData = {
        inputId: Math.random(), //id
        inputTitle: this.formData.inputTitle, //标题
        placeholder: JSON.stringify(this.formData.placeholder), //提示文字
        notNull: this.formData.isNeed ? 1 : 0, //是否必填
        inputType: "singlelinetext",
        inputName: "1"
      };
      this.moduleVoList.push(formData);
      let data = {
        action: "gfdffd", //保留字段随便传
        formId: "fgfgfggf", //保留字段随便传
        formTitle: "shenqibiaodan", //先填表单信息，录入的，现在随便填
        id: "", //控件编辑时候使用
        method: "qerer", //保留字段随便传
        moduleVoList: this.moduleVoList,
        orderBy: 1, //控件的排序
        templateId: "1" //按模版
      };
      ApiFormSaveFormTable(data)
        .then(() => {
          this.$message.info("保存成功");
        })
        .catch(() => {
          this.moduleVoList = [];
        });
    },
    handCheck() {
      ApiFormQueryFormTable({ templateId: "1" }).then(res => {
        let allData = [];
        res.data.map(items => {
          items.moduleVoList.map(item => {
            if (item.inputType == "singlelinetext") {
              let itemData = {
                placeholder: JSON.parse(item.placeholder),
                inputTitle: item.inputTitle,
                isNeed: item.notNull
              };
              allData.push(itemData);
            }
          });
        });
        this.formData = allData[allData.length - 1];
      });
    }
  }
};
</script>
<style scoped lang="less"></style>

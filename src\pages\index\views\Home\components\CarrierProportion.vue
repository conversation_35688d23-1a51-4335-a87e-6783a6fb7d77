<template>
  <div>
    <div id="CarrierProportion" ref="CarrierProportion" class="contain"></div>
  </div>
</template>

<script>
import { getCarrierProportionApi } from "@/pages/index/data/api/ComponyWatch";
import * as echarts from "echarts";
export default {
  data() {
    return {
      chartData: {},
      total: 0,
    };
  },
  mounted() {
    this.initData();
  },
  created() {},
  methods: {
    async initData() {
      this.chartData = await getCarrierProportionApi("");
      this.chartData.data.forEach((e) => {
        this.total = this.total + e.value;
      });
      this.drawLine();
    },
    drawLine() {
      //   console.log(this.$echarts.init);
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(this.$refs.CarrierProportion);
      let option = {
        tooltip: {
          position: [80, 0],
          trigger: "item",
          formatter: (parmas) => {
            return `${parmas.data.parkName} ： ${parmas.value} 家`;
          },
        },
        legend: {
          show: false,
          type: "scroll",
          orient: "vertical",
          right: "5%",
          y: "center",
          textStyle: {
            color: "#000000",
          },
        },
        series: [
          {
            name: "载体占比",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            // color: ['#6FACFF', '#F7B500', '#FF7467', '#50DFB3', '#5B76F9'],
            label: {
              normal: {
                show: true,
                position: "center",
                color: "#4c4a4a",
                formatter: "总载体数" + "\n\r" + "\n\r" + this.total + "家",
                textStyle: {
                  fontFamily: "微软雅黑",
                  fontWeight: "normal",
                  fontSize: 18,
                },
              },
              emphasis: {
                //中间文字显示
                show: true,
              },
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 20,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: this.chartData.data,
          },
        ],
      };
      // 绘制图表
      myChart.setOption(option);
      //多图表自适应
      window.addEventListener("resize", function() {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contain {
  width: 300px;
  height: 300px;
}
</style>

/**
 * <AUTHOR>
 * @time 2020-8-24
 * @dec API命名规范
 * @dec API + 模块名 + 接口最后一个词
 * @dec 示例接口： /insure/upload/fileUpload
 * @dec 命名：ApiInsuredFileUpload
 */ import api from "@/common/api";
import { BASE_URL } from "Config";

// 分页查询
export function ApiSecuritydepartmentCondition(params) {
  const middle = {
    request(params) {
      // to do something
      return params;
    },
    response(data) {
      // to do something
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    },
  };
  return api({
    url: BASE_URL + "/security/department/departmentCondition",
    method: "post",
    middle,
    params,
  });
}
// 新增部门
export function ApiSecuritycreate(params) {
  return api({
    url: BASE_URL + "/security/department/create",
    method: "post",
    params,
  });
}
// 修改部门信息
export function ApiSecurityupdate(params) {
  return api({
    url: BASE_URL + "/security/department/update",
    method: "post",
    params,
  });
}
// 删除部门信息
export function ApiSecuritydeleteById(params) {
  return api({
    url: BASE_URL + "/security/department/deleteById",
    method: "post",
    params,
  });
}
// 查询部门树
export function ApiSecurityfindDepartmentTree(params) {
  return api({
    url: BASE_URL + "/security/department/findDepartmentTree",
    method: "post",
    params,
  });
}

<template>
  <div>
    <div class="content notification">
      <div class="notification-title">
        <div class="notification-title-label">主题</div>
        <div class="notification-title-description">
          {{ $route.query.theme }}
        </div>
      </div>
      <div class="divided-line"></div>
      <div v-html="content" class="notification-content" id="printPage"></div>
    </div>
    <div class="footer">
      <a-button type="primary" @click="back">返回</a-button>
      <a-button type="primary" style="margin-left: 20px" @click="handleExport"
        >导出</a-button
      >
    </div>
  </div>
</template>

<script>
import JsPdfImg from "html2pdf-2img";
import moment from "moment";

import {
  ApiNoticeDetail,
  ApiNoticeReadCount,
  ApiNoticeUpdate,
} from "APIs/PortalManagement/NotificationManagement/index.js";
export default {
  data() {
    return {
      content: "",
      id: "",
      userName: JSON.parse(localStorage.getItem("USER_KEY")).name,
    };
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    moment,
    getDetail: function () {
      const id = this.$route.query.id;
      this.id = id;
      ApiNoticeDetail({ id }).then((res) => {
        if (res.code == 0) {
          console.log(res.data, "html========================");

          this.$set(this, "content", res.data);
          ApiNoticeReadCount().then((response) => {
            if (response.code == 0) {
              this.$bus.$emit("notify", response.data);
            }
          });
          ApiNoticeUpdate({ id }).then((result) => {
            //不处理返回结果
            console.log(result);
          });
        }
      });
    },
    back: function () {
      this.$router.go(-1);
      // this.$router.replace({
      //   path: "/portal-management/notification-management",
      //   query: {
      //     refresh: true,
      //   },
      // });
    },
    handleExport: function () {
      // document
      //   .getElementById("printPage")
      //   .setAttribute("style", "margin:none;padding:108px;");
      const curTime = moment().format("YYYY-MM-DD HH:mm:ss");
      console.log(curTime, curTime);
      let name = "";
      let watermark = "";
      if (this.$route.query.theme == "备案通知") {
        name = "备案审核通知凭证";
        watermark = "上海华泾经济发展有限公司";
      } else {
        name = this.$route.query.theme;
        watermark = this.userName + curTime;
      }

      new JsPdfImg("#printPage", `${name}`, {
        isPrint: true,
        pageBreak: [".title", "#area", "li", "h3"], // 当导出pdf时候，这个参数必填
        pageStartOffset: 200, // 每个页头的留空距离
        watermarkOption: {
          watermark_txt: watermark,
          z_index: 97,
          watermark_x: 0,
          watermark_y: 0,
          watermark_x_space: 80,
          watermark_y_space: 80,
          watermark_width: 480,
          watermark_fontsize: "24px",
          watermark_alpha: 0.08,
        },
      }).outPdf(() => {
        console.log("结束");
        // document
        //   .getElementById("printPage")
        //   .setAttribute("style", "margin:0 108px;padding:none;");
      });
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  background-color: #ffffff;
  padding: 32px;
}
.notification {
  &-title {
    display: flex;
    &-label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 68px;
      height: 36px;
      background: #f7f8fa;
      border-radius: 80px;
      font-size: 18px;
      font-weight: 500;
      color: #cccccc;
    }
    &-description {
      margin-left: 14px;
      height: 32px;
      font-size: 22px;
      font-weight: 500;
      text-align: LEFT;
      color: #1d2129;
      line-height: 32px;
    }
  }
  &-content {
    position: relative;
    padding: 150px;
    padding-bottom: 0px;
  }
}
.footer {
  margin-top: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.divided-line {
  width: 100%;
  height: 0px;
  opacity: 0.08;
  border: 1px solid #000000;
  margin-top: 16px;
}
</style>

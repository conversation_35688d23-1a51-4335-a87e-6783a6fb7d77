<template>
	<div>

	</div>
</template>
<script>
import { store } from "@/data/store";
export default {
	components: {},
	data() {
		return {
			formItemLayout: {
				labelCol: {
					xs: { span: 24 },
					sm: { span: 7 },
				},
				wrapperCol: {
					xs: { span: 24 },
					sm: { span: 17 },
				},
			},
			queryParam: {
				parkName: '星联',
				floor: '',
				area: '',
				status: '',
				date: '',
			},
			labelCol: { span: 4 },
			parkArr: [
				{
					name: '华泾镇',
					value: '1',
				},
				{
					name: '星联科技智慧园区',
					value: '2',
				},
			],
		};
	},
	methods: {
		//查询
		search() {
      console.log(this.queryParam);
      store.commit("query/setQueryData", [1,2,3]);
    },
	},
};
</script>
<style lang="less" scoped>
.all {
	display: flex;
	flex-wrap: nowrap;
	margin-top: 0.8rem;
	margin-left: 0.5rem;
	.label {
		display: flex;
		flex-wrap: nowrap;
		margin-right: 30px;
	}
	.btnGroup {
		margin-left: auto;
		margin-right: 10px;
		.query {
			margin-right: 10px;
		}
	}
}
/deep/.ant-select-selection--single {
	width: 12rem;
}
</style>

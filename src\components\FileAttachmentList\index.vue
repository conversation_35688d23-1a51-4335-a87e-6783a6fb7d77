<template>
  <div class="file-attachment" :class="{'file-attachment-short':shortStyle}">
    <div>
      <div>
        <span v-if="getMarked" class="title-marked">*</span>
        <span class="title-content">{{ title }}</span>
      </div>
      <div class="column-content">
        <div>
          <template v-for="(item, index) in leftDataSource">
            <div :key="index" class="file-attachment-list-item">
              <div
                :class="
                  getNameLinkStatus
                    ? 'file-attachment-list-item-link file-attachment-list-item-name'
                    : 'file-attachment-list-item-name'
                "
              >
                <a-tooltip>
                  <template slot="title">
                    {{ item.fileName }}
                  </template>
                  <div
                    style="color:#1777FF;cursor:pointer;"
                    @click="lookFile(item)"
                  >
                    {{ showFileName(item) }}
                  </div>
                </a-tooltip>
              </div>
              <div
                v-if="mode == 'normal'"
                class="file-attachment-list-item-look"
                @click="lookFile(item)"
              >
                查看
              </div>
              <div
                v-if="mode == 'normal'"
                class="file-attachment-list-item-delete"
                @click="deleteFile(item, 'left')"
              >
                删除
              </div>
            </div>
          </template>
        </div>
        <div class="column-content-right-column">
          <template v-for="(item, index) in rightDataSource">
            <div :key="index" class="file-attachment-list-item">
              <div
                :class="
                  getNameLinkStatus
                    ? 'file-attachment-list-item-link file-attachment-list-item-name'
                    : 'file-attachment-list-item-name'
                "
                @click="lookFile(item)"
              >
                <a-tooltip>
                  <template slot="title">
                    <div>{{ item.fileName }}</div>
                  </template>
                  <div
                    style="color:#1777FF; cursor:pointer;"
                    @click="lookFile(item)"
                  >
                    {{ showFileName(item) }}
                  </div>
                </a-tooltip>
              </div>
              <div
                v-if="mode == 'normal'"
                class="file-attachment-list-item-look"
                @click="lookFile(item)"
              >
                查看
              </div>
              <div
                v-if="mode == 'normal'"
                class="file-attachment-list-item-delete"
                @click="deleteFile(item, 'right')"
              >
                删除
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- <div v-if="isExisted">
      <a-icon
        type="check-circle"
        style="color: #19B21E; font-size: 24px;"
        theme="filled"
      />
    </div> -->
    <div v-if="previewOnlineInfo.visible">
      <file-preview-online
        :visible="previewOnlineInfo.visible"
        :type="previewOnlineInfo.type"
        :url="previewOnlineInfo.url"
        @closeModal="handleFilePreviewOnlineModalWithClosing"
        @previewCallback="previewCallback"
      />
    </div>
  </div>
</template>

<script>
/**
 * [props]
 * title 附件列表抬头 必填
 * fileList 附件列表数组  必填
 * marked 附件列表抬头是否被标记 选填
 * canPreview 附件是否可以预览 选填
 * canDelete 附件是否可以删除 选填
 * canNamelink 附件名称是否可以预览 选填
 * mode 附件查看模式，"normal" , "preview"
 * ifNeedPreviewOnline 附件是否在线预览，仅支持docx,pdf格式
 * [event]
 * deleteFile  (deleteFile, fileList)=>{}
 */
import * as utils from "@/common/utils/index.js";
import {
  ApiAttachmentDeleteById,
  // ApiAttachmentDownById,
} from "APIs/Common/index.js";
// import { BASE_URL } from "Config";
import FilePreviewOnline from "../FilePreviewOnline";
import { BASE_URL } from "Config";
export default {
  components: {
    FilePreviewOnline,
  },
  props: {
    title: {
      type: String,
      required: true,
    },
    fileList: {
      type: Array,
      required: true,
    },
    marked: {
      type: Boolean,
      default: false,
    },
    canPreview: {
      type: Boolean,
      default: true,
    },
    canDelete: {
      type: Boolean,
      default: true,
    },
    canNameLink: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: "normal",
    },
    ifNeedPreviewOnline: {
      type: Boolean,
      default: false,
    },
    shortStyle:{//为true时一行展示一个文件
      type: Boolean,
      default: false,
    }
  },
  computed: {
    getMarked() {
      return this.marked ? true : false;
    },
    isExisted() {
      return this.fileList && this.fileList.length > 0 ? true : false;
    },
    getNameLinkStatus() {
      return this.canNameLink ? true : false;
    },
  },
  watch: {
    fileList: {
      handler() {
        this.$nextTick(() => {
          this.init(this.fileList);
        });
      },
      immediate: true,
      deep: true,
    },
  },
  emits: ["deleteFile"],
  data() {
    return {
      leftDataSource: [],
      rightDataSource: [],
      dataSource: [],
      previewOnlineInfo: {
        visible: false,
        url: "",
        type: "",
      },
    };
  },
  methods: {
    init: function(fileList) {
      let tmpList = fileList;
      let leftDataSource = [];
      let rightDataSource = [];
      tmpList &&
        tmpList.length > 0 &&
        tmpList.forEach((element, index) => {
          if (!element.uid) {
            element["uid"] = utils.getRandomNum("a", 8);
          }
          if (this.shortStyle) {
            leftDataSource.push(element);
          }else{
            if (index % 2 == 0) {
              leftDataSource.push(element);
            } else {
              rightDataSource.push(element);
            }
          }
        });
      const dataSource = utils.deepClone(tmpList);
      this.$set(this, "dataSource", dataSource);
      this.$set(this, "leftDataSource", leftDataSource);
      this.$set(this, "rightDataSource", rightDataSource);
    },
    showFileName: function(file) {
      const sourceName = file && file.fileName;
      let targetName = "";
      if (sourceName && sourceName.length > 20) {
        targetName = sourceName.substring(0, 20) + "...";
      } else {
        targetName = sourceName;
      }
      return targetName;
    },
    lookFile: function(file) {
      console.log(file);
      const { fileId = "" } = file;
      if (!fileId) {
        this.$message.error("未获取到文件信息");
        return;
      }
      // const fileUrl = BASE_URL + "/common/attachment/download?id=" + fileId;
      const url = BASE_URL + "/common/attachment/download?id=" + fileId;

      if (this.ifNeedPreviewOnline) {
        //当前近支持.docx|.pdf格式
        if (
          file.fileType == "docx" ||
          file.fileType == "pdf" ||
          file.fileType == "png" ||
          file.fileType == "jpg" ||
          file.fileType == "jpeg"
        ) {
          this.$set(this.previewOnlineInfo, "visible", true);
          this.$set(this.previewOnlineInfo, "url", url);
          this.$set(this.previewOnlineInfo, "type", file.fileType);
        } else {
          window.open(url);
        }
      } else {
        window.open(url);
      }
    },
    deleteFile: function(file, type) {
      console.log(file, type);
      if (file.fileId) {
        //TODO 1，调用接口删除；2，删除本地文件
        const id = file.fileId;
        const param = { id };
        ApiAttachmentDeleteById(param).then((res) => {
          console.log(res);
          if (res.code == 0) {
            const tmpList = this.dataSource.filter((o) => o.uid != file.uid);
            this.$emit("deleteFile", file, tmpList);
            this.$message.info(file.fileName + " 删除成功");
          }
        });
      } else {
        // 不存在ID，只有UID时，直接删除本地文件
        const tmpList = this.dataSource.filter((o) => o.uid != file.uid);
        this.$emit("deleteFile", file, tmpList);
        this.$message.info(file.fileName + " 删除成功");
      }
    },
    handleFilePreviewOnlineModalWithClosing: function() {
      this.$set(this.previewOnlineInfo, "visible", false);
      this.$set(this.previewOnlineInfo, "url", "");
      this.$set(this.previewOnlineInfo, "type", "");
    },
    previewCallback: function(flag) {
      if (flag == "success") {
        console.log(flag);
      } else {
        console.log(flag);
        this.$message.info("在线预览加载失败，即将下载文件转为本地预览！");
        window.open(this.previewOnlineInfo.url);
        this.handleFilePreviewOnlineModalWithClosing();
      }
    },
  },
};
</script>

<style scoped lang="less">
.file-attachment {
  min-width: 50%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 20px;
  margin-right: 20px;
}
.title-marked {
  color: #ff0000;
}
.title-content {
  color: 16px;
  font-weight: bold;
}
.file-attachment-list-item {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  color: #86909c;
  height: 20px;
  line-height: 20px;
  font-weight: 400;
  &-name {
    // width: 180px;
    font-size: 16px;
    letter-spacing: 2px;
  }
  &-link {
    cursor: pointer;
    color: #1777ff;
  }
  &-look {
    white-space: nowrap;
    cursor: pointer;
    margin-left: 20px;
    color: #1777ff;
  }
  &-delete {
    white-space: nowrap;
    cursor: pointer;
    margin-left: 20px;
    color: #fe6a6a;
  }
}
.column-content {
  display: flex;
  &-right-column {
    margin-left: 80px;
  }
}
// 一行展示一个文件
.file-attachment-short{
  margin-left: 0;
  // >div{
  //   width: 100%;
  // }
  .column-content{
    >div{
      width: 100%;
    }
    .column-content-right-column {
      display: none;
      // margin-left: 0px !important;
    }
    .file-attachment-list-item-name{
      flex: 1;
    }
  }
}

</style>

<!--
* <AUTHOR>
* @time 2020-8-19
* @dec 系统管理 - 角色管理页面
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="应用端">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body
                  }
                "
                placeholder="请选择应用端"
                v-model="queryParam.appType"
                allowClear
              >
                <a-select-option
                  v-for="item in $store.getters['dictionaries/getType'](
                    'APPTYPE'
                  )"
                  :key="item.dictValue"
                  >{{ item.nameCn }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="角色名称">
              <a-input v-model="queryParam.name" placeholder="请输入角色名称" allowClear/>
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="() => (this.queryParam = {})"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator" v-if="$auth('mapi:security:role:create')">
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd"
        v-auth="'mapi:security:role:create'"
        >新建</a-button
      >
    </div>

    <s-table
      ref="table"
      size="default"
      rowKey="key"
      :columns="columns"
      :data="loadData"
    >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * pageSize + index + 1 }}
      </span>
      <!-- <span slot="status" slot-scope="text">
        <a-badge
          :status="text | statusTypeFilter"
          :text="text | statusFilter"
        />
      </span>-->
      <span slot="appType" slot-scope="text">
        <span>
          {{
            $store.getters["dictionaries/getNameFromTypeCode"]({
              type: "APPTYPE",
              code: text
            })
          }}
        </span>
      </span>
      <span slot="description" slot-scope="text">
        <span>{{ text | description }}</span>
      </span>
      <span slot="desc" slot-scope="text">
        <ellipsis :length="20" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="disabled" slot-scope="disabled, record">
        <template>
          <a-switch
            v-if="$auth('mapi:security:role:updateDisabled')"
            :checked="!disabled"
            @change="handleChangeDisabled(disabled, record)"
          />
          <span v-else>{{ disabled ? "否" : "是" }}</span>
        </template>
      </span>
      <span slot="action" slot-scope="text, record">
        <template>
          <a
            @click="handleConfigurepermissions(record)"
            v-auth="'mapi:security:role:saveRoleWithPermission'"
            >权限配置</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:role:update')"
          />
          <a @click="handleEdit(record)" v-auth="'mapi:security:role:update'"
            >修改</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:role:delete')"
          />
          <a-popconfirm
            title="确认删除?"
            @confirm="() => handleDelete(record)"
            v-auth="'mapi:security:role:delete'"
          >
            <a href="javascript:;">删除</a>
          </a-popconfirm>
        </template>
      </span>
    </s-table>
    <!-- 新增角色 -->
    <create-form
      ref="createModal"
      :visible="modal.createForm.visible"
      :data="modal.createForm.data"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
    <!-- 权限配置 -->
    <configurepermissions
      ref="configurepermissions"
      :visible="modal.configurepermissions.visible"
      :data="modal.configurepermissions.data"
      @cancel="handleConfigurepermissionsCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table"
import Ellipsis from "@/components/Ellipsis"
// API接口
import {
  ApiSecuritypageByCondition,
  ApiupsecuritydateDisabled,
  ApiSecuritydelete
} from "@/pages/index/data/api/SystemManagement/Role"
import CreateForm from "./compontents/CreateForm"
import Configurepermissions from "./compontents/Configurepermissions"

const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "serial" }
  },
  {
    title: "应用端",
    dataIndex: "appType",
    align: "center",
    scopedSlots: { customRender: "appType" }
  },
  {
    title: "角色名称",
    dataIndex: "name",
    align: "center",
    scopedSlots: { customRender: "description" }
  },
  {
    title: "角色描述",
    dataIndex: "desc",
    align: "center",
    scopedSlots: { customRender: "desc" }
  },
  {
    title: "是否启用",
    dataIndex: "disabled",
    align: "center",
    scopedSlots: { customRender: "disabled" }
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "200px",
    align: "center",
    scopedSlots: { customRender: "action" }
  }
]
export default {
  name: "SystemManagementRole",
  components: {
    Ellipsis,
    STable,
    CreateForm,
    Configurepermissions
  },
  data() {
    this.columns = columns

    return {
      pageNo: 1,
      pageSize: 10,
      // 视窗状态
      modal: {
        // 新建角色
        createForm: {
          visible: false,
          data: {}
        },
        // 权限配置
        configurepermissions: {
          visible: false,
          data: {}
        }
      },
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {
        // ascs: "",
        currentPage: 1,
        appType: undefined,
        // descs: "",
        // disabled: 1,
        name: "",
        // pageSize: 10,
        roleString: ""
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        this.pageNo = parameter.pageNo
        this.pageSize = parameter.pageSize
        this.queryParam.currentPage = parameter.pageNo
        this.queryParam.current = this.queryParam.currentPage
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        console.log("loadData request parameters:", requestParameters)
        this.$set(this, "pageNo", parameter.pageNo)
        this.$set(this, "pageSize", parameter.pageSize)
        return ApiSecuritypageByCondition(requestParameters).then((res) => {
          return res.data
        })
      },
      selectedRowKeys: [],
      selectedRows: [],
    }
  },
  created() {},
  methods: {
    onChange(checked) {
      console.log(`a-switch to ${checked}`)
    },
    // 新增角色 开启窗口
    handleAdd() {
      this.modal.createForm.visible = true
    },
    // 新增角色 关闭窗口
    handleCancel() {
      this.modal.createForm.visible = false
      this.modal.createForm.data = {}
      const form = this.$refs.createModal.form
      form.resetFields() // 清理表单数据（可不做）
    },
    // 是否启用
    handleChangeDisabled(value, record) {
      record.disabled = !value
      ApiupsecuritydateDisabled({
        disabled: !value,
        id: record.id
      })
        .then(() => {
          if (value) {
            this.$message.info("启用成功")
          } else {
            this.$message.info("禁用成功")
          }
        })
        .catch(() => {
          record.disabled = !record.disabled
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 修改数据
    handleEdit(record) {
      this.modal.createForm.visible = true
      this.modal.createForm.data = { ...record }
    },
    // 删除数据
    handleDelete(record) {
      console.log("删除事件", record.id)
      this.loading = true
      const params = {
        id: record.id
      }
      ApiSecuritydelete(params)
        .then((res) => {
          console.log("loadData", res)
          this.$message.info("删除成功")
          this.$refs.table.refresh()
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 权限配置
    handleConfigurepermissions(record) {
      this.modal.configurepermissions.visible = true
      this.modal.configurepermissions.data = record
    },
    // 权限配置 关闭窗口
    handleConfigurepermissionsCancel() {
      this.modal.configurepermissions.visible = false
      this.modal.configurepermissions.data = {}
      // const form = this.$refs.configurepermissions.form;
      this.$refs.configurepermissions.form
    },

    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    }
  }
}
</script>

export default {
  namespaced: true,
  state: {
    data: "",
  },
  getters: {
    data(state) {
      console.log(state);
      const info = localStorage.getItem("notification_info");
      console.log(info, "notification info~~~");
      if (!info) {
        let objStr = state.data;
        if (!objStr) {
          let tmp = {
            readNumber: 0,
          };
          return tmp;
        }
        return JSON.parse(state.data);
      }
      return JSON.parse(info);
    },
  },
  mutations: {
    setNotificationInfo(state, data) {
      localStorage.setItem("notification_info", data);
      state.data = data;
    },
  },
  actions: {
    dispatchNotification({ commit }, data) {
      commit("setNotificationInfo", JSON.stringify(data));
    },
  },
};

<!--
* <AUTHOR>
* @time 2021-9-15
* @dec 模板下载DOM
-->
<template>
  <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :help="
        data.optionsData.fileNum || data.optionsData.fileSize
          ? uploadTip
          : false
      "
      :label="'文件下载'"
      style="margin-bottom:unset"
      prop="fileList"
    >
      <a href="#" v-if="data.inputTitle">{{ data.inputTitle }}</a>
      <a href="#" v-else>下载</a>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

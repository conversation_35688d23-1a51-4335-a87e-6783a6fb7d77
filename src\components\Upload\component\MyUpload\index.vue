<template>
  <div class="uploadFileContent">
    <div class="uploadHeader">
      <div class="uploadFile">
        <a-upload
          :accept="getAccept"
          :multiple="true"
          :beforeUpload="beforeUpload"
          :action="getAction"
          :default-file-list="fileList"
          :data="getExtraData"
          @change="handleChange"
          :showUploadList="false"
          :disabled="!canUpload"
        >
          <div class="btn-group">
            <div class="btn-label">
              <span v-if="labelRequired == true" style="color:red">*</span
              >{{ businessLabel }}
            </div>
            <a-button class="bnt-upload" :disabled="!canUpload">
              <a-icon type="upload" /> 点击上传(Upload)
            </a-button>
          </div>
        </a-upload>
      </div>
    </div>

    <template>
      <div class="upload-info-list">
        <div v-for="(item, index) in fileList" :key="index">
          <div class="upload-file-item">
            <div>
              <a :disabled="!canPreview" @click="previewFIle(item)">{{
                item.fileName
              }}</a>
            </div>
            <a-popconfirm
              v-if="pageState != 2"
              title="是否确定删除此文件?"
              @confirm="removeFile(item)"
            >
              <a-icon class="delete-icon-item" type="delete" />
            </a-popconfirm>
            <!-- <div v-if="pageState != 2" @click="removeFile(item)">
              <a-icon class="delete-icon-item" type="delete" />
            </div> -->
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { getPreviewUrl, deleteAttachment } from "@/common/api/common/upload";
const FILE_SIZE_LIMITED = 10 * 1024 * 1024;
export default {
  props: {
    businessAccept: {
      type: String,
      required: false,
    }, //自定义模块accept文件格式字符串，如：".pdf,.png,.jpg,.jpeg,.doc,.docx,.xls,.xlsx",非必填
    businessInfo: {
      type: Object,
      required: false,
    }, //业务对象，暂定必包含业务id，id:"", 属性非必填
    businessCanUpload: {
      type: Boolean,
      required: false,
    }, //是否可以上传, 属性非必填，默认可上传
    businessCanPreview: {
      type: Boolean,
      required: false,
    }, //是否可以预览, 属性非必填,默认可预览
    businessPageState: {
      type: Number,
      required: true,
    }, //0新增，1编辑，2查看, 属性必填
    businessLabel: {
      type: String,
      required: true,
    },
    labelRequired: {
      type: Boolean,
      required: false,
    }, //businessLabel是否必填项
    archiveFileList: {
      type: Array,
      required: true,
    }, //回显列表
  },
  data() {
    return {
      uploadBaseInfo: {},
      fileList: [],
      fileName: "",
      uploadLabel: "",
    };
  },
  watch: {
    uploadInfo: {
      handler() {
        this.$nextTick(() => {
          this.initInfo();
        });
      },
      immediate: true,
      deep: true,
    },
    archiveFileList: {
      handler() {
        this.$nextTick(() => {
          console.log("回显~~~~", this.archiveFileList);
          this.$set(this, "fileList", this.archiveFileList || []);
        });
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    getAction() {
      return this.uploadBaseInfo?.host
        ? this.uploadBaseInfo.host
        : "https://dhwl-hss.oss-cn-shanghai.aliyuncs.com";
    },
    getAccept() {
      const defaultFomatters =
        ".pdf,.png,.jpg,.jpeg,.bmp,.heif,.doc,.docx,.xls,.xlsx,.mp4,.mpg";
      return this.businessAccept ? this.businessAccept : defaultFomatters;
    },
    canShowLabel() {
      console.log("!!!!", this.uploadLabel);
      return this.uploadLabel.length > 0 ? true : false;
    },
  },
  methods: {
    initInfo: function() {
      this.$set(this, "uploadBaseInfo", this.uploadInfo);
    },
    getExtraData: function(file) {
      const suffix = file.name.slice(file.name.lastIndexOf("."));
      const fileName = Date.now() + suffix;
      this.$set(this, "fileName", fileName);
      return {
        key: this.uploadBaseInfo.dir + "/" + fileName,
        OSSAccessKeyId: this.uploadBaseInfo.accessId,
        policy: this.uploadBaseInfo.policy,
        Signature: this.uploadBaseInfo.signature,
        success_action_status: "200", // 阿里成功返回204，修改为200
        // Filename: "specialKey",
      };
    },
    beforeUpload: function(file, fileList) {
      console.log(file, fileList);
      if (file.size > FILE_SIZE_LIMITED) {
        return false;
      }
    },
    handleChange(info) {
      let tmpFileList = this.fileList;
      let isContain = false;
      let tmpFile = {};

      if (tmpFileList.length > 0) {
        tmpFileList.forEach((item) => {
          if (item.uid == info.file.uid) {
            isContain = true;
          }
        });
      }

      if (!isContain) {
        tmpFile.uid = info.file.uid;
        tmpFile.type = info.file.type;
        tmpFile.fileName = info.file.name;
        tmpFile.lastModified = info.file.lastModified;
        tmpFile.fileSize = info.file.size;
        tmpFile.url =
          this.uploadBaseInfo.host +
          "/" +
          this.uploadBaseInfo.dir +
          "/" +
          this.fileName;

        tmpFileList.push(tmpFile);
        this.$set(this, "fileList", tmpFileList);
      }

      if (info.file.status !== "uploading") {
        console.log("~~~~upload", info.file, info.fileList);
      }
      if (info.file.status === "done") {
        console.log(
          "file-path--",
          this.uploadBaseInfo.host +
            "/" +
            this.uploadBaseInfo.dir +
            "/" +
            this.fileName
        );
        console.log(
          "~~~~upload",
          `${info.file.name} file uploaded successfully`
        );
        // this.$message.success(`${info.file.name} file uploaded successfully`);
        const uploadedUrl =
          this.uploadBaseInfo.host +
          "/" +
          this.uploadBaseInfo.dir +
          "/" +
          this.fileName;
        const previewParams = {
          url: uploadedUrl,
        };
        getPreviewUrl(previewParams).then((res) => {
          console.log("get preview url", res);
          if (res.code == 0) {
            this.$message.info("上传成功");
            this.fileList.forEach((element) => {
              if (element.uid == info.file.uid) {
                element["previewUrl"] = res.data;
              }
            });
          }
        });
      } else if (info.file.status === "error") {
        console.log("~~~~upload", `${info.file.name} file upload failed.`);
        // this.$message.error(`${info.file.name} file upload failed.`);
      }
      this.$emit("handleFileListCallback", this.fileList);
    },
    removeFile: function(record) {
      console.log("remove-record", record);
      if (record.id) {
        const ids = [];
        ids.push(record.id);
        const tmpParams = {
          ids,
          relationId: this.businessInfo?.id,
        };
        deleteAttachment(tmpParams).then((res) => {
          console.log("~~~del", res);
          if (res.code == 0) {
            let deleteFileList = this.fileList.filter((item) => {
              return item.id != record.id;
            });

            this.$set(this, "fileList", deleteFileList);
            console.log("deleted-list", deleteFileList);
            this.$emit("handleFileListCallback", deleteFileList);
            this.$message.info("删除成功");
          }
        });
      } else if (record.uid) {
        let deleteFileList = this.fileList.filter((item) => {
          return item.uid != record.uid;
        });

        this.$set(this, "fileList", deleteFileList);
        console.log("deleted-list", deleteFileList);
        this.$emit("handleFileListCallback", deleteFileList);
        this.$message.info("删除成功");
      }
    },
    previewFIle: function(record) {
      let url = "";
      // if (this.pageState == 0) {
      //   url = record?.previewUrl
      // } else {
      //   url = record?.url
      // }
      url = record?.previewUrl;
      if (url) {
        window.open(url);
      } else {
        console.log("...");
      }
    },
  },
};
</script>

<style lang="less">
.uploadFileContent {
  /* margin-left: 200px; */
  margin-top: 20px;
}
.uploadFileContent .uploadHeader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.uploadHeader {
  font-size: 14px;
  font-weight: bold;
}
.uploadHeader .uploadTip {
  margin-right: 20px;
}
.uploadHeader .uploadFile {
  width: 380px;
  .btn-group {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .btn-label {
      width: 200px;
      margin-right: 20px;
    }
    .bnt-upload {
      // margin-left: 20px;
    }
  }
}
.fileItem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.upload-info-list {
  margin-top: 20px;
  margin-left: 120px;
  .upload-file-item {
    margin-top: 10px;
    width: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .delete-icon-item:hover {
      color: red;
    }
  }
}
</style>

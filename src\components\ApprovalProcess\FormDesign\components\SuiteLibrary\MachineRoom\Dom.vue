<!--
* <AUTHOR>
* @time 2020-9-18
* @dec 机房资源套件
-->

<template>
  <div>
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <h3>机房资源套件</h3>
      <a-form-model-item label="申请主题">
        <a-input
          placeholder="[申请单位]关于用途的非航资源申请单-申请人-申请日期"
        />
      </a-form-model-item>
      <a-form-model-item label="航站楼">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="1">
            T1 机房
          </a-select-option>
          <a-select-option value="2">
            T2 东交机房
          </a-select-option>
          <a-select-option value="3">
            T2 楼内机房
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="机房">
        <a-select
          placeholder="请选择"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option value="A">
            A
          </a-select-option>
          <a-select-option value="B">
            B
          </a-select-option>
          <a-select-option value="C">
            C
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="机房作业类别">
        <a-select placeholder="请选择">
          <a-select-option value="A">
            配置变更和维护
          </a-select-option>
          <a-select-option value="B">
            资源变更
          </a-select-option>
          <a-select-option value="C">
            携带笔记本电脑
          </a-select-option>
          <a-select-option value="C">
            日常巡查
          </a-select-option>
          <a-select-option value="C">
            参观观察
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="资源申请单号">
        <a-input placeholder="请输入申请单号" />
      </a-form-model-item>
      <a-form-model-item label="进入机房日期">
        <a-date-picker @change="onChange" />
      </a-form-model-item>
      <a-form-model-item label="进入机房时间">
        <a-time-picker use12-hours @change="onChange" />
      </a-form-model-item>

      <div class="non-aviation-title-button">
        <div>请选择现场负责人员</div>
        <div>
          <a-button type="primary" size="small">
            <a-icon type="plus" />
            添加
          </a-button>
        </div>
      </div>
      <a-table
        :scroll="{ x: 1000 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * 10 + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
      <div class="non-aviation-title-button">
        <div>请选择进入人员</div>
        <div>
          <a-button type="primary" size="small">
            <a-icon type="plus" />
            添加
          </a-button>
        </div>
      </div>
      <a-table
        :scroll="{ x: 1000 }"
        :columns="columns"
        :data-source="values"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * 10 + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a @click="handleEdit(record)">修改</a>
            <a-divider type="vertical" />
            <a @click="handleDelete(record)">删除</a>
          </template>
        </span>
      </a-table>
      <a-form-model-item
        style="margin-top:20px"
        label="是否携带笔记本"
        :rules="[
          {
            required: data.notNull,
            message: '请选择',
            trigger: 'change'
          }
        ]"
      >
        <a-select placeholder="是否携带笔记本进入机房" @change="handleChange">
          <a-select-option value="1">
            是(携带)
          </a-select-option>
          <a-select-option value="2">
            否(不携带)
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "serial" }
  },
  {
    title: "姓名",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "联系方式",
    dataIndex: "phoneNo",
    key: "phoneNo"
  },
  {
    title: "身份证号/通行证号",
    dataIndex: "cardId",
    key: "cardId"
  },
  {
    title: "所属单位",
    dataIndex: "company",
    key: "company"
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "120px",
    scopedSlots: { customRender: "action" }
  }
]

const values = [
  {
    name: "王二", //姓名
    phoneNo: "14388896547", //联系方式
    cardId: "320902198408085030", //身份证号/通行证号
    company: "东方航空" //所属单位
  }
]
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columns,
      values,
      form: {
        machineroom: null
      }
    }
  },
  watch: {
    value(val) {
      console.log(`selected:`, val)
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
.non-aviation-title-button {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 10px 0;
}
</style>

<!--
* <AUTHOR>
* @time 2020-8-19
* @dec 系统管理 - 角色管理页面 -- 新增角色
-->
<template>
  <a-modal
    :title="title"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel')
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <!-- 检查是否有 id 并且大于0，大于0是修改。其他是新增，新增不显示主键ID -->
        <a-form-item v-show="data && data.id > 0" label=""> </a-form-item>
        <a-form-item label="应用端">
          <a-select
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
            placeholder="请选择应用端"
            v-decorator="[
              'appType',
              {
                rules: [
                  {
                    required: true,
                    message: '请选择应用端！'
                  }
                ]
              }
            ]"
          >
            <a-select-option
              v-for="item in $store.getters['dictionaries/getType']('APPTYPE')"
              :key="item.dictValue"
            >
              {{ item.nameCn }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="角色名称">
          <a-input
            v-decorator="[
              'name',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入角色名称！'
                  }
                ]
              }
            ]"
            autoComplete="off"
            placeholder="请输入角色名称"
            :maxLength="20"
          />
        </a-form-item>
        <a-form-item label="是否启用" v-show="!(data && data.id > 0)">
          <a-radio-group
            v-decorator="[
              'disabled',
              {
                initialValue: false,
                rules: [{ required: true, message: '请选择是否启用！' }]
              }
            ]"
          >
            <a-radio :value="false">
              是
            </a-radio>
            <a-radio :value="true">
              否
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="角色描述">
          <a-textarea
            placeholder="请添加角色描述"
            v-decorator="['desc', {}]"
            :maxLength="20"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick"
import {
  ApiSecuritycreate,
  ApiSecurityupdate
} from "@/pages/index/data/api/SystemManagement/Role"
// 表单字段
const fields = ["appType", "name", "desc", "disabled"]

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    }
    return {
      title: "新建角色",
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  created() {
    console.log("custom modal created")

    // 防止表单未注册
    fields.forEach((v) => this.form.getFieldDecorator(v))

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields))
      if (this.data && this.data.id) {
        this.title = "修改角色"
      } else {
        this.title = "新建角色"
      }
    })
  },
  methods: {
    handleOk() {
      this.loading = true
      this.form.validateFields((errors, values) => {
        if (!errors) {
          if (this.data && this.data.id) {
            values.id = this.data.id
            // 修改
            const requestParameters = Object.assign({}, values)
            ApiSecurityupdate(requestParameters)
              .then((res) => {
                console.log("loadData", res)
                this.$emit("cancel")
                this.loading = false
                // 重置表单数据
                this.form.resetFields()
                // 刷新表格
                this.$emit("ok")
                this.$message.info("修改成功")
              })
              .finally(() => {
                this.departmentListLoading = false
              })
          } else {
            // 新增
            console.log("loadDat1111a")
            const requestParameters = Object.assign({}, values)
            ApiSecuritycreate(requestParameters)
              .then((res) => {
                console.log("loadData", res)
                this.$emit("cancel")
                this.loading = false
                // 重置表单数据
                this.form.resetFields()
                // 刷新表格
                this.$emit("ok")
                this.$message.info("新增成功")
              })
              .finally(() => {
                this.departmentListLoading = false
              })
          }
        } else {
          this.loading = false
        }
      })
    }
  }
}
</script>

<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 多行文本控件
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '多行文本'"
      style="margin-bottom:unset"
      prop="multilinetext"
      :rules="[
        {
          required: data.notNull,
          message: '请输入',
          trigger: 'blur'
        }
      ]"
      ><a-input
        type="textarea"
        v-model="form.multilinetext"
        :placeholder="
          (data.placeholder && data.placeholder.placeholderText) || '请输入'
        "
      ></a-input>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          placeholder: {}
        };
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        multilinetext: null
      }
    };
  }
};
</script>
<style lang="less">
@import "../index.less";
</style>

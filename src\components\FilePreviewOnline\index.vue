<template>
  <div class="container">
    <a-modal
      title="在线预览"
      width="1200px"
      :visible="canDisplay"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="content">
        <div v-if="handleType == 'docx'">
          <vue-office-docx
            :src="docx_url"
            @rendered="renderedHandler"
            @error="errorHandler"
          />
        </div>
        <div v-else-if="handleType == 'pdf'">
          <vue-office-pdf
            :src="pdf_url"
            @rendered="renderedHandler"
            @error="errorHandler"
          />
        </div>
        <div
          v-else-if="
            handleType == 'png' || handleType == 'jpg' || handleType == 'jpeg'
          "
        >
          <img :src="img_url" class="preview-img" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
//引入VueOfficeDocx组件
import VueOfficeDocx from "@vue-office/docx";
//引入相关样式
import "@vue-office/docx/lib/index.css";
//引入VueOfficePdf组件
import VueOfficePdf from "@vue-office/pdf";
export default {
  components: {
    VueOfficeDocx,
    VueOfficePdf,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    url: {
      type: String,
      default: "",
    },
  },
  emits: ["previewCallback", "closeModal"],
  computed: {
    handleType() {
      const type = this.type.trim().toLowerCase();
      console.log(type, "~~~~");
      return type && this.typeList.includes(type) ? type : "";
    },
  },
  watch: {
    visible: {
      handler() {
        this.$nextTick(() => {
          console.log(this.visible, "visible~~~");
          this.$set(this, "canDisplay", this.visible);
        });
      },
      immediate: true,
      deep: true,
    },
    url: {
      handler() {
        this.$nextTick(() => {
          console.log("preview");
          const type = this.type.trim().toLowerCase();
          console.log(this.type, "type~~~");
          console.log(this.url, "url~~~");
          if (type && this.typeList.includes(type)) {
            if (type == "docx") {
              this.docx_url = this.url;
              console.log(this.docx_url, "url");
            } else if (type == "pdf") {
              this.pdf_url = this.url;
            } else if (type == "jpg" || type == "jpeg" || type == "png") {
              this.img_url = this.url;
            }
          }
        });
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      canDisplay: false,
      typeList: ["docx", "pdf", "jpg", "png", "jpeg"],
      docx_url: "",
      pdf_url: "",
      img_url: "",
    };
  },
  mounted() {
    console.log("mounted file preview");
  },
  methods: {
    renderedHandler: function () {
      console.log("渲染完成");
      this.$emit("previewCallback", "success");
      //暂时不提供预览，全部走下载
      // this.$emit("previewCallback", "failure");
    },
    errorHandler: function () {
      console.log("渲染失败");
      this.$emit("previewCallback", "failure");
    },
    handleCancel: function () {
      this.$emit("closeModal");
    },
  },
};
</script>

<style lang="less" scoped>
.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>

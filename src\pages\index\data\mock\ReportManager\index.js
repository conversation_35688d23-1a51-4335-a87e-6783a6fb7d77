import Mock from "mockjs"
//获取税收信息的数据的接口
const taxMessage = {
  code: 0,
  data: {
    records: [
      {
        id: 1,
        parkName: "星联企业园", //园区名称
        isLocal: 1, //是否属地
        taxAmount: 1000, //纳税金额
        componeyName: "金星科技"
      },
      {
        id: 2,
        parkName: "星联企业园", //园区名称
        isLocal: 2, //是否属地
        taxAmount: 1200, //纳税金额
        componeyName: "中国移动"
      },
      {
        id: 3,
        parkName: "星联企业园", //园区名称
        isLocal: 1, //是否属地
        taxAmount: 2000, //纳税金额
        componeyName: "长城股份"
      },
      {
        id: 4,
        parkName: "星联企业园", //园区名称
        isLocal: 2, //是否属地
        taxAmount: 2600, //纳税金额
        componeyName: "星光乐园"
      }
    ]
  },
  msg: null
}
Mock.mock("/Mock/reportManager/getTaxAnalysisReport", taxMessage)

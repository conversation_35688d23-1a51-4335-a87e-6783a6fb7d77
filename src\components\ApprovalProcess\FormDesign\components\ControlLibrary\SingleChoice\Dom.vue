<!--
* <AUTHOR>
* @time 2020-8-31
* @dec 单选控件 
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item
      :label="data.inputTitle || '单选'"
      style="margin-bottom:unset"
      prop="singleChoiceVal"
      :rules="[
        {
          required: data.notNull,
          message: '请选择',
          trigger: 'change'
        }
      ]"
    >
      <a-select
        :getPopupContainer="
          (triggerNode) => {
            return triggerNode.parentNode || document.body
          }
        "
        v-model="form.singleChoiceVal"
        :placeholder="data.placeholder.tipsTitleText || '请选择'"
      >
        <a-select-option
          v-for="(item, index) in data.optionsData"
          :key="index"
          :value="item.optionContent"
        >
          {{ item.optionContent }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          placeholder: ""
        }
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        singleChoiceVal: undefined
      }
    }
  }
}
</script>
<style lang="less">
@import "../index.less";
</style>

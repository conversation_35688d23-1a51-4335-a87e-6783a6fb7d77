<!--
* <AUTHOR>
* @time 2020-8-19
* @dec 系统管理 - 资源页面
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="8">
            <a-form-item label="资源名称">
              <a-input placeholder="请输入资源名称" v-model="queryParam.name" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="8">
            <a-form-item label="是否启用">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                placeholder="请选择是否启用"
                v-model="queryParam.enabled"
                @change="handEnabled"
              >
                <a-select-option value="false"> 是 </a-select-option>
                <a-select-option value="true"> 否 </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <template v-if="advanced">
            <a-col :md="8" :sm="8">
              <a-form-item label="链接地址">
                <a-input
                  style="width: 100%"
                  placeholder="请输入链接地址"
                  v-model="queryParam.resourceString"
                />
              </a-form-item>
            </a-col>
            <!-- <a-col :md="8" :sm="8">
              <a-form-item label="">
                <a-checkbox @change="handUsed" v-model="used">
                  只显示未分配的资源
                </a-checkbox>
              </a-form-item>
            </a-col> -->
          </template>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="() => (this.queryParam = {})"
                >重置</a-button
              >
              <a @click="toggleAdvanced" style="margin-left: 8px">
                {{ advanced ? "收起" : "展开" }}
                <a-icon :type="advanced ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="table-operator" v-if="$auth('mapi:security:resource:create')">
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd"
        v-auth="'mapi:security:resource:create'"
        >新建</a-button
      >
    </div>
    <s-table
      ref="table"
      size="default"
      rowKey="key"
      :columns="columns"
      :data="loadData"
    >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * 10 + index + 1 }}
      </span>
      <span slot="appType" slot-scope="text">
        {{
          $store.getters["dictionaries/getNameFromTypeCode"]({
            type: "APPTYPE",
            code: text,
          })
        }}
      </span>
      <span slot="description" slot-scope="text">
        <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
      </span>
      <span slot="disabled" slot-scope="text, record">
        <a-switch
          :checked="!record.disabled"
          @change="handleChangeDisabled(record.disabled, record)"
        />
      </span>
      <span slot="action" slot-scope="text, record">
        <template>
          <a
            @click="handleEdit(record)"
            v-auth="'mapi:security:resource:update'"
            >修改</a
          >
          <a-divider
            type="vertical"
            v-if="$auth('mapi:security:resource:delete')"
          />
          <a-popconfirm
            placement="bottomLeft"
            ok-text="确认"
            cancel-text="取消"
            v-auth="'mapi:security:resource:delete'"
            @confirm="handleDeleteOk(record)"
          >
            <template slot="title">
              <p>确认删除？</p>
            </template>
            <a>删除</a>
          </a-popconfirm>
        </template>
      </span>
    </s-table>

    <create-form
      ref="createModal"
      :visible="visible"
      :data="mdl"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table";
import Ellipsis from "@/components/Ellipsis";
// API接口
import {
  ApiSecurityDelete,
  ApiSecurityPageByCondition,
  ApiSecurityUpdateDisabled,
} from "@/pages/index/data/api/SystemManagement/Resources";
import CreateForm from "./compontents/CreateForm";
const columns = [
  {
    title: "序号",
    width: "100px",
    scopedSlots: { customRender: "serial" },
  },
  {
    title: "应用端",
    dataIndex: "appType",
    width: "100px",
    scopedSlots: { customRender: "appType" },
  },
  {
    title: "类型",
    width: "100px",
    dataIndex: "type",
    customRender: (text, row) => {
      return row.type == 'api' ? "接口" : "元素";
    },
  },
  {
    title: "资源名称",
    dataIndex: "name",
    scopedSlots: { customRender: "name" },
  },
  {
    title: "链接地址",
    width: "150px",
    dataIndex: "resourceString",
  },
  {
    title: "授权码",
    width: "150px",
    dataIndex: "permissionString",
  },
  {
    title: "是否启用",
    width: "150px",
    dataIndex: "disabled",
    scopedSlots: { customRender: "disabled" },
  },
  {
    title: "操作",
    dataIndex: "action",
    width: "150px",
    scopedSlots: { customRender: "action" },
  },
];

export default {
  name: "SystemManagementResources",
  components: {
    STable,
    Ellipsis,
    CreateForm,
  },
  data() {
    this.columns = columns;
    return {
      visible: false, //是否显示新增修改框
      mdl: null, //传入数据到新增
      // used: false, //显示
      // 查询参数
      // 高级搜索 展开/关闭
      advanced: false,
      queryParam: {
        currentPage: 1,
        name: "",
        enabled: undefined,
        // pageSize: "10",
        resourceString: "",
        // used: !this.used
      },
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        this.pageNo = parameter.pageNo;
        this.queryParam.currentPage = parameter.pageNo;
        this.queryParam.current = this.queryParam.currentPage;
        const requestParameters = Object.assign({}, parameter, this.queryParam);
        return ApiSecurityPageByCondition(requestParameters).then((res) => {
          return res.data;
        });
      },
      selectedRowKeys: [],
      selectedRows: [],
    };
  },
  methods: {
    //确认删除当前一列
    handleDeleteOk(record) {
      ApiSecurityDelete({ appType: record.appType, id: record.id })
        .then(() => {
          this.$refs.table.refresh(true);
          this.$message.info("删除成功");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 新增表单
    handleAdd() {
      this.mdl = null;
      this.visible = true;
    },
    // 修改表单
    handleEdit(record) {
      this.visible = true;
      this.mdl = { ...record };
    },
    // 是否启用表格调接口
    handleChangeDisabled(enable, record) {
      record.disabled = !enable;
      ApiSecurityUpdateDisabled({ disabled: !enable, id: record.id })
        .then(() => {
          if (enable) {
            this.$message.info("启用成功");
          } else {
            this.$message.info("禁用成功");
          }
        })
        .catch(() => {
          record.disabled = !record.disabled;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 未分配的资源
    // handUsed(e) {
    //   this.queryParam.used = !e.target.checked;
    // },
    // 是否启用搜索
    handEnabled(value) {
      this.queryParam.enabled = value;
    },
    // 取消表单弹框
    handleCancel() {
      this.visible = false;
      const form = this.$refs.createModal.form;
      form.resetFields(); // 清理表单数据（可不做）
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    // onSelectChange(selectedRowKeys, selectedRows) {
    //   this.selectedRowKeys = selectedRowKeys;
    //   this.selectedRows = selectedRows;
    // }
  },
};
</script>

/**
 * <AUTHOR>
 * @time 2020-8-14
 * @dec 表单验证
 */

/**
 * 表单验证
 */
import {
  // 姓名
  validName,
  // 手机号
  validateMobile,
  // 邮箱
  validateEmail,
  // 正整数
  validateInteger,
  // 大小写英文数字符号
  validatePressmissonStr
} from "./validate";

/**
 * 非空校验
 * @param  {Object} rule 规则对象
 * @param  {Number||String} value入参的值
 * @param  {Function} callback 回调函数返回值
 */
export function checkNull(rule, value, callback) {
  if (
    String(value).replace(/^\s+|\s+$/gm, "") === "" ||
    (value instanceof Array && value[0] === "") ||
    value === null ||
    value === undefined
  ) {
    callback(new Error(rule.message || "内容不能为空!"));
  } else {
    callback();
  }
}

/**
 * 姓名校验
 * @param  {Object} rule 规则对象
 * @param  {Number||String} value 入参的值
 * @param  {Function} callback 回调函数返回值
 * @dec 姓名不能在2，10未
 */
export function checkName(rule, value, callback) {
  if (value && !validName(value).status) {
    callback(new Error(rule.message || validName(value).msg));
  } else {
    callback();
  }
}

/**
 * 手机号校验
 * @param  {Object} rule 规则对象
 * @param  {Number||String} value 入参的值
 * @param  {Function} callback 回调函数返回值
 */
export function checkMobile(rule, value, callback) {
  if (value && !validateMobile(value).status) {
    callback(new Error(rule.message || validateMobile(value).msg));
  } else {
    callback();
  }
}

/**
 * 邮箱校验
 * @param  {Object} rule 规则对象
 * @param  {Number||String} value 入参的值
 * @param  {Function} callback 回调函数返回值
 */
export function checkEmail(rule, value, callback) {
  if (value && !validateEmail(value).status) {
    callback(new Error(rule.message || validateEmail(value).msg));
  } else {
    callback();
  }
}

/**
 * 正整数校验
 * @param  {Object} rule 规则对象
 * @param  {Number||String} value 入参的值
 * @param  {Function} callback 回调函数返回值
 */
export function checkPositiveNum(rule, value, callback) {
  if (value && !validateInteger(value).status) {
    callback(new Error(rule.message || validateInteger(value).msg));
  } else {
    callback();
  }
}

/**
 * 大小写英文数字符号校验
 * @param  {Object} rule 规则对象
 * @param  {Number||String} value 入参的值
 * @param  {Function} callback 回调函数返回值
 */
export function checkPressmissonStr(rule, value, callback) {
  if (value && !validatePressmissonStr(value).status) {
    callback(new Error(rule.message || validatePressmissonStr(value).msg));
  } else {
    callback();
  }
}

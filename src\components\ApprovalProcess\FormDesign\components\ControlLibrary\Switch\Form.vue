<!--
* <AUTHOR>
* @time 2020-9-3
* @dec 开关 -- 开关控件表单配置
-->
<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="标题">
      <a-input
        v-model="form.inputTitle"
        placeholder="申请主题"
        maxLength="20"
      />
    </a-form-model-item>
    <a-form-model-item label="提示文字">
      <a-input
        v-model="form.placeholder.placeholderText"
        placeholder="请输入"
        maxLength="50"
      />
    </a-form-model-item>
    <a-form-model-item label="开启提示">
      <a-input
        v-model="form.placeholder.openPlaceHolder"
        placeholder="请输入开启提示语"
      />
    </a-form-model-item>
    <a-form-model-item label="关闭提示">
      <a-input
        v-model="form.placeholder.closePlaceHolder"
        placeholder="请输入关闭提示语"
      />
    </a-form-model-item>
    <a-form-model-item label="是否必填">
      <a-switch v-model="form.notNull" />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        placeholder: {}
      }
    };
  },
  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  }
};
</script>

<!-- 载体查询 -->
<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <div class="carrier" v-else>
    <div class="carrierFrom">
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="园区名称">
              <a-input
                allowClear
                v-model="queryParam.parkName"
                placeholder="请输入园区名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="承租方">
              <a-input
                v-model="queryParam.tenantry"
                placeholder="请输入承租方名称"
              >
              </a-input>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="申请时间">
              <a-range-picker
                v-model="queryParam.requireTime"
                @change="changeRequireTime"
              ></a-range-picker>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="40" align="center">
          <!-- todo 镇领导角色药隐藏 -->
          <a-col :span="8">
            <a-form-item label="审核状态">
              <a-select
                :getPopupContainer="
                  (triggerNode) => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                allowClear
                v-model="queryParam.status"
                placeholder="全部"
              >
                <a-select-option
                  :value="item.value"
                  v-for="item in stateArr"
                  :key="item.value"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="16" align="right">
            <a-form-item :wrapper-col="{ span: 24 }">
              <a-button
                type="primary"
                @click="search"
                style="margin-right: 20px"
                >查询</a-button
              >
              <a-button type="default" @click="reset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-card style="width: 100%; margin-top: 20px">
      <div>
        <p
          style="
            font-size: 20px;
            color: #595959;
            font-weight: 600;
            line-height: 23px;
          "
        >
          备案登记审核查询列表
        </p>
      </div>
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        :rowKey="(record) => record.id"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </span>
        <span slot="parkName" slot-scope="text, record">
          <a href="javascript:;" @click="toDetail(text, record)">{{
            record.parkName
          }}</a>
        </span>
        <template slot="lessor" slot-scope="text">
          <a-tooltip>
            <template slot="title">
              {{ text }}
            </template>
            <div
              style="
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ text }}
            </div>
          </a-tooltip>
        </template>
        <template slot="createTime" slot-scope="text">
          {{ text }}
        </template>
        <template slot="operation" slot-scope="text, record">
          <a href="javascript:void(0);" @click="getStart(3, record)"> 查看 </a>
          <!-- status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成 -->
          <!-- 经发填报人 -->
          <template v-if="roles.includes('经发公司填报员')">
            <a
              href="javascript:void(0);"
              v-if="record.status === 1"
              @click="getStart(1, record)"
            >
              发起
            </a>
            <!-- <a v-if="record.status == 7" @click="toUpload(record)">上传合同</a> -->
            <a-popconfirm
              v-else-if="record.status === 6"
              title="是否发送通知？"
              @confirm="notify(record)"
            >
              <a href="javascript:void(0);"> 通知 </a>
            </a-popconfirm>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <!-- 一级审核员 -->
          <template v-else-if="roles.includes('经发公司审核员')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 2"
              @click="getStart(2, record)"
            >
              审核
            </a>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <!-- 二级审核员 -->
          <template v-else-if="roles.includes('经发公司复核员')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 3"
              @click="getStart(21, record)"
            >
              复核
            </a>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <!-- 镇领导 -->
          <template v-else-if="roles.includes('镇领导')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 5"
              @click="getStart(5, record)"
            >
              镇领导审核
            </a>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <!-- 镇领导复核 -->
          <template v-else-if="roles.includes('镇领导复核')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 12"
              @click="getStart(12, record)"
            >
              复核初审表
            </a>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <!-- 财务 -->
          <template v-else-if="roles.includes('财务')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 5"
              @click="getStart(5, record)"
            >
              审核流转表
            </a>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <!-- 经发上传初审表 -->
          <template v-else-if="roles.includes('经发上传初审表')">
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
            <!-- <a
              style="margin-left: 5px"
              v-if="canDownloadedPreliminary(record)"
              @click="downloadTrialTable(record)"
              >
                下载初审单
            </a> -->
            <!-- <a
              href="javascript:;"
              style="margin-left: 5px"
              v-if="record.status == 11"
              @click="toUpload(record)"
              >上传初审表</a
            > -->
            <!-- <a
              href="javascript:;"
              v-if="record.status == 4 && record.status != 8"
              style="margin-left: 5px"
              @click="downloadFlow(record)"
              >
                生成流转表
            </a> -->
            <!-- <a
              style="margin-left: 5px"
              v-if="record.status == 4"
              @click="toUpload(record)"
              >上传流转表</a
            > -->
            <a v-if="record.status == 7" @click="toUpload(record)">上传合同</a>
          </template>
          <template v-else-if="roles.includes('镇营商服务联席会')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 13"
              @click="getStart(13, record)"
            >
              上传会议纪要
            </a>
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
          </template>
          <template v-else-if="roles.includes('上传会议材料岗')">
            <a
              href="javascript:void(0);"
              v-if="record.status == 14"
              @click="getStart(14, record)"
            >
              上传会议材料
            </a>
          </template>
          <template v-else-if="roles.includes('资料归档岗')">
            <!-- <a href="javascript:void(0);" @click="getStart(3, record)">
              查看
            </a> -->
            <a-popconfirm
              v-if="record.status == 8"
              title="是否驳回该合同？"
              @confirm="rejectEvent(record)"
            >
              <a href="javascript:void(0);">驳回</a>
            </a-popconfirm>
          </template>
          <template>
            <a
              style="margin-left: 5px"
              v-if="canDownloadedPreliminary(record)"
              @click="downloadPreliminary(record)"
            >
              下载初审单
            </a>
          </template>
          <template>
            <a
              href="javascript:;"
              style="margin-left: 5px"
              v-if="canDownloadFlowTable(record)"
              @click="downloadFlow(record)"
            >
              下载流转表
            </a>
          </template>
        </template>
      </s-table>
    </a-card>
    <a-modal
      :title="
        roles.includes('镇营商服务联席会') ? '上传会议纪要' : '审核初审表'
      "
      :visible="workflowVisible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <!-- <a
        :href="downloadUrl"
        :download="attachment.newName"
        style="margin-left: 26px;"
        >下载查看流转表内容</a
      > -->

      <!-- <div @click="previewOnline(attachment)" class="attachmentItem">
        下载查看初审表
      </div> -->
      <div @click="downloadTrialTable" class="attachmentItem">
        下载查看初审表
      </div>
      <a-form :form="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-form-item
          label="初审意见："
          v-if="
            roles.includes('镇领导复核') || roles.includes('镇营商服务联席会')
          "
        >
          <p>
            {{ inputValue.length > 0 && inputValue[0].approver }}
            {{ inputValue.length > 0 && inputValue[0].approvalTime }} 通过
          </p>
        </a-form-item>
        <a-form-item
          label="经发公司意见："
          v-if="inputValue.length > 1 && roles.includes('镇营商服务联席会')"
        >
          <p>
            {{ inputValue[1].approver }}
            {{ inputValue[1].approvalTime }} 通过
          </p>
        </a-form-item>
        <a-form-item
          label="审核意见"
          v-if="!roles.includes('镇营商服务联席会')"
        >
          <a-radio-group
            v-decorator="[
              'operateType',
              {
                initialValue: 'submit',
                rules: [{ required: true, message: '请选择审核意见!' }],
              },
            ]"
          >
            <a-radio value="submit"> 通过 </a-radio>
            <a-radio value="reject"> 驳回 </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="roles.includes('镇营商服务联席会')">
          <div @click="uploadResolution" class="attachmentItem">
            上传会议纪要
          </div>
          <!-- <div>
            <FileAttachmentList
              title="决议文件"
              marked="true"
              :ifNeedPreviewOnline="true"
              @deleteFile="
                (file, fileList) => deleteFile(file, fileList)
              "
              :fileList="fileList"
            />
          </div> -->
        </a-form-item>
        <a-form-item
          label="审核意见"
          v-if="roles.includes('镇领导') || roles.includes('镇领导复核')"
        >
          <a-input placeholder="拟同意" v-model="approvalComments"></a-input>
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 引入上传文件的组件 -->
    <!-- ok、cancel都是为了接收子组件传过来的值并做处理的方法，visible、title都是父组件传给子组件的值 -->
    <!-- status=14,上传会议材料必填，仅支持pdf格式文件  -->
    <UploadModal
      v-if="this.current.status == 14"
      :visible="uploadVisible"
      accept=".pdf"
      @ok="uploadModalOk"
      @cancel="unloadCancel"
      :businessId="businessId"
      :title="title"
    >
    </UploadModal>
    <!-- <UploadModal
      v-else-if="this.current.status == 13"
      :visible="uploadVisible"
      accept=".pdf"
      @ok="uploadModalOk"
      @cancel="unloadCancel"
      :businessId="businessId"
      :title="title"
    >
      <div style="margin-bottom: 24px">
        <a-button type="primary" @click="download14" style="margin-right: 20px"
          >下载上传会议材料</a-button
        >
      </div>
    </UploadModal> -->
    <UploadModal
      v-else
      :visible="uploadVisible"
      @ok="uploadModalOk"
      @cancel="unloadCancel"
      :businessId="businessId"
      :title="title"
    >
    </UploadModal>
    <file-preview-online
      :visible="previewOnlineInfo.visible"
      :type="previewOnlineInfo.type"
      :url="previewOnlineInfo.url"
      @closeModal="handleFilePreviewOnlineModalWithClosing"
      @previewCallback="previewCallback"
    />
    <!-- 下载初审表，modalVisible和businessInfo传给子组件的变量 -->
    <preliminary-form
      v-if="preliminaryVisible"
      :modalVisible="preliminaryVisible"
      :businessInfo="preliminaryInfo"
      @closeModal="handlePreliminaryModal"
    ></preliminary-form>
    <flow-form
      v-if="flowVisible"
      :modalVisible="flowVisible"
      :businessInfo="flowInfo"
      @closeModal="handleFlowModal"
    ></flow-form>
  </div>
</template>

<script>
import FilePreviewOnline from "@/components/FilePreviewOnline";
import {
  ApiGetRecordReviewpageByCondition,
  ApiEditRecordReview,
  ApiUpdateStatusByAttachment,
  ApiEconomicCompanyAuditReject,
} from "@/pages/index/data/api/RegistrationRecordReview";
import {
  ApiGetBuildFloorNumberByPark,
  ApiSearchAllMergePark,
  ApiFindParkNamesByMergedName,
} from "@/pages/index/data/api/InfomationQuery";
// import { ApiGetApprovalRecords } from "@/pages/index/data/api/RegistrationRecordReview/Trial";
import STable from "@/components/Table";
// import FileAttachmentList from "@/components/FileAttachmentList"
// import Importer from "@/components/Importer"
import UploadModal from "./components/UploadModal";
import PreliminaryForm from "@/pages/common/views/Template/PreliminaryForm.vue";
import { BASE_URL } from "Config";
import FlowForm from "@/pages/common/views/Template/FlowTable.vue";
export default {
  components: {
    STable,
    // FileAttachmentList,
    // Importer,
    FlowForm,
    UploadModal,
    FilePreviewOnline,
    PreliminaryForm,
  },
  computed: {},
  data() {
    return {
      showEditSuccessInfo: true,
      uploadVisible1: false,
      flowVisible: false,
      flowInfo: {
        filingNumber: "",
      },
      title: "",
      inputValue: [],
      downloadUrl: "",
      uploadVisible: false,
      businessId: "", //review_completed_contract  审核完成合同
      attachment: {},
      current: {},
      roles: JSON.parse(localStorage.getItem("USER_KEY")).roles, //如果是镇领导 ,1级人员，2级审核人员，3级审核人员
      form: this.$form.createForm(this, { name: "coordinated" }),
      preliminaryVisible: false, //下载初审表的标识，默认不展示
      preliminaryInfo: {
        //下载初审表的时候传给后端的东西
        filingNumber: "",
      },
      fileList: [], //上传会议纪要附件列表
      isFlag: true,
      workflowVisible: false, //show or hide 流转表
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: "60px",
          fixed: "left",
          align: "center",
        },
        {
          title: "园区",
          dataIndex: "parkName",
          scopedSlots: { customRender: "parkName" },
          width: 220,
          fixed: "left",
          align: "center",
        },
        {
          title: "出租方",
          dataIndex: "lessor",
          width: 200,
          align: "center",
          scopedSlots: { customRender: "lessor" },
        },
        {
          title: "租赁面积（㎡）",
          dataIndex: "leaseArea",
          width: 130,
          align: "center",
        },
        {
          title: "承租方",
          dataIndex: "tenantry",
          align: "center",
          width: 220,
          scopedSlots: { customRender: "tenantry" },
        },
        {
          title: "合同类型",
          dataIndex: "contractTypeDesc",
          width: 100,
          align: "center",
        },
        {
          title: "申请时间",
          dataIndex: "createTime",
          width: 200,
          align: "center",
          scopedSlots: { customRender: "createTime" },
        },
        {
          title: "审核方式",
          dataIndex: "auditTypeDesc",
          align: "center",
          width: 160,
        },
        // status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成

        {
          title: "审核状态",
          dataIndex: "statusDesc",
          width: 160,
          align: "center",
        },
        {
          title: "操作",
          dataIndex: "operation",
          width: 150,
          align: "center",
          scopedSlots: { customRender: "operation" },
        },
      ],
      timeList: [],
      leaseEndTime: "",
      queryParam: {},
      labelCol: { span: 4 },
      parkArr: [
        {
          desc: "200平米以内",
          value: "0",
        },
        {
          desc: "201平米-500平米",
          value: "1",
        },
        {
          desc: "201平米-500平米",
          value: "2",
        },
        {
          desc: "201平米-500平米",
          value: "3",
        },
        {
          desc: "501平米-800平米",
          value: "4",
        },
        {
          desc: "800平米-1000平米",
          value: "4",
        },
        {
          desc: "1000平米以上",
          value: "4",
        },
      ],
      // // status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
      stateArr: [
        {
          name: "全部",
          value: "",
        },
        {
          name: "起草",
          value: 0,
        },
        {
          name: "发起",
          value: 1,
        },
        {
          name: "初审",
          value: 2,
        },
        {
          name: "复核",
          value: 3,
        },
        // {
        //   name: "上传流转表",
        //   value: 4,
        // },
        {
          name: "镇领导审核",
          value: 5,
        },
        {
          name: "上传合同",
          value: 7,
        },
        {
          name: "完成",
          value: 8,
        },
        {
          name: "经发驳回",
          value: 9,
        },
        // {
        //   name: "上传初审表",
        //   value: 11,
        // },
        {
          name: "镇领导复核",
          value: 12,
        },
        {
          name: "上传会议纪要",
          value: 13,
        },
        {
          name: "上传会议材料",
          value: 14,
        },
      ],
      enumerateObj: {
        buildingNums: [],
        parkList: [],
      }, // 查询条件对象
      previewOnlineInfo: {
        visible: false,
        url: "",
        type: "",
      },
      ifNeedPreviewOnline: true, //预留口，
      fnViewCalled: false, // 添加一个标志变量，表示fnView是否已经被调用
      domain_url: "",
      approvalComments: null, //镇领导审核意见
      rejectVisible: false, //归档员驳回modal是否可见
      rejectRecord: {}, //当前归档员驳回modal信息
    };
  },
  created() {
    if (this.$route.query.record) {
      let record = this.$route.query.record;
      if (record.status == 13) {
        this.form.setFieldsValue({
          id: record.id,
          filingNumber: record.filingNumber,
        });
        this.current = record;
        this.uploadResolution();
      } else if (record.status == 14) {
        this.current = record;
        this.uploadResolutionMaterial();
      }
    }
    let tmpDomain_url = BASE_URL;
    this.$set(this, "domain_url", tmpDomain_url);
    this.$set(
      this,
      "downloadUrl",
      `${tmpDomain_url}/common/attachment/download`
    );
    // this.init()
    if (this.$route.meta.level != 3) {
      this.getRoomArea();
    }
  },
  methods: {
    canDownloadedPreliminary: function (record) {
      let canDownload = false;
      //短流程
      if (record.auditType == 0) {
        if (record.status == 7 || record.status == 8) {
          canDownload = true;
        }
        //长流程
      } else if (record.auditType == 1) {
        if (
          record.status == 5 ||
          record.status == 12 ||
          record.status == 7 ||
          record.status == 8
        ) {
          canDownload = true;
        }
        //超长流程
      } else if (record.auditType == 2) {
        if (
          record.status == 5 ||
          record.status == 12 ||
          record.status == 13 ||
          record.status == 14 ||
          record.status == 7 ||
          record.status == 8
        ) {
          canDownload = true;
        }
      }
      return canDownload;
    },
    downloadPreliminary: function (record) {
      this.current = record;
      this.downloadTrialTable(record);
    },
    canDownloadFlowTable: function (record) {
      let canDownload = false;
      //长流程/超长流程
      if (record.auditType == 1 || record.auditType == 2) {
        if (record.status == 7 || record.status == 8) {
          canDownload = true;
        }
      }
      return canDownload;
    },
    //关闭初审表弹框的方法
    handlePreliminaryModal: function () {
      this.$set(this, "preliminaryInfo", {});
      this.$set(this, "preliminaryVisible", false);
    },
    //下载初审表的方法
    downloadTrialTable: function () {
      this.$set(this, "preliminaryVisible", true);
      this.$set(this, "preliminaryInfo", {
        filingNumber: this.current.filingNumber,
      });
    },
    downloadFlow: function (record) {
      this.$set(this, "flowVisible", true);
      this.$set(this, "flowInfo", { filingNumber: record.filingNumber });
    },
    handleFlowModal: function () {
      this.$set(this, "flowInfo", {});
      this.$set(this, "flowVisible", false);
    },
    //上传会议纪要弹窗
    uploadResolution() {
      this.title = "上传会议纪要";
      this.businessId = "upload_resolution";
      this.uploadVisible = true;
    },
    //上传会议材料弹窗
    uploadResolutionMaterial() {
      this.title = "上传会议材料";
      this.businessId = "meeting_materials";
      this.uploadVisible = true;
    },
    toUpload(record) {
      if (record.status == 11) {
        this.title = "上传初审表";
        this.businessId = "preliminary_form";
      } else if (record.status == 7) {
        this.title = "上传合同";
        this.businessId = "review_completed_contract";
      } else {
        this.title = "上传流转表";
        this.businessId = "workflow_form";
      }
      console.log(record, "yyyy1");
      this.current = record;
      this.uploadVisible = true;
    },
    //fileList是子组件传递过来的文件数据
    uploadModalOk(fileList) {
      this.modelValue = false;
      //校验一下只有上传文件后才能够进行下一步
      if (fileList.length !== 0) {
        this.handleFileCallback(fileList, this.current);
      } else {
        if (this.current.status == 7) {
          this.$message.error("请先上传合同");
        } else if (this.current.status == 13) {
          this.$message.error("请先上传会议纪要");
        } else if (this.current.status == 14) {
          this.$message.error("请先上传会议材料");
        }
      }
    },
    unloadCancel() {
      this.uploadVisible = false;
    },
    handleFileCallback(file, record) {
      if (this.isFlag) {
        this.isFlag = false;
        let params = {
          id: record.id,
          status: record.status == 7 ? 8 : record.status,
          attachmentList: file,
        };
        ApiUpdateStatusByAttachment(params).then((resp) => {
          if (resp.code === 0) {
            this.$message.success("上传成功！");
            // this.$set(this, "fileList", file);
            let params = {
              id: this.current.id,
              filingNumber: this.current.filingNumber,
              operateType: "submit",
              status: this.current.status,
            };
            this.showEditSuccessInfo = false; //只展示当前接口‘上传成功！’的提示
            this.fnView(params);
            this.fnViewCalled = true; // 设置标志变量为true，表示fnView已经被调用
            this.workflowVisible = false;
            // this.$refs.table.refresh(true);
            this.unloadCancel();
          } else {
            this.$message.error(resp.data.msg || "上传失败");
          }
        });
        setTimeout(() => {
          this.isFlag = true;
        }, 2000);
      }
    },
    async fnView(params) {
      let resp = await ApiEditRecordReview(params);
      if (resp.code === 0) {
        if (this.showEditSuccessInfo) {
          this.$message.success("操作成功");
        }
        this.$refs.table.refresh(true);
      }
      this.showEditSuccessInfo = true;
      console.log(resp, "resp");
    },
    changeRequireTime(value) {
      console.log(value, "value");
      this.queryParam.createTimeStart = "";
      this.queryParam.createTimeEnd = "";
      this.queryParam.createTimeStart =
        value && value[0] ? `${value[0].format("YYYY-MM-DD")} 00:00:00` : null;
      this.queryParam.createTimeEnd =
        value && value[0] ? `${value[1].format("YYYY-MM-DD")} 23:59:59` : null;
    },
    getApprovalComments(text) {
      console.log("getApprovalComments~~~text ", text);
      let hasComment = this.approvalComments ? true : false;
      if (hasComment) {
        return this.approvalComments;
      } else {
        let tmp = text;
        return tmp == "submit" ? "同意" : "不同意";
      }
    },
    //审核流转表通过
    handleOk() {
      if (this.fnViewCalled) {
        // 如果fnView已经被调用过，直接返回，不再执行后续逻辑
        return;
      }
      this.form.validateFields((err, values) => {
        if (!err) {
          //提交数据
          // console.info("success", values);
          // let temp = values["operateType"];
          console.log(this.current.status, "this.current.status ");
          let params = {
            id: this.current.id,
            filingNumber: this.current.filingNumber,
            operateType: "submit",
            superiorsApproval: this.current.status == 13 ? "submit" : "reject",
            ...values,
            status: this.current.status,
            approvalComments: this.getApprovalComments(values["operateType"]),
            superiorsApprovalOption: this.current.status == 13 ? 0 : "1", //是否需要上级领导审批 0：否 1：是
          };
          this.fnView(params);
          this.fnViewCalled = true; // 设置标志变量为true，表示fnView已经被调用
          this.workflowVisible = false;
        } else {
          console.log(values, "values");
        }
      });
    },
    //审核流转表驳回
    handleCancel() {
      this.workflowVisible = false;
    },
    fnShowWorkFlow(record) {
      // if (record.status == 5 && !record.preliminaryAttachment) {
      //   this.$message.error("初审表表数据为空,请联系管理员");
      //   return;
      // } else if (record.status == 12 && !record.attachment) {
      //   this.$message.error("流转表数据为空,请联系管理员");
      //   return;
      // }
      // if (record.status == 5) {
      //   this.downloadUrl = `${this.downloadUrl}?id=${record.preliminaryAttachment.id}`;
      //   this.attachment = record.preliminaryAttachment;
      // } else if (record.status == 12) {
      //   this.downloadUrl = `${this.downloadUrl}?id=${record.attachment.id}`;
      //   this.attachment = record.attachment;
      // }
      this.current = record;
      this.workflowVisible = true;
    },
    getStart(type, record) {
      console.log(record);
      if (type == 1) {
        // 发起
        this.$router.push({
          path: "/registration-record-review/record/reviewItem",
          query: {
            floorRoomId: record.floorRoomId,
            type: 1, //// status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
            filingNumber: record.filingNumber,
            id: record.id,
          },
        });
      } else if (type == 2) {
        //审核
        // 发起
        this.$router.push({
          path: "/registration-record-review/record/trial",
          query: {
            viewType: "check", //view 查看，check 或者空为操作页面否则只是查看
            status: 2, ////// status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
            filingNumber: record.filingNumber,
            id: record.id,
          },
        });
      } else if (type == 3) {
        console.log("查看");
        this.$router.push({
          path: "/registration-record-review/record/trial",
          query: {
            viewType: "view", //view 查看，check 或者空为操作页面否则只是查看
            status: record.status, //// status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
            filingNumber: record.filingNumber,
            id: record.id,
          },
        });
      } else if (type == 5) {
        // 镇领导审核
        // this.form.setFieldsValue({
        //   id: record.id,
        //   filingNumber: record.filingNumber,
        // });
        this.$router.push({
          path: "/registration-record-review/record/trial",
          query: {
            viewType: "check", //view 查看，check 或者空为操作页面否则只是查看
            status: record.status, //// status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
            filingNumber: record.filingNumber,
            id: record.id,
          },
        });
        // this.fnShowWorkFlow(record);
      } else if (type == 12) {
        // 镇领导复核
        // this.form.setFieldsValue({
        //   id: record.id,
        //   filingNumber: record.filingNumber,
        // });
        this.$router.push({
          path: "/registration-record-review/record/trial",
          query: {
            viewType: "check", //view 查看，check 或者空为操作页面否则只是查看
            status: record.status, //// status(value = "状态 0:起草 1:发起 2:初审 3:复核 4:上传流转表 5:镇领导审核流转表 6:经发公司审核流转表 7:经发上传合同 8:完成
            filingNumber: record.filingNumber,
            id: record.id,
          },
        });
        // ApiGetApprovalRecords({ contractInfoId: record.id }).then(res => {
        //   this.inputValue.push(res.data[0]);
        // });
        // this.fnShowWorkFlow(record);
      } else if (type == 21) {
        // 复核
        this.$router.push({
          path: "/registration-record-review/record/trial",
          query: {
            viewType: "check", //view 查看，check 或者空为操作页面否则只是查看
            status: 3, ////状态 0:起草 1:发起 2:初审 3:复核 4:合同上传 5:完成
            filingNumber: record.filingNumber,
            id: record.id,
          },
        });
      } else if (type == 6) {
        // 经发终审流转表
        this.form.setFieldsValue({
          id: record.id,
          filingNumber: record.filingNumber,
        });
        this.fnShowWorkFlow(record);
      } else if (type == 13) {
        //镇营商服务联席会
        this.form.setFieldsValue({
          id: record.id,
          filingNumber: record.filingNumber,
        });
        // ApiGetApprovalRecords({ contractInfoId: record.id }).then(res => {
        //   this.inputValue.push(res.data[1]);
        //   this.inputValue.push(res.data[0]);
        // });
        this.current = record;
        // this.fnShowWorkFlow(record);
        this.uploadResolution();
      } else if (type == 14) {
        //上传会议材料岗
        this.current = record;
        this.uploadResolutionMaterial();
      }
    },
    download14() {
      console.log(this.current.meetingMaterialsAttachment.id);
      this.ifNeedPreviewOnline = false; //关闭文件预览
      this.previewOnline(this.current.meetingMaterialsAttachment);
    },
    // rejectCancel() {
    //   this.$set(this, "rejectRecord", {});
    //   this.$set(this, "rejectVisible", false);
    // },
    // rejectAction(record) {
    //   this.$set(this, "rejectRecord", record);
    //   this.$set(this, "rejectVisible", true);
    // },
    //归档员驳回合同
    rejectEvent(record) {
      const _this = this;
      const params = {
        filingNumber: record.filingNumber,
        status: record.status,
      };
      ApiEconomicCompanyAuditReject(params).then((res) => {
        if (res.code == 0) {
          _this.$message.info("成功驳回");
          _this.$refs.table.refresh(true);
        } else {
          _this.$message.info("未成功驳回");
        }
      });
    },
    notify: function (record) {
      // 经发终审流转表
      this.form.setFieldsValue({
        id: record.id,
        filingNumber: record.filingNumber,
        operateType: "submit",
      });
      this.downloadUrl = `${this.downloadUrl}?id=${record.attachment.id}`;
      this.attachment = record.attachment;
      this.current = record;
      this.handleOk();
    },
    // 加载数据方法 必须为 Promise 对象
    async loadData({ pageNo, pageSize }) {
      console.log("pageNo", pageNo, "pageSize", pageSize, "loadData");
      this.pageNo = pageNo;
      this.pageSize = pageSize;
      let queryParam = Object.assign({}, this.queryParam, {
        currentPage: pageNo,
        pageSize: pageSize,
      });
      return ApiGetRecordReviewpageByCondition(queryParam).then((res) => {
        let dataObj = res.data;
        dataObj.data = res.data.records;
        dataObj.totalCount = res.data.total;
        dataObj.pageSize = res.data.size;
        dataObj.pageNo = res.data.current;
        return dataObj;
      });
    },
    changeParkName(value) {
      this.queryParam.buildingNumber = undefined;
      if (!value) {
        return;
      }
      ApiGetBuildFloorNumberByPark({
        parkName: value || this.queryParam.parkName,
      }).then((res) => {
        this.enumerateObj.buildingNums = res.data.buildingNumbers;
      });
    },
    reset() {
      this.queryParam = {
        parkName: "",
        pageNum: 1,
        pageSize: 10,
        mergedParkName: this.queryParam.mergedParkName,
      };
      this.leaseEndTime = [];
      this.$refs.table.refresh(true);
    },
    async getRoomArea() {
      this.parkArr = await this.$getDictByType({ dictCode: "roomArea" });
    },

    async init() {
      if (this.queryParam.mergedParkName) {
        return;
      }
      let industryComponey = await ApiSearchAllMergePark();
      industryComponey.data = industryComponey.data.filter(
        (item) => item != null
      );
      this.enumerateObj.industryComponey = industryComponey.data.map(
        (item) => item
      );
      this.queryParam.industryComponey = industryComponey.data[0];
      ApiFindParkNamesByMergedName({
        mergedParkName: industryComponey.data[0],
      }).then((res) => {
        console.log(res, "**园区名称**");
        this.enumerateObj.parkList = res.data;
        this.queryParam.parkName = res.data[0];
        if (!res.data || res.data.length == 0) return;
        ApiGetBuildFloorNumberByPark({
          parkName: res.data[0] || this.queryParam.parkName,
        }).then((res) => {
          this.enumerateObj.buildingNums = res.data.buildingNumbers;
        });
      });
    },
    search() {
      console.log(this.queryParam);
      this.$refs.table.refresh(true);
    },
    toDetail(text, record) {
      this.$router.push(
        `/registration-record-inquiry/query/detail?statu=1&lessor=` +
          record.lessor +
          `&id=` +
          record.id
      );
    },
    toCompany(text, record) {
      console.log(record, "record");
      if (record.id) {
        this.$router.push(
          `/information-query/enterprise-detail?id=${record.id}`
        );
      }
    },
    previewOnline: function (file) {
      console.log(file, "~~~~!@#");
      const { id = "" } = file;
      if (!id) {
        this.$message.error("未获取到文件信息");
        return;
      }
      // const fileUrl = BASE_URL + "/common/attachment/download?id=" + fileId;
      const url = `${this.domain_url}/common/attachment/download?id=${id}`;

      if (this.ifNeedPreviewOnline) {
        //当前近支持.docx|.pdf格式
        if (file.fileType == "docx" || file.fileType == "pdf") {
          this.$set(this.previewOnlineInfo, "visible", true);
          this.$set(this.previewOnlineInfo, "url", url);
          this.$set(this.previewOnlineInfo, "type", file.fileType);
        } else {
          window.open(url);
        }
      } else {
        window.open(url);
      }
    },
    handleFilePreviewOnlineModalWithClosing: function () {
      this.$set(this.previewOnlineInfo, "visible", false);
      this.$set(this.previewOnlineInfo, "url", "");
      this.$set(this.previewOnlineInfo, "type", "");
    },
    previewCallback: function (flag) {
      if (flag == "success") {
        console.log(flag);
      } else {
        console.log(flag);
        this.$message.info("在线预览加载失败，即将为您下载文件至本地预览！");
        window.open(this.previewOnlineInfo.url);
        this.handleFilePreviewOnlineModalWithClosing();
      }
    },
    //删除已上传会议纪要文件
    deleteFile: function (file, fileList) {
      console.log(file, "deleteFile", fileList);
      this.$set(this, "fileList", fileList);
    },
  },
};
</script>

<style lang="less" scoped>
.carrier {
  display: flex;
  flex-wrap: wrap;
  margin-top: 15px;

  .carrierFrom {
    width: 100%;
    border-width: 0px;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
  }

  .tablePart {
    margin-top: 30px;
    width: 100%;
    height: 150px;
    border-width: 0px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 2px;
    display: flex;
    flex-wrap: wrap;

    .sort {
      margin-left: auto;

      .select {
        color: rgba(19, 194, 194);
        margin-top: 5px;
        margin-right: 5px;
        width: 130px;
      }
    }
  }

  .table {
    width: 100%;
    margin-top: 10px;
  }
}
.attachmentItem {
  color: #1777ff;
  cursor: pointer;
  margin-bottom: 20px;
}
</style>

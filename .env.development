###
 # @Author: 王正勇 <EMAIL>
 # @Date: 2025-03-31 16:12:54
 # @LastEditors: 王正勇 <EMAIL>
 # @LastEditTime: 2025-05-08 16:42:38
 # @FilePath: \zhys-admin\.env.development
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
// 开发 环境配置
NODE_ENV = 'development'
BASE_URL ="/"

// 缓存设置
// 用户数据缓存key
VUE_APP_USER_KEY = 'USER_KEY'
// 权限数据缓存key
VUE_APP_PERMISSIONS_KEY = 'PERMISSIONS_KEY'
// 角色数据缓存key
VUE_APP_ROLES_KEY = 'ROLES_KEY'
// 路由数据缓存key
VUE_APP_ROUTES_KEY = 'ROUTES_KEY'
// 字典数据缓存key
VUE_APP_DICT_KEY = 'DICT_KEY'

// API地址 
# VUE_APP_MOCK_HOST = 'http://**************:8008/admin'
// sit
# //VUE_APP_MOCK_HOST = 'http://*************:630/mapi'
VUE_APP_MOCK_HOST = 'http://*************:8009/mapi'
#验证信息
#VUE_APP_MOCK_HOST = 'http://*************:8009/mapi'

VUE_APP_MOCK_HOST_DEV=true
# 1开发环境，2uat环境,3生产环境
VUE_APP_ENVIRONMENT_MODE = 1
<template>
  <div class="common-table">
    <a-table
      :columns="innerColumns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
    >
    </a-table>
  </div>
</template>

<script>
export default {
  props: {
    columns: {
      type: Array,
      required: true,
    },
    loadData: {
      type: Function,
      required: true,
    },
    showPagination: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {
      dataSource: [],
      loading: false,
      pagination: {
        showSizeChanger: true,
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共有 ${total} 条数据`,
        onChange: this.onPageChange,
        onShowSizeChange: this.onPageSizeChange,
      },
      slotKeys: [],
    };
  },
  computed: {
    innerColumns() {
      return this.columns.map((col) => {
        const slotName = col.scopedSlots && Object.keys(col.scopedSlots)[0];
        console.log(col.scopedSlots, "~~~~!!!!");
        console.log(col.scopedSlots && Object.keys(col.scopedSlots), "keys~~");
        // console.log(Object.keys(col.scopedSlots));
        console.log(slotName, "~~~2@#$%^");
        // console.log(col.scopedSlots && col.scopedSlots[slotName], "当前列字段");
        const colSlotName = col.scopedSlots && col.scopedSlots[slotName];
        console.log(colSlotName, "当前列字段");
        console.log(colSlotName && this.$scopedSlots[colSlotName]);
        console.log("custom-render~~~~");
        console.log("first");
        return {
          ...col,
          customRender:
            slotName &&
            ((text, record) =>
              colSlotName &&
              colSlotName == record[colSlotName] &&
              this.$scopedSlots[colSlotName](text)),
        };
      });
    },
  },
  watch: {
    showPagination: {
      handler() {
        this.$nextTick(() => {
          if (!this.showPagination) {
            this.$set(this, "pagination", false);
          }
        });
      },
      immediate: true,
      deep: true,
    },
    $scopedSlots: {
      handler() {
        this.$nextTick(() => {
          console.log(this.$scopedSlots["type"], "~!@#");
          console.log(Object.keys(this.$scopedSlots), "this.$slots.keys~~~");
          this.$set(this, "slotKeys", Object.keys(this.$scopedSlots));
        });
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.onLoad();
  },
  methods: {
    onLoad: function(queryParams) {
      if (this.loadData) {
        this.loading = true;
        let data = this.loadData({
          ...queryParams,
          pageNo: this.showPagination ? this.pagination.current : 1, //这里如果不需要分页器显示，默认no为1，临时处理
          pageSize: this.showPagination ? this.pagination.pageSize : 10, //这里如果不需要分页器显示，默认size为10，临时处理
        });
        data
          .then((result) => {
            this.$set(this, "dataSource", result.records || []);
            if (this.showPagination) {
              const pager = { ...this.pagination };
              pager.total = result.total || 0;
              this.pagination = pager;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        console.log("没有配置数据源");
      }
    },
    onPageChange: function(current, size) {
      console.log(current, size);
      const pager = { ...this.pagination };
      pager.current = current;
      this.pagination = pager;
      console.log(this.pagination, "no");
      this.onLoad();
    },
    onPageSizeChange: function(current, size) {
      console.log(current, size);
      const pager = { ...this.pagination };
      pager.current = 1;
      pager.pageSize = size;
      this.pagination = pager;
      console.log(this.pagination, "size");
      this.onLoad();
    },
    search: function(searchParams) {
      console.log(searchParams, "查询参数");
      //点击查询时先重新分页pageNo
      if (this.showPagination) {
        const pager = { ...this.pagination };
        pager.current = 1;
        this.pagination = pager;
      }
      this.onLoad(searchParams);
    },
  },
};
</script>

<style>
.common-table {
  margin: 20px;
}
</style>

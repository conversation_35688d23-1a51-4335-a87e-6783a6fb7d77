<template>
  <div>
    <!-- <div class="fileInfoTitle">相关文件</div> -->
    <my-upload
      :key="getUploadKey"
      :businessAccept="businessAccept"
      :uploadInfo="uploadInfo"
      :businessInfo="businessInfo"
      :canUpload="businessCanUpload"
      :canPreview="businessCanPreview"
      :pageState="businessPageState"
      :businessLabel="businessLabel"
      :labelRequired="labelRequired"
      :archiveFileList="archiveFileList"
      @handleFileListCallback="onFileListCallback"
    />
  </div>
</template>

<script>
import MyUpload from "./component/MyUpload";
// import { getUploadInfo } from "@/common/api/common/upload";
export default {
  props: {
    businessAccept: {
      type: String,
      required: false,
    }, //自定义模块accept文件格式字符串，如：".pdf,.png,.jpg,.jpeg,.doc,.docx,.xls,.xlsx",非必填
    businessInfo: {
      type: Object,
      required: false,
    }, //业务对象，暂定必包含业务id，id:"", 属性非必填
    businessCanUpload: {
      type: Boolean,
      required: false,
    }, //是否可以上传, 属性非必填，默认可上传
    businessCanPreview: {
      type: Boolean,
      required: false,
    }, //是否可以预览, 属性非必填,默认可预览
    businessPageState: {
      type: Number,
      required: true,
    }, //0新增，1编辑，2查看, 属性必填
    businessLabel: {
      type: String,
      required: true,
    },
    labelRequired: {
      type: Boolean,
      required: false,
    }, //businessLabel是否必填项
    archiveFileList: {
      type: Array,
      required: true,
    }, //回显列表
  },
  components: {
    MyUpload,
  },
  computed: {
    getUploadKey() {
      return Date.now() + "upload";
    },
  },
  data() {
    return {
      uploadInfo: {
        accessId: "",
        dir: "",
        expire: 0,
        host: "",
        policy: "",
        signature: "",
      },
    };
  },
  mounted() {
    console.log("!!!!upload", this.businessLabel);
    // getUploadInfo({}).then((res) => {
    //   console.log("uplaod---", res);
    //   this.$set(this, "uploadInfo", res.data);
    // });
  },
  methods: {
    onFileListCallback: function(fileList) {
      this.$emit("handleFileList", fileList);
    },
  },
};
</script>

<style>
.fileInfoTitle {
  font-size: 18px;
  font-weight: bold;
}
</style>
<style lang="less" scoped>
/deep/ .upload-info-list .upload-file-item {
  width: 110px !important;
  .anticon {
    color: red !important;
  }
}
</style>

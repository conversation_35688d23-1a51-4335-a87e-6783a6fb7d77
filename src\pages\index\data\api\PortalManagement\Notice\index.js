/*
 * @Author: 王正勇 <EMAIL>
 * @Date: 2025-03-31 16:12:54
 * @LastEditors: 王正勇 <EMAIL>
 * @LastEditTime: 2025-04-20 09:40:03
 * @FilePath: \zhys-admin\src\pages\index\data\api\PortalManagement\Notice\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @time 2020-9-11
 */
import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * @dec 接口：/activiti/announcement/create
 * @dec 命名：ApiActivitiCreate
 * @dec 接口功能 :新增公告管理
 */
export function ApiActivitiCreate(params) {
  return api({
    url: BASE_URL + "/activiti/announcement/create",
    method: "post",
    params
  });
}
/**
 * @dec 接口：/activiti/announcement/delete
 * @dec 命名：ApiActivitiCreate
 * @dec 接口功能 :删除公告管理
 */

export function ApiActivitiDelete(params) {
  return api({
    url: BASE_URL + "/activiti/announcement/delete",
    method: "post",
    params
  });
}
/**
 * @dec 接口：/activiti/announcement/announcementCondition
 * @dec 命名：ApiActivitiAnnouncementCondition
 * @dec 接口功能 :公告管理列表
 */

export function ApiActivitiAnnouncementCondition(params) {
  const middle = {
    request(params) {
      return params;
    },
    response(data) {
      data.data.pageNo = data.data.current;
      data.data.totalCount = data.data.total;
      data.data.data = data.data.records;
      return data;
    }
  };
  return api({
    url: BASE_URL + "/activiti/announcement/announcementCondition",
    method: "post",
    middle,
    params
  });
}
/**
 * @dec 接口：/aactiviti/announcement/update
 * @dec 命名：ApiActivitiUpdate
 * @dec 接口功能 :修改公告管理
 */

export function ApiActivitiUpdate(params) {
  return api({
    url: BASE_URL + "/activiti/announcement/update",
    method: "post",
    params
  });
}

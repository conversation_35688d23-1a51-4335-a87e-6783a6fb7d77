<template>
  <a-form
    :form="form"
    :formLayout="formLayout"
    :label-col="formItemLayout.labelCol"
    :wrapper-col="formItemLayout.wrapperCol"
  >
    <grayCard :data="data"></grayCard>
    <a-card style="margin-top: 12px">
      <commonTitle
        >初审信息<a-button @click="showApprovalRecords()"
          >审批记录</a-button
        ></commonTitle
      >
      <AgreeItemView
        :isProfessionalProp="isProfessional"
        @changeIsProfessional="changeIsProfessional"
        :ref="
          item.titleName.includes('租赁单价')
            ? 'isProfessionalRef'
            : `AgreeItemView${index}`
        "
        @call="getisProfessionalFile"
        v-model="agreementList[index]"
        style="margin-bottom: 24px"
        v-for="(item, index) in agreementList"
        :annex="item.attachments"
        :title="{ titleName: `${item.titleName}： `, value: item.titleValue }"
        :numType="item.numType"
        :index="item.index"
        :key="item.index"
        :approvalList="getItemApprovalList(item.titleName)"
        :rejectList="getRejectList()"
      ></AgreeItemView>
      <!-- 初审意见 -->
      <commonTitle>初审小结</commonTitle>
      <a-row>
        <a-col span="12">
          <a-form-item label="初审小结">
            <a-textarea
              placeholder="请输入初审小结"
              maxLength="500"
              v-decorator="[
                'approvalSummary',
                {
                  rules: [{ required: true, message: '请输入初审小结' }],
                },
              ]"
            />
          </a-form-item>
          <a-form-item label="审核方式">
            <a-radio-group
              v-decorator="[
                'auditType',
                {
                  rules: [{ required: true, message: '请选择流转方式' }],
                },
              ]"
              @change="handleFormLayoutChange"
            >
              <a-radio :value="0" :disabled="processFlow == 1"> 备案 </a-radio>
              <a-radio :value="1" :disabled="processFlow == 1"> 审批 </a-radio>
              <a-radio :value="2"> 会审 </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 初审意见 end -->
    </a-card>
    <!-- 按钮组 -->
    <a-row type="flex" justify="space-around" align="middle">
      <a-col span="6" class="flex-col confirm_clos">
        <a-button type="danger " @click="handleSubmit(1)">驳回</a-button>
        <a-button type="default" @click="handleSubmit(2)">返回</a-button>
        <a-button type="primary" @click="handleSubmit(3)" :loading="ApiLoading"
          >保存</a-button
        >

        <a-button type="primary" :loading="ApiLoading" @click="handleSubmit(4)"
          >提交审核</a-button
        >
      </a-col>
    </a-row>
    <!-- 审批记录的弹框 -->
    <a-modal
      v-model="visible"
      title="审批记录"
      @cancel="closeModal"
      @ok="closeModal"
      :footer="null"
    >
      <ApprovalRecords :visible="visible" :id="id"></ApprovalRecords>
    </a-modal>
  </a-form>
</template>

<script>
import AgreeItemView from "../components/AgreeItemView";
import grayCard from "../components/GrayCard";
import commonTitle from "../components/CommonTitle";
import ApprovalRecords from "../components/ApprovalRecords";
import {
  ApiGetReviewItem,
  ApiEditRecordReview,
} from "@/pages/index/data/api/RegistrationRecordReview";
// import { ApiGetDictByParentCode } from "@/pages/index/data/api/Common";
export default {
  components: {
    AgreeItemView,
    grayCard,
    commonTitle,
    ApprovalRecords,
  },
  async mounted() {
    //从缓存中取出字典表数据
    this.$set(this, "approvalList", await this.$getDictValue());
    console.log(this.approvalList, "1111111777777");
  },
  created() {
    this.id = this.$route.query.id;
    ApiGetReviewItem({ filingNumber: this.$route.query.filingNumber }).then(
      (resp) => {
        this.data = resp.data;
        // console.log(this.data, "resp.data");
        let contractReviews = resp.data.contractReviews;
        let tempContractReviews = this.sortViewItem(contractReviews);
        contractReviews = tempContractReviews.filter((res)=>{
          return !!res.titleName
        })
        //  console.log(contractReviews,'contractReviews')
        // contractReviews = tempContractReviews;
    
        this.isProfessional = resp.data.contractInfo.isProfessional || 0;
           
        contractReviews.forEach((item) => {
             if (item.titleName.includes("租赁单价")) {
            this.showIsProfessional = true;
            let arr1 = item.attachments.filter(
              (it) => it.type == "pricing_basis"
            );
            let arr2 = item.attachments.filter(
              (it) => it.type == "pricing_basis_review_by_jingfa"
            );
            arr2.forEach((e) => {
              e.businessType = e.type;
              e.fileId = e.id;
              e.fileName = e.oldName;
              e.fileNewName = e.newName;
            });
            item.attachments = arr1;
            item.attachmentsIsProfessional = arr2;
          }

          
         
        });
        this.agreementList = contractReviews;
        this.status = resp.data.contractInfo.status;
        this.processFlow = resp.data.contractInfo.processFlow; // 如果是异常流程，默认审核方式为备案
        // 回显数据
        this.form.setFieldsValue({
          approvalSummary: resp.data.contractInfo.approvalSummary,
          auditType: resp.data.contractInfo.auditType,
        });
        console.log(this.data);
      }
    );
    // ApiGetDictByParentCode({ dictCode: "approval_list" }).then(resp => {
    //   let approvalList = resp.data.map(item => {
    //     return item.desc;
    //   });
    //   this.approvalList = approvalList;
    //   console.log(approvalList);
    // });
    // ApiGetDictByParentCode({ dictCode: "reject_list" }).then(resp => {
    //   let rejectList = resp.data.map(item => {
    //     return item.desc;
    //   });
    //   this.rejectList = rejectList;
    //   console.log(rejectList);
    // });
  },

  data() {
    return {
      showIsProfessional: false, //默认不展示
      isProfessionalValid: true,
      isProfessional: 0, //是否为专业机构制定：0是 1否 选择是，直接选择拟同意；选择否，需上传经发公司对定价依据的复核材料
      attachmentList: [],
      approvalList: null,
      rejectList: null,
      id: "",
      ApiLoading: false,
      visible: false, //控制审批记录的弹框
      data: {}, //创建审核详情
      agreementData: [], //协议列表
      form: this.$form.createForm(this, { name: "reviewer_form" }),
      formLayout: "horizontal",
      clickSave: false, //标识是否已经点击过保存按钮false没有，true点击了
      status: undefined, //标识节点的变量0起草等
      processFlow: "", //流程类型
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 4 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
      },
      agreementList: [],
    };
  },
  methods: {
    //获取每一个初审条款的默认意见
    //接收传过来的条款名字
    getItemApprovalList(name) {
      //根据条款名字和字典表中的名字进行匹配，找到是哪一个条款，filter返回的还是数组
      let tempArr = this.approvalList.filter((item) => item.nameCn == name);
      // console.log(name, tempArr, tempArr[0], "pppppp");
      //去掉附件的哪一项，只选择条款
      if (tempArr[0]) {
        //将字典表中条款的意见数组拿到
        let tempChildArr = tempArr[0].childs;
        // console.log(tempArr[0].childs, tempChildArr, " mmmmmm");
        //去掉没有默认意见的项
        if (tempChildArr) {
          //利用map遍历出默认意见放入数组中，最后直接返回新生成的数组。
          return tempChildArr.map((item) => {
            return item.dictValue;
          });
        } else {
          return [];
        }
      }
    },
    //不同意没有默认的列表数据，返回一个空数组
    getRejectList() {
      return [];
    },
    sortViewItem(array) {
      array = JSON.parse(JSON.stringify(array));
      array.sort((a, b) => {
        return a.index - b.index;
      });
      //复制出一个新数组，操作原来的数组因为操作会改变原数组
      let newArray = [...array];
      // array.shift()它会修改原始数组，将数组的第一个元素移除，并返回该元素的值。
      //index为0代表是附件需要放到合适的位置
      let first = array.shift();
      //如果不为0代表是条款因此不能删除第一条所以返回复制后的数组
      if (first.index !== 0) {
        return newArray;
      }
      let titleName = first.titleName;
      let attachIndex = parseInt(titleName.split("-")[1]);
      first.numType = array[attachIndex - 1].numType;
      array.splice(attachIndex, 0, first);
      console.log(array, "array");
      return array;
    },
    valiedataData(data) {
      let valid = true;
      for (let i = 0; i < data.length; i++) {
        if (
          data[i].index !== 0 &&
          ((!data[i].agreement && data[i].agreement !== 0) || !data[i].advice)
        ) {
          valid = false;
          console.log(data[i]);
          break;
        }
      }
      return valid;
    },
    // 初审信息全部拟同意为true
    valiedataDataAllTrue(data) {
      let valid = true;
      for (let i = 0; i < data.length; i++) {
        if (data[i].index !== 0 && data[i].agreement != 1) {
          valid = false;
          console.log(data[i]);
          break;
        }
      }
      return valid;
    },
    //点击审批记录调用的方法
    showApprovalRecords() {
      this.visible = true;
    },
    //关闭弹框调用的方法
    closeModal() {
      this.visible = false;
    },
    changeIsProfessional(e) {
      console.log("changeIsProfessional", e);
      this.isProfessional = e;
    },
    getisProfessionalFile(status, file) {
      this.isProfessionalValid = status;
      this.attachmentList = file;
      console.log("getisProfessionalFile", status, file);
    },
    /**
     *
     * @param {*} params
     */
    fnView(params) {
      this.ApiLoading = true;
      ApiEditRecordReview(params)
        .then((res) => {
          this.ApiLoading = false;
          if (res.code === 0) {
            switch (params.operateType) {
              case "reject":
                this.$message.success("驳回成功");
                break;
              case "save":
                console.log(66666);
                this.$message.success("保存成功");
                break;
              case "submit":
                this.$message.success("提交审核成功");
                break;
            }
            console.log("已经点击了");
            //调用接口后将变量改为true点击保存不会再调用接口
            this.clickSave = true;
            setTimeout(() => {
              this.ApiLoading = false;
              this.$router.back(-1);
            }, 2000);
          } else {
            this.$message.error(`操作失败${res.data.msg}`);
          }
        })
        .catch((err) => {
          this.ApiLoading = false;
          console.log("审核报错", err);
        });
    },
    /**
     *
     * @param {*} type 1 驳回 2返回 3 保存 4 提交审核
     */
    handleSubmit(type) {
      let params;
      let self = this;
      if (this.showIsProfessional) {
        self.$refs.isProfessionalRef[0].getFileResult();
      }
      switch (type) {
        case 1:
          //ToDO
          this.$confirm({
            title: "确认驳回？",
            content: "此操作将驳回，请确认是否继续",
            onOk() {
              console.log("驳回");
              self.form.validateFields((error, value) => {
                console.log(error, value);
                if (error) {
                  console.log(error, "提交");
                } else {
                  if (!self.isProfessionalValid) {
                    self.$message.error("有附件未上传，请检查！");
                    return;
                  }
                  if (!self.valiedataData(self.agreementList)) {
                    self.$message.error("请完善审批信息后再驳回");
                    return;
                  }
                  // 初审信息 不能全部拟同意 才能驳回
                  if (self.valiedataDataAllTrue(self.agreementList)) {
                    self.$message.error("请检查审批信息后再驳回");
                  } else {
                    params = {
                      isProfessional: self.isProfessional,
                      attachmentList: self.attachmentList,
                      filingNumber: self.$route.query.filingNumber,
                      id: self.data.contractInfo.id,
                      contractReviewEntries: self.agreementList.map((item) => ({
                        reviewEntry: item.titleName,
                        reviewEntryContent: item.titleValue,
                        reviewEntryIndex: item.index,
                        opinion: item.agreement,
                        contractInfoId: self.data.contractInfo.id,
                        remark: item.advice,
                        id: item.id,
                      })),
                      status: self.status,
                      operateType: "reject", //保存 save  驳回 reject  提交 submit
                      approvalSummary:
                        self.form.getFieldValue("approvalSummary"),
                      approvalComments:
                        self.form.getFieldValue("approvalSummary"),
                      auditType: self.form.getFieldValue("auditType"),
                    };
                    self.fnView(params);
                  }
                }
              });
            },
            onCancel() {},
          });
          break;
        case 2:
          //ToDO
          console.log("返回");
          this.$router.back(-1);
          break;
        case 3:
          //ToDO
          console.log("保存", this.clickSave, this.agreementList);
          params = {
            isProfessional: this.isProfessional,
            attachmentList: this.attachmentList,
            filingNumber: this.$route.query.filingNumber,
            id: this.data.contractInfo.id,
            contractReviewEntries: this.agreementList.map((item) => ({
              reviewEntry: item.titleName,
              reviewEntryContent: item.titleValue,
              reviewEntryIndex: item.index,
              opinion: item.agreement,
              contractInfoId: this.data.contractInfo.id,
              remark: item.advice,
              id: item.id,
            })),
            status: self.status,
            operateType: "save", //保存 save  驳回 reject  提交 submit
            approvalSummary: this.form.getFieldValue("approvalSummary"),
            approvalComments: this.form.getFieldValue("approvalSummary"),
            auditType: this.form.getFieldValue("auditType"),
          };
          //如果已经点击过保存之后又点击就不让调用接口了
          if (this.clickSave === false) {
            this.fnView(params);
          }
          break;
        case 4:
          console.log("提交审核", this.agreementList);
          this.$confirm({
            title: "确认提交审核？",
            content: "此操作将提交审核，请确认是否继续",
            onOk() {
              self.form.validateFields((error, value) => {
                console.log(error, value, self.status);
                if (error) {
                  console.log(error, "提交");
                } else {
                  if (!self.isProfessionalValid) {
                    self.$message.error("有附件未上传，请检查！");
                    return;
                  }
                  if (!self.valiedataData(self.agreementList)) {
                    self.$message.error("请完善审批信息后再提交审核");
                  } else {
                    params = {
                      isProfessional: self.isProfessional,
                      attachmentList: self.attachmentList,
                      filingNumber: self.$route.query.filingNumber,
                      id: self.data.contractInfo.id,
                      contractReviewEntries: self.agreementList.map((item) => ({
                        reviewEntry: item.titleName,
                        reviewEntryContent: item.titleValue,
                        reviewEntryIndex: item.index,
                        opinion: item.agreement,
                        contractInfoId: self.data.contractInfo.id,
                        remark: item.advice,
                        id: item.id,
                      })),
                      status: self.status,
                      operateType: "submit", //保存 save  驳回 reject  提交 submit
                      approvalSummary:
                        self.form.getFieldValue("approvalSummary"),
                      approvalComments:
                        self.form.getFieldValue("approvalSummary"),
                      auditType: self.form.getFieldValue("auditType"),
                    };
                    self.fnView(params);
                  }
                }
              });
            },
            onCancel() {},
          });

          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 头部
.topColumn {
  display: flex;
  > .toptitle {
    height: 32px;
    background: #f7f8fa;
    border-radius: 80px;
    width: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
  }
  > .toptitleContent {
    font-size: 22px;
    font-family: PingFang SC, PingFang SC-500;
    font-weight: 500;
    text-align: LEFT;
    color: #1d2129;
    line-height: 32px;
  }
}

// 底部按钮
.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 32px;
}
.confirm_model {
}
//底部按钮end
</style>

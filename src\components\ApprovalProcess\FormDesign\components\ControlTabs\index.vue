<!--
* <AUTHOR>
* @time 2020-9-1
* @dec 设计主页-表单控件
-->
<template>
  <div class="control-tabs-page">
    <a-tabs
      default-active-key="1"
      @change="callback"
      :tabBarStyle="tabBarStyle"
    >
      <a-tab-pane key="1" tab="控件库">
        <div class="control-tabs-page-main">
          <a-spin :spinning="loading"> </a-spin>
          <div
            v-for="(item, index) in controlList.filter(
              item =>
                item.componentType === '001' ||
                item.componentType === '002' ||
                item.componentType === '003'
            )"
            :key="index"
            v-show="!loading"
          >
            <p class="control-tabs-page-main-type">
              <a-icon type="tag" />
              {{
                item.componentType === "001"
                  ? "布局型控件"
                  : item.componentType === "002"
                  ? "输入型控件"
                  : "选择型控件"
              }}
            </p>
            <a-row :gutter="[10, 10]" class="sortablejs">
              <a-col
                :md="12"
                :sm="24"
                v-for="(v, index) in item.list"
                :key="index"
                :datatype="v.type"
                :dataname="v.name"
              >
                <div class="control-flex">
                  <span>{{ v.name }}</span>
                  <a-icon
                    :type="iconMaps[v.type.split('_')[1]]"
                    @click.stop="handleClick(v)"
                  />
                  <div class="sortablejs-handle"></div>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="套件库">
        <div class="control-tabs-page-main">
          <a-row :gutter="[10, 10]" class="sortablejs">
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_nonAviation"
              dataname="非航资源套件"
            >
              <div class="control-flex">
                <span>非航资源套件</span>
                <a-icon :type="iconMaps['nonAviation']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_machineRoom"
              dataname="机房资源套件"
            >
              <div class="control-flex">
                <span>机房资源套件</span>
                <a-icon :type="iconMaps['machineRoom']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_personnelRecord"
              dataname="机房人员备案套件"
            >
              <div class="control-flex">
                <span>机房人员备案套件</span>
                <a-icon :type="iconMaps['personnelRecord']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_hotWork"
              dataname="作业人员套件"
            >
              <div class="control-flex">
                <span>作业人员套件</span>
                <a-icon :type="iconMaps['operator']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_manageMent"
              dataname="办理人员信息套件"
            >
              <div class="control-flex">
                <span>办理人员信息套件</span>
                <a-icon :type="iconMaps['manage']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_jdjfOffline"
              dataname="机电机房线下套件"
            >
              <div class="control-flex">
                <span>机电机房线下套件</span>
                <a-icon :type="iconMaps['manage']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
            <a-col
              :md="12"
              :sm="24"
              datatype="SuiteLibrary_VPNApply"
              dataname="VPN可访问主机套件"
            >
              <div class="control-flex">
                <span>VPN访问主机套件</span>
                <a-icon :type="iconMaps['manage']" />
                <div class="sortablejs-handle"></div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-tab-pane>
      <!-- <a-tab-pane key="3" tab="人事套件">
        人事套件
      </a-tab-pane> -->
    </a-tabs>
  </div>
</template>
<script>
import { ApiFormFindAllFormComponent } from "@/data/api/components/ApprovalProcess";
import sortablejs from "sortablejs";
export default {
  data() {
    return {
      loading: false,
      controlList: [], //控件列表
      iconMaps: {
        money: "pay-circle",
        identity: "idcard",
        counter: "calculator",
        dateSelection: "credit-card",
        landline: "phone",
        multilineText: "ordered-list",
        text: "edit",
        multipleChoice: "unordered-list",
        number: "border",
        phone: "phone",
        picture: "picture",
        score: "aliwangwang",
        singleChoice: "ellipsis",
        switch: "file-done",
        sketchMap: "picture",
        timeSelection: "credit-card",
        timeFrame: "credit-card",
        upload: "cloud-upload",
        button: "border",
        template: "unordered-list",
        explainText: "ordered-list",
        nonAviation: "unordered-list",
        machineRoom: "unordered-list",
        personnelRecord: "unordered-list",
        operator: "unordered-list",
        manage: "unordered-list",
        templateDownload: "cloud-download"
      },
      tabBarStyle: {
        margin: "unset"
      } //tabBar样式
    };
  },
  mounted() {
    this.findAllFormComponent();
  },
  methods: {
    //点击事件
    handleClick(v) {
      this.$emit("addComponent", v.type, v.name);
    },
    //切换tab
    callback(key) {
      console.log(key);
      this.initSortable();
    },
    //查询控件列表
    findAllFormComponent() {
      this.loading = true;
      let params = {};
      ApiFormFindAllFormComponent(params)
        .then(res => {
          let arrAllList = res.data;
          this.controlListFilter(arrAllList);
          this.initSortable();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //控件列表过滤
    controlListFilter(arr) {
      console.log(arr);
      let newArrList = {};
      arr.forEach(item => {
        if (!newArrList[item.componentType]) {
          newArrList[item.componentType] = [item];
        } else {
          newArrList[item.componentType].push(item);
        }
      });
      console.log(newArrList);
      newArrList = Object.keys(newArrList).map(item => {
        return {
          componentType: item,
          list: newArrList[item]
        };
      });
      this.controlList = newArrList;
    },
    initSortable() {
      this.$nextTick(() => {
        document.querySelectorAll(".sortablejs").forEach(elt => {
          new sortablejs(elt, {
            group: {
              name: "shared",
              pull: "clone",
              put: false // 不允许拖拽进这个列表
            },
            animation: 150,
            handle: ".sortablejs-handle",
            sort: false // 设为false，禁止sort
            // onEnd: () => {
            // var itemEl = evt.item; // dragged HTMLElement
            // evt.to; // target list
            // evt.from; // previous list
            // evt.oldIndex; // element's old index within old parent
            // evt.newIndex; // element's new index within new parent
            // evt.clone; // the clone element
            // evt.pullMode = false; // when item is in another sortable: `"clone"` if cloning, `true` if moving
            // }
            // onMove: (evt, originalEvent) => {
            // let node = evt.to.querySelector(".sortable-chosen");
            // console.log(node);
            // if (node && node.parentElement !== null) {
            //   node.parentElement.removeChild(node);
            // }
            // evt;
            // originalEvent;
            // console.log(evt.to.querySelector('[draggable="true"]'));
            // }
          });
        });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.control-tabs-page {
  .control-flex {
    display: flex;
    height: 32px;
    line-height: 32px;
    padding: 0 5px 0 8px;
    justify-content: space-between;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    position: relative;
    cursor: grab;
    .sortablejs-handle {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }
    & > span {
      flex: 1;
    }
    i {
      position: relative;
      line-height: 32px;
      z-index: 1;
    }
  }
  .control-tabs-page-main {
    padding: 12px;
    padding-bottom: 16px;
    min-height: 100px;
    .control-tabs-page-main-type {
      margin: 7px 0;
      font-weight: 700;
    }
    .ant-spin {
      width: 100%;
      line-height: 200px;
      text-align: center;
    }
  }
}
</style>

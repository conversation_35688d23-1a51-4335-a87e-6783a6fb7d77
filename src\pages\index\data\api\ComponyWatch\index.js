import api from "@/common/api";
import { BASE_URL } from "Config";

/**
 * 载体监控展示
 */
export function getAreaPolicyApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/areaPolicy",
    method: "get",
    params,
  });
}
export function getAreaOccupancyApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/areaOccupancy",
    method: "get",
    params,
  });
}
export function getIndustrialParkAreaApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/industrialParkArea",
    method: "get",
    params,
  });
}
export function getCarrierProportionApi(params) {
  return api({
    url: BASE_URL + "/home/<USER>/CarrierProportion",
    method: "get",
    params,
  });
}

<!--
* <AUTHOR>
* @time 2020-9-7
* @dec 表单引擎 - 审批套件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="12">
        <a-row :gutter="[50, 50]">
          <a-col>
            <machine-room-dom :data="formData"></machine-room-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="12">
        <machine-room-form v-bind:data.sync="formData"></machine-room-form
      ></a-col>
    </a-row>
  </a-card>
</template>
<script>
// 单选控件 DOM/Form
import {
  MachineRoomDom,
  MachineRoomForm
} from "@/components/ApprovalProcess/FormDesign/components/SuiteLibrary/MachineRoom";
export default {
  components: {
    MachineRoomDom,
    MachineRoomForm
  },
  data() {
    return {
      formData: {}
    };
  }
};
</script>
<style scoped lang="less"></style>

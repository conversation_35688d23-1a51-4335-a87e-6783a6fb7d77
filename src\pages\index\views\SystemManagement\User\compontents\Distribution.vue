<!--
* <AUTHOR>
* @time 2020-8-21
* @dec 系统管理 - 用户管理 - 角色分配
-->
<template>
  <a-modal
    title="用户角色分配"
    :width="690"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="modelCancel"
  >
    <a-spin :spinning="loading">
      <a-form layout="inline" style="margin-bottom:20px">
        <a-form-item label="角色分配">
          <a-select
            v-model="queryParam.fenpei"
            placeholder="请选择"
            default-value="0"
            style="width: 200px"
            @change="handleChangeF"
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
          >
            <a-select-option value="0">{{
              $store.getters["dictionaries/getNameFromTypeCode"]({
                type: "APPTYPE",
                code: "pc"
              })
            }}</a-select-option>
            <a-select-option value="1">{{
              $store.getters["dictionaries/getNameFromTypeCode"]({
                type: "APPTYPE",
                code: "pad"
              })
            }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
      <a-transfer
        :data-source="mockData"
        :list-style="{
          width: '300px',
          height: '300px'
        }"
        :target-keys="targetKeys"
        :render="renderItem"
        @change="handleChange"
      />
    </a-spin>
  </a-modal>
</template>

<script>
import {
  ApiSecurityFindUserWithRoles,
  ApiSecuritySaveUserWithRoles
} from "@/pages//index/data/api/SystemManagement/User"
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    dataItem: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      mockData: [],
      targetKeys: [],
      queryParam: {
        fenpei: "0"
      },
      pcOptions: [],
      padOptions: [],
      chooseType: null
    }
  },
  watch: {
    visible: {
      handler: function(flag) {
        if (flag) {
          this.queryParam = {
            fenpei: "0"
          }
          this.findUserWithRoles(this.dataItem, "PC")
          this.findUserWithRoles(this.dataItem, "PAD")
        }
      },
      deep: true
    }
  },
  methods: {
    //关闭时清空初始值
    clearData() {
      this.mockData = []
      this.targetKeys = []
      this.queryParam = {}
      this.pcOptions = []
      this.padOptions = []
      this.chooseType = null
    },
    //确定提交
    handleOk() {
      this.loading = true
      let pcOptions = this.pcOptions.filter((item) => {
        return item.own
      })
      let padOptions = this.padOptions.filter((item) => {
        return item.own
      })
      let subList = pcOptions.concat(padOptions)
      if (pcOptions.length === 0) {
        subList.push({
          appType: "pc",
          own: true,
          roleId: "",
          roleName: "",
          userId: this.dataItem
        })
      }
      if (padOptions.length === 0) {
        subList.push({
          appType: "pad",
          own: true,
          roleId: "",
          roleName: "",
          userId: this.dataItem
        })
      }
      console.log("待提交", subList)
      ApiSecuritySaveUserWithRoles(subList)
        .then((res) => {
          if (res.code == 0) {
            this.clearData()
            this.$emit("cancel")
            // 刷新表格
            this.$emit("ok")
            this.$message.info("分配权限成功!")
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    //数据处理格式
    getMock(Options) {
      const targetKeys = []
      const mockData = []
      for (let i = 0; i < Options.length; i++) {
        const data = {
          key: i.toString(),
          title: `${Options[i].roleName}`,
          chosen: Options[i].own
        }
        if (data.chosen) {
          targetKeys.push(data.key)
        }
        mockData.push(data)
      }
      this.mockData = mockData
      this.targetKeys = targetKeys
    },
    //数据渲染
    renderItem(item) {
      const customLabel = <span class="custom-item">{item.title}</span>
      return {
        label: customLabel, // for displayed item
        value: item.title // for title and filter matching
      }
    },
    //角色分配选择事件
    handleChangeF(value) {
      if (value === "0") {
        this.chooseType = "pc"
        this.getMock(this.pcOptions)
      } else if (value === "1") {
        this.chooseType = "pad"
        this.getMock(this.padOptions)
      }
    },
    //勾选事件
    handleChange(targetKeys, direction, moveKeys) {
      console.log(targetKeys, direction, moveKeys)
      let allList = []
      if (this.chooseType == "pc") {
        allList = this.pcOptions
      } else if (this.chooseType == "pad") {
        allList = this.padOptions
      } else {
        return false
      }
      allList.forEach((item, index) => {
        if (targetKeys.includes(String(index))) {
          item.own = true
        } else {
          item.own = false
        }
      })
      targetKeys.forEach((item) => {
        allList.forEach((a, index) => {
          if (item == index) {
            a
          }
        })
      })
      this.targetKeys = targetKeys
    },
    //根据角色类型请求接口
    findUserWithRoles(uId, type) {
      const params = {
        id: uId,
        appType: type
      }
      ApiSecurityFindUserWithRoles(params).then((res) => {
        // console.log(type, res.data);
        this.pcOptions = type == "PC" ? res.data : this.pcOptions
        this.padOptions = type == "PAD" ? res.data : this.padOptions
        this.chooseType = "pc"
        this.getMock(this.pcOptions)
      })
    },
    //弹框取消
    modelCancel() {
      this.clearData()
      this.$emit("cancel")
    }
  }
}
</script>

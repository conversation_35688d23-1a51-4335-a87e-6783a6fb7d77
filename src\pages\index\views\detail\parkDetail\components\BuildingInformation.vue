<template>
  <div class="building-information-content">
    <div style="display: flex; flex-direction: row-reverse">
      <div style="display: flex; align-items: center; margin-left: 20px">
        <div class="doNot"></div>
        <div>未租</div>
      </div>
      <div style="display: flex; align-items: center; margin-left: 20px">
        <div class="doing"></div>
        <div>近三个月到期</div>
      </div>
      <div style="display: flex; align-items: center">
        <div class="done"></div>
        <div>已租</div>
      </div>
    </div>
    <div class="tab">
      <a-tabs
        default-active-key="1"
        :tab-position="mode"
        :style="{ height: '100px' }"
        @change="changeBuilding"
      >
        <a-tab-pane v-for="i in buildingList" :key="i" :tab="`${i}栋`">
          <!-- Content of tab {{ i }} -->
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="main-content">
      <div class="main" v-for="(m, idx) in floorList" :key="idx">
        <div class="floor-num">
          {{ m.floorNumber }}楼 <br />
          {{ m.floorRoomTotalArea }}平米
        </div>
        <div class="company">
          <div
            class="company-item"
            v-for="(i, idx) in m.companyList"
            :key="idx"
            :style="{
              backgroundColor:
                i.mature == 1
                  ? '#bcbeb9'
                  : i.mature == 2
                  ? '#b6aa37'
                  : '#66ccff',
            }"
          >
            <div v-if="i.mature != 1">{{ i.companyName }}</div>
            <div v-if="i.roomArea && i.mature != 1">{{ i.roomArea }}平米</div>
            <div v-if="i.mature != 1">
              {{ i.leaseEndTime }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { queryBuildingMsg } from "@/pages/demo/data/api/api/park";
import moment from "moment";
export default {
  data() {
    return {
      // 过滤前
      floorNumberList: [],
      floorNumberMsgList: [],
      // 过滤后
      buildingList: [],
      floorList: [],
      companyList: [],
      mainList: [
        {
          count: "一楼",
          total: "321.3平米",
          companyList: [
            {
              name: "上海XX科技股份有限公司",
              area: "153平米",
              time: "2035/06/31",
              flag: false,
            },
            {
              name: "上海XX科技股份有限公司",
              area: "153平米",
              time: "2035/06/31",
              flag: true,
            },
            {
              name: null,
              area: "153平米",
              time: null,
              flag: false,
            },
          ],
        },
        {
          count: "二楼",
          total: "321.3平米",
          companyList: [
            {
              name: "上海XX科技股份有限公司",
              area: "153平米",
              time: "2035/06/31",
              flag: false,
            },
            {
              name: null,
              area: "153平米",
              time: null,
              flag: false,
            },
          ],
        },
        {
          count: "三楼",
          total: "321.3平米",
          companyList: [
            {
              name: null,
              area: "153平米",
              time: null,
              flag: false,
            },
          ],
        },
      ],
      active: "",
      currentTime: "",
    };
  },
  // computed: {
  //   isThreeMature(val) {
  //     console.log(moment(this.currentTime).diff(moment(val), "month"));
  //     return moment(this.currentTime).diff(moment(val), "month") < 3;
  //   },
  // },
  mounted() {
    console.log(" mounted");
    if (!this.$route.query.parkName) {
      return;
    }
    this.currentTime = new Date();
    let params = {
      parkName: this.$route.query.parkName,
      pageNum: 1,
      pageSize: 10,
    };
    queryBuildingMsg(params).then((res) => {
      this.buildingList = res.data.builidngNumbers;
      this.active = this.buildingList[0];
      // 楼层
      this.floorNumberList = res.data.floorNumberList;

      this.floorNumberMsgList = res.data.floorNumberMsgList;
      this.handleData();
    });
  },
  methods: {
    handleData() {
      this.floorList = this.floorNumberList.filter((e) => {
        return e.buildingNumber == this.active;
      });
      // 房间
      let list = [];
      list = this.floorNumberMsgList.filter((e) => {
        return e.buildingNumber == this.active;
      });
      console.log(this.floorList,list,'11111111111111')
      // list  当前楼栋 所有房间信息
      this.floorList.forEach((item) => {
        this.companyList = list.filter((e) => {
          return (
            e.buildingNumber == this.active && e.floorNumber == item.floorNumber
          );
        });
        item["companyList"] = this.companyList;
      });
      this.floorList.forEach((c) => {
        c.companyList.forEach((i) => {
          if (i.leaseEndTime) {
            i.leaseEndTime = moment(i.leaseEndTime).format("YYYY-MM-DD");
            /**
             * mature 1未租 2 三月以内到期 3 三月以外到期(已租)
             */
            let dateDiff = 0;
            let m1 = moment(new Date());
            let m2 = moment(i.leaseEndTime);
            dateDiff = m2.diff(m1, "day");
            if (dateDiff > 90) {
              i["mature"] = 3;
            }
            if (dateDiff <= 90 && dateDiff > 0) {
              i["mature"] = 2;
            }
            if (dateDiff < 0) {
              i["mature"] = 1;
            }
            i.month = moment(new Date()).diff(moment(i.leaseEndTime), "month");
          } else {
            i["mature"] = 1;
          }
        });
      });
    },
    changeBuilding(val) {
      this.active = val;
      this.handleData();
    },
  },
};
</script>

<style lang="less" scoped>
.building-information-content {
  /deep/ .ant-tabs-bar {
    border: none;
  }
}

.done {
  background: #66ccff;
  width: 10px;
  height: 10px;
  margin-right: 5px;
}
.doing {
  background: #b6aa37;
  width: 10px;
  height: 10px;
  margin-right: 5px;
}
.doNot {
  background: #bcbeb9;
  width: 10px;
  height: 10px;
  margin-right: 5px;
}
.tab {
  display: flex;
  justify-content: space-between;
  background: #fff;
  height: 50px;
  line-height: 50px;
  margin-bottom: 10px;
}
.item {
  flex: 1;
  text-align: center;
  border: 1px solid #3e60ec;
  font-weight: 700;
  cursor: pointer;
}
.active {
  background: #2f54eb;
  color: #fff;
}
.floor-num {
  min-width: 150px;
  height: 100px;
  padding-top: 16px;
  width: 150px;
  text-align: center;
  background: #eff3fc;
}
// /* 设置滚动条的颜色 */
// ::-webkit-scrollbar {
//     background-color: #ffffff; /* 滚动条背景色 */
//   }
// /* 设置滑块（thumb）的颜色 */
// ::-webkit-scrollbar-thumb {
//   background-color: #3e60ec; /* 滑块颜色 */
//   height: 4px;
//   border-radius: 6px;
// }
.main {
  overflow-x: auto;
  display: flex;
  margin-bottom: 16px;
  background: #fff;
}
.main-content {
  width: 100%;
  background: #fff;
  min-height: 360px;
}
.company {
  display: flex;
  justify-content: space-around;
  flex: 1;
}
.company-item {
  min-width: 145px;
  flex: 1;
  border-right: 4px solid #fff;
  color: #fff;
  padding: 6px;
}
.company-item:last-child {
  border-right: none;
}
</style>

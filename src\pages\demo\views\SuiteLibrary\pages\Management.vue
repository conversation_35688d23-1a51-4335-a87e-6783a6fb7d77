<!--
* <AUTHOR>
* @time 202-4-7
* @dec 表单引擎 - 管理人员信息套件
-->
<template>
  <a-card>
    <a-row :gutter="50">
      <a-col :span="12">
        <a-row :gutter="[50, 50]">
          <a-col>
            <manage-dom :data="formData"></manage-dom>
          </a-col>
          <a-col>{{ formData }}</a-col>
        </a-row>
      </a-col>
      <a-col :span="12">
        <manage-form v-bind:data.sync="formData"></manage-form
      ></a-col>
    </a-row>
  </a-card>
</template>
<script>
// 单选控件 DOM/Form
import {
  ManageDom,
  ManageForm
} from "@/components/ApprovalProcess/FormDesign/components/SuiteLibrary/Management";
export default {
  components: {
    ManageDom,
    ManageForm
  },
  data() {
    return {
      formData: {}
    };
  }
};
</script>
<style scoped lang="less"></style>

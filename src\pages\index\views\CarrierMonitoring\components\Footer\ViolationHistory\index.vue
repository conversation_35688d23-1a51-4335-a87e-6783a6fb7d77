<template>
  <div class="recording">
    <div class="tilte"><span>违规记录</span></div>
    <div class="list">
      <div class="list_item" v-for="item in listData" :key="item.id">
        <div
          class="pic"
          :class="
            item.level == 1
              ? 'serious'
              : item.level == 2
              ? 'slight'
              : 'noProsecution'
          "
        >
          <span>{{
            item.level == 1 ? "严重" : item.level == 2 ? "轻微" : "不予追究"
          }}</span>
        </div>
        <div class="msg">
          <a-tooltip>
            <template slot="title">
              {{ item.content }}
            </template>
            <span>"{{ item.company }}违规,违规内容说明..."</span>
          </a-tooltip>
        </div>
        <div class="time">
          <span>{{ item.time }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiGetViolationHistory } from "../../../../../data/api/EnterpriseMonitoring";
export default {
  components: {},
  data() {
    return {
      listData: []
      // listData: [
      //   {
      //     id: 1,
      //     pic: "serious",
      //     msg: "上海传卓电子有限公司因破环设施违规,违规内容说明...",
      //     picMsg: "严重",
      //     time: "2023-7-19"
      //   },
      //   {
      //     id: 2,
      //     pic: "slight",
      //     msg: "固安捷贸易有限公司因自身问题违规,违规内容说明...",
      //     picMsg: "轻微",
      //     time: "2023-8-21"
      //   },
      //   {
      //     id: 3,
      //     pic: "noProsecution",
      //     msg: "上海鑫磊投资有限公司因退租问题违规,违规内容说明...",
      //     picMsg: "不予追究",
      //     time: "2023-5-08"
      //   }
      // ]
    };
  },
  mounted() {
    this.getAllHistory();
  },
  methods: {
    //获取所有记录
    getAllHistory() {
      ApiGetViolationHistory().then(res => {
        this.listData = res.data.records;
        console.log(res.data);
      });
    }
  },
  computed: {}
};
</script>

<style scoped lang="less">
.recording {
  .tilte {
    font-size: 16px;
    color: black;
    font-weight: 600;
    margin-top: 20px;
    margin-left: 20px;
  }
  .list {
    margin-top: 20px;
    .list_item {
      display: flex;
      margin-left: 15px;
      margin-right: 10px;
      margin-bottom: 10px;
      border-bottom: 1px dashed rgb(199, 197, 197);
      .pic {
        width: 80px;
        height: 30px;
        border-radius: 5px;
        text-align: center;
        line-height: 30px;
        font-weight: 600;
        margin-bottom: 5px;
      }
      .slight {
        color: rgb(23, 142, 235);
        background-color: rgb(230, 240, 249);
      }
      .serious {
        color: rgb(254, 117, 106);
        background-color: rgb(255, 234, 231);
      }
      .noProsecution {
        color: rgb(23, 142, 235);
        background-color: rgb(230, 240, 249);
      }
      .msg {
        margin-left: 15px;
        color: black;
      }
      .time {
        width: 70px;
      }
    }
  }
}
</style>

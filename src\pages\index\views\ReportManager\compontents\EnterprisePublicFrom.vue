<template>
  <div>
    <a-form class="all">
      <a-form-item label="统计周期" class="label">
        <a-space direction="vertical" :size="12">
          <a-date-picker v-model="default0" @change="date($event)" />
        </a-space>
      </a-form-item>
      <a-form-item label="园区名称" class="label">
        <a-select
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
          v-model="default1"
          @change="getName($event)"
        >
          <a-select-option
            :value="item.name"
            v-for="item in parkArr"
            :key="item.value"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="企业性质" class="label">
        <a-select
          v-model="newCharacter"
          mode="multiple"
          placeholder="请选择"
          @change="getCharacter($event)"
          maxTagCount="1"
          maxTagTextLength="6"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
        >
          <a-select-option
            :value="character.name"
            v-for="character in characterArr"
            :key="character.value"
            >{{ character.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="是否属地企业" class="label">
        <a-select
          v-model="default2"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
          @change="getTerritorial($event)"
        >
          <a-select-option
            :value="territorial.name"
            v-for="territorial in territorialEnterprises"
            :key="territorial.value"
            >{{ territorial.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="企业分布" class="label">
        <a-select v-model="default3" @change="getKinds($event)">
          <a-select-option
            :getPopupContainer="
              (triggerNode) => {
                return triggerNode.parentNode || document.body
              }
            "
            :value="kinds.name"
            v-for="kinds in enterprisesKinds"
            :key="kinds.value"
            >{{ kinds.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="是否重点企业" class="label">
        <a-select v-model="default4" @change="getImportant($event)">
          <a-select-option
            :value="important.name"
            v-for="important in importantEnterprises"
            :key="important.value"
            >{{ important.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <div class="btnGroup">
        <a-button type="primary" class="query" @click="query()">
          <a-icon type="search"></a-icon>
          查询
        </a-button>
        <a-button type="primary" class="export" @click="deduced()"
          >导出</a-button
        >
      </div>
    </a-form>
  </div>
</template>
<script>
import moment from "moment"
import { store } from "@/data/store"
export default {
  components: {},
  data() {
    return {
      parkArr: [
        {
          name: "华泾镇",
          value: "1"
        },
        {
          name: "星联科技智慧园区",
          value: "2"
        }
      ],
      territorialEnterprises: [
        {
          name: "全部",
          value: "1"
        },
        {
          name: "中外合资",
          value: "2"
        }
      ],
      enterprisesKinds: [
        {
          name: "全部",
          value: "1"
        },
        {
          name: "半导体",
          value: "2"
        }
      ],
      importantEnterprises: [
        {
          name: "全部",
          value: "1"
        },
        {
          name: "是",
          value: "2"
        },
        {
          name: "否",
          value: "3"
        }
      ],
      characterArr: [
        {
          name: "高新技术",
          value: "1"
        },
        {
          name: "专精特新",
          value: "2"
        },
        {
          name: "外资总部",
          value: "3"
        },
        {
          name: "上市公司",
          value: "4"
        }
      ],
      newCharacter: [],
      time: "",
      default0: "",
      default1: "",
      default2: "",
      default3: "",
      default4: "",
      // 定义一个数组去接收后端查询传过来的数据
      queryData: [1, 2, 3]
    }
  },
  methods: {
    //获取用户选择的时间
    date(e) {
      this.time = moment(e).format("YYYY MM DD")
      // console.log(this.time);
    },
    //获取用户选择的企业
    getName(e) {
      // console.log(e);
      this.default1 = e
    },
    //获取企业性质
    getCharacter(e) {
      console.log(e)
    },
    //获取属地企业
    getTerritorial(e) {
      this.default2 = e
      // console.log(e);
    },
    //获取企业分布
    getKinds(e) {
      this.default3 = e
      // console.log(e);
    },
    //获取重点企业
    getImportant(e) {
      this.default4 = e
      // console.log(e);
    },
    //查询按钮
    query() {
      //发送请求传参数获取相应的列表渲染
      // let newCharacter = this.newCharacter;
      // let default0 = this.default0;
      // let default1 = this.default1;
      // let default2 = this.default2;
      // let default3 = this.default3;
      // let default4 = this.default4;
      // let params = {
      //   newCharacter,default0,default1,default2,default3,default4
      // };
      // 点击按钮进行一些操作，然后保存后端传的数据到store中
      // this.$store.commit("saveCompanyQueryData", this.queryData);
    },
    //导出按钮
    deduced() {}
  },
  created() {
    console.log(this.queryData)
    store.commit("tableData/saveCompanyQueryData", this.queryData)
  }
}
</script>
<style lang="less" scoped>
.all {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.8rem;
  margin-left: 0.5rem;
  .label {
    display: flex;
    flex-wrap: nowrap;
    margin-right: 30px;
  }
  .btnGroup {
    margin-left: auto;
    margin-right: 10px;
    margin-top: -10px;
    .query {
      margin-right: 10px;
    }
  }
}
/deep/.ant-select-selection--single {
  width: 12rem;
}
/deep/.ant-select-selection--multiple {
  width: 12rem;
}
</style>

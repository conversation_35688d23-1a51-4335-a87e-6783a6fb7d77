<template>
  <div class="input-content" style="width: 100%">
    <a-input
      style="width: 100%"
      @change="showHistory"
      @focus="
        choice == 1 ? (showHistoryList = true) : (showHistoryList = false)
      "
      @blur="blurInput"
      v-model="localValue"
      :placeholder="placeholder"
      maxLength="50"
    />
    <ul v-if="showHistoryList">
      <li v-for="word in historyList" :key="word" @click="setInputValue(word)">
        {{ word }}
      </li>
    </ul>
  </div>
</template>

<script>
// TODO 提示信息从后台接口返回
export default {
  props: {
    value: {
      type: String,
      required: true,
      default: "",
    },
    placeholder: {
      type: String,
      default: "初审意见",
    },
    historyList: [],
    choice: Number,
  },
  created() {
    console.log(this.choice, "llllll");
  },
  data() {
    return {
      inputValue: "",
      localValue: this.value,
      showHistoryList: false,
      // historyList: ["拟同意", "不同意", "希望再斟酌一下"]
    };
  },
  watch: {
    value(newVal) {
      console.log("bgbgbgbg");
      this.localValue = newVal;
      this.$emit("input", this.localValue);
    },
    // 监听choice的值
    choice(newVal) {
      console.log(newVal, "rrrr");
      //如果值为1代表选了同意
      if (newVal == 1) {
        //将意见数组的第一个值赋给input的值
        this.value = this.historyList[0];
        this.localValue = this.value;
        console.log(this.value, this.localValue, "oooo");
        // this.localValue = this.historyList[0];
      } else {
        //如果不为1代表选择了不同意或者没选择
        //input的值为空字符串不展示任何东西
        this.localValue = "";
        this.value = "";
        console.log(this.value, this.localValue, "oooo");
      }
    },
  },
  methods: {
    blurInput(val) {
      console.log("blurInput", val);
      setTimeout(() => {
        this.showHistoryList = false;
      }, 300);
    },
    setInputValue(word) {
      this.inputValue = word;
      this.$emit("input", word);
      this.showHistoryList = false;
    },
    showHistory(e) {
      console.log(e.target.value, this.inputValue, "bbbb");
      const inputValue = e.target.value;
      if (inputValue !== this.inputValue) {
        // 当输入的值有变化时，清空历史提示列表
      } else {
        console.log(this.historyList, "cccc");
        // 获取与输入值相同的历史提示列表
        const words = this.historyList.filter(
          (word) => word.indexOf(inputValue) > -1
        );
        if (words.length > 0) {
          // 如果有历史提示结果，则显示列表
          this.historyList = words;
        }
      }
      this.inputValue = inputValue;
      this.$emit("input", inputValue);
    },
  },
  computed: {
    hasHistoryList() {
      return this.historyList.length > 0;
    },
  },
};
</script>

<style lang="less" scoped>
.input-content {
  position: relative;
  width: 100%;
  > ul {
    list-style: none;
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100%;
    z-index: 10;
    > li {
      padding: 5px;
      background: #ffffff;
      cursor: pointer;
    }
  }
}
</style>

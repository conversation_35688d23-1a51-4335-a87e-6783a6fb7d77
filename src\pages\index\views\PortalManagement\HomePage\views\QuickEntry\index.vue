<!--
* <AUTHOR>
* @time 2020-9-9
* @dec 业务管理 - 机房人员备案
-->
<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="标题">
              <a-input
                v-model="queryParam.templateName"
                placeholder="请输入标题"
              />
            </a-form-item>
          </a-col>
          <a-col :md="(!advanced && 8) || 24" :sm="24">
            <span
              class="table-page-search-submitButtons"
              :style="
                (advanced && { float: 'right', overflow: 'hidden' }) || {}
              "
            >
              <a-button type="primary" @click="$refs.table.refresh(true)"
                >查询</a-button
              >
              <a-button
                style="margin-left: 8px"
                @click="() => (this.queryParam = {})"
                >重置</a-button
              >
            </span>
          </a-col>
        </a-row>
      </a-form>
      <div class="table-operator">
        <a-button type="primary" icon="plus" @click="handleAdd">添加</a-button>
      </div>
    </div>

    <s-table
      ref="table"
      size="default"
      rowKey="id"
      :columns="columns"
      :data="loadData"
      :showPagination="true"
    >
      <span slot="serial" slot-scope="text, record, index">
        {{ (pageNo - 1) * 10 + index + 1 }}
      </span>

      <!-- <span slot="description" slot-scope="text">
        <ellipsis :length="4" tooltip>{{ text }}</ellipsis>
      </span> -->
      <span slot="templateIcon" slot-scope="text">
        <img alt="暂无图片或未正确显示" :src="text" style="height:40px" />
      </span>

      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record)">修改</a>
          <a-divider type="vertical" />
          <a-popconfirm
            title="请认真核对，选中数据是否删除?"
            @confirm="() => handleDel(record)"
          >
            <a href="javascript:;">删除</a>
          </a-popconfirm>
        </template>
      </span>
    </s-table>
    <edit
      ref="EditModel"
      :visible="editVisible"
      :data="editData"
      @cancel="handleCancel"
      @ok="$refs.table.refresh()"
    />
  </a-card>
</template>

<script>
// 表格组件
import STable from "@/components/Table"
// import Ellipsis from "@/components/Ellipsis";
import Edit from "./compontents/Edit"

// API接口
import {
  ApiBusinessPageByCondition,
  ApiBusinessDeleteByFlowProcessRecordId
} from "@/pages/index/data/api/PortalManagement/HomePage/QuickEntry"

const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "serial" }
  },
  {
    title: "标题",
    dataIndex: "templateName"
  },
  {
    title: "图标",
    dataIndex: "templateIcon",
    scopedSlots: { customRender: "templateIcon" }
  },

  {
    title: "操作",
    dataIndex: "action",
    // fixed: "right",
    scopedSlots: { customRender: "action" }
  }
]

export default {
  name: "PortalManagementHomePageQuickEntry",
  components: {
    STable,
    // Ellipsis,
    Edit
  },
  data() {
    this.columns = columns
    return {
      editVisible: false, //修改弹框
      editData: {}, //修改提交的数据
      // 查询参数
      queryParam: {
        ascs: "",
        currentPage: 0,
        descs: "",
        templateId: "",
        templateName: ""
        // pageSize: 10
      },
      selectedRowKeys: [],
      // 加载数据方法 必须为 Promise 对象
      loadData: (parameter) => {
        // console.log("parameter", this.queryParam);
        this.pageNo = parameter.pageNo
        this.queryParam.currentPage = parameter.pageNo
        this.queryParam.current = this.queryParam.currentPage
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        return ApiBusinessPageByCondition(requestParameters)
          .then((res) => {
            return res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
    }
  },
  created() {},
  methods: {
    //关闭弹框
    handleCancel() {
      this.editVisible = false
      const form = this.$refs.EditModel.form
      form.resetFields() // 清理表单数据（可不做）
    },
    //添加
    handleAdd() {
      this.editVisible = true
      this.editData = {}
    },
    // 修改数据
    handleEdit(record) {
      this.editVisible = true
      this.editData = { ...record }
    },
    // 删除数据
    handleDel(record) {
      this.loading = true
      const params = {
        id: record.id
      }
      ApiBusinessDeleteByFlowProcessRecordId(params)
        .then(() => {
          this.$message.info("删除成功")
          this.$refs.table.refresh()
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

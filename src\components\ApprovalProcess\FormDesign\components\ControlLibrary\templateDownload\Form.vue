<!--
* <AUTHOR>
* @time 2021-9-15
* @dec 模板下载Form
-->
<template>
  <a-form-model
    :model="form"
    :label-col="labelCol"
    :rules="rules"
    :wrapper-col="wrapperCol"
  >
    <a-form-model-item label="文件名称">
      <a-input
        v-model="form.inputTitle"
        placeholder="请输入文件名称（注：必须带后缀名）"
        maxLength="20"
      />
    </a-form-model-item>
    <a-form-model-item label="文件地址">
      <a-input
        v-model="form.placeholder.fileAddress"
        placeholder="请输入文件地址(例：/minio桶名/文件名)"
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { checkPositiveNum } from "@/common/validate";

export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        optionsData: {},
        placeholder: {}
      },
      rules: {
        fileSize: [
          {
            validator: checkPositiveNum,
            trigger: "blur"
          }
        ]
      }
    };
  },

  watch: {
    data(data) {
      this.form = data;
    },
    form: {
      handler: function(form) {
        this.$emit("update:data", form);
      },
      deep: true
    }
  },
  methods: {}
};
</script>

<!--
* <AUTHOR>
* @time 2020-8-21
* @dec 系统管理 - 用户管理 - 设置过期时间
-->
<template>
  <a-modal
    title="设置过期时间"
    :width="640"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="
      () => {
        $emit('cancel');
      }
    "
  >
    <a-spin :spinning="loading">
      <a-form :form="form" v-bind="formLayout">
        <a-form-item label="设置过期时间">
          <a-date-picker
            show-time
            :disabled-date="disabledDate"
            :disabled-time="disabledDateTime"
            v-decorator="[
              'expireTime',
              {
                rules: [{ required: true, message: '请选择过期时间' }]
              }
            ]"
          >
            <template slot="renderExtraFooter">
              <a @click="setOneMonth" style="margin-right:10px">一个月</a
              ><a @click="setThreeMonth">三个月</a>
            </template></a-date-picker
          >
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from "lodash.pick";
import moment from "moment";
import { ApiSecuritySetExpireTime } from "@/pages//index/data/api/SystemManagement/User";

// 表单字段
const fields = ["expireTime"];

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    dataItem: {
      type: Object,
      default: () => null
    }
  },
  data() {
    this.formLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 13 }
      }
    };
    return {
      loading: false,
      form: this.$form.createForm(this),
      formDisabled: false, //默认表单可编辑
      modelTitle: null
    };
  },
  created() {
    // 防止表单未注册
    fields.forEach(v => this.form.getFieldDecorator(v));

    // 当 data 发生改变时，为表单设置值
    this.$watch("data", () => {
      this.data && this.form.setFieldsValue(pick(this.data, fields));
    });
  },
  methods: {
    moment,
    //确定提交
    handleOk() {
      this.loading = true;
      this.form.validateFields((errors, values) => {
        if (!errors) {
          console.log("values", values);
          const params = {
            ...values,
            expireTime: values["expireTime"].format("YYYY-MM-DD HH:mm:ss"),
            userId: this.dataItem
          };
          // params.expireTime = params.expireTime + " 23:59:59";
          ApiSecuritySetExpireTime(params)
            .then(res => {
              if (res.code == 0) {
                this.$emit("cancel");
                // 重置表单数据
                this.form.resetFields();
                // 刷新表格
                this.$emit("ok");
                this.$message.info("设置成功");
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.loading = false;
        }
      });
    },
    //预设时间（一个月）
    setOneMonth() {
      let oneMonth = moment(new Date()).add(1, "months");
      this.form.setFieldsValue({
        expireTime: oneMonth
      });
    },
    //预设时间（三个月）
    setThreeMonth() {
      let threeMonth = moment(new Date()).add(3, "months");
      this.form.setFieldsValue({
        expireTime: threeMonth
      });
    },
    range(start, end) {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    },
    //禁用日期处理
    disabledDate(current) {
      return current && current < moment().startOf("day");
    },
    //禁用时间处理
    disabledDateTime(current) {
      if (
        moment(current)
          .startOf("day")
          .isSame(moment().startOf("day"))
      ) {
        let hour, minute;
        hour = this.range(0, 24).splice(0, moment().hour());
        if (moment(current).hour() === moment().hour()) {
          minute = this.range(0, 60).splice(0, moment().minute());
        } else {
          minute = [];
        }
        return {
          disabledHours: () => hour,
          disabledMinutes: () => minute
        };
      } else {
        return {};
      }
    }
  }
};
</script>

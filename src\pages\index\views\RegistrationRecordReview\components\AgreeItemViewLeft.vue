<template>
  <div
    class="agree-item-view-left"
    :class="{
      'agree-item-view-left-bg1': numType == 1,
      'agree-item-view-left-bg2': numType == 2,
      'agree-item-view-left-bg3': numType == 3,
      'agree-item-view-left-bg4': numType == 4,
      'agree-item-view-left-bg5': numType == 5,
    }"
  >
    <!-- type1 绿色  -->
    <div
      v-if="index"
      class="agree-item-left-num"
      :class="{
        'agree-item-left-num-bg1': numType == 1,
        'agree-item-left-num-bg2': numType == 2,
        'agree-item-left-num-bg3': numType == 3,
        'agree-item-left-num-bg4': numType == 4,
        'agree-item-left-num-bg5': numType == 5,
      }"
    >
      {{ index }}
    </div>
    <div class="agree-item-left-content">
      <div
        v-if="index && title.titleName"
        class="agree-item-left-content-title"
        :class="{
          'agree-item-left-content-title-border':
            (annex && !Array.isArray(annex) && annex.filePath) ||
            (annex && Array.isArray(annex) && annex.length > 0),
        }"
      >
        <span class="agree-item-left-content-title-name">
          {{ title.titleName }}
        </span>
        <span v-html="title.value" class="agree-item-left-content-title-value">
        </span>
      </div>
      <div
        class="agree-item-left-content-desc"
        v-if="
          (index &&
            ((annex && !Array.isArray(annex) && annex.filePath) ||
              (annex && Array.isArray(annex) && annex.length > 0))) ||
          (!index && title.titleName)
        "
      >
        <!-- 选项类型 -->
        <template v-if="index">
          <!-- 传入数组 -->
          <template v-if="Array.isArray(annex) && annex.length > 0">
            <div style="padding-bottom: 9px">
              <a-row :gutter="24">
                <a-col
                  span="12"
                  :key="item.id"
                  v-for="item in annex"
                  style="width: 100%; display: flex"
                >
                  <!-- <a
                    :href="`${downloadUrl}?id=${item.id}`"
                    :download="annex.oldName"
                    >{{ item.oldName }}</a
                  > -->
                  <div
                    :key="index"
                    @click="previewOnline(item, true)"
                    class="attachmentItem attachmentItem100"
                  >
                    <a-tooltip :title="item.oldName">
                      {{ item.oldName }}
                    </a-tooltip>
                  </div>
                  <span
                    class="download-link"
                    style="width: 80px"
                    @click="previewOnline(item, false)"
                    >下载</span
                  >
                </a-col>
              </a-row>
            </div>
          </template>
          <div style="padding-bottom: 9px" v-else-if="annex && annex.filePath">
            <a
              :href="`${downloadUrl}?id=${annex.filePath}`"
              :download="annex.fileName"
              >{{ annex.fileName }}ss</a
            >
          </div>
        </template>
        <!-- 附件类型 -->
        <template v-else>
          <template>
            <div style="padding-bottom: 9px; display: flex">
              <span style="display: inline-block; width: 95px">{{
                title.titleName
              }}</span>
              <div
                v-for="(item, index) in annex"
                :key="index"
                style="width: calc(100% - 95px); display: flex"
              >
                <div
                  :key="index"
                  @click="previewOnline(item, true)"
                  class="attachmentItem attachmentItem100"
                >
                  <a-tooltip :title="item.oldName">
                    {{ item.oldName }}
                  </a-tooltip>
                </div>
                <span class="download-link" @click="previewOnline(item, false)"
                  >下载</span
                >
              </div>
            </div>
          </template>
        </template>
      </div>
      <slot></slot>
      <div v-if="previewOnlineInfo.visible">
        <file-preview-online
          :visible="previewOnlineInfo.visible"
          :type="previewOnlineInfo.type"
          :url="previewOnlineInfo.url"
          @closeModal="handleFilePreviewOnlineModalWithClosing"
          @previewCallback="previewCallback"
        />
      </div>
    </div>

    <!-- <a-modal title="1234" :visible="true" :footer="null"> -->
    <!-- <file-preview-online
      :visible="true"
      type="docx"
      url="http://static.shanhuxueyuan.com/test6.docx"
    /> -->
    <!-- </a-modal> -->
  </div>
</template>

<script>
import FilePreviewOnline from "@/components/FilePreviewOnline";
import { BASE_URL } from "Config";
export default {
  components: {
    FilePreviewOnline,
  },
  data() {
    return {
      downloadUrl: "",
      previewOnlineInfo: {
        visible: false,
        url: "",
        type: "",
      },
      ifNeedPreviewOnline: true, //预留口，
      domain_url: "",
    };
  },
  props: {
    /**
     * @params 标题值 1 green 2 blue 3 orange
     */
    numType: {
      type: Number,
      default: 2,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    // 附件
    annex: {
      type: Object,
      default: () => {
        return null;
      },
    },
    //序号
    index: {
      type: [Number, String],
      default: "",
    },
    //标题
    title: {
      type: Object,
      default: () => ({ titleName: "titleName", value: "title" }),
    },
  },
  mounted() {
    let tmpDomainUrl = BASE_URL;
    this.$set(this, "domain_url", tmpDomainUrl);
    this.$set(
      this,
      "downloadUrl",
      `${tmpDomainUrl}/mapi/common/attachment/download`
    );
  },
  methods: {
    previewOnline: function (file, ifNeedPreviewOnline) {
      this.ifNeedPreviewOnline = ifNeedPreviewOnline;
      console.log(file, "~~~~!@#");
      const { id = "" } = file;
      if (!id) {
        this.$message.error("未获取到文件信息");
        return;
      }
      // const fileUrl = BASE_URL + "/common/attachment/download?id=" + fileId;
      const url = `${this.domain_url}/common/attachment/download?id=${id}`;

      if (this.ifNeedPreviewOnline) {
        //当前近支持.docx|.pdf格式
        if (
          file.fileType == "docx" ||
          file.fileType == "pdf" ||
          file.fileType == "jpg" ||
          file.fileType == "png" ||
          file.fileType == "jpeg"
        ) {
          this.$set(this.previewOnlineInfo, "visible", true);
          this.$set(this.previewOnlineInfo, "url", url);
          this.$set(this.previewOnlineInfo, "type", file.fileType);
        } else {
          window.open(url);
        }
      } else {
        window.open(url);
      }
    },
    handleFilePreviewOnlineModalWithClosing: function () {
      this.$set(this.previewOnlineInfo, "visible", false);
      this.$set(this.previewOnlineInfo, "url", "");
      this.$set(this.previewOnlineInfo, "type", "");
    },
    previewCallback: function (flag) {
      if (flag == "success") {
        console.log(flag);
      } else {
        console.log(flag);
        // this.$message.info("在线预览加载失败，即将为您下载文件至本地预览！");
        window.open(this.previewOnlineInfo.url);
        this.handleFilePreviewOnlineModalWithClosing();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.agree-item-view-left {
  display: flex;
  width: 100%;
  display: flex;
  background: #f0fcf8;
  border-radius: 6px;
  //green
  &-bg1 {
    background: #f0fcf8;
  }
  //blue
  &-bg2 {
    background: #eef7ff;
  }
  // yellow
  &-bg3 {
    background: #fff7ec;
  }
  //setup-youself
  &-bg4 {
    background: #f0fcf8;
  }
  > .agree-item-left-num {
    width: 34px;

    border-radius: 6px 0px 0px 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: #fff;
    &-bg1 {
      background: #26c06d;
    }
    &-bg2 {
      background: #1777ff;
    }
    &-bg3 {
      background: #ec9a50;
    }
    &-bg4 {
      background: #26c06d;
    }
  }

  > .agree-item-left-content {
    padding: 0px 32px 0px 24px;
    width: 100%;
    /deep/ .ant-col-12 {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .agree-item-left-content-title {
      display: flex;
      width: 50%;
      width: 100%;
      align-items: center;
      font-size: 16px;
      padding: 19px 0px 19px 0px;
      > .agree-item-left-content-title-name {
        color: #86909c;
        white-space: nowrap;
        margin-right: 15px;
      }
      > .agree-item-left-content-title-value {
        color: #000;
        white-space: pre-wrap;
      }
    }
    > .agree-item-left-content-title-border {
      border-bottom: 1px solid #999;
    }
    > .agree-item-left-content-desc {
      font-size: 16px;
      padding-top: 9px;
      > a {
        margin-right: 10px;
      }
    }
  }
}
.attachmentItem100 {
  width: calc(100% - 40px);
}
.attachmentItem50 {
  width: calc(50% - 40px);
}
.attachmentItem {
  cursor: pointer;
  display: flex;
  align-items: center;
  background: #f3f9ff;
  overflow: hidden;
  span {
    display: block;
    width: calc(100% - 40px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #1777ff;
    cursor: pointer;
    margin-right: 5px;
  }
}
.download-link {
  display: inline-block;
  width: 80px;
  color: #1777ff;
  cursor: pointer;
  margin-left: 10px;
}
</style>

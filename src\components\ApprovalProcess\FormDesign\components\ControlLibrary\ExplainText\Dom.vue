<!--
* <AUTHOR>
* @time 2020-11-24
* @dec 说明文字控件
-->
<template>
  <div>
    <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item
        :label="data.inputTitle || '说明主题'"
        style="margin-bottom:unset"
      >
      </a-form-model-item>
    </a-form-model>
    <!-- <div>
      {{ data.optionsData.content || "说明内容" }}
    </div> -->
    <div v-if="data.optionsData.content">
      <div
        v-for="(item, index) in data.optionsData.content.split('\n')"
        :key="index"
      >
        {{ item }}
      </div>
    </div>
    <div v-else>说明内容</div>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        explainText: null
      }
    };
  }
};
</script>
<style lang="less" scoped>
@import "../index.less";
</style>

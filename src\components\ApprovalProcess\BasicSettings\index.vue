<!--
* <AUTHOR>
* @time 2020-9-2
* @dec 基础表单配置
-->
<template>
  <div id="basicSettings">
    <a-card>
      <a-form-model
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="审批流程名称" prop="templateName">
          <a-input
            maxLength="50"
            v-model="form.templateName"
            placeholder="请输入审批流程名称"
          >
          </a-input>
        </a-form-model-item>
        <a-form-model-item label="选择分组" prop="gruopId">
          <a-spin :spinning="groupNameloading">
            <a-select
              :getPopupContainer="
                triggerNode => {
                  return triggerNode.parentNode || document.body;
                }
              "
              ref="select"
              placeholder="请选择分组"
              allowClear
              v-model="form.gruopId"
            >
              <a-select-option
                v-for="item in groupList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.groupName }}
              </a-select-option>
            </a-select>
          </a-spin>
        </a-form-model-item>

        <a-form-model-item label="审批类型" prop="approvalType">
          <a-select
            placeholder="请选择审批类型"
            allowClear
            v-model="form.approvalType"
            @change="
              () => {
                form.businessType = undefined;
              }
            "
          >
            <a-select-option
              v-for="item in options.approvalType"
              :key="item.dictValue"
              :value="item.dictValue"
            >
              {{ item.nameCn }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="业务类型" prop="businessType">
          <a-select
            placeholder="请选择业务类型"
            allowClear
            v-model="form.businessType"
            :getPopupContainer="
              triggerNode => {
                return triggerNode.parentNode || document.body;
              }
            "
          >
            <a-select-option
              v-for="item in options.businessType"
              :key="item.dictValue"
              :value="item.dictValue"
            >
              {{ item.nameCn }}
            </a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="模板图标" prop="icon" ref="fileList">
          <div class="clearfix">
            <a-upload
              action=""
              list-type="picture-card"
              :file-list="fileList"
              :before-upload="beforeUpload"
              accept="image/*"
              :remove="
                () => {
                  form.icon = '';
                  $nextTick(() => {
                    $refs.fileList.onFieldChange();
                  });
                }
              "
              @preview="handlePreview"
              @change="handleUpload"
            >
              <div v-if="this.fileList < 1">
                <a-icon type="plus" />
                <div class="ant-upload-text">
                  选择上传
                </div>
              </div>
            </a-upload>
            <a-modal
              :visible="previewVisible"
              :footer="null"
              @cancel="handleCancel"
            >
              <img alt="example" style="width: 100%" :src="previewImage" />
            </a-modal>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="审批流程说明"
          prop="remark"
          :help="
            form.remark ? '(' + form.remark.length + '/100)' : '最多输入100字'
          "
        >
          <div class="basicSettings-remark">
            <a-input
              v-model="form.remark"
              placeholder="请填写流程说明"
              maxLength="100"
              type="textarea"
              :autoSize="{ minRows: 4, maxRows: 4 }"
              suffix="RMB"
            />
            <!-- @input="remarkInput" -->
            <!-- <span class="basicSettings-remark-number">({{ txtVal }}/100)</span> -->
          </div>
        </a-form-model-item>
        <!-- <a-form-model-item>
          <a-button class="basic-save" type="primary" @click="handleOk"
            >保存</a-button
          >
          <a-divider type="vertical" class="ant-divider-theme-no-line" />
          <a-button class="basic-reset" @click="handleReset">重置</a-button>
        </a-form-model-item> -->
      </a-form-model>
    </a-card>
  </div>
</template>
<script>
import {
  ApiactivitifindAllAtGroupTemplate,
  // Apiactiviticreate,
  Apicommonupload
} from "@/data/api/components/ApprovalProcess";
// import { throttle } from "throttle-debounce";
import { mapGetters } from "vuex";
// let throttled = throttle(300, false, (form, vm) => {
// vm.$store.commit("approvalProcess/setBasicSettings", vm.formatData(form));
// });
function getBase64(file) {
  if (!file) return;
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

export default {
  // name: "applyAgent",
  data() {
    return {
      txtVal: 0,
      remark: "",
      groupNameloading: false,
      previewVisible: false,
      previewImage: "",
      fileList: [],
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        // 审批类型
        approvalType: undefined,
        // 业务类型
        businessType: undefined,
        templateName: "",
        remark: "",
        icon: "",
        gruopId: undefined
      },
      rules: {
        templateName: [
          {
            required: true,
            message: "请输入流程审批名称",
            trigger: "blur"
          }
        ],
        gruopId: [{ required: true, message: "请选择分组" }],
        approvalType: [{ required: true, message: "请选择审批类型" }],
        businessType: [{ required: true, message: "请选择业务类型" }],
        icon: [{ required: true, message: "请上传图片" }]
      },
      groupList: [],
      options: {
        approvalType: this.$store.getters["dictionaries/getType"]("ApplyType"),
        businessType: []
      }
    };
  },
  watch: {
    // form: {
    //   handler: function(form) {
    //     throttled(form, this);
    //   },
    //   deep: true
    // },
    basicSettings(data) {
      this.setData(data);
    },
    "form.approvalType": {
      handler() {
        // this.form.businessType = undefined;
        this.options.businessType = this.$store.getters[
          "dictionaries/getChildByTypeCode"
        ]("ApplyType", this.form.approvalType);
      },
      immediate: true
    }
  },
  destroyed() {},
  computed: {
    // 监听vuex数据变化
    ...mapGetters("approvalProcess", ["basicSettings"])
  },
  methods: {
    // 动态显示多行输入框字数
    // remarkInput() {
    //   this.txtVal = this.form.remark.length;
    // },

    // 回显数据
    setData(data) {
      this.form = {
        // 审批类型
        approvalType: data.approvalType || undefined,
        // 业务类型
        businessType: data.businessType || undefined,
        templateName: data.templateName || "",
        remark: data.remark || "",
        icon: data.icon || "",
        gruopId: data.gruopId
      };
      if (data.icon) {
        let obj = {
          uid: "-1",
          name: "xxx.png",
          thumbUrl: data.icon,
          type: "image/png"
        };
        this.fileList = [obj];
      }
    },
    // 获取数据
    getData() {
      return { ...this.form };
    },
    //默认调用查询分组接口
    findAllAtGroupTemplate() {
      this.loading = true;
      let params = {
        // groupName: "",
        // id: "",
        // updata: "true"
      };
      ApiactivitifindAllAtGroupTemplate(params)
        .then(res => {
          this.groupList = res.data;
          console.log("groupList", this.groupList);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCancel() {
      this.previewVisible = false;
    },

    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      console.log("121", file.preview, file.url);
      this.previewImage = file.url || file.preview || file.thumbUrl;
      this.previewVisible = true;
    },
    beforeUpload() {
      return false;
    },
    // 上传图片调用接口
    handleUpload({ file, fileList }) {
      this.fileList = fileList;
      if (fileList.length > 1 || fileList.length < 1) {
        return false;
      }
      this.loading = true;
      let params = {
        objectId: "5",
        name: "1",
        type: "jpg",
        file: file
      };
      Apicommonupload(params)
        .then(res => {
          this.form.icon = res.data.thumbnailUrl;
          this.$refs.fileList.onFieldChange();
        })
        .catch(() => {
          this.fileList = [];
        })
        .finally(() => {
          this.loading = false;
        });
    }
  },
  mounted() {
    this.findAllAtGroupTemplate();
  }
};
</script>
<style lang="less" scoped>
#basicSettings {
  width: 600px;
  margin: auto;
  height: calc(100vh - 65px);
  overflow-y: auto;
}
// .basicSettings-remark {
//   display: flex;
//   justify-content: flex-end;
//   align-items: flex-end;
// }
</style>

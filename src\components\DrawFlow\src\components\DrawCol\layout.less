
.branch-wrap {
    display: inline-flex;
    width: 100%;
    .branch-box-wrap {
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      min-height: 270px;
      width: 100%;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      .branch-box {
        display: flex;
        overflow: visible;
        min-height: 180px;
        height: auto;
        border-bottom: 2px solid #cccccc;
        border-top: 2px solid #cccccc;
        position: relative;
        margin-top: 15px;
        .add-branch {
          border: none;
          outline: none;
          user-select: none;
          justify-content: center;
          font-size: 12px;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          border-radius: 15px;
          color: #0089ff;
          background: #fff;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
          position: absolute;
          top: -16px;
          left: 50%;
          transform: translateX(-50%);
          transform-origin: center center;
          cursor: pointer;
          z-index: 1;
          display: inline-flex;
          align-items: center;
          -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
        .col-box {
          background: #f0f2f5;
          display: inline-flex;
          -webkit-box-orient: vertical;
          -webkit-box-direction: normal;
          flex-direction: column;
          -webkit-box-align: center;
          align-items: center;
          position: relative;
          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
            margin: auto;
            width: 2px;
            height: 100%;
            background-color: #cacaca;
          }
  
          .top-left-cover-line {
            position: absolute;
            height: 3px;
            width: 50%;
            background-color: #f0f2f5;
            top: -2px;
            left: -1px;
          }
          .bottom-left-cover-line {
            position: absolute;
            height: 3px;
            width: 50%;
            background-color: #f0f2f5;
            bottom: -2px;
            left: -1px;
          }
          .top-right-cover-line {
            position: absolute;
            height: 3px;
            width: 50%;
            background-color: #f0f2f5;
            top: -2px;
            right: -1px;
          }
          .bottom-right-cover-line {
            position: absolute;
            height: 3px;
            width: 50%;
            background-color: #f0f2f5;
            bottom: -2px;
            right: -1px;
          }
        }
      }
    }
  }
  
.dingflow-design .auto-judge .sort-left,
.dingflow-design .auto-judge .sort-right {
  position: absolute;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 1;
}
.condition-node {
  min-height: 220px;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  -webkit-box-flex: 1;
  .condition-node-box {
    padding-top: 30px;
    padding-right: 50px;
    padding-left: 50px;
    -webkit-box-pack: center;
    justify-content: center;
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-flex: 1;
    flex-grow: 1;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      width: 2px;
      height: 100%;
      background-color: #cacaca;
    }
    .auto-judge {
      position: relative;
      width: 220px;
      min-height: 72px;
      background: #ffffff;
      border-radius: 4px;
      padding: 14px 19px;
      cursor: pointer;
      .close {
        width: 14px;
        height: 14px;
        display: none;
        position: absolute;
        right: -2px;
        top: -2px;
        font-size: 14px;
        text-align: center;
        line-height: 20px;
        z-index: 2;
        color: rgba(25, 31, 37, 0.56);
      }
    .priority-title {
      display: block;
      margin-right: 10px;
      float: right;
      color: rgba(25, 31, 37, 0.56);
    }
      &:hover {
        .close{
          display: block;
        }
        .priority-title{
          display: none;
        }
        &::after {
          border: 1px solid #3296fa;
          box-shadow: 0 0 6px 0 rgba(50, 150, 250, 0.3);
        }
      }
      &::after{
          pointer-events: none;
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 2;
          border-radius: 4px;
          border: 1px solid transparent;
          transition: all 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
          box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
      }
      .title-wrapper {
        position: relative;
        font-size: 12px;
        color: #15bc83;
        text-align: left;
        line-height: 16px;
        .editable-title {
          line-height: 15px;
          overflow: hidden; 
          border-bottom: dashed 1px transparent;
          display: inline-block;
          max-width: 120px;
          white-space: nowrap;
          text-overflow: ellipsis;
          &:hover {
            border-bottom: dashed 1px #ffffff;
          }
          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 40px;
          }
        }
       
      }
      .content {
        font-size: 14px;
        color: #191f25;
        text-align: left;
        margin-top: 6px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .sort-right {
        right: 0;
        border-left: 1px solid #f6f6f6;
      }
    }
  }
}
import api from "@/common/api";
import { BASE_URL } from "Config";

// 企业查询
export function backGroundPageByCondition(params) {
  return api({
    url: BASE_URL + "/manager/companyBasic/screen/backGroundPageByCondition",
    method: "post",
    params,
  });
}
// 企业 基本信息
export function getCompanyById(params) {
  return api({
    url: BASE_URL + "/manager/companyBasic/screen/getCompanyById",
    method: "post",
    params,
  });
}
// 企业 重点关注
export function updateKey(params) {
  return api({
    url: BASE_URL + "/manager/companyBasic/screen/updateKey",
    method: "post",
    params,
  });
}
// 企业 入驻园区信息
export function getResidingPark(params) {
  return api({
    url: BASE_URL + "/manager/companyBasic/screen/getResidingPark",
    method: "post",
    params,
  });
}
// 企业税收基本信息
export function getCompanyTaxationBasic(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxation/screen/getCompanyTaxationBasic",
    method: "post",
    params,
  });
}
// 企业税收信息分页查询
export function pageOneCompany(params) {
  return api({
    url: BASE_URL + "/manager/companyTaxation/screen/pageOneCompany",
    method: "post",
    params,
  });
}

<template>
  <div>
    <a-upload
      name="file"
      :accept="accept"
      :multiple="false"
      :headers="headers"
      :beforeUpload="beforeUpload"
      :disabled="disabled == true"
      :showUploadList="false"
    >
      <!-- :action="action" -->
      <!-- @change="handleChange" -->
      <template v-if="!useCustom">
        <a-button type="primary" :disabled="disabled == true">
          <a-icon type="upload" /> 上传资料</a-button
        ></template
      >
      <template v-else>
        <slot name="import"></slot>
      </template>
    </a-upload>
  </div>
</template>

<script>
/**
 * props:
 * accept  接受上传的文件类型  default: ".doc,.docx,.pdf", type: String, required:false
 * headers  设置上传的请求头部，IE10 以上有效  type: Object, required:false
 * action  上传的地址 type: String, required:false
 * multiple  是否支持多选文件，ie10+ 支持  default: false, type: Boolean, required:false
 * disabled 是否禁用  default: false, type: Boolean, required:false
 * useCustom 是否自定义设置上传按钮及文本 default: false, type: Boolean, required:false
 * isLimited  是否限制文件大小 default: true, type: Boolean, required:false
 * limitedSize  default: MAX_FILE_SIZE_LIMITED, type: Number, required:false
 * businessId: String, 业务关联ID required:true
 * event:
 * onFileOverSize Func(sizeInfoVO)
 *  sizeInfoVO = {
      file: file,
      limited: MAX_FILE_SIZE_LIMITED,
    }
   handleFileCallback Func(file)
 */
const MAX_FILE_SIZE_LIMITED = 50 * 1024 * 1024;
import { BASE_URL } from "Config";
import { ApiAttachmentUpload } from "APIs/Common/index.js";
export default {
  props: {
    accept: {
      type: String,
      default: ".doc,.docx,.pdf,.png,.jpeg,.jpg",
    }, //接受上传的文件类型
    headers: Object, //设置上传的请求头部，IE10 以上有效
    action: {
      type: String,
      required: false,
      default: BASE_URL + "/common/attachment/upload",
    }, //上传的地址
    multiple: {
      type: Boolean,
      default: false,
    }, //是否支持多选文件，ie10+ 支持
    disabled: {
      type: Boolean,
      default: false,
    }, //是否禁用
    useCustom: {
      type: Boolean,
      default: false,
    }, //是否自定义设置上传按钮及文本
    isLimited: {
      type: Boolean,
      default: true,
    }, //是否限制文件大小
    limitedSize: {
      type: Number,
      default: MAX_FILE_SIZE_LIMITED,
    }, //限制导入文件大小
    businessId: String, //业务关联ID
  },
  data() {
    return {
      fileInfo: {
        file: null,
      },
    };
  },
  methods: {
    beforeUpload: function (file, fileList) {
      console.log(this.businessId, "businessId");
      console.log(file, fileList, "111");
      if (!this.businessId) {
        return false;
      }
      if (this.accept == ".pdf" && file.type != "application/pdf") {
        //仅支持上传pdf文件
        console.log(this.accept, this.file);
        this.$message.error("请上传pdf文件");
        return false;
      }
      console.log(111111);
      if (this.isLimited) {
        let tmpVO = {
          file: file,
          limited: MAX_FILE_SIZE_LIMITED,
        };
        if (this.limitedSize && this.limitedSize > 0) {
          if (file.size > this.limitedSize) {
            tmpVO["limited"] = this.limitedSize;
            this.$emit("onFileOverSize", tmpVO);
            return false;
          }
        } else {
          if (file.size > MAX_FILE_SIZE_LIMITED) {
            this.$emit("onFileOverSize", tmpVO);
            return false;
          }
        }
      }
      const nameAndType = file.name;
      let tmpArr = [];
      let name, type;
      if (nameAndType) {
        tmpArr = nameAndType.split(".");
        name = tmpArr[0];
        type = tmpArr[1];
      }

      const formData = new FormData();
      formData.append("file", file);
      formData.append("businessType", this.businessId);
      formData.append("fileName", name);
      formData.append("fileType", type);
      ApiAttachmentUpload(formData)
        .then((res) => {
          console.log(res);
          if (res.code == 0) {
            this.$message.info("上传成功!");
            const fileId = res.data?.id;
            const fileName = res.data?.oldName;
            const businessType = res.data?.type;
            const fileUrl = "/common/attachment/download?id=" + fileId;
            const fileNameArr = fileName.split(".");
            const fileType = fileNameArr[1];
            const fileNewName = res.data?.newName;
            console.log(fileUrl, "url~~~");
            const result = {
              fileId,
              fileName,
              businessType,
              fileUrl,
              fileType,
              fileNewName,
            };
            this.$emit("handleFileCallback", result);
          }
        })
        .catch((error) => {
          console.log(error);
          this.$message.error(error.data.msg || "上传失败");
        });
      return false;
    },
    // handleChange(info) {
    //   if (info.file.status !== "uploading") {
    //     console.log("~~~~upload", info.file, info.fileList);
    //   }
    //   if (info.file.status === "done") {
    //     console.log(
    //       "~~~~upload",
    //       `${info.file.name} file uploaded successfully`
    //     );
    //   } else if (info.file.status === "error") {
    //     console.log("~~~~upload", `${info.file.name} file upload failed.`);
    //   }
    //   this.$emit("handleFileCallback", info.file);
    // },
  },
};
</script>

<style></style>

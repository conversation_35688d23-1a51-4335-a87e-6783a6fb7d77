/*
 * @Author: 王正勇 <EMAIL>
 * @Date: 2025-03-31 17:00:07
 * @LastEditors: 王正勇 <EMAIL>
 * @LastEditTime: 2025-05-08 16:49:44
 * @FilePath: \zhys-admin\config\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const config = {};
let BASE_URL = "";
console.log(window.location.hostname, "window.location");

const ENVIRONMENT_MODE = process.env.VUE_APP_ENVIRONMENT_MODE; //1开发环境，2uat环境，3product环境
console.log(ENVIRONMENT_MODE, "ENVIRONMENT_MODE");
if (ENVIRONMENT_MODE == 1) {
  if (process.env.NODE_ENV === "development") {
    // BASE_URL = "https://hss-dev.fairyclass.cn/web-manage/mapi"
    BASE_URL = "http://*************:8009" + "/mapi";
    // BASE_URL = "/mapi";
  } else {
    //
    BASE_URL = "http://*************:8008" + "/mapi";
  }
} else if (ENVIRONMENT_MODE == 2) {
  if (process.env.NODE_ENV === "development") {
    // BASE_URL = "https://hss-dev.fairyclass.cn/web-manage/mapi"

    BASE_URL = "/mapi";
  } else {
    if (window.location.hostname == "*************") {
      BASE_URL = "http://*************:9011/backend" + "/mapi";
    } else {
      BASE_URL = "https://huajing.fairyclass.cn/mapi";
    }
  }
} else if (ENVIRONMENT_MODE == 3) {
  // BASE_URL = "http://*************:8008" + "/mapi";
  // BASE_URL = "http://**************:10208" + "/mapi";
  BASE_URL = "http://**************:32001/backend" + "/mapi";
}
console.log(BASE_URL, "~~~~base-url");
export { BASE_URL };
export default config;

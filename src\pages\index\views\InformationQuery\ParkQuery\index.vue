<template>
  <router-view v-if="$route.meta.level == 3"> </router-view>
  <div v-else>
    <a-card>
      <a-form
        :form="form"
        :label-col="formItemLayout.labelCol"
        :wrapper-col="formItemLayout.wrapperCol"
        @submit="handleSubmit"
      >
        <a-row :gutter="40" align="center">
          <a-col :span="8">
            <a-form-item label="归并园区">
              <!-- <a-input
                placeholder="请输入归并园区名称"
                v-model="queryParam.mergedParkName"
              ></a-input> -->
              <a-select
                :getPopupContainer="
                  triggerNode => {
                    return triggerNode.parentNode || document.body;
                  }
                "
                allowClear
                v-model="queryParam.mergedParkName"
                @change="changeIndustryComponey"
                placeholder="全部"
                style="width:100%"
              >
                <a-select-option
                  :value="item"
                  v-for="item in enumerateObj.industryComponey"
                  :key="item"
                  >{{ item }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="园区名称">
              <a-input
                placeholder="请输入园区名称"
                v-model="queryParam.parkName"
                allowClear
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8" align="right" justify="center">
            <a-form-item :wrapper-col="{ span: 24 }">
              <a-button type="primary" @click="search" style="margin-right:20px"
                >查询</a-button
              >
              <a-button type="default" @click="reset">重置</a-button>
              <a-button
                type="primary"
                class="export"
                style="margin-left:15px"
                @click="deduced()"
                >导出</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>
    <a-card style="width:100%;margin-top:15px">
      <div style="display: flex; justify-content: end; margin-bottom: 20px">
        <a-button type="primary" @click="add()">新增</a-button>
      </div>
      <s-table
        ref="table"
        size="default"
        :columns="columns"
        :data="loadData"
        :scroll="{ x: 1000 }"
        rowKey="serialNo"
      >
        <span slot="serial" slot-scope="text, record, index">
          {{ (pageNo ? pageNo - 1 : 0) * pageSize + index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template>
            <a href="javascript:;" @click="toDetail(text, record)">查看</a>
          </template>
          <template>
            <a
              href="javascript:;"
              style="margin-left: 10px"
              @click="toEdit(record)"
              >编辑</a
            >
          </template>
          <template>
            <a
              href="javascript:;"
              style="margin-left: 10px"
              @click="toAddBuilding(record)"
              >新增楼号</a
            >
          </template>
          <template>
            <a
              href="javascript:;"
              style="margin-left: 10px"
              @click="toEditBuilding(record)"
              >编辑楼号</a
            >
          </template>
        </span>
      </s-table>
    </a-card>
    <addParkInfo
      :visible="visible"
      :parkName="parkName"
      :id="id"
      @close="close"
      @refresh="search"
      :formData="formData"
    ></addParkInfo>
    <!-- 新增园区 -->
    <newParkInfo
      :visible="newVisible"
      @close="newClose"
      @refresh="search"
    ></newParkInfo>
    <!-- 新增或编辑楼栋号/楼层 -->
    <newParkBuildingInfo
      :newBuildingRecord = "newBuildingRecord"
      :title="newBuildingTitle"
      :visible="newBuildingVisible"
      @close="newBuildingClose"
      @refresh="search"
    ></newParkBuildingInfo>
  </div>
</template>

<script>
import { queryParkBuildings } from "@/pages/demo/data/api/api/park";
import STable from "@/components/Table";
import addParkInfo from "./components/addParkInfo.vue";
import newParkInfo from "./components/newParkInfo.vue";
import newParkBuildingInfo from "./components/newParkBuildingInfo.vue";
import { parseNumFloat } from "@/common/utils/utils.js";
import {
  ApiSearchAllMergePark,
  ApiExportParkinfoMation,
  ApiGetParkInfo
} from "@/pages/index/data/api/InfomationQuery";
export default {
  components: {
    STable,
    addParkInfo,
    newParkInfo,
    newParkBuildingInfo
  },
  data() {
    return {
      enumerateObj: {
        industryComponey: []
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 22 },
          sm: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        }
      },
      pageNum: 1,
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "serial" },
          width: 80,
          fixed: "left"
        },
        {
          title: "园区",
          // width: 400,
          dataIndex: "parkName",
          align: "center"
        },
        {
          title: "归并园区",
          dataIndex: "mergedParkName",
          align: "center"
        },
        {
          title: "实业公司",
          dataIndex: "lessor",
          align: "center"
        },
        {
          title: "总面积(平米)",
          dataIndex: "parkTotalArea",
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          }
        },
        {
          title: "楼宇数(栋)",
          dataIndex: "buildingNumbers",
          align: "center"
        },
        {
          title: "入驻数(家)",
          dataIndex: "parkEnterpriseSettlementCount",
          align: "center"
        },
        {
          title: "空置率(%)",
          dataIndex: "parkVacancyRate",
          align: "center",
          customRender(text) {
            return parseNumFloat(text);
          }
        },
        {
          title: "区属地率(%)",
          dataIndex: "districtTerritorialRate",
          align: "center",
          width: 120
        },
        {
          title: "镇属地率(%)",
          dataIndex: "townTerritorialRate",
          align: "center",
          width: 120
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "center",
          width: 170,
          scopedSlots: { customRender: "action" }
        }
      ],
      queryParam: {
        parkName: undefined
      },
      parkName: "", //存放园区名字给子组件
      id: "", //存放id给子组件
      visible: false, //控制编辑弹框的变量
      formData: {}, //用来存放获取的表单数据，将数据传给子组件
      newVisible: false, //新增弹窗
      newBuildingVisible: false,//楼号弹窗
      newBuildingRecord:{},
      newBuildingTitle:"新增楼号"
    };
  },
  created() {
    this.getMergeData();
  },
  methods: {
    //点击添加按钮调用的方法
    add() {
      this.newVisible = true;
    },
    newClose(data){
      this.newVisible = data;
    },
    //点击新增楼号按钮调用的方法
    toAddBuilding: function(record) {
      console.log(record, "rrrr");
      this.newBuildingTitle = "新增楼号"
      this.newBuildingVisible = true;
      this.newBuildingRecord = record
    },
    //点击编辑楼号按钮调用的方法
    toEditBuilding: function(record) {
      this.newBuildingTitle = "编辑楼号"
      this.newBuildingVisible = true;
      this.newBuildingRecord = record
    },
    //接收子组件传来的值关闭弹框
    newBuildingClose(data) {
      this.newBuildingVisible = data;
    },
    deduced() {
      ApiExportParkinfoMation(this.queryParam);
    },
    loadData: function(values) {
      this.pageNo = values.pageNo;
      this.pageSize = values.pageSize;
      const requestParameters = Object.assign(
        { pageNum: values.pageNo, pageSize: values.pageSize },
        this.queryParam
      );
      return queryParkBuildings(requestParameters).then(res => {
        let dataObj = res.data;
        dataObj.data = res.data.parksList;
        return dataObj;
      });
    },
    async getMergeData() {
      let industryComponey = await ApiSearchAllMergePark();
      this.enumerateObj.industryComponey = industryComponey.data;
    },
    search: function() {
      this.$refs.table.loadData({}, this.queryParam);
    },
    reset: function() {
      const param = {
        parkName: undefined
      };
      this.queryParam = param;
      this.$refs.table.loadData({}, this.queryParam);
    },
    toDetail(text, record) {
      this.$router.push(
        `/information-query/park-detail?parkName=${record.parkName}`
      );
    },
    //点击编辑按钮调用的方法
    toEdit: function(record) {
      ApiGetParkInfo({ parkId: record.parkId }).then(res => {
        this.formData = res.data;
      });
      console.log(record, "rrrr");
      this.visible = true;
      this.parkName = record.parkName;
      this.id = record.parkId;
    },
    //接收子组件传来的值关闭弹框
    close(data) {
      this.visible = data;
    }
  }
};
</script>

<style lang="less" scoped>
.sort {
  text-align: right;
}
/deep/.ant-select {
  width: 10rem;
}
</style>

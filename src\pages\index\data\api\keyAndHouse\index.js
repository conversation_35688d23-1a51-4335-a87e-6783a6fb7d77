import api, { formDownLoad } from "@/common/api";
import { BASE_URL } from "Config";
/**
 * @dec 接口：manager/BusinessClue/pageByCondition
 * @dec 命名：ApiActivitiCreate
 * @dec 接口功能 :新增公告管理
 */
export function pageByCondition(params) {
  return api({
    url: BASE_URL + "/manager/BusinessClue/pageByCondition",
    method: "post",
    params
  });
}
export function saveBusinessClue(params) {
  return api({
    url: BASE_URL + "/manager/BusinessClue/saveBusinessClue",
    method: "post",
    params
  });
}

export function getCompanyAbbr(params) {
  return api({
    url: BASE_URL + "/manager/household/getCompanyAbbr",
    method: "post",
    params
  });
}
/**
 * @dec 接口：/manager/BusinessClue/deleteById
 * @dec 命名：ApiActivitiCreate
 * @dec 接口功能 ：更新
 */

export function updatesaveBusinessClue(params) {
  return api({
    url: BASE_URL + "/manager/BusinessClue/updatesaveBusinessClue",
    method: "post",
    params
  });
}
//获取详情
export function getDetailById(params) {
  return api({
    url: BASE_URL + "/manager/BusinessClue/getDetailById",
    method: "post",
    params
  });
}

//审批
export function approvalBusinessClue(params) {
  return api({
    url: BASE_URL + "/manager/BusinessClue/approvalBusinessClue",
    method: "post",
    params
  });
}
//查看重点线索审批记录

export function getApprovalHistory(params) {
  return api({
    url: BASE_URL + "/manager/ContractApprovalHistory/getApprovalHistory",
    method: "post",
    params
  });
}
//户管列表
export function getPageByCondition(params) {
  return api({
    url: BASE_URL + "/manager/household/pageByCondition",
    method: "post",
    params
  });
}
//户管详情
export function getHouseholdDetail(params) {
  return api({
    url: BASE_URL + "/manager/household/getDetailById",
    method: "post",
    params
  });
}
//户管详情基本信息
export function getHouseholdBaseInfo(params) {
  return api({
    url: BASE_URL + "/manager/household/getHouseholdInfo",
    method: "post",
    params
  });
}
//新增、保存户管
export function addHousehold(params) {
  return api({
    url: BASE_URL + "/manager/household/saveHousehold",
    method: "post",
    params
  });
}
//编辑户管
export function updateHousehold(params) {
  return api({
    url: BASE_URL + "/manager/household/updateHousehold",
    method: "post",
    params
  });
}
//审批户管信息
export function approvalHousehold(params) {
  return api({
    url: BASE_URL + "/manager/household/approvalHousehold",
    method: "post",
    params
  });
}

//获取所有的营商分布数据

export function getCompanyAbbrList(params) {
  return api({
    url: BASE_URL + "/manager/household/getCompanyAbbrList",
    method: "post",
    params
  });
}
//区域
export function findAllParkName(params) {
  return api({
    url: BASE_URL + "/manage/admin/findAllParkName",
    method: "post",
    params
  });
}
//楼宇
export function queryAllBuilding(params) {
  return api({
    url: BASE_URL + "/buildings/parkBuilding/queryAllBuilding",
    method: "post",
    params
  });
}
//批量导出重点线索
export function exportBusinessClub(params) {
  return formDownLoad("/manager/BusinessClue/exportBusinessClub", params);
}
//批量导出户管
export function exportHousehold(params) {
  return formDownLoad("/manager/household/exportHousehold", params);
}

//manage/admin/findAllParkName
//buildings/parkBuilding/queryAllBuilding

/* eslint-disable no-unused-vars */
/**
 * <AUTHOR>
 * @time 2020-9-10
 * @dec 各种节点类
 */
import { HashCode } from "../../utils";
export class Node {
  nodeId;
  type;
  content = "请选择";
  constructor({ id, type, isRow }) {
    this.groupId = id;
    this.id = HashCode();
    this.type = type;
    this.title = setDefaultTitle(type);
    this.isRow = isRow;
  }
}
export class ConditionNode {
  content = "请选择";
  constructor({ groupId, type, id, isRow }) {
    this.id = HashCode();
    this.groupId = groupId;
    this.type = type;
    this.title = setDefaultTitle(type);
    this.groupPid = id;
    this.isRow = isRow;
    this.data = {};
  }
}
export class RowNode extends Node {
  data = {};
  constructor({ id, type, isRow }) {
    super({ id, type, isRow });
  }
}
export class CopyNode {
  id = HashCode();
  content = "请选择";
  data = {};
  constructor({ id, childNode, ...node }) {
    return Object.assign(node, this);
  }
}

/**
 * <AUTHOR>
 * @time 2020-10-11
 * @dec 初始化节点默认标题
 * @param {String} type 节点类型
 * @return {String} 返回默认标题 type为 '1':发起人 '2':审批人 '3':规则条件 '4':抄送人 '5':结束节点 '6':流转至
 */
function setDefaultTitle(type) {
  let title;
  switch (type) {
    case "1":
      title = "发起人";
      break;
    case "2":
      title = "审批人";
      break;
    case "3":
      title = "规则条件";
      break;
    case "4":
      title = "抄送人";
      break;
    case "5":
      title = "结束节点";
      break;
    case "6":
      title = "流转至";
      break;
  }
  return title;
}

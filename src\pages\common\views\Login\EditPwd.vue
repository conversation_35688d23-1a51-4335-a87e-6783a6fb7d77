<template>
  <common-layout>
    <div class="top">
      <div class="desc">初次登录请重置密码</div>
    </div>
    <div class="login">
      <a-form @submit="onSubmit" :form="form">
        <a-alert
          type="error"
          :closable="true"
          v-show="error"
          :message="error"
          showIcon
          style="margin-bottom: 24px"
        />
        <a-form-item>
          <a-input
            autocomplete="autocomplete"
            size="large"
            placeholder="请输入用户名"
            v-decorator="[
              'username',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入用户名',
                    whitespace: true,
                  },
                ],
              },
            ]"
          >
            <a-icon slot="prefix" type="user" />
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-input
            size="large"
            placeholder="请输入旧密码"
            autocomplete="autocomplete"
            type="password"
            v-decorator="[
              'password',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入旧密码',
                    whitespace: true,
                  },
                ],
              },
            ]"
          >
            <a-icon slot="prefix" type="lock" />
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-input
            size="large"
            placeholder="请输入新密码"
            autocomplete="autocomplete"
            type="password"
            v-decorator="[
              'updatePassword',
              {
                rules: [
                  {
                    required: true,
                    message: '请输入新密码',
                    whitespace: true,
                    trigger: 'blur',
                  },
                  {
                    validator: validatePass,
                  },
                ],
              },
            ]"
          >
            <a-icon slot="prefix" type="lock" />
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-input
            size="large"
            placeholder="请再次输入新密码"
            autocomplete="autocomplete"
            type="password"
            v-decorator="[
              'checkUpdatePassword',
              {
                rules: [
                  {
                    required: true,
                    message: '请再次输入新密码',
                    whitespace: true,
                    trigger: 'blur',
                  },
                  {
                    validator: validateCheckPass,
                  },
                ],
              },
            ]"
          >
            <a-icon slot="prefix" type="lock" /> </a-input
        ></a-form-item>
        <a-form-item>
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input
                size="large"
                placeholder="请输入验证码"
                v-decorator="[
                  'captcha',
                  {
                    rules: [
                      {
                        required: true,
                        message: '请输入验证码',
                        whitespace: true,
                      },
                    ],
                  },
                ]"
              >
                <a-icon slot="prefix" type="bulb" />
              </a-input>
            </a-col>
            <a-col :span="12">
              <img
                alt="暂无图片或未正确显示"
                title="点击切换"
                :src="'data:image/jpg;base64,' + this.captchaImg"
                id="captcha_img"
                width="180px"
                height="38px"
                style="margin-bottom: 3px"
                @click="changeImg()"
              />
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item>
          <a-button
            :loading="loading"
            style="width: 100%; margin-top: 24px"
            size="large"
            htmlType="submit"
            type="primary"
            >登录</a-button
          >
        </a-form-item>
      </a-form>
    </div>
  </common-layout>
</template>
<script>
import CommonLayout from "@/components/Layouts/CommonLayout";
// import { login, getRoutesConfig } from "@/services/user";
/**
 * @param ApiMapiLogin 登录接口
 * @param ApiMapiPermission 权限接口
 * @param ApiMapiCommonMenu 获取路由接口
 * @param ApiMapiCaptcha 获取验证码接口
 */
import {
  ApiMapiLogin,
  ApiMapiPermission,
  ApiMapiCommonMenu,
  ApiMapiCaptcha,
} from "@/data/api/common/auth";
import { setAuthorization, removeAuthorization } from "@/common/api";
// import { loadRoutes } from "@/common/router/routerUtil";
import { loadRoutes } from "@/common/router/routerUtil";
import { mapMutations } from "vuex";

export default {
  name: "Login",
  components: { CommonLayout },
  data() {
    return {
      loading: false,
      error: "",
      form: this.$form.createForm(this),
      captchaImg: "",
      captchakey: "",
    };
  },
  created() {},
  mounted() {
    this.getMapiCaptcha();
  },
  computed: {
    systemName() {
      return this.$store.state.setting.systemName;
    },
  },
  methods: {
    validatePass(rule, value, callback) {
      console.log(rule);
      const trimmedValue = value.trim();
      const pattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,16}$/;

      if (trimmedValue && !pattern.test(trimmedValue)) {
        callback(new Error("密码格式：必须包含数字和字母，密码位数8-16位。"));
        return;
      }
      const password = this.form.getFieldValue("password");
      if (trimmedValue === password) {
        callback(new Error("新密码不能与原密码一致"));
      }
      callback();
    },

    validateCheckPass(rule, value, callback) {
      console.log(value, "1231dsfs");
      const updatePassword = this.form.getFieldValue("updatePassword");
      if (value === "") {
        callback(new Error("请再次确认密码"));
      } else if (value !== updatePassword) {
        console.log("确认密码", value, updatePassword);
        callback(new Error("请输入相同密码"));
      } else {
        callback();
      }
    },
    handleClose() {
      this.visible = false;
      this.$router.push({
        path: `editPwd`,
      });
    },
    changeImg() {
      this.getMapiCaptcha();
    },
    getMapiCaptcha() {
      ApiMapiCaptcha()
        .then((res) => {
          this.captchaImg = res.data.img;
          this.captchakey = res.data.key;
          // console.log("图片", this.captchaImg);
          // console.log("图片", this.captchakey);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    ...mapMutations("account", [
      "setUser",
      "setPermissions",
      "setRoles",
      "clearRouterMapInvisible",
    ]),
    onSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err) => {
        if (!err) {
          this.loading = true;
          const username = this.form.getFieldValue("username");
          const password = this.form.getFieldValue("password");
          const updatePassword = this.form.getFieldValue("updatePassword");
          const captcha = this.form.getFieldValue("captcha");
          removeAuthorization();
          ApiMapiLogin({
            username,
            password,
            updatePassword,
            captcha,
            captchakey: this.captchakey,
          })
            .then(this.afterLogin)
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    afterLogin(res) {
      console.log("res", res);
      if (res.code == 0) {
        this.setUser(res.data);
        setAuthorization({
          token: res.data.utoken,
          expireAt: new Date(
            res.data.expireAt || new Date().getTime() * 1000 * 60 * 60 * 2
          ),
        });
        // 获取全局字典
        this.$store.dispatch("dictionaries/getDictionaries");
        ApiMapiPermission()
          .then((res) => {
            console.log(res);
            this.setPermissions(res.data);
            ApiMapiCommonMenu()
              .then((res) => {
                this.clearRouterMapInvisible();
                loadRoutes(
                  {
                    router: this.$router,
                    store: this.$store,
                    i18n: this.$i18n,
                  },
                  res.data
                );
                this.$message.success("登录成功", 3);
                this.$router.push("/");
              })
              .finally(() => {
                this.loading = false;
              });
          })
          .catch(() => {
            this.loading = false;
          })
          .finally(() => {});
        // this.setRoles(roles);
        // 获取路由配置
        // getRoutesConfig().then(result => {
        //   const routesConfig = result.data.data;
        //   loadRoutes(
        //     { router: this.$router, store: this.$store, i18n: this.$i18n },
        //     routesConfig
        //   );
        // });
      } else {
        this.error = res.msg;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.common-layout {
  .top {
    text-align: center;
    .header {
      .title {
        font-size: 33px;
        color: @title-color;
        font-family: "Myriad Pro", "Helvetica Neue", Arial, Helvetica,
          sans-serif;
        font-weight: 600;
        position: relative;
        top: 2px;
      }
    }
    .desc {
      font-size: 20px;
      color: @text-color-second;
      margin-top: 12px;
      margin-bottom: 40px;
    }
  }
  .login {
    width: 368px;
    margin: 0 auto;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
</style>

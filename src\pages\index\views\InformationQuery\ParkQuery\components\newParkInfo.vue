<template>
  <div class="form">
    <a-modal
      title="新增园区基础信息"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model :model="modalForm" ref="ruleForm" :rules="rules">
        <a-form-model-item
          label="实业公司"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          prop="lessor"
        >
          <a-select
            v-model="modalForm.lessor"
            placeholder="全部"
            @change="handleChangeLessor"
          >
            <a-select-option
              :value="item"
              v-for="item in parkArr"
              :key="item"
              >{{ item }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          label="归并园区"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          prop="mergedParkId"
        >
          <a-select
            v-model="modalForm.mergedParkId"
            placeholder="全部"
            @change="handleChange"
          >
            <a-select-option
              :value="item.mergedParkId"
              v-for="item in mergedParkIdArr"
              :key="item.mergedParkId"
              >{{ item.mergedParkName }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          prop="parkName"
          label="园区名称"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            v-model="modalForm.parkName"
            placeholder="请输入园区名称"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item
          prop="buildingNumber"
          label="楼号"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-input
            v-model="modalForm.buildingNumber"
            placeholder="请输入楼号"
          ></a-input>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
  // ApiEditParkInfo,
import { 
  ApiGetIndustryCompany,
  ApiFindMergedByLessor,
  ApiParkSave
} from "@/pages/index/data/api/InfomationQuery";
export default {
  props: {
    visible: Boolean
  },
  data() {
    return {
      isFlag:true,
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      modalForm: {
        lessor:undefined,//  出租方（实业公司名称）
        mergedParkId:undefined, //归并园区id
        mergedParkName:undefined,// 归并园区名
        parkName:"",//园区名称
        buildingNumber:""
      },
      parkArr:[],//实业公司
      mergedParkIdArr:[],//归并园区
      rules: {
        lessor: [
          { required: true, message: "请选择实业公司", trigger: "blur" },
        ],
        mergedParkId: [
          { required: true, message: "请选择归并园区", trigger: "blur" },
        ],
        parkName: [
          { required: true, message: "请选择园区名称", trigger: "blur" },
        ],
        buildingNumber: [
          { required: true, message: "请选择楼号", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    visible: {
      immediate: true,
      handler: function (val) {
        if (val) {
          console.log('newParkInfothisvisible',val);
          this.mergedParkIdArr = []
          this.init()
        }
      }
    },
  },
  mounted() {
    console.log('newParkInfothis.visible',this.visible);
  },
  methods: {
    handleChange(val){
      console.log(val);
      this.mergedParkIdArr.forEach(el => {
        if (el.mergedParkId == val) {
          this.modalForm.mergedParkName = el.mergedParkName
          console.log(this.modalForm);
        }
      });
    },
    // 获取归并园区列表
    handleChangeLessor(val){
      this.modalForm.mergedParkId = undefined
      ApiFindMergedByLessor({ lessor: val }).then((res) => {
        this.mergedParkIdArr = res.data;
        console.log(this.modalForm);
        
        console.log("newParkInfo获取归并园区列表====this.parkArray",this.mergedParkIdArr );
      });
    },
    // 获取实业公司列表
    async init() {
      let parkArr = await ApiGetIndustryCompany();
      this.parkArr = parkArr.data;
      console.log("newParkInfo获取归实业公司列表====this.parkArray",this.parkArr);
    },
    //弹框点击确认调用的方法
    handleOk: function() {
      if(!this.isFlag) return
      this.isFlag = false
      console.log(this.modalForm,  "kkkkk");
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          ApiParkSave(this.modalForm).then(res => {
            console.log(res.data);
            //将弹框隐藏
            if (res.code == 0) {
              this.handleCancel()
              this.$message.info(res.data);
              this.$emit("refresh", true);
            }else{
              this.$message.error(res.data||res.msg);
            }
          }).catch((error) => {
            //将弹框隐藏
            console.log('error',error);
            this.$message.error(error.data.msg);
          })
        }
      });
      setTimeout(()=>{
        this.isFlag = true
      },2000)
    },
    //关闭弹框调用的方法
    handleCancel: function() {
      //将弹框隐藏
      this.$emit("close", false);
      this.$refs.ruleForm.resetFields()
    }
  }
};
</script>
<style lang="less" scoped>

</style>
